package main

import (
	"context"
	"flag"
	"fmt"
	"liteframe/internal/common/constant"
	"os"
	"strconv"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/registry"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"

	"liteframe/internal/microservices/friend/boot"

	_ "go.uber.org/automaxprocs"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name = constant.ServiceNameFriend
	// Version is the version of the compiled software.
	Version       string
	ServerVersion string
	ClientVersion string

	// friend service type
	ServerType int
	// 服务器ID
	ServerId int

	id, _   = os.Hostname()
	PidFile = constant.ServiceNameFriend + ".pid"
)

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server, rr registry.Registrar, meta map[string]string) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(meta),
		kratos.Logger(logger),
		kratos.Server(
			gs,
			hs,
		),
		kratos.Registrar(rr),
		kratos.AfterStart(afterStart),
	)
}

func afterStart(context.Context) error {
	pid := fmt.Sprintf("%d", os.Getpid())
	f, err := os.OpenFile(PidFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		panic(err)
	}
	err = os.WriteFile(PidFile, []byte(pid), 0)
	if err != nil {
		panic(err)
	}
	err = f.Close()
	if err != nil {
		panic(err)
	}
	return nil
}

func main() {
	flag.Parse()

	serverKey := ServerType*10000 + ServerId

	// 初始化配置
	bf := boot.NewBootConf()
	bc := bf.Run(id, Name)
	defer bf.Stop()

	bc.Server.Id = id
	bc.Server.Name = Name
	bc.Server.Version = Version

	// 初始化log
	logger := boot.NewBootLog(bc).Run()

	log.Info("FriendService Init：", serverKey, "Version:", Version)

	metaData := map[string]string{}
	metaData["ServerVersion"] = ServerVersion
	metaData["ClientVersion"] = ClientVersion
	metaData["sid"] = strconv.Itoa(ServerId)
	metaData["type"] = strconv.Itoa(ServerType)

	app, cleanup, err := wireApp(bc.Server, bc.Data, bc, logger, metaData)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
