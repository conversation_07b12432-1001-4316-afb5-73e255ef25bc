//GrpcAddressType:FriendServer
//GrpcServerType:all
syntax = "proto3";

package Aurora.PlayerInfoServer;

import "google/api/annotations.proto";

option go_package = "gameserver/api/friendservice/v1;v1";

//ServiceStart
service Friendservice {

  rpc AddFriendApply(AddFriendApplyInfo) returns (AddFriendApplyResult) // 内网
  {
      option (google.api.http) = {
        post : "/gameserver/api/friendservice/addfriendapply",
        body : "*",
     };
  }

  rpc ApproveFriendApply(ApproveFriendApplyInfo) returns (ApproveFriendApplyResult) // 内网
  {
      option (google.api.http) = {
        post : "/gameserver/api/friendservice/approvefriendapply",
        body : "*",
     };
  }


  rpc RemoveFriendApply(RemoveFriendApplyInfo) returns (RemoveFriendApplyResult) // 内网
  {
      option (google.api.http) = {
        post : "/gameserver/api/friendservice/removefriendapply",
        body : "*",
     };
  }

rpc GetFriendApplyList(GetFriendApplyListInfo) returns (GetFriendApplyListResult) // 内网
{
    option (google.api.http) = {
      post : "/gameserver/api/friendservice/getfriendapplylist",
      body : "*",
    };
}


rpc AddFriendPlayer(AddFriendPlayerInfo) returns (AddFriendResult) // 内网
{
    option (google.api.http) = {
      post : "/gameserver/api/friendservice/addfriendplayer",
      body : "*",
   };
}
rpc DelFriendPlayer(DelFriendPlayerInfo) returns (DelFriendResult) // 内网
{
    option (google.api.http) = {
      post : "/gameserver/api/friendservice/delfriendplayer",
      body : "*",
    };
}
rpc SetFriendPlayer(SetFriendPlayerInfo) returns (SetFriendResult) // 内网
{
   option (google.api.http) = {
     post : "/gameserver/api/friendservice/setfriendplayer",
     body : "*",
   };
}
rpc GetFriendPlayerList(GetFriendPlayerListInfo) returns (GetFriendPlayerListInfoResult) // 内网 外网
{
option (google.api.http) = {
post : "/gameserver/api/friendservice/getfriendplayerlist",
body : "*",
};
}

rpc CreateFriendGroup(CreateFriendGroupInfo) returns (CreateFriendGroupResult) // 内网
{
//    option (google.api.http) = {
//      post : "/gameserver/api/friendservice/createfriendgroup",
//      body : "*",
//    };
}

rpc SetFriendGroup(SetFriendGroupInfo) returns (SetFriendGroupResult) // 内网
{
//    option (google.api.http) = {
//      post : "/gameserver/api/friendservice/setfriendgroup",
//      body : "*",
//    };
}

rpc DelFriendGroup(DelFriendGroupInfo) returns (DelFriendGroupResult) // 内网
{
//    option (google.api.http) = {
//      post : "/gameserver/api/friendservice/delfriendgroup",
//      body : "*",
//    };
}

rpc AddPlayerFriendGroup(AddPlayerFriendGroupInfo) returns (AddPlayerFriendGroupResult) // 内网
{
//    option (google.api.http) = {
//      post : "/gameserver/api/friendservice/addplayerfriendgroup",
//      body : "*",
//    };
}



rpc DelPlayerFriendGroup(AddPlayerFriendGroupInfo) returns (AddPlayerFriendGroupResult) // 内网
{
//    option (google.api.http) = {
//      post : "/gameserver/api/friendservice/delplayerfriendgroup",
//      body : "*",
//    };
}

rpc SetFriendRemarks(SetFriendRemarksInfo) returns (SetFriendRemarksResult) // 内网
{
//    option (google.api.http) = {
//      post : "/gameserver/api/friendservice/setfriendremarks",
//      body : "*",
//    };
}

rpc SetSelfAttention(SetSelfAttentionInfo) returns (SetSelfAttentionResult) // 内网
{
//    option (google.api.http) = {
//      post : "/gameserver/api/friendservice/setselfattention",
//      body : "*",
//    };
}

rpc SetGroupBase(GroupBaseInfo) returns (CreateGroupResult) // 内网
{
//    option (google.api.http) = {
//      post : "/gameserver/api/friendservice/setgroupbase",
//      body : "*",
//    };
}

rpc ApplyJoinGroup(ApplyGroupListInfo) returns (ApplyGroupListResult) // 内网
{
//    option (google.api.http) = {
//      post : "/gameserver/api/friendservice/applylistgroup",
//      body : "*",
//    };
}

rpc DelGroupPlayer(DelGroupPlayerInfo) returns (DelGroupPlayerResult) // 内网
{
//    option (google.api.http) = {
//      post : "/gameserver/api/friendservice/delgroupplayer",
//      body : "*",
//    };
}

rpc PlayerInviteJoinGroup(PlayerInviteJoinGroupInfo) returns (PlayerInviteJoinGroupResult) // 内网
{
//    option (google.api.http) = {
//      post : "/gameserver/api/friendservice/playerinvitejoingroup",
//      body : "*",
//    };
}
rpc GetGroupList(GetGroupListInfo) returns (GetGroupListResult) // 内网 外网
{
option (google.api.http) = {
post : "/gameserver/api/friendservice/getgrouplist",
body : "*",
};
}
rpc GetGroupPlayerListByGroupId(GetGroupPlayerListByGroupIdInfo) returns (GetGroupPlayerListByGroupIdResult) // 内网 外网
{
option (google.api.http) = {
post : "/gameserver/api/friendservice/getgrouplistbygroupid",
body : "*",
};
}

rpc SetFriendBaseInfoRep(SetFriendBaseInfo) returns (SetFriendBaseInfoResult) // 内网
{
   option (google.api.http) = {
     post : "/gameserver/api/friendservice/setfriendbaseinfo",
     body : "*",
   };
}

rpc GetMyGroupInviteJoinGroupListRep(GetMyGroupInviteJoinGroupList) returns (GetMyGroupInviteJoinGroupListResult) // 内网 外网
{
option (google.api.http) = {
post : "/gameserver/api/friendservice/getmygroupinvitejoingrouplist",
body : "*",
};
}

}
//ServiceEnd

//Type:Http
//Type:Inner
enum RelationType
{
node = 0;
friend = 1;
black = 2;
enemy = 3;
}

//Type:Http
//Type:Inner
enum FriendBaseEnum
{
remark = 0;
notAddFriend = 1;
recommendTeam = 2;
}

//Type:Http
message AddFriendPlayerInfo // add好友数据
{
uint64 myGuid = 1; //我的玩家ID
uint64 targetGuid = 2; //目标玩家ID
int32 serverId = 3; // 玩家所在服ID
int32 relationType = 4; // 1:好友 2:黑名单 3:仇人
int32 operationType = 5; // 操作信息
}



//Type:Http
message AddFriendResult
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
uint64 targetGuid = 3; //目标玩家ID
int32 serverId = 4; // 玩家所在服ID
int32 relationType = 5; // 1:好友 2:黑名单 3:仇人
int32 operationType = 6; // 操作信息
}
//Type:Http
message AddFriendApplyInfo 
{
  uint64 myGuid = 1;
  uint64 targetGuid = 2;
}
//Type:Http
message AddFriendApplyResult 
{
  int32 result = 1;
  uint64 myGuid = 2;
  uint64 targetGuid = 3;
}
//Type:Http
message AddFriendApplyResult2
{
  int32 test01 = 1;
  uint64 test02 = 2;
  uint64 targetGuid = 3;
}
//Type:Http
message RemoveFriendApplyInfo 
{
  uint64 myGuid = 1;
  uint64 targetGuid = 2;//为0表示一键拒绝 不为0表示单个拒绝
}
//Type:Http
message RemoveFriendApplyResult
{
  int32 result = 1;
  uint64 myGuid = 2;
  uint64 targetGuid = 3;
}
//Type:Http
message GetFriendApplyListInfo 
{
  uint64 myGuid = 1;
}
//Type:Http
message FriendApplyInfo 
{
  uint64 guid = 1;
  int32 serverId = 2;
}
//Type:Http
message GetFriendApplyListResult 
{
  int32 result = 1;
  uint64 myGuid = 2;
  repeated FriendApplyInfo applyList = 3;
}

//Type:Http
message ApproveFriendApplyInfo  // 批准好友申请请求
{  
  uint64 myGuid = 1;
  uint64 targetGuid = 2;//为0表示一键同意 不为0表示单个同意
  int32 serverId = 3;
}


//Type:Http
message ApproveFriendApplyResult  // 批准好友申请响应
{
  int32 result = 1;
  uint64 myGuid = 2;
  uint64 targetGuid = 3;
}


//Type:Http
message DelFriendPlayerInfo // add好友数据
{
uint64 myGuid = 1; //我的玩家ID
uint64 targetGuid = 2; //目标玩家ID
int32 serverId = 3; // 玩家所在服ID
int32 relationType = 4; // 1:好友 2:黑名单 3:仇人
int32 operationType = 5; // 操作信息
}

//Type:Http
message DelFriendResult
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
uint64 targetGuid = 3; //目标玩家ID
int32 serverId = 4; // 玩家所在服ID
int32 relationType = 5; // 1:好友 2:黑名单 3:仇人
int32 operationType = 6; // 操作信息
}

//Type:Http
message SetFriendPlayerInfo // add好友数据
{
uint64 myGuid = 1; //我的玩家ID
uint64 targetGuid = 2; //目标玩家ID
int32 serverId = 3; // 玩家所在服ID
int32 relationType = 4; // 1:好友 2:黑名单 3:仇人
int32 operationType = 5; // 操作信息

}

//Type:Http
message SetFriendResult
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
uint64 targetGuid = 3; //目标玩家ID
int32 serverId = 4; // 玩家所在服ID
int32 relationType = 5; // 1:好友 2:黑名单 3:仇人
int32 operationType = 6; // 操作信息

}

//Type:Http
message GetFriendPlayerListInfo // add好友数据
{
uint64 guid = 1; //玩家ID
int32 serverId = 2; // 玩家所在服ID
int32 relationType = 3; //0 全部  1:好友 2:黑名单 3:仇人
int32 operationType = 5; // 操作信息  0 全部， 1 对应关系联系人， 2 我的关注列表， 3 关注我的列表， 4 我的分组列表， 5 好友关系组  6 第一次登录 尝试给关注我的好友发信息

}

//Type:Http
message FriendPlayerBase // 好友基础数据
{
uint64 guid = 1; //guid
int32 serverId = 2; //好友的服务器id
int32 friendPoint = 3; //好友度
string remarks = 4; //好友备注

}

//Type:Http
message FriendAttentionBase // 关注玩家id
{
uint64 guid = 1; //guid
int32 serverId = 2; //好友的服务器id
}

//Type:Http
message FriendGroupPlayer // 我的分组
{
int32 groupId = 1; //组id
string groupName = 2; //分组名称
repeated uint64 groupPlayerGuid = 3; //分组玩家的gui

}

//Type:Http
message FriendGroupRelation // 关系组
{
int32 relationType = 1; //关系类型
repeated uint64 playerGuid = 2; //玩家的gui
}

//Type:Http
message GetFriendPlayerListInfoResult
{
int32 result = 1;
uint64 guid = 2; //请求人的guid
repeated FriendPlayerBase friendList = 3; //好友列表
repeated FriendAttentionBase selfAttentionList = 4; //我关注列表
repeated FriendAttentionBase otherAttentionList = 5; //关注我的列表
repeated FriendGroupPlayer friendGroupList = 6; //好友分组列表
repeated FriendGroupRelation friendRelationList = 7; //关系组
int32 operationType = 8; // 操作信息

}

//Type:Http
message CreateFriendGroupInfo // 好友分组
{
uint64 myGuid = 1; //我的玩家ID
string groupName = 2; //分组名称
int32 operationType = 5; // 操作信息

}

//Type:Http
message CreateFriendGroupResult // 好友分组
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
int32 groupId = 3; //分组id
string groupName = 4; //分组名称
int32 operationType = 5; // 操作信息

}

//Type:Http
message SetFriendGroupInfo // 好友分组
{
uint64 myGuid = 1; //我的玩家ID
int32 groupId = 2; //分组id
string groupName = 3; //分组名称
int32 operationType = 5; // 操作信息

}

//Type:Http
message SetFriendGroupResult // 好友分组
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
int32 groupId = 3; //分组id
string groupName = 4; //分组名称
int32 operationType = 5; // 操作信息

}

//Type:Http
message DelFriendGroupInfo // 好友分组
{
uint64 myGuid = 1; //我的玩家ID
int32 groupId = 2; //分组id
int32 operationType = 3; // 操作信息

}

//Type:Http
message DelFriendGroupResult // 好友分组
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
int32 groupId = 3; //分组id
int32 operationType = 4; // 操作信息

}

//Type:Http
message AddPlayerFriendGroupInfo // 好友分组
{
uint64 myGuid = 1; //我的玩家ID
int32 groupId = 2; //分组id
repeated uint64 playerListGuid = 3; //guid list
int32 operationType = 4; // 操作信息

}

//Type:Http
message AddPlayerFriendGroupResult // 好友分组
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
int32 groupId = 3; //分组id
repeated uint64 playerListGuid = 4; //guid list
int32 operationType = 5; // 操作信息

}

//Type:Http
message SetFriendRemarksInfo // 好友备注
{
uint64 myGuid = 1; //我的玩家ID
uint64 targetGuid = 2; //目标玩家ID
string remarks = 3; //好友备注
int32 operationType = 4; // 操作信息
}

//Type:Http
message SetFriendRemarksResult // 好友备注
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
uint64 targetGuid = 3; //目标玩家ID
string remarks = 4; //好友备注
int32 operationType = 5; // 操作信息
}

//Type:Http
message SetSelfAttentionInfo // 关注好友
{
uint64 myGuid = 1; //我的玩家ID
uint64 targetGuid = 2; //目标玩家ID
int32 type = 3; //类型 1 加 2 减
int32 operationType = 4; // 操作信息

}

//Type:Http
message SetSelfAttentionResult // 关注好友
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
uint64 targetGuid = 3; //目标玩家ID
int32 type = 4; //类型 1 加 2 减
int32 operationType = 5; // 操作信息

}

//Type:Http
message GroupBaseInfo // 群组 基础 创建 删除 修改信息
{
uint64 myGuid = 1; //我的玩家ID
int32 serverId = 2; //服务器id
uint64 groupId = 3; //群组id
string groupName = 4; //群组名称
string groupNotice = 5; //群组通知
int32 groupType = 6; //群组类型
int32 type = 7; //类型 1 加 2 减 3 修改
int32 operationType = 8; // 操作信息
}

//Type:Http
message CreateGroupResult // 群组基础
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
int32 serverId = 3; //服务器id
uint64 groupId = 4; //群组id
string groupName = 5; //群组名称
string groupNotice = 6; //群组通知
int32 groupType = 7; //群组类型
int32 type = 8; //类型 1 加 2 减 3 修改
int32 operationType = 9; // 操作信息
}

//Type:Http
message RedisGroupBaseInfo // redis 群组基础信息
{
int32 serverId = 1; //服务器id
uint64 groupId = 2; //群组id
string groupName = 3; //群组名称
string groupNotice = 4; //群组通知
int32 groupType = 5; //群组类型
}

//Type:Http
message RedisFriendItemKey // redis 好友的基础信息
{
uint64 guid = 1; //guid
int32 serverId = 2; // 服务器id
int32 friendPoint = 3; //好感度
string remarks = 4; //备注
}

//Type:Http
message ApplyGroupListInfo // 邀请 加入群组
{
uint64 myGuid = 1; //我的玩家ID
int32 serverId = 2; //服务器id
uint64 groupId = 3; //群组id
uint64 targetGuid = 4; //对方的guid
string targetServerId = 5; // 对方的服务器id
int32 type = 6; //类型 1 邀请
int32 operationType = 7; // 操作信息

}

//Type:Http
message ApplyGroupListResult // 关注好友
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
int32 serverId = 3; //服务器id
uint64 groupId = 4; //群组id
uint64 targetGuid = 5; //对方的guid
string targetServerId = 6; // 对方的服务器id
int32 type = 7; //类型 1 邀请
int32 operationType = 8; // 操作信息

}

//Type:Http
message DelGroupPlayerInfo // 删除群组玩家
{
uint64 myGuid = 1; //我的玩家ID
int32 serverId = 2; //服务器id
uint64 groupId = 3; //群组id
uint64 targetGuid = 4; //对方的guid
string targetServerId = 5; // 对方的服务器id
int32 operationType = 6; // 操作信息

}

//Type:Http
message DelGroupPlayerResult // 关注好友
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
int32 serverId = 3; //服务器id
uint64 groupId = 4; //群组id
uint64 targetGuid = 5; //对方的guid
string targetServerId = 6; // 对方的服务器id
int32 operationType = 7; // 操作信息
}

//Type:Http
message PlayerInviteJoinGroupInfo // 是否同意邀请
{
uint64 myGuid = 1; //我的玩家ID
int32 serverId = 2; //服务器id
uint64 groupId = 3; //群组id
int32 res = 4; // 0 拒绝 1 通过
int32 operationType = 5; // 操作信息

}

//Type:Http
message PlayerInviteJoinGroupResult // 关注好友
{
int32 result = 1;
uint64 myGuid = 2; //我的玩家ID
int32 serverId = 3; //服务器id
uint64 groupId = 4; //群组id
int32 res = 5; // 0 拒绝 1 通过
int32 operationType = 6; // 操作信息
}

//Type:Http
message GetGroupListInfo // 拉起群组列表
{
uint64 myGuid = 1; //我的玩家ID
int32 serverId = 2; //服务器id
int32 type = 3; //1 我的 2 全服的  3 某个
int32 operationType = 4; // 操作信息
uint64 groupId = 5; //某个人群组信息
}

//Type:Http
message ClientGroupPlayerInfo // 群组 成员信息
{
uint64 guid = 1;
int32 type = 2; // 1 群主 2 普通
}

//Type:Http
message ClientGroupBase // 客户端群组基础信息
{
RedisGroupBaseInfo groupBase = 1;
repeated ClientGroupPlayerInfo groupPlayers = 2;
}

//Type:Http
message GetGroupListResult // 群组列表
{
int32 result = 1;
int32 operationType = 2; // 操作信息
repeated ClientGroupBase groupList = 3;
}

//Type:Http
message GetGroupPlayerListByGroupIdInfo // 拉群组成员列表
{
uint64 groupId = 1; //群组id
int32 operationType = 2; // 操作信息
}

//Type:Http
message GetGroupPlayerListByGroupIdResult // 拉群组成员列表
{
int32 result = 1;
repeated uint64 playerList = 2;
}

//Type:Http
message SetFriendBaseInfo // 拉群组成员列表
{
uint64 guid = 1;
int32 operationType = 2; // 1 获取， 2 修改
repeated int32 setInfo = 3;
}

//Type:Http
message SetFriendBaseInfoResult // 拉群组成员列表
{
int32 result = 1;
int32 operationType = 2; // 1 获取， 2 修改
repeated int32 setInfo = 3;
}

//Type:Http
message GetMyGroupInviteJoinGroupList // 拉 我被邀请的列表
{
uint64 myGuid = 1; //我的guid
int32 operationType = 2; //
int32 result = 3;
int32 serverId = 4; //服务器id

}

//Type:Http
message GetMyGroupInviteJoinGroupListResult // 拉 我被邀请的列表
{
uint64 myGuid = 1; //我的guid
int32 operationType = 2; //
int32 result = 3;
repeated uint64 groupList = 4; // 被邀请的群组id
}

