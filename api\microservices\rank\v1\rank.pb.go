//GrpcAddressType:Rank
//GrpcServerType:server,world

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.23.2
// source: microservices/rank/v1/rank.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "liteframe/api/microservices/playerinfo/v1"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RankGroupType int32

const (
	RankGroupType_RoleRank RankGroupType = 0
)

// Enum value maps for RankGroupType.
var (
	RankGroupType_name = map[int32]string{
		0: "RoleRank",
	}
	RankGroupType_value = map[string]int32{
		"RoleRank": 0,
	}
)

func (x RankGroupType) Enum() *RankGroupType {
	p := new(RankGroupType)
	*p = x
	return p
}

func (x RankGroupType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RankGroupType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_rank_v1_rank_proto_enumTypes[0].Descriptor()
}

func (RankGroupType) Type() protoreflect.EnumType {
	return &file_microservices_rank_v1_rank_proto_enumTypes[0]
}

func (x RankGroupType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RankGroupType.Descriptor instead.
func (RankGroupType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{0}
}

// Type:Http
type RankType int32

const (
	RankType_RankTypeNone RankType = 0
	// 总评分
	RankType_FightPoint RankType = 101
)

// Enum value maps for RankType.
var (
	RankType_name = map[int32]string{
		0:   "RankTypeNone",
		101: "FightPoint",
	}
	RankType_value = map[string]int32{
		"RankTypeNone": 0,
		"FightPoint":   101,
	}
)

func (x RankType) Enum() *RankType {
	p := new(RankType)
	*p = x
	return p
}

func (x RankType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RankType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_rank_v1_rank_proto_enumTypes[1].Descriptor()
}

func (RankType) Type() protoreflect.EnumType {
	return &file_microservices_rank_v1_rank_proto_enumTypes[1]
}

func (x RankType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RankType.Descriptor instead.
func (RankType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{1}
}

type RankRoleInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uid           uint64                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Kserver       int32                  `protobuf:"varint,3,opt,name=kserver,proto3" json:"kserver,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Level         int32                  `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`
	FightPoint    int64                  `protobuf:"varint,7,opt,name=fightPoint,proto3" json:"fightPoint,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RankRoleInfo) Reset() {
	*x = RankRoleInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RankRoleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankRoleInfo) ProtoMessage() {}

func (x *RankRoleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankRoleInfo.ProtoReflect.Descriptor instead.
func (*RankRoleInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{0}
}

func (x *RankRoleInfo) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *RankRoleInfo) GetKserver() int32 {
	if x != nil {
		return x.Kserver
	}
	return 0
}

func (x *RankRoleInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RankRoleInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *RankRoleInfo) GetFightPoint() int64 {
	if x != nil {
		return x.FightPoint
	}
	return 0
}

type SyncRoleInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Info          *RankRoleInfo          `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncRoleInfoRequest) Reset() {
	*x = SyncRoleInfoRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncRoleInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncRoleInfoRequest) ProtoMessage() {}

func (x *SyncRoleInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncRoleInfoRequest.ProtoReflect.Descriptor instead.
func (*SyncRoleInfoRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{1}
}

func (x *SyncRoleInfoRequest) GetInfo() *RankRoleInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

// The response message containing the message
type SyncRoleInfoReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncRoleInfoReply) Reset() {
	*x = SyncRoleInfoReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncRoleInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncRoleInfoReply) ProtoMessage() {}

func (x *SyncRoleInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncRoleInfoReply.ProtoReflect.Descriptor instead.
func (*SyncRoleInfoReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{2}
}

func (x *SyncRoleInfoReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type RankValueInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RankType      int32                  `protobuf:"varint,1,opt,name=rankType,proto3" json:"rankType,omitempty"`
	RankValue     int64                  `protobuf:"varint,2,opt,name=rankValue,proto3" json:"rankValue,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RankValueInfo) Reset() {
	*x = RankValueInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RankValueInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankValueInfo) ProtoMessage() {}

func (x *RankValueInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankValueInfo.ProtoReflect.Descriptor instead.
func (*RankValueInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{3}
}

func (x *RankValueInfo) GetRankType() int32 {
	if x != nil {
		return x.RankType
	}
	return 0
}

func (x *RankValueInfo) GetRankValue() int64 {
	if x != nil {
		return x.RankValue
	}
	return 0
}

type UpdateRoleRankRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uid           uint64                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Kserver       int32                  `protobuf:"varint,3,opt,name=kserver,proto3" json:"kserver,omitempty"`
	Info          []*RankValueInfo       `protobuf:"bytes,4,rep,name=info,proto3" json:"info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRoleRankRequest) Reset() {
	*x = UpdateRoleRankRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRoleRankRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleRankRequest) ProtoMessage() {}

func (x *UpdateRoleRankRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleRankRequest.ProtoReflect.Descriptor instead.
func (*UpdateRoleRankRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateRoleRankRequest) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *UpdateRoleRankRequest) GetKserver() int32 {
	if x != nil {
		return x.Kserver
	}
	return 0
}

func (x *UpdateRoleRankRequest) GetInfo() []*RankValueInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type UpdateRoleRankReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRoleRankReply) Reset() {
	*x = UpdateRoleRankReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRoleRankReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRoleRankReply) ProtoMessage() {}

func (x *UpdateRoleRankReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRoleRankReply.ProtoReflect.Descriptor instead.
func (*UpdateRoleRankReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateRoleRankReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

// Type:Inner
type RemoveInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uid           uint64                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Kserver       int32                  `protobuf:"varint,3,opt,name=kserver,proto3" json:"kserver,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveInfo) Reset() {
	*x = RemoveInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveInfo) ProtoMessage() {}

func (x *RemoveInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveInfo.ProtoReflect.Descriptor instead.
func (*RemoveInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{6}
}

func (x *RemoveInfo) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *RemoveInfo) GetKserver() int32 {
	if x != nil {
		return x.Kserver
	}
	return 0
}

type RemoveRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Role          *RemoveInfo            `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveRoleRequest) Reset() {
	*x = RemoveRoleRequest{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRoleRequest) ProtoMessage() {}

func (x *RemoveRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRoleRequest.ProtoReflect.Descriptor instead.
func (*RemoveRoleRequest) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{7}
}

func (x *RemoveRoleRequest) GetRole() *RemoveInfo {
	if x != nil {
		return x.Role
	}
	return nil
}

type RemoveRoleReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveRoleReply) Reset() {
	*x = RemoveRoleReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveRoleReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveRoleReply) ProtoMessage() {}

func (x *RemoveRoleReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveRoleReply.ProtoReflect.Descriptor instead.
func (*RemoveRoleReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{8}
}

func (x *RemoveRoleReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

// Type:Http
type RoleRankInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uid           uint64                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Level         int32                  `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
	FightPoint    int64                  `protobuf:"varint,6,opt,name=fightPoint,proto3" json:"fightPoint,omitempty"`
	Rank          int32                  `protobuf:"varint,7,opt,name=rank,proto3" json:"rank,omitempty"`
	Tilte         int32                  `protobuf:"varint,8,opt,name=tilte,proto3" json:"tilte,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoleRankInfo) Reset() {
	*x = RoleRankInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoleRankInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleRankInfo) ProtoMessage() {}

func (x *RoleRankInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleRankInfo.ProtoReflect.Descriptor instead.
func (*RoleRankInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{9}
}

func (x *RoleRankInfo) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *RoleRankInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RoleRankInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *RoleRankInfo) GetFightPoint() int64 {
	if x != nil {
		return x.FightPoint
	}
	return 0
}

func (x *RoleRankInfo) GetRank() int32 {
	if x != nil {
		return x.Rank
	}
	return 0
}

func (x *RoleRankInfo) GetTilte() int32 {
	if x != nil {
		return x.Tilte
	}
	return 0
}

// Type:Http
type RankDataInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Role          *RoleRankInfo          `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	RankValue     int64                  `protobuf:"varint,10,opt,name=rankValue,proto3" json:"rankValue,omitempty"`
	RankValue2    int64                  `protobuf:"varint,11,opt,name=rankValue2,proto3" json:"rankValue2,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RankDataInfo) Reset() {
	*x = RankDataInfo{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RankDataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RankDataInfo) ProtoMessage() {}

func (x *RankDataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RankDataInfo.ProtoReflect.Descriptor instead.
func (*RankDataInfo) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{10}
}

func (x *RankDataInfo) GetRole() *RoleRankInfo {
	if x != nil {
		return x.Role
	}
	return nil
}

func (x *RankDataInfo) GetRankValue() int64 {
	if x != nil {
		return x.RankValue
	}
	return 0
}

func (x *RankDataInfo) GetRankValue2() int64 {
	if x != nil {
		return x.RankValue2
	}
	return 0
}

// Type:Http
type GetRankListReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Uid   uint64                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// 排行榜大类 0：角色 1：公会 2：队伍 3:单个幻灵
	RankGroup int32 `protobuf:"varint,3,opt,name=rankGroup,proto3" json:"rankGroup,omitempty"`
	// 排行榜类型
	RankType int32 `protobuf:"varint,4,opt,name=rankType,proto3" json:"rankType,omitempty"`
	// 三级页签 0：总榜 1：好友
	RankFlag int32 `protobuf:"varint,5,opt,name=rankFlag,proto3" json:"rankFlag,omitempty"`
	// 分页 0：全部  （总共100行&每页50行）
	RankPage      int32 `protobuf:"varint,6,opt,name=rankPage,proto3" json:"rankPage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRankListReq) Reset() {
	*x = GetRankListReq{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRankListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRankListReq) ProtoMessage() {}

func (x *GetRankListReq) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRankListReq.ProtoReflect.Descriptor instead.
func (*GetRankListReq) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{11}
}

func (x *GetRankListReq) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *GetRankListReq) GetRankGroup() int32 {
	if x != nil {
		return x.RankGroup
	}
	return 0
}

func (x *GetRankListReq) GetRankType() int32 {
	if x != nil {
		return x.RankType
	}
	return 0
}

func (x *GetRankListReq) GetRankFlag() int32 {
	if x != nil {
		return x.RankFlag
	}
	return 0
}

func (x *GetRankListReq) GetRankPage() int32 {
	if x != nil {
		return x.RankPage
	}
	return 0
}

// Type:Http
type GetRankListReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Uid           uint64                 `protobuf:"varint,1,opt,name=uid,proto3" json:"uid,omitempty"`
	RankGroup     int32                  `protobuf:"varint,2,opt,name=rankGroup,proto3" json:"rankGroup,omitempty"`
	RankType      int32                  `protobuf:"varint,3,opt,name=rankType,proto3" json:"rankType,omitempty"`
	RankFlag      int32                  `protobuf:"varint,4,opt,name=rankFlag,proto3" json:"rankFlag,omitempty"`
	RankPage      int32                  `protobuf:"varint,5,opt,name=rankPage,proto3" json:"rankPage,omitempty"`
	Data          []*RankDataInfo        `protobuf:"bytes,10,rep,name=data,proto3" json:"data,omitempty"`
	SelfInfo      *RankDataInfo          `protobuf:"bytes,20,opt,name=selfInfo,proto3" json:"selfInfo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRankListReply) Reset() {
	*x = GetRankListReply{}
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRankListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRankListReply) ProtoMessage() {}

func (x *GetRankListReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_rank_v1_rank_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRankListReply.ProtoReflect.Descriptor instead.
func (*GetRankListReply) Descriptor() ([]byte, []int) {
	return file_microservices_rank_v1_rank_proto_rawDescGZIP(), []int{12}
}

func (x *GetRankListReply) GetUid() uint64 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *GetRankListReply) GetRankGroup() int32 {
	if x != nil {
		return x.RankGroup
	}
	return 0
}

func (x *GetRankListReply) GetRankType() int32 {
	if x != nil {
		return x.RankType
	}
	return 0
}

func (x *GetRankListReply) GetRankFlag() int32 {
	if x != nil {
		return x.RankFlag
	}
	return 0
}

func (x *GetRankListReply) GetRankPage() int32 {
	if x != nil {
		return x.RankPage
	}
	return 0
}

func (x *GetRankListReply) GetData() []*RankDataInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *GetRankListReply) GetSelfInfo() *RankDataInfo {
	if x != nil {
		return x.SelfInfo
	}
	return nil
}

var File_microservices_rank_v1_rank_proto protoreflect.FileDescriptor

const file_microservices_rank_v1_rank_proto_rawDesc = "" +
	"\n" +
	" microservices/rank/v1/rank.proto\x12\x17Aurora.PlayerInfoServer\x1a\x1cgoogle/api/annotations.proto\x1a$playerinfo/v1/playerinfostruct.proto\"\x84\x01\n" +
	"\fRankRoleInfo\x12\x10\n" +
	"\x03uid\x18\x01 \x01(\x04R\x03uid\x12\x18\n" +
	"\akserver\x18\x03 \x01(\x05R\akserver\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x14\n" +
	"\x05level\x18\x06 \x01(\x05R\x05level\x12\x1e\n" +
	"\n" +
	"fightPoint\x18\a \x01(\x03R\n" +
	"fightPoint\"P\n" +
	"\x13SyncRoleInfoRequest\x129\n" +
	"\x04info\x18\x01 \x01(\v2%.Aurora.PlayerInfoServer.RankRoleInfoR\x04info\"+\n" +
	"\x11SyncRoleInfoReply\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\"I\n" +
	"\rRankValueInfo\x12\x1a\n" +
	"\brankType\x18\x01 \x01(\x05R\brankType\x12\x1c\n" +
	"\trankValue\x18\x02 \x01(\x03R\trankValue\"\x7f\n" +
	"\x15UpdateRoleRankRequest\x12\x10\n" +
	"\x03uid\x18\x01 \x01(\x04R\x03uid\x12\x18\n" +
	"\akserver\x18\x03 \x01(\x05R\akserver\x12:\n" +
	"\x04info\x18\x04 \x03(\v2&.Aurora.PlayerInfoServer.RankValueInfoR\x04info\"-\n" +
	"\x13UpdateRoleRankReply\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\"8\n" +
	"\n" +
	"RemoveInfo\x12\x10\n" +
	"\x03uid\x18\x01 \x01(\x04R\x03uid\x12\x18\n" +
	"\akserver\x18\x03 \x01(\x05R\akserver\"L\n" +
	"\x11RemoveRoleRequest\x127\n" +
	"\x04role\x18\x01 \x01(\v2#.Aurora.PlayerInfoServer.RemoveInfoR\x04role\")\n" +
	"\x0fRemoveRoleReply\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\"\x94\x01\n" +
	"\fRoleRankInfo\x12\x10\n" +
	"\x03uid\x18\x01 \x01(\x04R\x03uid\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x14\n" +
	"\x05level\x18\x05 \x01(\x05R\x05level\x12\x1e\n" +
	"\n" +
	"fightPoint\x18\x06 \x01(\x03R\n" +
	"fightPoint\x12\x12\n" +
	"\x04rank\x18\a \x01(\x05R\x04rank\x12\x14\n" +
	"\x05tilte\x18\b \x01(\x05R\x05tilte\"\x87\x01\n" +
	"\fRankDataInfo\x129\n" +
	"\x04role\x18\x01 \x01(\v2%.Aurora.PlayerInfoServer.RoleRankInfoR\x04role\x12\x1c\n" +
	"\trankValue\x18\n" +
	" \x01(\x03R\trankValue\x12\x1e\n" +
	"\n" +
	"rankValue2\x18\v \x01(\x03R\n" +
	"rankValue2\"\x94\x01\n" +
	"\x0eGetRankListReq\x12\x10\n" +
	"\x03uid\x18\x01 \x01(\x04R\x03uid\x12\x1c\n" +
	"\trankGroup\x18\x03 \x01(\x05R\trankGroup\x12\x1a\n" +
	"\brankType\x18\x04 \x01(\x05R\brankType\x12\x1a\n" +
	"\brankFlag\x18\x05 \x01(\x05R\brankFlag\x12\x1a\n" +
	"\brankPage\x18\x06 \x01(\x05R\brankPage\"\x94\x02\n" +
	"\x10GetRankListReply\x12\x10\n" +
	"\x03uid\x18\x01 \x01(\x04R\x03uid\x12\x1c\n" +
	"\trankGroup\x18\x02 \x01(\x05R\trankGroup\x12\x1a\n" +
	"\brankType\x18\x03 \x01(\x05R\brankType\x12\x1a\n" +
	"\brankFlag\x18\x04 \x01(\x05R\brankFlag\x12\x1a\n" +
	"\brankPage\x18\x05 \x01(\x05R\brankPage\x129\n" +
	"\x04data\x18\n" +
	" \x03(\v2%.Aurora.PlayerInfoServer.RankDataInfoR\x04data\x12A\n" +
	"\bselfInfo\x18\x14 \x01(\v2%.Aurora.PlayerInfoServer.RankDataInfoR\bselfInfo*\x1d\n" +
	"\rRankGroupType\x12\f\n" +
	"\bRoleRank\x10\x00*,\n" +
	"\bRankType\x12\x10\n" +
	"\fRankTypeNone\x10\x00\x12\x0e\n" +
	"\n" +
	"FightPoint\x10e2\xd5\x02\n" +
	"\vRankService\x12j\n" +
	"\fSyncRoleInfo\x12,.Aurora.PlayerInfoServer.SyncRoleInfoRequest\x1a*.Aurora.PlayerInfoServer.SyncRoleInfoReply\"\x00\x12t\n" +
	"\x12UpdateRoleRankInfo\x12..Aurora.PlayerInfoServer.UpdateRoleRankRequest\x1a,.Aurora.PlayerInfoServer.UpdateRoleRankReply\"\x00\x12d\n" +
	"\n" +
	"RemoveRole\x12*.Aurora.PlayerInfoServer.RemoveRoleRequest\x1a(.Aurora.PlayerInfoServer.RemoveRoleReply\"\x002\x9c\x01\n" +
	"\fRankListHttp\x12\x8b\x01\n" +
	"\x0fGetRankListData\x12'.Aurora.PlayerInfoServer.GetRankListReq\x1a).Aurora.PlayerInfoServer.GetRankListReply\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/api/rankservice/ranklistB\"Z gameserver/api/rankservice/v1;v1b\x06proto3"

var (
	file_microservices_rank_v1_rank_proto_rawDescOnce sync.Once
	file_microservices_rank_v1_rank_proto_rawDescData []byte
)

func file_microservices_rank_v1_rank_proto_rawDescGZIP() []byte {
	file_microservices_rank_v1_rank_proto_rawDescOnce.Do(func() {
		file_microservices_rank_v1_rank_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_microservices_rank_v1_rank_proto_rawDesc), len(file_microservices_rank_v1_rank_proto_rawDesc)))
	})
	return file_microservices_rank_v1_rank_proto_rawDescData
}

var file_microservices_rank_v1_rank_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_microservices_rank_v1_rank_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_microservices_rank_v1_rank_proto_goTypes = []any{
	(RankGroupType)(0),            // 0: Aurora.PlayerInfoServer.RankGroupType
	(RankType)(0),                 // 1: Aurora.PlayerInfoServer.RankType
	(*RankRoleInfo)(nil),          // 2: Aurora.PlayerInfoServer.RankRoleInfo
	(*SyncRoleInfoRequest)(nil),   // 3: Aurora.PlayerInfoServer.SyncRoleInfoRequest
	(*SyncRoleInfoReply)(nil),     // 4: Aurora.PlayerInfoServer.SyncRoleInfoReply
	(*RankValueInfo)(nil),         // 5: Aurora.PlayerInfoServer.RankValueInfo
	(*UpdateRoleRankRequest)(nil), // 6: Aurora.PlayerInfoServer.UpdateRoleRankRequest
	(*UpdateRoleRankReply)(nil),   // 7: Aurora.PlayerInfoServer.UpdateRoleRankReply
	(*RemoveInfo)(nil),            // 8: Aurora.PlayerInfoServer.RemoveInfo
	(*RemoveRoleRequest)(nil),     // 9: Aurora.PlayerInfoServer.RemoveRoleRequest
	(*RemoveRoleReply)(nil),       // 10: Aurora.PlayerInfoServer.RemoveRoleReply
	(*RoleRankInfo)(nil),          // 11: Aurora.PlayerInfoServer.RoleRankInfo
	(*RankDataInfo)(nil),          // 12: Aurora.PlayerInfoServer.RankDataInfo
	(*GetRankListReq)(nil),        // 13: Aurora.PlayerInfoServer.GetRankListReq
	(*GetRankListReply)(nil),      // 14: Aurora.PlayerInfoServer.GetRankListReply
}
var file_microservices_rank_v1_rank_proto_depIdxs = []int32{
	2,  // 0: Aurora.PlayerInfoServer.SyncRoleInfoRequest.info:type_name -> Aurora.PlayerInfoServer.RankRoleInfo
	5,  // 1: Aurora.PlayerInfoServer.UpdateRoleRankRequest.info:type_name -> Aurora.PlayerInfoServer.RankValueInfo
	8,  // 2: Aurora.PlayerInfoServer.RemoveRoleRequest.role:type_name -> Aurora.PlayerInfoServer.RemoveInfo
	11, // 3: Aurora.PlayerInfoServer.RankDataInfo.role:type_name -> Aurora.PlayerInfoServer.RoleRankInfo
	12, // 4: Aurora.PlayerInfoServer.GetRankListReply.data:type_name -> Aurora.PlayerInfoServer.RankDataInfo
	12, // 5: Aurora.PlayerInfoServer.GetRankListReply.selfInfo:type_name -> Aurora.PlayerInfoServer.RankDataInfo
	3,  // 6: Aurora.PlayerInfoServer.RankService.SyncRoleInfo:input_type -> Aurora.PlayerInfoServer.SyncRoleInfoRequest
	6,  // 7: Aurora.PlayerInfoServer.RankService.UpdateRoleRankInfo:input_type -> Aurora.PlayerInfoServer.UpdateRoleRankRequest
	9,  // 8: Aurora.PlayerInfoServer.RankService.RemoveRole:input_type -> Aurora.PlayerInfoServer.RemoveRoleRequest
	13, // 9: Aurora.PlayerInfoServer.RankListHttp.GetRankListData:input_type -> Aurora.PlayerInfoServer.GetRankListReq
	4,  // 10: Aurora.PlayerInfoServer.RankService.SyncRoleInfo:output_type -> Aurora.PlayerInfoServer.SyncRoleInfoReply
	7,  // 11: Aurora.PlayerInfoServer.RankService.UpdateRoleRankInfo:output_type -> Aurora.PlayerInfoServer.UpdateRoleRankReply
	10, // 12: Aurora.PlayerInfoServer.RankService.RemoveRole:output_type -> Aurora.PlayerInfoServer.RemoveRoleReply
	14, // 13: Aurora.PlayerInfoServer.RankListHttp.GetRankListData:output_type -> Aurora.PlayerInfoServer.GetRankListReply
	10, // [10:14] is the sub-list for method output_type
	6,  // [6:10] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_microservices_rank_v1_rank_proto_init() }
func file_microservices_rank_v1_rank_proto_init() {
	if File_microservices_rank_v1_rank_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_microservices_rank_v1_rank_proto_rawDesc), len(file_microservices_rank_v1_rank_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_microservices_rank_v1_rank_proto_goTypes,
		DependencyIndexes: file_microservices_rank_v1_rank_proto_depIdxs,
		EnumInfos:         file_microservices_rank_v1_rank_proto_enumTypes,
		MessageInfos:      file_microservices_rank_v1_rank_proto_msgTypes,
	}.Build()
	File_microservices_rank_v1_rank_proto = out.File
	file_microservices_rank_v1_rank_proto_goTypes = nil
	file_microservices_rank_v1_rank_proto_depIdxs = nil
}
