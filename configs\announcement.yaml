server:
  http:
    addr: 0.0.0.0:8001
    timeout: 25
  grpc:
    addr: 0.0.0.0:9001
    timeout: 25

data:
  redis:
    addr: 10.1.8.64:6379
    passwd: ""
    read_timeout: 10
    write_timeout: 10

# 日志配置
log:
  level: -1       # 日志级别: -1=DEBUG, 0=INFO, 1=WARN, 2=ERROR, 3=DPanic, 4=Panic, 5=Fatal
  console: true   # 是否输出到控制台

# 服务注册配置
registry:
  etcd:
    endpoints: ["10.1.8.64:2379"]
    username: ""
    password: ""
    timeout: 5
    prefix: "/jijichao"

# 配置中心配置
config:
  etcd:
    endpoints: ["10.1.8.64:2379"] 
    username: ""
    password: ""
    timeout: 5
    prefix: "/jijichao"

# 本地备用公告配置
notices:
  - noticeId: "1"
    autoOpen: 1
    title: "本地备用公告"
    priority: "1"
    context1: "这是本地备用公告，仅在etcd中无公告时显示"
    context2: ""
    context3: ""
    createTime: 1704959625
    lastTime: 1740959625
    bgUrl: ""