//
// @Author:zhouchen
// @Description: main函数
// @Data: Created in 21:10 2023/6/6

package main

import (
	"context"
	"flag"
	"fmt"
	"github.com/go-kratos/kratos/contrib/registry/etcd/v2"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/transport/http"
	"liteframe/internal/common/constant"
	"liteframe/internal/microservices/redeemcode/conf"
	zaplog "liteframe/pkg/zap"
	"os"

	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	_ "go.uber.org/automaxprocs"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name = constant.ServiceNameRedeemCode
	// Version is the version of the compiled software.
	Version       string
	ServerVersion string
	ClientVersion string
	// flagconf is the config flag.
	flagconf string

	id, _   = os.Hostname()
	PidFile = constant.ServiceNameRedeemCode + ".pid"
)

func init() {
	flag.StringVar(&flagconf, "conf", "../configs/redeemcode.yaml", "config path, eg: -conf redeemcode.yaml")
}

func newApp(logger log.Logger, gr *grpc.Server, hs *http.Server, r *etcd.Registry, meta map[string]string) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(meta),
		kratos.Logger(logger),
		kratos.Server(
			gr,
			hs,
		),
		kratos.Registrar(r),
		kratos.AfterStart(afterStart),
	)
}

func afterStart(context.Context) error {
	pid := fmt.Sprintf("%d", os.Getpid())
	f, err := os.OpenFile(PidFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		panic(err)
	}
	err = os.WriteFile(PidFile, []byte(pid), 0)
	if err != nil {
		panic(err)
	}
	err = f.Close()
	if err != nil {
		panic(err)
	}
	return nil
}

func main() {
	flag.Parse()

	// 初始化日志编码器
	encoder := zapcore.EncoderConfig{
		TimeKey:        "t",
		LevelKey:       "level",
		NameKey:        "logger",
		CallerKey:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stack",
		EncodeTime:     zapcore.ISO8601TimeEncoder,
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.FullCallerEncoder,
	}

	// 1. 加载本地配置
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
		),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}

	// 根据配置文件中的设置来设置日志级别
	// 默认使用DEBUG级别（-1）
	logLevel := zapcore.DebugLevel
	consoleOutput := true

	// 注意：需要先使用protoc编译conf.proto才能使用此功能
	// 暂时使用默认设置，直到proto文件被重新编译
	/*
		if bc.Data != nil && bc.Data.Log != nil {
			logLevel = zapcore.Level(bc.Data.Log.Level)
			consoleOutput = bc.Data.Log.Console
		}
	*/

	// 创建日志记录器
	zlogger := zaplog.NewZapLogger(
		Name, consoleOutput,
		encoder,
		zap.NewAtomicLevelAt(logLevel),
		zap.AddStacktrace(
			zap.NewAtomicLevelAt(zapcore.ErrorLevel)),
		zap.Development(),
	)

	// 创建日志帮助器，用于输出信息
	logHelper := log.NewHelper(zlogger)

	// 输出服务名称和版本
	logHelper.Infof("Starting service: name=%s, version=%s, log_level=%s",
		Name, Version, logLevel.String())

	// 创建标准日志记录器
	logger := log.With(zlogger,
		"caller", log.DefaultCaller,
		"service.version", Version,
	)

	// watch key
	if err := c.Watch("redeemCodeGroup", func(key string, value config.Value) {
		if err := c.Scan(&bc); err != nil {
			panic(err)
		}
	}); err != nil {
		panic(err)
	}

	// 配置中心读取
	//setupEtcdConfigWatch(&bc, bc.Etcdconfig)

	metaData := map[string]string{}
	metaData["ServerVersion"] = ServerVersion
	metaData["ClientVersion"] = ClientVersion

	app, cleanup, err := wireApp(bc.Server, bc.Data, &bc, logger, metaData)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// 启动和等待退出信号
	if err := app.Run(); err != nil {
		panic(err)
	}
}

//
//// setupEtcdConfigWatch 设置etcd配置监听
//func setupEtcdConfigWatch(bc *conf.Bootstrap, etcdConfig *conf.EtcdConfig) {
//	if etcdConfig == nil || etcdConfig.IsOpen < 1 || len(etcdConfig.Endpoints) == 0 {
//		log.Info("etcd config is disabled or not configured")
//		return
//	}
//
//	// 创建etcd客户端
//	client, err := clientv3.New(clientv3.Config{
//		Endpoints:   etcdConfig.Endpoints,
//		DialTimeout: etcdConfig.DialTimeout.AsDuration(),
//		Username:    etcdConfig.Username,
//		Password:    etcdConfig.Password,
//	})
//	if err != nil {
//		log.Errorf("failed to create etcd client: %v", err)
//		return
//	}
//
//	// 创建配置处理器
//	handler := etcd_handler.New(client)
//	defer handler.Stop()
//
//	// 监听配置变更
//	for _, keyItem := range etcdConfig.Keys {
//		watchKey := etcdConfig.Prefix + keyItem.Key
//
//		// 设置监听回调
//		err = handler.Register(watchKey, false, func(kv *config_watcher.KeyValue) {
//			log.Infof("config changed for key %s", kv.Key)
//			// 处理配置变更
//			handleConfigChange(bc, keyItem.WatchName, string(kv.Value))
//		})
//
//		if err != nil {
//			log.Errorf("failed to register watch for key %s: %v", watchKey, err)
//			continue
//		}
//
//		log.Infof("registered watch for key %s", watchKey)
//	}
//
//	// 启动一个goroutine处理配置变更事件
//	go func() {
//		ch := handler.Kv()
//		for kv := range ch {
//			handler.KvHandle(kv)
//		}
//	}()
//}
//
//// handleConfigChange 处理配置变更
//func handleConfigChange(bc *conf.Bootstrap, watchName string, value string) {
//	// 根据watchName处理不同的配置更新
//	switch watchName {
//	case "redeemCodeGroup":
//		// 解析并更新配置
//		c := config.New(
//			config.WithSource(
//				file.NewSource(value),
//			),
//		)
//		if err := c.Load(); err != nil {
//			log.Errorf("failed to load config: %v", err)
//			return
//		}
//		if err := c.Scan(bc); err != nil {
//			log.Errorf("failed to scan config: %v", err)
//			return
//		}
//		log.Info("config updated successfully")
//	// 可以添加其他配置类型的处理
//	default:
//		log.Infof("unknown watch name: %s", watchName)
//	}
//}
