﻿
using System.Collections.Concurrent;
using System.Net.Sockets;
using System.Runtime.InteropServices;
using LiteFrame.Framework;
using BattleServer.Server;
using BattleServer.Game;
using LiteFrame.Game;
using YamlDotNet.Core;
using LiteFrame.Helper;
using BattleServer.Service;
using BattleServer.Nats;
using BattleServer.Game.AutoChess;

namespace BattleServer.Framework
{
    public class SceneManager : GlobalManager<SceneManager>
    {
        private class SceneData
        {
            public ushort m_nSceneID;
            public ushort m_nCurrentPlayerCount;
            public ushort m_nUpcomingPlayerCount;
            public SceneData()
            {
                m_nSceneID = 0;
                m_nCurrentPlayerCount = 0;
                m_nUpcomingPlayerCount = 0;
            }

            public ushort CalcPlayerCount()
            {
                return (ushort)(m_nCurrentPlayerCount + m_nUpcomingPlayerCount);
            }
        }

        // 管理AutoChessScene实例
        private readonly ConcurrentDictionary<long, AutoChessScene> _autoChessScenes = new ConcurrentDictionary<long, AutoChessScene>();

        // 超时检查相关
        private int _timeoutCheckAccumulator = 0;
        private const int TIMEOUT_CHECK_INTERVAL_MS = 5000; // 每5秒检查一次超时

        private ushort m_nLastSceneID = 0;
        private ConcurrentDictionary<ushort, Scene> m_SceneList = new ConcurrentDictionary<ushort, Scene>();
        private Dictionary<int, List<SceneData>> m_CommonSceneList = new Dictionary<int, List<SceneData>>();

        //private Entity m_RootEntity;

        private List<ushort> m_CopySceneFactoryTaskCountList = new List<ushort>();


        //public Session m_Session;

        private SessionComponent m_SessionComponet;

        private MessageHandlerComponent messageHandlerComponent;

        //private NatsClient m_NatsClient;

        //#region MQ测试代码随时删除
        //private bool testflag = true;
        //public Session tempSession;
        //#endregion

        public SceneManager()
        {
            LogName = $"GlobalManager_SceneManager";
        }

        public override void OnDestroy()
        {
            Release();

            base.OnDestroy();
        }

        public bool Init()
        {
            //m_NatsClient = natsClient;
            InitCopySceneFactories();
            CreateAllCommonScenes();
            InitEntitySystem();
            return true;
        }

        public bool Release()
        {
            ClearEntitySystem();
            DestroyAllScenes();
            ClearCopySceneFactories();

            return true;
        }

        public void InitCopySceneFactories()
        {
            //const byte nFactoryCount = 4;
            //for (byte nIndex = 0; nIndex < nFactoryCount; ++nIndex)
            //{
            //    ServiceID serviceID = new ServiceID();
            //    {
            //        serviceID.m_eType = EServiceType.COPY_SCENE_FACTORY;
            //        serviceID.m_nIndex = nIndex;
            //    }
            //    ServiceManager.Instance.CreateService(serviceID);

            //    m_CopySceneFactoryTaskCountList.Add(0);
            //}
        }

        public void ClearCopySceneFactories()
        {
            //for (byte nIndex = 0; nIndex < m_CopySceneFactoryTaskCountList.Count; ++nIndex)
            //{
            //    ServiceID serviceID = new ServiceID();
            //    {
            //        serviceID.m_eType = EServiceType.COPY_SCENE_FACTORY;
            //        serviceID.m_nIndex = nIndex;
            //    }
            //    ServiceManager.Instance.DestroyService(serviceID);
            //}

            //m_CopySceneFactoryTaskCountList.Clear();
        }

        public bool AcquireCopySceneFactory(out byte nFactoryIndex)
        {
            nFactoryIndex = byte.MaxValue;

            bool bFound = false;
            byte nMinTaskFactoryIndex = 0;
            ushort nMinTaskCount = 0;
            for (byte nIndex = 0; nIndex < m_CopySceneFactoryTaskCountList.Count; ++nIndex)
            {
                ushort nTaskCount = m_CopySceneFactoryTaskCountList[nIndex];
                if (!bFound || (nTaskCount < nMinTaskCount))
                {
                    bFound = true;

                    nMinTaskCount = nTaskCount;
                    nMinTaskFactoryIndex = nIndex;
                }
            }

            if (bFound)
            {
                if (nMinTaskCount < ushort.MaxValue)
                {
                    ++m_CopySceneFactoryTaskCountList[nMinTaskFactoryIndex];
                    nFactoryIndex = nMinTaskFactoryIndex;
                    return true;
                }
                else
                {
                    Log.Error($"Acquire Copy Scene Factory Fail! There Is No Idle Factory.");
                    return false;
                }
            }
            else
            {
                Log.Error($"Acquire Copy Scene Factory Fail! Factory Is Not Found.");
                return false;
            }
        }

        public void ReleaseCopySceneFactory(byte nFactoryIndex)
        {
            if (nFactoryIndex < m_CopySceneFactoryTaskCountList.Count)
            {
                if (m_CopySceneFactoryTaskCountList[nFactoryIndex] > 0)
                {
                    --m_CopySceneFactoryTaskCountList[nFactoryIndex];
                }
                else
                {
                    Log.Error($"Release Copy Scene Factory Fail! Factory Task Count Is Zero.");
                }
            }
            else
            {
                Log.Error($"Release Copy Scene Factory Fail! Factory Index Is Invalid.");
            }
        }

        public bool InitEntitySystem()
        {
            //m_RootEntity = ReferencePool.Acquire<Entity>();
            //WorkUnit.EntitySystem.AddRootEntity(m_RootEntity);

            //m_RootEntity.AddComponent<CopySceneComponent>();
            MessageAddress address = new MessageAddress();
            address.SetAsGlobalMgr(0, 0, 1);
            //m_RootEntity.AddComponent<SessionComponent>(address);
            //m_RootEntity.AddComponent<TwinTreeSeasonPlayComponent>();
            //m_RootEntity.AddComponent<SceneMatchComponent>();

            MessageIDComponent messageIDComponent = new MessageIDComponent();
            MessageIDComponentSystem.OnAwake(messageIDComponent);


            messageHandlerComponent = new MessageHandlerComponent();
            //MessageHandlerComponent.Instance = messageHandlerComponent;
            MessageHandlerComponentSystem.OnAwake(messageHandlerComponent);


            m_SessionComponet = new SessionComponent();
            SessionComponentSystem.OnAwake1(m_SessionComponet, address);

            //m_Session = new Session();
            //m_Session.m_MessageAddress = address;

            IPacketDispatcher packetDispatcher = new ServerSessionDispatcher();
            PacketDispatcherManager.Instance.SetDispatcher(packetDispatcher);

            return true;
        }
        public bool ClearEntitySystem()
        {
            //WorkUnit.EntitySystem.RemoveRootEntity(m_RootEntity);
            //m_RootEntity.Release();
            //m_RootEntity = null;

            return true;
        }

        public override void Tick(int nDeltaTime)
        {
            base.Tick(nDeltaTime);

            // 更新所有AutoChessScene
            var scenesToRemove = new List<long>();
            foreach (var scene in _autoChessScenes.Values)
            {
                try
                {
                    scene.Tick(nDeltaTime);
                }
                catch (Exception ex)
                {
                    // 检查是否是BattleStateManager未初始化的错误
                    if (ex.Message.Contains("BattleStateManager is not initialized"))
                    {
                        Log.Warning($"[SceneManager] AutoChessScene {scene.BattleId} has uninitialized BattleStateManager, marking for removal");
                        scenesToRemove.Add(scene.BattleId);
                    }
                    else
                    {
                        Log.Error($"[SceneManager] AutoChessScene {scene.BattleId} tick error: {ex.Message}");
                        Log.Error($"[SceneManager] Stack trace: {ex.StackTrace}");
                    }
                }
            }

            // 移除有问题的场景
            foreach (var battleId in scenesToRemove)
            {
                Log.Info($"[SceneManager] Removing problematic AutoChessScene {battleId}");
                RemoveAutoChessScene(battleId);
            }

            // 定期检查EnterBattle超时（每5秒检查一次）
            _timeoutCheckAccumulator += nDeltaTime;
            if (_timeoutCheckAccumulator >= TIMEOUT_CHECK_INTERVAL_MS)
            {
                _timeoutCheckAccumulator = 0;
                try
                {
                    BattleServer.Service.BattleService.CheckEnterBattleTimeouts();
                }
                catch (Exception ex)
                {
                    Log.Error($"[SceneManager] EnterBattle timeout check error: {ex.Message}");
                }
            }

            //Log.Debug($"Tick {Thread.CurrentThread.ManagedThreadId.ToString()}");

            //#region MQ测试代码随时删除
            //if (testflag)
            //{
            //    TestSendPacket().Coroutine();
            //    testflag = false;
            //}
            //#endregion
        }

        public override void OnStart()
        {
            base.OnStart();

            Log.Debug("SceneManager Start");
        }

        public override void OnStop()
        {
            base.OnStop();

            // 清理所有AutoChessScene
            foreach (var scene in _autoChessScenes.Values)
            {
                try
                {
                    scene.Dispose();
                }
                catch (Exception ex)
                {
                    Log.Error($"[SceneManager] Error disposing AutoChessScene {scene.BattleId}: {ex.Message}");
                }
            }
            _autoChessScenes.Clear();

            Log.Debug("SceneManager Stop");
        }

        /// <summary>
        /// 添加AutoChessScene到管理器
        /// </summary>
        public void AddAutoChessScene(AutoChessScene scene)
        {
            if (scene == null) return;

            if (_autoChessScenes.TryAdd(scene.BattleId, scene))
            {
                Log.Info($"[SceneManager] Added AutoChessScene {scene.BattleId} to thread management");
            }
            else
            {
                Log.Warning($"[SceneManager] AutoChessScene {scene.BattleId} already exists");
            }
        }

        /// <summary>
        /// 从管理器移除AutoChessScene
        /// </summary>
        public void RemoveAutoChessScene(long battleId)
        {
            if (_autoChessScenes.TryRemove(battleId, out var scene))
            {
                try
                {
                    scene.Dispose();
                    Log.Info($"[SceneManager] Removed AutoChessScene {battleId} from thread management");
                }
                catch (Exception ex)
                {
                    Log.Error($"[SceneManager] Error disposing AutoChessScene {battleId}: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 获取AutoChessScene
        /// </summary>
        public AutoChessScene GetAutoChessScene(long battleId)
        {
            _autoChessScenes.TryGetValue(battleId, out var scene);
            return scene;
        }

        /// <summary>
        /// 获取活跃的AutoChessScene数量
        /// </summary>
        public int GetAutoChessSceneCount()
        {
            return _autoChessScenes.Count;
        }

        private ushort GenerateSceneID()
        {
            int nTryCount = ushort.MaxValue;
            while (--nTryCount >= 0)
            {
                ushort nSceneID = ++m_nLastSceneID;
                if (nSceneID == 0)
                {
                    continue;
                }
                else if (m_SceneList.ContainsKey(nSceneID))
                {
                    continue;
                }
                else
                {
                    return nSceneID;
                }
            }
            throw new Exception($"Generate Scene ID Fail!");
        }

        public Session GetSession()
        {
            return m_SessionComponet.m_Session;
        }
        private void CreateAllCommonScenes()
        {
            //Dictionary<int, SceneDefine> sceneDefineList = SceneDefineCategory.Instance.GetAll();
            //foreach (SceneDefine sceneDefine in sceneDefineList.Values)
            //{
            //    if (!IsSelfServerScene(sceneDefine.ServerNum))
            //    {
            //        continue;
            //    }
            //    long currentTime = ATimer.Instance.DateTicks;
            //    if (sceneDefine.SceneType == (int)SceneType.Static)
            //    {
            //        List<SceneData> sceneLineList = new List<SceneData>();
            //        m_CommonSceneList.Add(sceneDefine.Id, sceneLineList);
            //        byte nLineCount;
            //        if (sceneDefine.LineType == (int)SceneLineType.None)
            //        {
            //            nLineCount = 1;
            //        }
            //        else
            //        {
            //            nLineCount = (byte)sceneDefine.LineCount;
            //            if (nLineCount < 1)
            //            {
            //                nLineCount = 1;
            //            }
            //        }
            //        ////windows上暂时先开两个线
            //        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            //        {
            //            //nLineCount = (byte)sceneDefine.LineCount;
            //            //Log.Info($"sceneDefine.Id={sceneDefine.Id} nLineCount={nLineCount}");
            //            if (nLineCount > (byte)ServerConfigManager.Instance.AppConfig.WindowsLineNumber)
            //            {
            //                nLineCount = (byte)ServerConfigManager.Instance.AppConfig.WindowsLineNumber;
            //            }
            //        }
            //        for (byte nLineIndex = 0; nLineIndex < nLineCount; ++nLineIndex)
            //        {
            //            ushort nSceneID = GenerateSceneID();
            //            if (nSceneID != 0)
            //            {
            //                Scene scene = SceneFactory.CreateScene(sceneDefine.Id, nSceneID, nLineIndex);
            //                _InitSceneCommonData(scene, sceneDefine.Id, nSceneID, nLineIndex);

            //                SceneData sceneData = new SceneData();
            //                sceneData.m_nSceneID = nSceneID;
            //                sceneLineList.Add(sceneData);

            //                ThreadManager.Instance.StartWorkUnit(scene.WorkUnit, ThreadType.COMMON_THREAD);
            //            }
            //        }
            //    }
            //    if (sceneDefine.SceneType == (int)SceneType.Copy)
            //    {
            //        InitCopySceneVoxel(sceneDefine.Id, sceneDefine.NavPath);
            //    }

            //    long endTime = ATimer.Instance.DateTicks;
            //    Log.Warning($"InitCopySceneVoxel Scene:{sceneDefine.Id} cost time: {endTime - currentTime}");
            //}
        }

        private void DestroyAllScenes()
        {
            foreach (Scene scene in m_SceneList.Values)
            {
                ThreadManager.Instance.StopWorkUnit(scene.WorkUnit, ThreadType.COMMON_THREAD);
            }
            m_SceneList.Clear();
            m_CommonSceneList.Clear();
        }

       
       


        

        public void UpdatePlayerCount(int nSceneConfigID, byte nLineIndex, ushort nPlayerCount)
        {
            if (m_CommonSceneList.TryGetValue(nSceneConfigID, out List<SceneData> sceneLineList))
            {
                if (nLineIndex < sceneLineList.Count)
                {
                    SceneData sceneData = sceneLineList[nLineIndex];
                    sceneData.m_nUpcomingPlayerCount = 0;
                    sceneData.m_nCurrentPlayerCount = nPlayerCount;

                    //ELog.Info($"ScenePlayerCount nSceneConfigID:{nSceneConfigID}, nLineIndex:{nLineIndex}, nPlayerCount:{nPlayerCount} !!!!!",
                    //        EFT.LoginFlow);
                }
            }
        }

        

       
        // 根据场景配置Id获取该场景的数量，包含了分线的数量
        public int GetCommonSceneCountBySceneId(int sceneDefineId)
        {
            if (m_CommonSceneList == null || m_CommonSceneList.Count < 1)
            {
                return 0;
            }

            if (!m_CommonSceneList.TryGetValue(sceneDefineId, out List<SceneData> sceneDataList))
            {
                return 0;
            }

            if (sceneDataList == null || sceneDataList.Count < 1)
            {
                return 0;
            }

            return sceneDataList.Count;
        }

        // 通过场景配置Id列表获得这些配置实际的场景Id
        public List<int> GetSceneIdListBySceneDefineIdList(List<int> sceneDefineIdList)
        {
            if (sceneDefineIdList == null || sceneDefineIdList.Count < 1)
            {
                return null;
            }

            List<int> result = new List<int>();
            foreach (int sceneDefineId in sceneDefineIdList)
            {
                if (!m_CommonSceneList.TryGetValue(sceneDefineId, out List<SceneData> sceneDataList))
                {
                    continue;
                }

                foreach (SceneData sceneData in sceneDataList)
                {
                    result.Add(sceneData.m_nSceneID);
                }
            }

            return result;
        }

        // 通过场景配置Id列表获得这些配置实际的场景Id
        public List<int> GetSceneIdListBySceneDefineId(int sceneDefineId)
        {
            List<int> result = new List<int>();

            if (!m_CommonSceneList.TryGetValue(sceneDefineId, out List<SceneData> sceneDataList))
            {
                return result;
            }

            foreach (SceneData sceneData in sceneDataList)
            {
                result.Add(sceneData.m_nSceneID);
            }

            return result;
        }





        //[Msg(typeof(S2S_CloseServer))]
        //public static async ATask S2S_CloseServer_Handler(Session session, S2S_CloseServer request, S2S_RetCloseServer s2S_RetCloseServer, Action reply)
        //{
        //    s2S_RetCloseServer.Replyed = false;
        //    if (request == null || session == null)
        //    {
        //        Log.Error($" Scene Manager S2S_CloseServer_Handler request or session or session.Parent or session.Parent.ComponentParent is nil");
        //        s2S_RetCloseServer.Error = GlobalCommonDefine.INVALID_ID;
        //        reply();
        //        return;
        //    }
        //    //双生树存储
        //    TwinTreeSeasonPlayComponent.Instance.TwinTreeSeasonDBProxy.SaveTwinTreeSeasonDBData(ServerNumHelper.ServerNum);
        //    s2S_RetCloseServer.Replyed = true;
        //    reply();
        //    await ATask.NothingTask;
        //}

        //// 注意：这里只是把逻辑写到了SceneManager中，这段逻辑的执行在ServerStage线程上，不一定跟SceneManager处于同一线程，发个消息到SceneManager
        //[EventInvoker(typeof(EventDaysChangeFive))]
        //public static void OnDaysChangeFive(Entity entity, EventDaysChangeFive param)
        //{
        //    if (param == null)
        //    {
        //        return;
        //    }

        //    if (param.UseEventType != DaysChangeEventType.EServerStage)
        //    {
        //        return;
        //    }

        //    S2S_SyncDayChangeToSceneManager msg = new S2S_SyncDayChangeToSceneManager();
        //    msg.ChangeFiveParam = new EventDaysChangeFive
        //    {
        //        UseEventType = param.UseEventType,
        //        DayCount = param.DayCount,
        //        CurDayOfWeek = param.CurDayOfWeek,
        //        CurMonth = param.CurMonth,
        //        CurDay = param.CurDay,
        //        IsOnline = param.IsOnline
        //    };
        //    Packet packet = Packet.Create(msg);
        //    ServerPacketHelper.ToSelfServerGlobalMgr(packet, GlobalManagerType.SERVER_SCENE_MANAGER);
        //    PacketDispatcherManager.Dispatch(packet);
        //}

        //#region MQ测试代码随时删除
        //public async ATask TestSendPacket() 
        //{           
        //    S2W_NotifyOnlineUserNormalStatus reqNotifyNormal = new S2W_NotifyOnlineUserNormalStatus() { CharGuid = 1, GamePlayerId = 1 };
        //    var packet = Packet.Create(reqNotifyNormal);
        //    packet.TargetAddress.SetAsGlobalMgr(ServerNumHelper.World, GlobalManagerType.WORLD_ONLINE_USER_MANAGER);
        //    //packet.TargetAddress.Zwid = 29;

        //    tempSession = ReferencePool.Acquire<Session>();
        //    tempSession.Address.SetAsGlobalMgr((ushort)ServerNumHelper.ServerNum ,GlobalManagerType.SERVER_SCENE_MANAGER);
        //    Console.WriteLine("Send S2W_NotifyOnlineUserNormalStatus To MQ!");

        //    W2S_NotifyOnlineUserNormalStatus worldNotifyNormalResponse = await tempSession.SendAsync(packet) as W2S_NotifyOnlineUserNormalStatus;
        //    Console.WriteLine("aaa");
        //    //session.Release();
        //}
        //#endregion

        public async ATask CreateScene()
        {
            Session session = GetSession();
            if (session != null)
            {
                DeliverCopySceneConstruction response;


                AssignCopySceneConstruction request = new AssignCopySceneConstruction();
                {
                    request.SceneID = 1;
                    request.SceneConfigID = 1;
                    request.CopySceneConfigID = 2;
                    request.CreatorGUID = 4;
                }
                Packet packet = Packet.Create(request);

                //ServiceID serviceID = new ServiceID();
                //{
                //    serviceID.m_eType = EServiceType.COPY_SCENE_FACTORY;
                //    serviceID.m_nIndex = nFactoryIndex;
                //}
                ServerPacketHelper.ToSelfServerGlobalMgr(packet);

                response = await session.SendAsync(packet) as DeliverCopySceneConstruction;

                Log.Info($"response  {Thread.CurrentThread.ManagedThreadId.ToString()}");
            }
        }
        public async Task<bool> TestCreateScene()
        {
            Log.Debug($"TestCreateScene {Thread.CurrentThread.ManagedThreadId.ToString()}");

            await Task.Delay(100);

            return true;
        }

        [Msg(typeof(AssignCopySceneConstruction))]
        public static void AssignCopySceneConstructionHandler(Session session, AssignCopySceneConstruction request, DeliverCopySceneConstruction response, Action reply)
        {
            //response.Scene = SceneFactory.CreateCopyScene(request.SceneConfigID, request.CopySceneConfigID, request.CreatorGUID, request.SceneID);
            Log.Debug($"AssignCopySceneConstructionHandler {Thread.CurrentThread.ManagedThreadId.ToString()}");

            reply();
        }
    }
}
