
//下面这行很重要，是表明你这个Service用于哪个URL的，见游戏服的GrpcAddressType
//GrpcAddressType:CharList

syntax = "proto3";

package charlist.v1;

import "google/api/annotations.proto";

option go_package = "charlist/api/charlist/v1;v1";

//ServiceStart
service CharListDotNet {
rpc SaveCharList(SaveCharListRequest) returns (SaveCharListReply) {
}
}
//ServiceEnd

service CharListHttp {
rpc GetCharListData (GetCharListReq) returns (GetCharListReply) {
option (google.api.http) = {
get : "/api/charlist/getcharlist/{account}",
};
}
}

message CharListMsg{
int32 zwid = 1;
string account = 2;
uint64 charGuid = 3;
string charName = 4;
int32 level = 5;
}

message SaveCharListRequest {
CharListMsg charList = 1;
}

// The response message containing the message
message SaveCharListReply {
int32 result = 1;
}

//Type:Http
message GetCharListReq
{
string account = 1;
}

//Type:Http
message CharListDataMsg
{
string account = 1;
uint64 charGuid = 2;
string charName = 3;
int32 level = 4;
}

//Type:Http
message ZwidDataMsg
{
int32 zwid = 1;
repeated CharListDataMsg charListData = 2;
}

//Type:Http
message GetCharListReply
{
int32 result = 1;
repeated ZwidDataMsg zwidData = 2;
}
