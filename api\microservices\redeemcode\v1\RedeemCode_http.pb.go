// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.28.1
// source: microservices/redeemcode/v1/RedeemCode.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationRedeemCodeAddBatchRedeemCode = "/Aurora.PlayerInfoServer.RedeemCode/AddBatchRedeemCode"
const OperationRedeemCodeDeleteBatchRedeemCode = "/Aurora.PlayerInfoServer.RedeemCode/DeleteBatchRedeemCode"
const OperationRedeemCodeGetBatchRedeemCode = "/Aurora.PlayerInfoServer.RedeemCode/GetBatchRedeemCode"

type RedeemCodeHTTPServer interface {
	// AddBatchRedeemCode 通过GMT平台添加一批新礼包码
	AddBatchRedeemCode(context.Context, *RedeemCodeBatchInfo) (*AddBatchRedeemCodeRes, error)
	// DeleteBatchRedeemCode 根据批次ID删除礼包码
	DeleteBatchRedeemCode(context.Context, *CodeBatchReq) (*DeleteCodeBatchRes, error)
	// GetBatchRedeemCode 根据批次ID查询礼包码Info
	GetBatchRedeemCode(context.Context, *CodeBatchReq) (*RedeemCodeBatchInfo, error)
}

func RegisterRedeemCodeHTTPServer(s *http.Server, srv RedeemCodeHTTPServer) {
	r := s.Route("/")
	r.POST("/gmt/redeemcode/add", _RedeemCode_AddBatchRedeemCode0_HTTP_Handler(srv))
	r.GET("/gmt/redeemcode/get/{BatchId}", _RedeemCode_GetBatchRedeemCode0_HTTP_Handler(srv))
	r.DELETE("/gmt/redeemcode/delete/{BatchId}", _RedeemCode_DeleteBatchRedeemCode0_HTTP_Handler(srv))
}

func _RedeemCode_AddBatchRedeemCode0_HTTP_Handler(srv RedeemCodeHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RedeemCodeBatchInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRedeemCodeAddBatchRedeemCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddBatchRedeemCode(ctx, req.(*RedeemCodeBatchInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddBatchRedeemCodeRes)
		return ctx.Result(200, reply)
	}
}

func _RedeemCode_GetBatchRedeemCode0_HTTP_Handler(srv RedeemCodeHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CodeBatchReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRedeemCodeGetBatchRedeemCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetBatchRedeemCode(ctx, req.(*CodeBatchReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RedeemCodeBatchInfo)
		return ctx.Result(200, reply)
	}
}

func _RedeemCode_DeleteBatchRedeemCode0_HTTP_Handler(srv RedeemCodeHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in CodeBatchReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRedeemCodeDeleteBatchRedeemCode)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DeleteBatchRedeemCode(ctx, req.(*CodeBatchReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DeleteCodeBatchRes)
		return ctx.Result(200, reply)
	}
}

type RedeemCodeHTTPClient interface {
	AddBatchRedeemCode(ctx context.Context, req *RedeemCodeBatchInfo, opts ...http.CallOption) (rsp *AddBatchRedeemCodeRes, err error)
	DeleteBatchRedeemCode(ctx context.Context, req *CodeBatchReq, opts ...http.CallOption) (rsp *DeleteCodeBatchRes, err error)
	GetBatchRedeemCode(ctx context.Context, req *CodeBatchReq, opts ...http.CallOption) (rsp *RedeemCodeBatchInfo, err error)
}

type RedeemCodeHTTPClientImpl struct {
	cc *http.Client
}

func NewRedeemCodeHTTPClient(client *http.Client) RedeemCodeHTTPClient {
	return &RedeemCodeHTTPClientImpl{client}
}

func (c *RedeemCodeHTTPClientImpl) AddBatchRedeemCode(ctx context.Context, in *RedeemCodeBatchInfo, opts ...http.CallOption) (*AddBatchRedeemCodeRes, error) {
	var out AddBatchRedeemCodeRes
	pattern := "/gmt/redeemcode/add"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRedeemCodeAddBatchRedeemCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RedeemCodeHTTPClientImpl) DeleteBatchRedeemCode(ctx context.Context, in *CodeBatchReq, opts ...http.CallOption) (*DeleteCodeBatchRes, error) {
	var out DeleteCodeBatchRes
	pattern := "/gmt/redeemcode/delete/{BatchId}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRedeemCodeDeleteBatchRedeemCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "DELETE", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *RedeemCodeHTTPClientImpl) GetBatchRedeemCode(ctx context.Context, in *CodeBatchReq, opts ...http.CallOption) (*RedeemCodeBatchInfo, error) {
	var out RedeemCodeBatchInfo
	pattern := "/gmt/redeemcode/get/{BatchId}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationRedeemCodeGetBatchRedeemCode))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
