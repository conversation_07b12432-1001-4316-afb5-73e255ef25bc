﻿using LiteFrame.Framework;

namespace BattleServer.Game
{
    [MessageID((ushort)CustomMessageType.AssignCopySceneConstruction)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(DeliverCopySceneConstruction))]
    public class AssignCopySceneConstruction : IRequest
    {
        public ushort SceneID;
        public int SceneConfigID;
        public int CopySceneConfigID;
        public long CreatorGUID;

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.DeliverCopySceneConstruction)]
    [MessageType(MessageClass.IResponse)]
    public class DeliverCopySceneConstruction : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public Scene Scene;

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.AssignCopySceneDestruction)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(DeliverCopySceneDestruction))]
    public class AssignCopySceneDestruction : IRequest
    {
        public Scene Scene;

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.DeliverCopySceneDestruction)]
    [MessageType(MessageClass.IResponse)]
    public class DeliverCopySceneDestruction : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.SceneHasShutDown)]
    [MessageType(MessageClass.IMessage)]
    public class SceneHasShutDown : IMessage
    {
        public Scene Scene = null;
        public void Clear()
        {
            Scene = null;
        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.S2S_ReqWaitCopySceneInitFinished)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(S2S_ResWaitCopySceneInitFinished))]
    public class S2S_ReqWaitCopySceneInitFinished : IRequest
    {
        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.S2S_ResWaitCopySceneInitFinished)]
    [MessageType(MessageClass.IResponse)]
    public class S2S_ResWaitCopySceneInitFinished : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    // 全服广播消息，上行消息包，这个消息是发给场景管理器的，先通知场景管理器全服广播，然后由场景管理器通知到各个场景，再有各个场景广播给本场景所有玩家
    [MessageID((ushort)CustomMessageType.S2S_BroadCastToServerAllPlayer)]
    [MessageType(MessageClass.IMessage)]
    public class S2S_BroadCastToServerAllPlayer : IMessage
    {
        public IMessage msg;
        public bool toClient = false;

        public void Clear()
        {
            msg = null;
            toClient = false;
        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    // 全服广播消息，下行消息包
    [MessageID((ushort)CustomMessageType.S2S_SceneManagerBroadCastToServerAllPlayer)]
    [MessageType(MessageClass.IMessage)]
    public class S2S_SceneManagerBroadCastToServerAllPlayer : IMessage
    {
        public IMessage msg;
        public bool toClient = false;

        public void Clear()
        {
            msg = null;
            toClient = false;
        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    // 提交荒野首领死亡
    [MessageID((ushort)CustomMessageType.S2S_TwinTreeWildernessLeaderDead)]
    [MessageType(MessageClass.IMessage)]
    public class S2S_TwinTreeWildernessLeaderDead : IMessage
    {
        public int SceneId;
        public int SceneDefineId;
        public int LineIndex;

        public void Clear()
        {
            SceneId = -1;
            SceneDefineId = -1;
            LineIndex = -1;
        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    // 提交次元水晶进度完成
    [MessageID((ushort)CustomMessageType.S2S_TwinTreeDimensionalCrystalFinish)]
    [MessageType(MessageClass.IMessage)]
    public class S2S_TwinTreeDimensionalCrystalFinish : IMessage
    {
        public int SceneId;
        public int SceneDefineId;
        public int LineIndex;

        public void Clear()
        {
            SceneId = -1;
            SceneDefineId = -1;
            LineIndex = -1;
        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    //// 请求双生树玩法分线进度信息
    //[MessageID((ushort)CustomMessageType.S2S_ReqOtherTwinTreeLinePlayProcess)]
    //[MessageType(MessageClass.IMessage)]
    //public class S2S_ReqOtherTwinTreeLinePlayProcess : IMessage
    //{
    //    public uint GamePlayerId;
    //    public int PlayType;

    //    public void Clear()
    //    {
    //        GamePlayerId = 0;
    //        PlayType = (int)TwinTreeSeasonPlayType.None;
    //    }

    //    public bool Precheck
    //    {
    //        get { return true; }
    //    }
    //}

    //// 让次元水晶生成场景给对应玩法同步进度
    //[MessageID((ushort)CustomMessageType.S2S_NotifyTwinTreeAllLineProcess)]
    //[MessageType(MessageClass.IMessage)]
    //public class S2S_NotifyTwinTreeAllLineProcess : IMessage
    //{
    //    public uint GamePlayerId;
    //    public int PlayType;

    //    public void Clear()
    //    {
    //        GamePlayerId = 0;
    //        PlayType = (int)TwinTreeSeasonPlayType.None;
    //    }

    //    public bool Precheck
    //    {
    //        get { return true; }
    //    }
    //}

    // 同步开服时间给双生树玩法管理器
    [MessageID((ushort)CustomMessageType.S2S_SyncServerOpenDayToTwinTreeSeasonPlay)]
    [MessageType(MessageClass.IMessage)]
    public class S2S_SyncServerOpenDayToTwinTreeSeasonPlay : IMessage
    {
        public DateTime ServerOpenDataTime;

        public void Clear()
        {
            ServerOpenDataTime = DateTime.Now;
        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    // 同步双生树积分
    [MessageID((ushort)CustomMessageType.S2S_SyncIntegralToTwinTreeSeasonPlay)]
    [MessageType(MessageClass.IMessage)]
    public class S2S_SyncIntegralToTwinTreeSeasonPlay : IMessage
    {
        public long Integral;
        public int ZoneWorldId;

        public void Clear()
        {
            ZoneWorldId = -1;
            Integral = 0;
        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    //// 请求双生树服务器追猎积分排名数据
    //[MessageID((ushort)CustomMessageType.S2S_ReqTwinTreeServerChasingScore)]
    //[MessageType(MessageClass.IRequest)]
    //[ResponseType(typeof(S2S_RetTwinTreeServerChasingScore))]
    //public class S2S_ReqTwinTreeServerChasingScore : IRequest
    //{
    //    public long CharGuid;

    //    public void Clear()
    //    {
    //    }

    //    public bool Precheck
    //    {
    //        get { return true; }
    //    }
    //}

    //// 返回双生树追猎积分数据
    //[MessageID((ushort)CustomMessageType.S2S_RetTwinTreeServerChasingScore)]
    //[MessageType(MessageClass.IResponse)]
    //public class S2S_RetTwinTreeServerChasingScore : IResponse
    //{
    //    public int Error { get; set; }
    //    public string Message { get; set; }
    //    public bool Replyed { get; set; }

    //    public List<TwinTreeServerChasingScore> TwinTreeServerChasingScore = null;

    //    public void Clear()
    //    {
    //        TwinTreeServerChasingScore = null;
    //    }

    //    public bool Precheck
    //    {
    //        get { return true; }
    //    }
    //}

    // 请求双生树服务器追猎积分排名经验加成值
    [MessageID((ushort)CustomMessageType.S2S_ReqTwinTreeServerRankAdditionCoefficient)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(S2S_RetTwinTreeServerRankAdditionCoefficient))]
    public class S2S_ReqTwinTreeServerRankAdditionCoefficient : IRequest
    {
        public int ZoneWorldId;

        public long CharGuid;

        public void Clear()
        {
            ZoneWorldId = -1;
        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    // 返回双生树追猎积分数据
    [MessageID((ushort)CustomMessageType.S2S_RetTwinTreeServerRankAdditionCoefficient)]
    [MessageType(MessageClass.IResponse)]
    public class S2S_RetTwinTreeServerRankAdditionCoefficient : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public float AdditionCoefficient = 0.0f;

        public void Clear()
        {
            AdditionCoefficient = 0.0f;
        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    // 请求进入副本的时候判断副本是否还可以再进入
    [MessageID((ushort)CustomMessageType.S2S_ReqCopySceneCanEnter)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(S2S_RetCopySceneCanEnter))]
    public class S2S_ReqCopySceneCanEnter : IRequest
    {
        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    // 返回副本是否还可以再进入
    [MessageID((ushort)CustomMessageType.S2S_RetCopySceneCanEnter)]
    [MessageType(MessageClass.IResponse)]
    public class S2S_RetCopySceneCanEnter : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return false; }
        }
    }

    // 原服Server关服
    [MessageID((ushort)CustomMessageType.S2S_CloseServer)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(S2S_RetCloseServer))]
    public class S2S_CloseServer : IRequest
    {
        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.S2S_RetCloseServer)]
    [MessageType(MessageClass.IResponse)]
    public class S2S_RetCloseServer : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }


    // 原服Server关服
    [MessageID((ushort)CustomMessageType.S2S_CloseServer_KServer)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(S2S_RetCloseServer_KServer))]
    public class S2S_CloseServer_KServer : IRequest
    {

        public int nZoneWorldId { get; set; }
        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }



    [MessageID((ushort)CustomMessageType.S2S_RetCloseServer_KServer)]
    [MessageType(MessageClass.IResponse)]
    public class S2S_RetCloseServer_KServer : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }


    // 原服Server关服
    [MessageID((ushort)CustomMessageType.S2S_CloseServerKickPlayer)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(S2S_RetCloseServerKickPlayer))]
    public class S2S_CloseServerKickPlayer : IRequest
    {
        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.S2S_RetCloseServerKickPlayer)]
    [MessageType(MessageClass.IResponse)]
    public class S2S_RetCloseServerKickPlayer : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }


    // 原服Server关服
    [MessageID((ushort)CustomMessageType.S2S_CloseServerDB)]
    [MessageType(MessageClass.IMessage)]
    public class S2S_CloseServerDB : IMessage
    {
        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.S2S_RetCloseServerDB)]
    [MessageType(MessageClass.IMessage)]
    public class S2S_RetCloseServerDB : IMessage
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }



    // 原服Server关服
    [MessageID((ushort)CustomMessageType.S2S_CloseAllThinClient)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(S2S_RetCloseAllThinClient))]
    public class S2S_CloseAllThinClient : IRequest
    {
        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.S2S_RetCloseAllThinClient)]
    [MessageType(MessageClass.IResponse)]
    public class S2S_RetCloseAllThinClient : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    // 原服Server关服
    [MessageID((ushort)CustomMessageType.S2S_TeamCreateCopyScene)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(S2S_RetTeamCreateCopyScene))]
    public class S2S_TeamCreateCopyScene : IRequest
    {


        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.S2S_RetTeamCreateCopyScene)]
    [MessageType(MessageClass.IResponse)]
    public class S2S_RetTeamCreateCopyScene : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }
        public ushort SceneId { set; get; }

        public int CopySceneId { set; get; }

        public int SceneConfigId { set; get; }

        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.S2S_DestoryCopySceneForTeam)]
    [MessageType(MessageClass.IMessage)]
    public class S2S_DestoryCopySceneForTeam : IMessage
    {
        public ushort SceneId { set; get; }

        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.S2S_ReqCopySceneAdmittanceCheck)]
    [MessageType(MessageClass.IRequest)]
    [ResponseType(typeof(S2S_RetCopySceneAdmittanceCheck))]
    public class S2S_ReqCopySceneAdmittanceCheck : IRequest
    {
        public long PlayerGuid { set; get; }

        public void Clear()
        {

        }

        public bool Precheck
        {
            get { return true; }
        }
    }

    [MessageID((ushort)CustomMessageType.S2S_RetCopySceneAdmittanceCheck)]
    [MessageType(MessageClass.IResponse)]
    public class S2S_RetCopySceneAdmittanceCheck : IResponse
    {
        public int Error { get; set; }
        public string Message { get; set; }
        public bool Replyed { get; set; }

        public bool IsNeedCreate { get; set; }


        public bool Precheck
        {
            get { return false; }
        }
    }
}