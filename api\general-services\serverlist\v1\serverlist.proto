// @Author:zhouchen
// @Description: 消息包文件定义
// @Data: Created in 20:01 2023/6/6
syntax = "proto3";

package serverlist.v1;

import "google/api/annotations.proto";

option go_package = "serverlist/api/serverlist/v1;v1";

// The relation service definition.
service Serverlist {
  rpc GetServerListData (GetServerListReq) returns (GetServerListReply) {
    option (google.api.http) = {
      get : "/serverlist/api/serverlist/getserverlist/{targetPlatformId}",
    };
  }
  rpc GetIsWhitelist (GetIsWhitelistReq) returns (GetIsWhitelistReply) {
    option (google.api.http) = {
      get : "/serverlist/api/serverlist/getiswhitelist/{addrip}/{serverid}",
    };
  }
  rpc SetServerInfo (KratosServerInfo) returns (KratosServerInfo) {
    option (google.api.http) = {
      post : "/serverlist/api/serverlist/setserverinfo",
      body : "*",
    };
  }
}

message GetServerListReq {
  string targetPlatformId = 1;
}

message KratosServerInfo {
  string ServerPlayerCount = 1;
  string ServerStatus = 2;
  int32 ServerId = 3;
}

message ServerListData {
//  string BoNo = 1;
//  string Expand = 2;
  string Ip = 3;                // 登录服IP地址（兼容旧字段，实际对应loginIp）
  int32 Port = 4;              // 登录服端口（兼容旧字段，实际对应loginPort）
  int32 Recommend = 5;
  int32 ServerId = 6;
  string ServerName = 7;
//  string ServerSep = 8;
  int32 Status = 9; // 服务器状态：1=流畅，2=拥挤，3=爆满，4=未上线，5=维护
  int32 ServerBigId = 10;
//  string TargetPlatformId = 11;
//  string MaxPlayerCount = 12;
//  string LoginNewPlayer = 13;
  KratosServerInfo ServerInfo = 14;
//  string GameServerIp = 15;     // 游戏服IP地址
//  string GameServerPort = 16;   // 游戏服端口
  int64 ServerOpenTime = 17;   // 开服时间（Unix时间戳）
  int32 IsNew = 18;            // 是否新服
}

message GetServerListReply {
  int32 result = 1;
  repeated ServerListData serverlistdata = 2;
}

message GetIsWhitelistReq {
  string addrip = 1;
  string serverid = 2;
}

message GetIsWhitelistReply {
  int32 whiteresult = 1;
  int32 serverstate = 2;
}