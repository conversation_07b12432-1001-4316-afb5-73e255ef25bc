// 协议 ID 类型为 short，-32767 到 32767
//StartMessageID = 1000; // 必须以;分号结束
//MaxMessageID = 1999; // 必须以;分号结束
syntax = "proto3";
option go_package = "liteframe/internal/common/protos/cs";

option csharp_namespace = "Game.Core";


import "PublicMessage.proto";
import "PublicEnum.proto";

//客户端登录 Logic 服请求
message CL_LOGIN_REQ
{
	int64 platformID = 1;
	string token = 2;
	string nickName = 3;
	string headUrl = 4;
	string version = 5;
	string driverId = 6;   //设备 id
	int32 loginState = 7;   //登陆的状态  0  登陆  1 重连
	int32 playerage = 8;   //年龄
	int32 playerbirthday = 9;   //生日（19901010）
	PBDeviceInfo deviceInfo = 10;   //设备信息
	string sdkToken = 11;//第三方 Token
	PB_MidasInfo midasInfo = 12;
	LoginByType gameCenterLoginType = 13;//游戏中心登录类型
	string channelOpenid = 14;//渠道的 openid
	PBASAIadData ASAIadData = 15;//IOS 广告信息
	string sdkVersion = 16;//海外版本 SDK 版本号
	string sdkUserId = 17;//sdk UserId
	string sdkGameChannel = 18;//Sdk GameChannel
}

//玩家数据请求
message CL_PlayerData_REQ
{
   bool guideOpenState = 1;		//新手的状态是否开启
}

//客户端到服务器的 heartbeat    消息 的 id 必须是固定的 1002
message CLHeartBeat
{
	int32 timeSpentLastRequest = 1;		//上次返回 请求和返回的时差 来判断网络耗时
}

//请求服务器的时间
message CLUpdateServerTime
{

}
//======================唤醒的时候请求服务器时间结束=========================================


//gm 指令
message CLGmReq
{
	string content = 1;
}

//玩家注销登出
message CL_PlayerData_LoginOut
{

}


//物品使用
message CLUseItemReq
{
	UseItemParam useParam = 1; //使用参数
}
//同步功能解锁
message CLFuntionUnLockReq
{
	 int32 funtionID = 1;
	 int32 singlePartcial = 2;
	 int32 Partcial = 3;
}


//====================邮件相关开始=======================
//请求邮件信息
message CLMailAllListReq
{
}
//玩家读取一封邮件的
message CLReadMailReq
{
	int64 mailId = 1; //邮件 ID
}
//玩家领取东西
message CLReceiveMailReq
{
	int64 mailId =1; //想领取的邮件 ID
}
//玩家领取所有邮件
message CLReceiveAllMailReq
{
}
//玩家删除邮件
message CLDelMailReq
{
	int64 mailId = 1; //想删除邮件 id
}
//一键删除邮件
message CLDelAllReadMailReq
{
}
//问卷奖励邮件
message CLMailQuestionAwardReq
{
}
//====================邮件相关结束=======================


//=====================账号信息开始=============================
//玩家修改名字
message CLChangeNameReq
{
	string Name = 1;   	//名字
	int32 type = 2;		//起名的方式  0 起名界面 1 玩家改名
}

//修改个性签名
message CLChangeSignReq
{
	string signContent = 1; //签名内容
}

//修改玩家性别
message CLChangeGenderReq
{
	EGenderType gender = 1; //枚举性别
}

//=====================账号信息结束=============================
//===================新版新手引导开始==========================
//新手的下一步操作信息
message CLNewGuideStepInfoReq
{
	PBCommonKeyValue curNewGuideSetpInfo = 1;
}

//新手客户端触发的新手信息
message CLNewGuideClientStartReq
{
	PBCommonKeyValue startNewGuideSetpInfo = 1;
}

//客户端触发新手引导组
message CLClientTriggerGuideReq
{
	int32 groupId = 1;		//触发的组ID
}
//引导步骤完成
message CLGuideStepFinishReq
{
	PBIntPair guide = 1;	//完成的引导信息(key:组ID,val:子步骤ID)
}

//强制引导组完成(非正常情况调用)
message CLGuideGroupFinishReq
{
	int32 groupId = 1;		//引导组ID
}
//===================新版新手引导结束==========================

//======================签到开始=========================================
//请求签到消息
message CLSignInInfoRequest
{
	SignInToType SignInType = 1;		//签到的类型
}

//领取签到奖励信息
message CLSignInRewardRequest
{
	SignInToType SignInType = 1;		//签到的类型
	int32 DayNum = 2;                   //签到的天数
}
//自动领取七日签到奖励信息
message CLSevenDaySignInRewardRequest
{
}
//一键领取签到奖励信息
message CLSignMonthRewardRequest
{
}

//======================签到结束=========================================

//=====================玩家设置相关开始=============================
//向服务端同步设置数据
message CLSyncSettingReq
{
	PBSettingData data = 1;//设置相关结构体
}

//=====================玩家设置相关结束=============================

//======================社交分裂开始=========================================
//同步  每次打开界面在同步一下
message CLAllFriendInvitationReq
{

}
//领取拉新奖励
message CLFriendRasinRewardReq
{
	int32 tableID = 1;
}
//打开对应玩家任务
message CLFriendGrowthQuestRewardReq
{
	int64 playerUID = 1;
}
//领取对应玩家任务奖励
message CLFriendGrowthQuestReceiveRewardReq
{
	int64 playerUID = 1;
	int32 tableID = 2;
}
//领取对应玩家所有任务奖励
message CLFriendGrowthQuestReceiveAllRewardReq
{
	int64 playerUID = 1;
}
//分享
message CLSharePosterReq
{
	int64 curPlayerUID = 1;
}
//分享
message CLEnterInvitationCodeReq
{
	sint64 invitationCode = 1; //邀请码
}
//======================社交分裂开始=========================================

//=====================通用阶段宝箱相关数据=============================

//完成阶段宝箱
message CLFinishCommonExpBoxData
{
	int32 box_id = 1;
	repeated int32  box_index = 2;
}

//======================通用阶段宝箱相关数据=============================
//======================问卷  Start=====================================
//打开问卷页面
message CLQuestReq
{

}
//问卷奖励邮件
message CLQuestAwardToMailReq
{
	int32 questId = 1;//问卷 id
}
//======================问卷  End=====================================


//请求其他玩家信息
message CLPlayerOtherInfo
{
	int64 otherPlayerId = 1; //其他玩家游戏内 ID
}

//=====================公会系统开始==================================
//创建公会
message CLGuildCreate
{
	string name 	= 1;	//名称
	string notice 	= 2;	//宣言
	int32 iconId 	= 3;	//图标
	bool freeJoin 	= 4;	//true 自由加入 false 需要审批，
	int32 reqStage	= 5;	//审批时需具备的关卡条件：0.无限制 1.困难 2.疯狂 3.地狱
	string announcement = 6; // 联盟公告
}
//主按钮请求
message CLGuildMain
{

}
//一键加入
message CLGuildFastJoin
{

}
//公会搜索
message CLGuildSearch
{
	string text		= 1;	//公会名称或 ID
}
//公会申请
message CLGuildApply
{
	int64 id		= 1;	//公会 ID，提交用
}
//公会大厅
message CLGuildHall
{

}
//公会科技
message CLGuildTech
{

}
//公会科技强化
message CLGuildTechLevelup
{
	int32 id		= 1; //科技 ID
}
//公会科技重置
message CLGuildTechReset
{

}
//公会商店
message CLGuildShop
{

}
//公会商店购买
message CLGuildShopBuy
{
    int32 id                = 1; //商品 ID
    int32 type              = 2; //购买类型 1.free 2.adfree 3.normal
    int32 count             = 3; //购买次数
}
//修改公会信息
message CLGuildEdit
{
	GuildOpt opt	= 1;	//具体操作 0.修改旗帜 1.修改名字 2.修改公告 3.修改申请条件 下面的字段根据此操作提交对应值。
	string name 	= 2;	//名称
	string notice 	= 3;	//宣言
	int32 iconId 	= 4;	//图标
	bool freeJoin 	= 5;	//true 自由加入 false 需要审批，
	int32 reqStage	= 6;	//审批时需具备的关卡条件：0.无限制 1.困难 2.疯狂 3.地狱   （先按等级开发）
	string announcement = 7; // 联盟公告 (opt = GuildOpt_EditAnnouncement (新增) 时使用)
}

//公会申请列表
message CLGuildApplyMgrList
{

}
//公会成员管理
message CLGuildMemberMgrList
{

}
//公会申请列表操作
message CLGuildApplyMgr
{
	GuildOpt opt		= 1;	//具体操作 5.批准 6.拒绝
	int64 platformID	= 2;	//操作的玩家 id
}
//公会成员管理
message CLGuildMemberMgr
{
	GuildOpt opt		= 1;	//具体操作 8.任命会长 9.任命副会长 10.任命为普通成员 11.踢人
	int64 platformID	= 2;	//操作的玩家 id
}
//退出公会
message CLGuildQuit
{

}
//解散公会
message CLGuildDismiss
{

}
//弹劾会长
message CLGuildImpeach
{

}
//发送世界邀请
message CLGuildSendWorldInvite
{
	string text	= 1; //邀请内容
}
//发送私聊邀请
message CLGuildSendPlayerInvite
{
	string text	= 1; //邀请内容
	int64  toPlayerId = 2;   //私聊的对象 ID
}
//公会邀请加入
message CLGuildInviteJoin
{
	int64 id		= 1;	//公会 ID，提交用
}
//公会捐献
message CLGuildDonate
{
	int32 opt		= 1;	//捐献操作：1.免费 2.金币 3.钻石
}
//公会排行榜
message CLGuildRank
{

}
//公会日志
message CLGuildLog
{

}
//公会砍价礼包点击
message CLGuildBargainingInfo
{
	int64 id	= 1;	//礼包唯一 ID
}
//公会砍价礼包提醒砍价
message CLGuildBargainingNotice
{
	int64 id	= 1;	//礼包唯一 ID
}
//公会砍价礼包砍价
message CLGuildBargaining
{
	int64 id	= 1;	//礼包唯一 ID
}
//公会砍价礼包购买
message CLGuildBargainingBuy
{
	int64 id	= 1;	//礼包唯一 ID
}
//公会 BOSS 进入
message CLGuildBossEnter
{

}
//公会 BOSS 挑战
message CLGuildBossAtkBegin
{

}
//公会 BOSS 扫荡
message CLGuildBossSweep
{

}
//公会 BOSS 次数购买
message CLGuildBossBuyCount
{
	int32 buyCount = 1; //购买次数
}
//公会 BOSS 总排行
message CLGuildBossRank
{

}
//=====================公会系统结束===================================
//=====================聊天开始===================================
// 聊天
message CLSendChatInfo
{
  ChatType  chattype =1;
  string    content = 2;	//内容
  int64     toPlayer = 3;   //私聊的对象
}
// 聊天去掉私聊红点
message CLPrivateRed
{
  int64     toPlayer = 1;   //私聊的对象
}
// 聊天拉取
message CLWorldMsgGet
{
  ChatType  chattype =1;
  int64     msgId = 2;   //聊天编号  从这个编号往后发 100
}
//=====================聊天结束===================================


//=====================好友开始===================================
//请求推荐好友
message CLRecommendFriendReq
{
	bool changeBatch = 1;//是否是换一批请求 true:是 false:否
}

//请求好友信息 (会同步好友列表，好友申请列表和黑名单)
message CLSyncAllFriendsReq
{

}
//请求好友礼物
message CLAllGiftReq
{

}
//添加好友
message CLAddOneFriendReq
{
	int64 playerUid = 1;//添加的好友 id
}

//同意好友
message CLAgreeFriendReq
{
	int64 playerUid = 1;//同意的好友 id(0 表示一键同意)
}
//拒绝好友
message CLRefuseFriendReq
{
	int64 playerUid = 1;//拒绝的好友 id(0 表示一键拒绝)
}
//删除一个好友
message CLDelOneFriendReq
{
	int64 playerUid = 1;//删除的好友 id
}
//操作黑名单
message CLblacklistOperationReq
{
	int64 playerId = 1;	//要操作黑名单的 id
	int32 type = 2;		//1:添加，2：移除
}
//=====================好友结束===================================>

//======================问卷红点========================================
// 问卷去掉红点
message CLQuestRed
{
  int32 questId = 1;//问卷 id
}
//======================问卷红点========================================

//======================BI=========================================
//BI
message CLBILog
{
    repeated string params	= 1; //数据
	int32 type=2; //类型
}
//客户端领取广告奖励
message CLClientAdReceive
{
  	int32  Adtype=1; //  激励视频类型 IronSourceADype
}
//======================BI=========================================
//公会 TOP 请求
message CLGuildTopList
{
	
}


//======================头像开始=========================================
// 请求头像信息
message CLHeadIconReq
{
}
//替换头像
message CLReplaceHeadIconReq
{
	int32 headIconId = 1;//头像 id
}
//手动解锁头像，前提是已拥有。
message CLUnlockHeadIcon
{
	int32 headIconId = 1;
}
//======================头像结束=========================================

//======================头像框开始=========================================
// 请求头像框信息
message CLHeadFrame
{
}
//替换头像框
message CLReplaceHeadFrame
{
	int32 headFrameId = 1;//头像框 id
}
//手动解锁头像框，前提是已拥有。
message CLUnlockHeadFrame
{
	int32 headFrameId = 1;
}
//======================头像框结束=========================================

//======================商城购买礼包=========================================
message CLGiftBuy
{
	int32 giftId = 1;//礼包ID
}
//======================商城购买礼包结束=========================================

//======================限时商城购买礼包=========================================
message CLTimeGiftBuy
{
	int32 timeGiftId = 1;//TimeGiftPacks礼包ID
}
//======================限时商城购买礼包结束======================================

//======================七日签到开始=========================================
// 请求七日签到信息
message CLSevenSignInGetData
{
}

// 请求领取七日签到奖励
message CLSevenSignInGetAward
{
	int32 signInDay = 1; 	// 签到天数
}
//======================七日签到结束=========================================

//======================每日签到开始=========================================
// 请求每日签到信息
message CLDailySignInGetData
{
}

// 请求领取每日签到奖励
message CLDailySignInGetAward
{
	int32 signInDay = 1; 	// 领取（第几）天奖励
}

// 请求领取每日签到累签奖励
message CLDailySignInGetAccruedAward
{
	int32 id = 1; 	// 累签id
}
//======================每日签到结束=========================================

//======================首冲礼包=========================================
message CLFirstChargeGetReward
{
	int32 giftId = 1;	//档位
	int32 days = 2;		//第几天的
}
//购买首冲礼包
message CLBuyFirstChargeGift
{
	int32 giftId = 1;	//礼包档位ID
}
//======================首冲礼包结束=========================================

//======================充值返利开始==========================================
//请求充值返利数据
message CLTopupRebateGetData 
{
}

//请求领取充值返利奖励
message CLTopupRebateGetAward
{
    int32 topupTaskId = 1; // 充值返利任务Id
}
//======================充值返利结束==========================================

//======================月卡开始==========================================
//请求月卡数据
message CLMonthlyCardGetData 
{
}
//请求购买月卡数据
message CLMonthlyCardBuyCard
{	
	int32 cardId = 1;	// 月卡Id
}
//======================月卡结束==========================================

//======================月卡2.0开始==========================================
//请求月卡2.0数据
message CLMonthlyCardNewGetData 
{
}
//请求月卡2.0额外奖励
message CLMonthlyCardNewGetExtraReward
{
}
//======================月卡2.0结束==========================================

//======================等级基金开始==========================================
//请求充值返利数据
message CLGradedFundGetData 
{
}

//请求购买等级基金
message CLGradedFundBuyFund
{
	int32 gradedFundIdx = 1; // 基金阶段索引[表格Id]
}

//请求领取普通等级基金 
message CLGradedFundGetComWeal
{
	int32 levelStageIdx = 1; // 等级阶段索引[表格Id]
}

//请求领取超级等级基金
message CLGradedFundGetSuperWeal
{
	int32 levelStageIdx = 1; // 等级阶段索引[表格Id]
}
//======================等级基金结束==========================================

//======================任务开始==========================================
//请求提交任务 （领取任务奖励）
message CLMissionSubmit
{
	int32 mission_type = 1; // 任务类型
}

//请求提交任务 （领取任务奖励）
message CLMissionSubmitById
{
	repeated int32 mission_id_list = 1; //任务ID list
}
//======================任务结束==========================================

//=====================支付开始===================================
// 付款预请求
message CLPaymentPreRequestReq
{
	string goodsRegisterId = 1; 	// 商品注册ID
	int32 moduleType = 2;	   		// 支付对应模块
	int32 goodsPrice = 3;			// 产品实际支付价格（RMB级别为元，此参数不带引号，请用数字类型处理）
	string channelId = 4;			// 渠道标识
	string gameGoodsId = 5;			// 游戏商品ID
}
//=====================支付结束===================================

//=====================活动开始===================================
// 领取活动奖励
message CLGetActivityReward
{
	int32 activity_id = 1; 	// 活动ID
	repeated int32 reward_param = 2;	// 奖励参数
}

// 请求七日活动数据
message CLGetSevenDayActivityData
{
	int32 activity_id = 1; 	// 活动ID
}
//=====================活动开始===================================

//=====================删除账号开始===================================
// 删除账号
message CLDeleteAccount
{
	string deviceId = 1;			// 用户设备ID
	string token = 2; 				// 橙柚账号用户登录token，cysid
	string cyid = 3; 				// 橙柚账号用户ID,cyid
}
//=====================删除账号结束===================================

//=====================礼包码开始===================================
// 领取礼包码对应道具奖励
message CLRedeemCodeRewardReq
{
	string RedeemCodeId = 1;  // 礼包码ID
}
//=====================礼包码结束===================================

//======================千抽开始=========================================
//请求千抽信息
message CLGachaBonusGetData
{

}
//请求千抽奖励
message CLGachaBonusGetAward
{
	int32 stage = 1; 		// 关卡id
}

//======================千抽结束=========================================

//=====================功能预告开始=================================
// 领取功能预告对应奖励
message CLGetFuncPrevReward
{
	int32 FuncId = 1;	// 功能预告 Id
}
//=====================功能预告结束==================================

//=====================问卷开始=================================
// 领取问卷奖励
message CLGetQuestionReward
{
	int32 id = 1;	// 问卷id
}
//=====================问卷结束==================================

// 轮盘抽奖
message CLGachaWheelReward
{
}
//=============爬塔开始=============================
//首页
message CL_TowerMain
{
}
//爬塔开始
message CL_TowerStart
{
	int32 stageId = 1;//关卡id
}
//=============爬塔结束=============================
//============日进斗金开始=============================
//首页
message CLTotalRechargeGetReward
{
   int32 tableId = 1;//天ID
}
//=============日进斗金结束=============================
//============好友邀请开始=============================
//邀请任务列表
message CLInviteTaskList
{
}
//邀请分享
message CLInviteTaskShare
{
	int32 id = 1; // 平台
}
//领取邀请奖励
message CLInviteTaskGetReward
{
	int32 id = 1; // 任务id
}
//=============好友邀请结束=============================
//====================竞技场开始=======================

//竞技场获取数据
message CLArenaGetData
{
}

//竞技场请求挑战对手
message CLArenaReqChallenge
{
	int32 rivalIdx = 1; // 对手索引 1~3
}

//===================竞技场结束========================

//=================== 体力开始 ========================
// 请求打开体力界面
message CLOpenPowerBuyingReq
{        
        
}
// 请求购买体力
message CLPowerBuyingReq
{
        int32 buyType = 1;                // 购买类型：普通、补买和看视频
        int32 buyNum = 2;                // 购买量
}
// 请求打开食堂餐食界面 空字段
message CLPowerRewardReq
{
}
// 请求一键领取食堂餐食 空字段
message CLAllPowerRewardReq
{
}
// 请求领取指定体力包
message CLOnePowerRewardReq
{
    int64 generateTime = 1;        // 体力包生成时间
}
//=================== 体力结束 ========================

//=================== 天道修为开始 ========================
// 请求当前信息
message CLHeavenlyDaoInfoReq
{
}
// 请求晋升
message CLHeavenlyDaoPromoteReq
{
}
//=================== 天道修为结束 ========================
//=====================每日特惠礼包相关数据=============================

//周卡领取
message CLWeekCardReq
{
    int32 giftId=1; //礼物ID 
	int32 index=2; //礼物Index 
}
//======================每日特惠礼包相关数据=============================

//	=========================== 挂机奖励开始 ===========================
// 请求打开挂机奖励页面
message CLOpenHookReq
{
}
// 点击领取发送领取奖励请求
message CLGetHookRewardReq
{

}
// 点击领取后发送领取额外奖励请求 
message CLGetHookExtraRewardReq
{

}
//	=========================== 挂机奖励结束 ===========================
//=================== 举报开始 ========================
// 举报信息
message CLTipOffReq
{
  int64 playerId = 1;  // 被举报者的玩家ID
  int32 TipOffType = 2;  // 举报类型
  string TipOffContent = 3;  // 举报说明
}

//=================== 举报结束 ========================
//=================== 好友开始 ========================
//赠送礼物
message CLSendSingleGiftReq
{
	int64 giftRecipientPlayerId = 1;//礼物接收者id
}
//领取单个礼物
message CLReciveSingleGiftReq
{
	string uidTime = 1;	//赠送者的id+下划线+赠送时间
}
//一键赠送
message CLSendAllGiftReq
{
	repeated int64 uids = 1;//当前好友的id列表
}
//一键领取
message CLReciveAllGiftReq
{
	repeated int64 uids = 1;//当前好友的id列表
}
//=================== 好友结束 ========================

//=================== 英雄开始 ========================
//英雄列表
message CLHeroListReq
{
}
//英雄升级
message CLHeroLevelUpReq
{
	int32 heroId = 1;					// 英雄id
}
//英雄觉醒等级升级
message CLHeroAwakeLevelUpReq
{
	int32 heroId = 1;					// 英雄id
}
//=================== 英雄结束 ========================

//=================== 阵容开始 ========================
//获取所有阵容信息
message CLLineupListReq
{
}
//阵容槽位解锁
message CLLineupUnlockSlot
{
	int32 id = 1;					// 槽位id
}
//切换阵容
message CLLineupSwitchReq
{
	int32 id = 1;				  // 槽位id
}
//英雄上阵
message CLLineupSetReq
{
	int32 heroId = 1;					// 上阵英雄id
	int32 replaceHeroId = 2;  // 被替换的英雄id
}
//阵容重命名
message CLLineupRenameReq
{
	int32 id = 1;							// 槽位id
	string new_name = 2;			// 新的阵容名称
}
//=================== 阵容结束 ========================

//=================== 赛季buff开始 ========================
//赛季buff信息
message CLSeasonBuffReq
{
}
//=================== 赛季buff结束 ========================

//=================== 战斗开始 ========================
//开始匹配
message CLMatchReq
{
	
}

//请求开始回合战斗
message CLRoundBattleStartReq
{
	
}

//buffer 选择
message CLSelectBufferReq
{
	int32  bufferId=1;
}

//进入战场
message CLEnterSceneReq
{
	int32  sceneId=1;           //战场ID
}

//英雄合成
message CLMergeReq
{
	int32 from = 1;	//合成源格子ID
	int32 to = 2; //合成目标格子ID
	repeated PBMoveOperation moves = 3; // 本次合成前发生的所有移动操作
}

//准备
message CLReadyReq
{
	repeated PBMoveOperation moves = 3; // 确认准备前要同步的移动操作
}

//战斗结束
message CLRoundBattleEndReq
{
	bool win = 1; // 战斗胜利或者失败
	int32 kills = 2; // 击杀数
}

// 结算相关
// 结算额外广告奖励
message CLClaimAdRewardReq {
    AdRewardType type = 1; // BATTLE_SUPPLY_DROP/BATTLE_BLESSING
}
//=================== 战斗结束 ========================

//=================== 赛季开始 ========================
// 请求领取奖励
message CLClaimSeasonRewardReq {
    RewardType type = 1; // 奖励类型
    int32 id = 2;        // 奖励ID (这里对应 MainRank 的段位ID)
}
// 请求赛季相关信息
message CLSeasonInfoReq {

}
//=================== 赛季结束 ========================
