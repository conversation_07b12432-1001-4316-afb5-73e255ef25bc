[BattleService] Player 10106021303 (可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000075737 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000092047 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleService] Player 90000056864 (AI_可靠的.穿越者) Level 0 Trophy 1 from server 10102 lineup: [101, 102, 103]
[BattleStateManager_431971945414661] Initialized
[PlayerManager_431971945414661] Player 10106021303 (可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431971945414661] Player 90000075737 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431971945414661] Player 90000092047 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431971945414661] Player 90000056864 (AI_可靠的.穿越者) Trophy 1 Health 3
[PlayerManager_431971945414661] Initialized with 4 players
[BattleInstanceManager] Initialized for battle 431971945414661
[OpponentPairManager] Initialized for battle 431971945414661
[BuffManager_431971945414661] Initialized
[CheckerBoard_431971945414661] Cleared checkerboard
[CheckerBoard_431971945414661] Initialized
[AutoChessScene_431971945414661] Event handlers registered
[AutoChessScene_431971945414661] Battle 431971945414661 initialized with 4 players, waiting for all players to enter
[SceneManager] Added AutoChessScene 431971945414661 to thread management
[BattleService] Battle 431971945414661 created successfully with 4 players
[BattleService] Battle 431971945414661 is now waiting for all players to call EnterBattle RPC
[BattleService] Current battle state: StateNone (waiting for player entry)
[BattleService] Players expected to enter: [10106021303, 90000075737, 90000092047, 90000056864]
[BattleService] ===== IMPORTANT: Battle will NOT start until ALL players call EnterBattle =====
[BattleService] ===== GameServer should call EnterBattle for each player after scene loading =====
[BattleService] ===== BOT STRATEGY: Player 10106021303 is real player, others are bots =====
[BattleService] Auto-entered bot player 90000075737 immediately
[BattleService] Auto-entered bot player 90000092047 immediately
[BattleService] Auto-entered bot player 90000056864 immediately
[BattleService] Battle 431971945414661 bots auto-entered: 3/4 players ready
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 entered battle 431971945414661 (initial), current count: 4
[BattleService] All 4 players entered battle 431971945414661, starting battle state machine
[BattleService] Entered players: [90000075737, 90000092047, 90000056864, 10106021303]
[BattleService] Triggering AutoChessScene.StartBattleStateMachine() for battle 431971945414661
[AutoChessScene_431971945414661] StartBattleStateMachine() called for battle 431971945414661
[AutoChessScene_431971945414661] BattleStateManager is ready, starting first round...
[AutoChessScene_431971945414661] Current state before starting: StateNone
[BattleStateManager_431971945414661] ===== STARTING NEW ROUND 1 =====
[BattleStateManager_431971945414661] Round 1 has buff selection: False
[BattleStateManager_431971945414661] Publishing RoundStartedEvent for round 1
[AutoChessScene_431971945414661] Round 1 started
[BattleStateManager_431971945414661] Setting state to StateRoundStart for round 1
[BattleStateManager_431971945414661] State: StateNone -> StateRoundStart (R1, 1000ms)
[AutoChessScene_431971945414661] HandleRoundStart: 4 active players
[AutoChessScene_431971945414661] Valid player: 10106021303, Health: 3
[AutoChessScene_431971945414661] Valid player: 90000075737, Health: 3
[AutoChessScene_431971945414661] Valid player: 90000092047, Health: 3
[AutoChessScene_431971945414661] Valid player: 90000056864, Health: 3
[AutoChessScene_431971945414661] No instance found for player 10106021303 when saving board data
[AutoChessScene_431971945414661] No instance found for player 90000075737 when saving board data
[AutoChessScene_431971945414661] No instance found for player 90000092047 when saving board data
[AutoChessScene_431971945414661] No instance found for player 90000056864 when saving board data
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000056864 vs Player 10106021303
[OpponentPairManager] Random pair: Player 90000075737 vs Player 90000092047
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_431971945414661] Created 4 opponent pairs
[PlayerManager_431971945414661] Set player opponents, count: 4
[BattleInstanceManager] Created instance 431971945414661_1 for active players 90000056864 vs 10106021303
[CheckerBoard_431971945414661] Cleared checkerboard
[CheckerBoard_431971945414661] Initialized
[BattleInstance] 431971945414661_1 created with players: 90000056864, 10106021303
[BattleInstanceManager] Created instance 431971945414661_2 for active players 90000075737 vs 90000092047
[CheckerBoard_431971945414661] Cleared checkerboard
[CheckerBoard_431971945414661] Initialized
[BattleInstance] 431971945414661_2 created with players: 90000075737, 90000092047
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_431971945414661] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431971945414661] Cleaned orphaned entities for player 90000075737
[AutoChessScene_431971945414661] Cleaned orphaned entities for player 90000092047
[AutoChessScene_431971945414661] Cleaned orphaned entities for player 90000056864
[PlayerManager_431971945414661] Reset all players ready status
[AutoChessScene_431971945414661] Round started with 2 battle instances
[AutoChessScene_431971945414661] Generating 5 heroes for all players in round 1
[CheckerBoard_431971945414661] Placed entity 1 at position (9, 3)
[CheckerBoard_431971945414661] Created entity ID:1 ConfigID:101 at (9, 3) for player 10106021303
[CheckerBoard_431971945414661] Placed entity 2 at position (6, 5)
[CheckerBoard_431971945414661] Created entity ID:2 ConfigID:102 at (6, 5) for player 10106021303
[CheckerBoard_431971945414661] Placed entity 3 at position (9, 6)
[CheckerBoard_431971945414661] Created entity ID:3 ConfigID:102 at (9, 6) for player 10106021303
[CheckerBoard_431971945414661] Placed entity 4 at position (7, 1)
[CheckerBoard_431971945414661] Created entity ID:4 ConfigID:102 at (7, 1) for player 10106021303
[CheckerBoard_431971945414661] Placed entity 5 at position (7, 5)
[CheckerBoard_431971945414661] Created entity ID:5 ConfigID:101 at (7, 5) for player 10106021303
[CheckerBoard_431971945414661] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431971945414661] Generated 5 heroes for player 10106021303 in Enemy area
[AutoChessScene_431971945414661] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[CheckerBoard_431971945414661] Placed entity 1 at position (1, 1)
[CheckerBoard_431971945414661] Created entity ID:1 ConfigID:102 at (1, 1) for player 90000075737
[CheckerBoard_431971945414661] Placed entity 2 at position (4, 5)
[CheckerBoard_431971945414661] Created entity ID:2 ConfigID:101 at (4, 5) for player 90000075737
[CheckerBoard_431971945414661] Placed entity 3 at position (1, 2)
[CheckerBoard_431971945414661] Created entity ID:3 ConfigID:103 at (1, 2) for player 90000075737
[CheckerBoard_431971945414661] Placed entity 4 at position (5, 1)
[CheckerBoard_431971945414661] Created entity ID:4 ConfigID:102 at (5, 1) for player 90000075737
[CheckerBoard_431971945414661] Placed entity 5 at position (1, 5)
[CheckerBoard_431971945414661] Created entity ID:5 ConfigID:103 at (1, 5) for player 90000075737
[CheckerBoard_431971945414661] Generated 5/5 heroes for player 90000075737: 5 placed, 0 in temporary slots
[CheckerBoard_431971945414661] Generated 5 heroes for player 90000075737 in My area
[AutoChessScene_431971945414661] Generated 5 heroes for player 90000075737: 5 placed on board, 0 in temporary slots
[CheckerBoard_431971945414661] Placed entity 6 at position (7, 3)
[CheckerBoard_431971945414661] Created entity ID:6 ConfigID:102 at (7, 3) for player 90000092047
[CheckerBoard_431971945414661] Placed entity 7 at position (10, 3)
[CheckerBoard_431971945414661] Created entity ID:7 ConfigID:103 at (10, 3) for player 90000092047
[CheckerBoard_431971945414661] Placed entity 8 at position (7, 5)
[CheckerBoard_431971945414661] Created entity ID:8 ConfigID:103 at (7, 5) for player 90000092047
[CheckerBoard_431971945414661] Placed entity 9 at position (9, 3)
[CheckerBoard_431971945414661] Created entity ID:9 ConfigID:103 at (9, 3) for player 90000092047
[CheckerBoard_431971945414661] Placed entity 10 at position (8, 5)
[CheckerBoard_431971945414661] Created entity ID:10 ConfigID:101 at (8, 5) for player 90000092047
[CheckerBoard_431971945414661] Generated 5/5 heroes for player 90000092047: 5 placed, 0 in temporary slots
[CheckerBoard_431971945414661] Generated 5 heroes for player 90000092047 in Enemy area
[AutoChessScene_431971945414661] Generated 5 heroes for player 90000092047: 5 placed on board, 0 in temporary slots
[CheckerBoard_431971945414661] Placed entity 6 at position (2, 2)
[CheckerBoard_431971945414661] Created entity ID:6 ConfigID:102 at (2, 2) for player 90000056864
[CheckerBoard_431971945414661] Placed entity 7 at position (5, 1)
[CheckerBoard_431971945414661] Created entity ID:7 ConfigID:102 at (5, 1) for player 90000056864
[CheckerBoard_431971945414661] Placed entity 8 at position (1, 3)
[CheckerBoard_431971945414661] Created entity ID:8 ConfigID:102 at (1, 3) for player 90000056864
[CheckerBoard_431971945414661] Placed entity 9 at position (4, 4)
[CheckerBoard_431971945414661] Created entity ID:9 ConfigID:101 at (4, 4) for player 90000056864
[CheckerBoard_431971945414661] Placed entity 10 at position (5, 4)
[CheckerBoard_431971945414661] Created entity ID:10 ConfigID:102 at (5, 4) for player 90000056864
[CheckerBoard_431971945414661] CheckTimes limit (4) reached for 1 hero types for player 90000056864
[CheckerBoard_431971945414661] Generated 5/5 heroes for player 90000056864: 5 placed, 0 in temporary slots
[CheckerBoard_431971945414661] Generated 5 heroes for player 90000056864 in My area
[AutoChessScene_431971945414661] Generated 5 heroes for player 90000056864: 5 placed on board, 0 in temporary slots
[AutoChessScene_431971945414661] Player status: Total=4, Active=4
[AutoChessScene_431971945414661] Player 10106021303: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431971945414661] Player 90000075737: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431971945414661] Player 90000092047: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431971945414661] Player 90000056864: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431971945414661] Sending RoundStart notifications to 4 active players...
[AutoChessScene_431971945414661] RoundStart board data: player:90000056864 heroes:5
[AutoChessScene_431971945414661] RoundStart board data: player:10106021303 heroes:5
[AutoChessScene_431971945414661] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431971945414661] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431971945414661] RoundStart board data: player:90000075737 heroes:5
[AutoChessScene_431971945414661] RoundStart board data: player:90000092047 heroes:5
[AutoChessScene_431971945414661] Sending RoundStart to Player 90000075737 on GameServer 10102
[AutoChessScene_431971945414661] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431971945414661] RoundStart board data: player:90000075737 heroes:5
[AutoChessScene_431971945414661] RoundStart board data: player:90000092047 heroes:5
[AutoChessScene_431971945414661] Sending RoundStart to Player 90000092047 on GameServer 10102
[AutoChessScene_431971945414661] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431971945414661] RoundStart board data: player:90000056864 heroes:5
[AutoChessScene_431971945414661] RoundStart board data: player:10106021303 heroes:5
[AutoChessScene_431971945414661] Sending RoundStart to Player 90000056864 on GameServer 10102
[AutoChessScene_431971945414661] RoundStart message: Buffers=0, PlayerBoards=2
[AutoChessScene_431971945414661] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 431971945414661 state to StateRoundStart
[AutoChessScene_431971945414661] State change sent to GameServer: StateNone -> StateRoundStart (R1)
[BattleStateManager_431971945414661] BattleStateChangedEvent published successfully
[BattleStateManager_431971945414661] ===== ROUND 1 INITIALIZATION COMPLETE =====
[AutoChessScene_431971945414661] Battle state machine started successfully for battle 431971945414661
[AutoChessScene_431971945414661] Current state after starting: StateRoundStart
[BattleService] Battle state update result: False, new state: StateRoundStart
[BattleStateManager_431971945414661] State: StateRoundStart -> StatePreparation (R1, 65000ms)
[AutoChessScene_431971945414661] Preparation phase started
[PlayerManager_431971945414661] Player 90000075737 ready status set to True
[PlayerManager_431971945414661] Player 90000092047 ready status set to True
[PlayerManager_431971945414661] Player 90000056864 ready status set to True
[AutoChessScene_431971945414661] Auto-ready 3 additional bots
[AutoChessScene_431971945414661] Free operation phase started
[BattleService] Updated battle 431971945414661 state to StatePreparation
[AutoChessScene_431971945414661] State change sent to GameServer: StateRoundStart -> StatePreparation (R1)
[BattleStateManager_431971945414661] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431971945414661] MergeHero operation: Player 10106021303, From GridID 35 → To GridID 37
[CheckerBoard_431971945414661] Merged entity from (6, 5) to (7, 1). Star level increased from 1 to 2
[AutoChessScene_431971945414661] MergeHero operation: Player 10106021303, From GridID 51 → To GridID 41
[CheckerBoard_431971945414661] Merged entity from (9, 3) to (7, 5). Star level increased from 1 to 2
[PlayerManager_431971945414661] Player 10106021303 ready status set to True
[PlayerManager_431971945414661] All players are ready!
[AutoChessScene_431971945414661] All players are ready, transitioning to next state
[BattleStateManager_431971945414661] State: StatePreparation -> StateBattleStarting (R1, 1000ms)
[AutoChessScene_431971945414661] Applying battle start buffs for all players
[AutoChessScene_431971945414661] Camp info for player 90000056864: 5 heroes added
[AutoChessScene_431971945414661] Camp info for player 10106021303: 3 heroes added
[AutoChessScene_431971945414661] Created RoundBattleStart request for player 10106021303, Team order: [90000056864, 10106021303], total GridIDs used: 8
[AutoChessScene_431971945414661] Sent RoundBattleStart to Player 10106021303 vs Opponent 90000056864 with 2 teams
[AutoChessScene_431971945414661] Camp info for player 90000075737: 5 heroes added
[AutoChessScene_431971945414661] Camp info for player 90000092047: 5 heroes added
[AutoChessScene_431971945414661] Created RoundBattleStart request for player 90000075737, Team order: [90000075737, 90000092047], total GridIDs used: 10
[AutoChessScene_431971945414661] Sent RoundBattleStart to Player 90000075737 vs Opponent 90000092047 with 2 teams
[AutoChessScene_431971945414661] Camp info for player 90000075737: 5 heroes added
[AutoChessScene_431971945414661] Camp info for player 90000092047: 5 heroes added
[AutoChessScene_431971945414661] Created RoundBattleStart request for player 90000092047, Team order: [90000075737, 90000092047], total GridIDs used: 10
[AutoChessScene_431971945414661] Sent RoundBattleStart to Player 90000092047 vs Opponent 90000075737 with 2 teams
[AutoChessScene_431971945414661] Camp info for player 90000056864: 5 heroes added
[AutoChessScene_431971945414661] Camp info for player 10106021303: 3 heroes added
[AutoChessScene_431971945414661] Created RoundBattleStart request for player 90000056864, Team order: [90000056864, 10106021303], total GridIDs used: 8
[AutoChessScene_431971945414661] Sent RoundBattleStart to Player 90000056864 vs Opponent 10106021303 with 2 teams
[AutoChessScene_431971945414661] Sent RoundBattleStart notifications with seed: 294312215
[BattleService] Updated battle 431971945414661 state to StateBattleStarting
[AutoChessScene_431971945414661] State change sent to GameServer: StatePreparation -> StateBattleStarting (R1)
[BattleStateManager_431971945414661] BattleStateChangedEvent published successfully
[AutoChessScene_431971945414661] Player 10106021303 set ready status to True
[BattleStateManager_431971945414661] State: StateBattleStarting -> StateBattleInProgress (R1, 65000ms)
[AutoChessScene_431971945414661] Starting all battle instances
[BattleInstance] 431971945414661_1 battle started
[BattleInstance] 431971945414661_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_431971945414661] Auto EndBattle for bot 90000075737 vs bot 90000092047, random result: bot 90000075737 wins = True
[AutoChessScene_431971945414661] Player 90000075737 sent EndBattleReq (win: True), instance: 431971945414661_2
[AutoChessScene_431971945414661] Waiting for opponent 90000092047 to send EndBattleReq for instance 431971945414661_2
[AutoChessScene_431971945414661] Player 90000092047 sent EndBattleReq (win: False), instance: 431971945414661_2
[BattleInstance] 431971945414661_2 battle finished, winner: 90000075737, loser: 90000092047
[AutoChessScene_431971945414661] Battle instance 431971945414661_2 completed: Winner 90000075737, Loser 90000092047
[AutoChessScene_431971945414661] Bot 90000056864 vs real player 10106021303, waiting for real player result
[AutoChessScene_431971945414661] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 431971945414661 state to StateBattleInProgress
[AutoChessScene_431971945414661] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R1)
[BattleStateManager_431971945414661] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] EndBattle uid: 10106021303, win: True
[AutoChessScene_431971945414661] Player 10106021303 sent EndBattleReq (win: True), instance: 431971945414661_1
[AutoChessScene_431971945414661] Auto EndBattle for bot 90000056864 vs real player 10106021303, bot result: False
[BattleInstance] 431971945414661_1 battle finished, winner: 10106021303, loser: 90000056864
[AutoChessScene_431971945414661] Battle instance 431971945414661_1 completed: Winner 10106021303, Loser 90000056864
[AutoChessScene_431971945414661] All battle instances finished, proceeding to settlement
[BattleStateManager_431971945414661] State: StateBattleInProgress -> StateRoundSettlement (R1, 5000ms)
[AutoChessScene_431971945414661] Processing battle results
[PlayerManager_431971945414661] Player 90000056864 health reduced by 1, current health: 2
[AutoChessScene_431971945414661] Player 90000056864 lost 1 health, winner: 10106021303
[AutoChessScene_431971945414661] Sent RoundBattleEnd notification (isEnd=false) - Winner: 10106021303, Loser: 90000056864
[PlayerManager_431971945414661] Player 90000092047 health reduced by 1, current health: 2
[AutoChessScene_431971945414661] Player 90000092047 lost 1 health, winner: 90000075737
[AutoChessScene_431971945414661] Sent RoundBattleEnd notification (isEnd=false) - Winner: 90000075737, Loser: 90000092047
[AutoChessScene_431971945414661] Checking players elimination
[AutoChessScene_431971945414661] Active players remaining: 4
[AutoChessScene_431971945414661] Waiting for all players to confirm round settlement before starting new round
[AutoChessScene_431971945414661] Auto-confirming round settlement for bot 90000075737
[AutoChessScene_431971945414661] Auto-confirming round settlement for bot 90000092047
[AutoChessScene_431971945414661] Auto-confirming round settlement for bot 90000056864
[BattleService] Updated battle 431971945414661 state to StateRoundSettlement
[AutoChessScene_431971945414661] State change sent to GameServer: StateBattleInProgress -> StateRoundSettlement (R1)
[BattleStateManager_431971945414661] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED EnterBattle RPC =====
[BattleService] Player 10106021303 is calling EnterBattle
[BattleService] NATS communication is working correctly!
[BattleService] Player 10106021303 confirmed round settlement, current state: StateRoundSettlement
[AutoChessScene_431971945414661] Player 10106021303 confirmed round settlement, count: 4
[AutoChessScene_431971945414661] Real player 10106021303 confirmed, auto-confirming all bots
[AutoChessScene_431971945414661] All players confirmed round settlement, starting new round
[PlayerManager_431971945414661] Player 10106021303 ready status set to False
[PlayerManager_431971945414661] Player 90000075737 ready status set to False
[PlayerManager_431971945414661] Player 90000092047 ready status set to False
[PlayerManager_431971945414661] Player 90000056864 ready status set to False
[BattleStateManager_431971945414661] ===== STARTING NEW ROUND 2 =====
[BattleStateManager_431971945414661] Round 2 has buff selection: True
[BattleStateManager_431971945414661] Publishing RoundStartedEvent for round 2
[AutoChessScene_431971945414661] Round 2 started
[BattleStateManager_431971945414661] Setting state to StateRoundStart for round 2
[BattleStateManager_431971945414661] State: StateRoundSettlement -> StateRoundStart (R2, 1000ms)
[AutoChessScene_431971945414661] HandleRoundStart: 4 active players
[AutoChessScene_431971945414661] Valid player: 10106021303, Health: 3
[AutoChessScene_431971945414661] Valid player: 90000075737, Health: 3
[AutoChessScene_431971945414661] Valid player: 90000092047, Health: 2
[AutoChessScene_431971945414661] Valid player: 90000056864, Health: 2
[AutoChessScene_431971945414661] Player 10106021303 has 3 entities to save
[PlayerManager_431971945414661] Saved board data: player:10106021303 entities:3
[PlayerManager_431971945414661] Saved prev round data: player:10106021303 entities:3
[AutoChessScene_431971945414661] Player 90000075737 has 5 entities to save
[PlayerManager_431971945414661] Saved board data: player:90000075737 entities:5
[PlayerManager_431971945414661] Saved prev round data: player:90000075737 entities:5
[AutoChessScene_431971945414661] Player 90000092047 has 5 entities to save
[PlayerManager_431971945414661] Saved board data: player:90000092047 entities:5
[PlayerManager_431971945414661] Saved prev round data: player:90000092047 entities:5
[AutoChessScene_431971945414661] Player 90000056864 has 5 entities to save
[PlayerManager_431971945414661] Saved board data: player:90000056864 entities:5
[PlayerManager_431971945414661] Saved prev round data: player:90000056864 entities:5
[OpponentPairManager] Generating opponent pairs for round 1, active players: 4, eliminated: 0
[OpponentPairManager] Random pair: Player 90000092047 vs Player 10106021303
[OpponentPairManager] Random pair: Player 90000075737 vs Player 90000056864
[OpponentPairManager] Generated 2 opponent pairs for round 1
[AutoChessScene_431971945414661] Created 4 opponent pairs
[PlayerManager_431971945414661] Set player opponents, count: 4
[BattleInstanceManager] Created instance 431971945414661_1 for active players 90000092047 vs 10106021303
[CheckerBoard_431971945414661] Cleared checkerboard
[CheckerBoard_431971945414661] Initialized
[BattleInstance] 431971945414661_1 created with players: 90000092047, 10106021303
[BattleInstanceManager] Created instance 431971945414661_2 for active players 90000075737 vs 90000056864
[CheckerBoard_431971945414661] Cleared checkerboard
[CheckerBoard_431971945414661] Initialized
[BattleInstance] 431971945414661_2 created with players: 90000075737, 90000056864
[BattleInstanceManager] Created 2 battle instances for 4 players
[AutoChessScene_431971945414661] Restoring player 10106021303 to Enemy area (rows 6-10) based on current instance position
[CheckerBoard_431971945414661] Placed entity 1 at position (6, 1)
[CheckerBoard_431971945414661] Created entity ID:1 ConfigID:102 at (6, 1) for player 10106021303
[AutoChessScene_431971945414661] Restored entity 1: (7,1)->(6,1), GridID:37->31
[CheckerBoard_431971945414661] Placed entity 2 at position (6, 2)
[CheckerBoard_431971945414661] Created entity ID:2 ConfigID:101 at (6, 2) for player 10106021303
[AutoChessScene_431971945414661] Restored entity 2: (7,5)->(6,2), GridID:41->32
[CheckerBoard_431971945414661] Placed entity 3 at position (6, 3)
[CheckerBoard_431971945414661] Created entity ID:3 ConfigID:102 at (6, 3) for player 10106021303
[AutoChessScene_431971945414661] Restored entity 3: (9,6)->(6,3), GridID:54->33
[AutoChessScene_431971945414661] Restored board: player:10106021303 entities:3/3
[AutoChessScene_431971945414661] Restoring player 90000075737 to My area (rows 1-5) based on current instance position
[CheckerBoard_431971945414661] Placed entity 1 at position (1, 1)
[CheckerBoard_431971945414661] Created entity ID:1 ConfigID:102 at (1, 1) for player 90000075737
[AutoChessScene_431971945414661] Restored entity 1: (1,1)->(1,1), GridID:1->1
[CheckerBoard_431971945414661] Placed entity 2 at position (1, 2)
[CheckerBoard_431971945414661] Created entity ID:2 ConfigID:103 at (1, 2) for player 90000075737
[AutoChessScene_431971945414661] Restored entity 2: (1,2)->(1,2), GridID:2->2
[CheckerBoard_431971945414661] Placed entity 3 at position (1, 3)
[CheckerBoard_431971945414661] Created entity ID:3 ConfigID:103 at (1, 3) for player 90000075737
[AutoChessScene_431971945414661] Restored entity 3: (1,5)->(1,3), GridID:5->3
[CheckerBoard_431971945414661] Placed entity 4 at position (1, 4)
[CheckerBoard_431971945414661] Created entity ID:4 ConfigID:101 at (1, 4) for player 90000075737
[AutoChessScene_431971945414661] Restored entity 4: (4,5)->(1,4), GridID:23->4
[CheckerBoard_431971945414661] Placed entity 5 at position (1, 5)
[CheckerBoard_431971945414661] Created entity ID:5 ConfigID:102 at (1, 5) for player 90000075737
[AutoChessScene_431971945414661] Restored entity 5: (5,1)->(1,5), GridID:25->5
[AutoChessScene_431971945414661] Restored board: player:90000075737 entities:5/5
[AutoChessScene_431971945414661] Restoring player 90000092047 to My area (rows 1-5) based on current instance position
[CheckerBoard_431971945414661] Placed entity 4 at position (1, 1)
[CheckerBoard_431971945414661] Created entity ID:4 ConfigID:102 at (1, 1) for player 90000092047
[AutoChessScene_431971945414661] Restored entity 4: (7,3)->(1,1), GridID:39->1
[CheckerBoard_431971945414661] Placed entity 5 at position (1, 2)
[CheckerBoard_431971945414661] Created entity ID:5 ConfigID:103 at (1, 2) for player 90000092047
[AutoChessScene_431971945414661] Restored entity 5: (7,5)->(1,2), GridID:41->2
[CheckerBoard_431971945414661] Placed entity 6 at position (1, 3)
[CheckerBoard_431971945414661] Created entity ID:6 ConfigID:101 at (1, 3) for player 90000092047
[AutoChessScene_431971945414661] Restored entity 6: (8,5)->(1,3), GridID:47->3
[CheckerBoard_431971945414661] Placed entity 7 at position (1, 4)
[CheckerBoard_431971945414661] Created entity ID:7 ConfigID:103 at (1, 4) for player 90000092047
[AutoChessScene_431971945414661] Restored entity 7: (9,3)->(1,4), GridID:51->4
[CheckerBoard_431971945414661] Placed entity 8 at position (1, 5)
[CheckerBoard_431971945414661] Created entity ID:8 ConfigID:103 at (1, 5) for player 90000092047
[AutoChessScene_431971945414661] Restored entity 8: (10,3)->(1,5), GridID:57->5
[AutoChessScene_431971945414661] Restored board: player:90000092047 entities:5/5
[AutoChessScene_431971945414661] Restoring player 90000056864 to Enemy area (rows 6-10) based on current instance position
[CheckerBoard_431971945414661] Placed entity 6 at position (6, 1)
[CheckerBoard_431971945414661] Created entity ID:6 ConfigID:102 at (6, 1) for player 90000056864
[AutoChessScene_431971945414661] Restored entity 6: (1,3)->(6,1), GridID:3->31
[CheckerBoard_431971945414661] Placed entity 7 at position (6, 2)
[CheckerBoard_431971945414661] Created entity ID:7 ConfigID:102 at (6, 2) for player 90000056864
[AutoChessScene_431971945414661] Restored entity 7: (2,2)->(6,2), GridID:8->32
[CheckerBoard_431971945414661] Placed entity 8 at position (6, 3)
[CheckerBoard_431971945414661] Created entity ID:8 ConfigID:101 at (6, 3) for player 90000056864
[AutoChessScene_431971945414661] Restored entity 8: (4,4)->(6,3), GridID:22->33
[CheckerBoard_431971945414661] Placed entity 9 at position (6, 4)
[CheckerBoard_431971945414661] Created entity ID:9 ConfigID:102 at (6, 4) for player 90000056864
[AutoChessScene_431971945414661] Restored entity 9: (5,1)->(6,4), GridID:25->34
[CheckerBoard_431971945414661] Placed entity 10 at position (6, 5)
[CheckerBoard_431971945414661] Created entity ID:10 ConfigID:102 at (6, 5) for player 90000056864
[AutoChessScene_431971945414661] Restored entity 10: (5,4)->(6,5), GridID:28->35
[AutoChessScene_431971945414661] Restored board: player:90000056864 entities:5/5
[AutoChessScene_431971945414661] Cleaned orphaned entities for player 10106021303
[AutoChessScene_431971945414661] Cleaned orphaned entities for player 90000075737
[AutoChessScene_431971945414661] Cleaned orphaned entities for player 90000092047
[AutoChessScene_431971945414661] Cleaned orphaned entities for player 90000056864
[PlayerManager_431971945414661] Reset all players ready status
[AutoChessScene_431971945414661] Round started with 2 battle instances
[AutoChessScene_431971945414661] Generating buff options for all players
[BuffManager_431971945414661] Generated 3 buff options for player 10106021303: [110, 109, 104]
[AutoChessScene_431971945414661] Generated 3 buff options for player 10106021303: [110, 109, 104]
[BuffManager_431971945414661] Generated 3 buff options for player 90000075737: [104, 107, 108]
[AutoChessScene_431971945414661] Generated 3 buff options for player 90000075737: [104, 107, 108]
[BuffManager_431971945414661] Generated 3 buff options for player 90000092047: [107, 106, 103]
[AutoChessScene_431971945414661] Generated 3 buff options for player 90000092047: [107, 106, 103]
[BuffManager_431971945414661] Generated 3 buff options for player 90000056864: [101, 105, 103]
[AutoChessScene_431971945414661] Generated 3 buff options for player 90000056864: [101, 105, 103]
[AutoChessScene_431971945414661] Player status: Total=4, Active=4
[AutoChessScene_431971945414661] Player 10106021303: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431971945414661] Player 90000075737: Eliminated=False, Health=3, HasInstance=True
[AutoChessScene_431971945414661] Player 90000092047: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_431971945414661] Player 90000056864: Eliminated=False, Health=2, HasInstance=True
[AutoChessScene_431971945414661] Sending RoundStart notifications to 4 active players...
[AutoChessScene_431971945414661] RoundStart: Player 10106021303 buff options: [110, 109, 104]
[AutoChessScene_431971945414661] RoundStart board data: player:90000092047 heroes:5
[AutoChessScene_431971945414661] RoundStart board data: player:10106021303 heroes:3
[AutoChessScene_431971945414661] Sending RoundStart to Player 10106021303 on GameServer 10102
[AutoChessScene_431971945414661] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431971945414661] RoundStart: Player 90000075737 buff options: [104, 107, 108]
[AutoChessScene_431971945414661] RoundStart board data: player:90000075737 heroes:5
[AutoChessScene_431971945414661] RoundStart board data: player:90000056864 heroes:5
[AutoChessScene_431971945414661] Sending RoundStart to Player 90000075737 on GameServer 10102
[AutoChessScene_431971945414661] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431971945414661] RoundStart: Player 90000092047 buff options: [107, 106, 103]
[AutoChessScene_431971945414661] RoundStart board data: player:90000092047 heroes:5
[AutoChessScene_431971945414661] RoundStart board data: player:10106021303 heroes:3
[AutoChessScene_431971945414661] Sending RoundStart to Player 90000092047 on GameServer 10102
[AutoChessScene_431971945414661] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431971945414661] RoundStart: Player 90000056864 buff options: [101, 105, 103]
[AutoChessScene_431971945414661] RoundStart board data: player:90000075737 heroes:5
[AutoChessScene_431971945414661] RoundStart board data: player:90000056864 heroes:5
[AutoChessScene_431971945414661] Sending RoundStart to Player 90000056864 on GameServer 10102
[AutoChessScene_431971945414661] RoundStart message: Buffers=3, PlayerBoards=2
[AutoChessScene_431971945414661] Successfully sent RoundStart notifications to all 4 players via NATS
[BattleService] Updated battle 431971945414661 state to StateRoundStart
[AutoChessScene_431971945414661] State change sent to GameServer: StateRoundSettlement -> StateRoundStart (R2)
[BattleStateManager_431971945414661] BattleStateChangedEvent published successfully
[BattleStateManager_431971945414661] ===== ROUND 2 INITIALIZATION COMPLETE =====
[BattleStateManager_431971945414661] Buff selection timer started: 25000ms
[BattleStateManager_431971945414661] State: StateRoundStart -> StatePreparation (R2, 65000ms)
[AutoChessScene_431971945414661] Preparation phase started
[BuffManager_431971945414661] Added buff 104 (Buff_104) to player 90000075737
[AutoChessScene_431971945414661] Auto-selected buff 104 for bot player 90000075737
[CheckerBoard_431971945414661] Placed entity 11 at position (2, 3)
[CheckerBoard_431971945414661] Created entity ID:11 ConfigID:103 at (2, 3) for player 90000075737
[CheckerBoard_431971945414661] Placed entity 12 at position (4, 1)
[CheckerBoard_431971945414661] Created entity ID:12 ConfigID:101 at (4, 1) for player 90000075737
[CheckerBoard_431971945414661] Placed entity 13 at position (3, 3)
[CheckerBoard_431971945414661] Created entity ID:13 ConfigID:103 at (3, 3) for player 90000075737
[CheckerBoard_431971945414661] Placed entity 14 at position (2, 1)
[CheckerBoard_431971945414661] Created entity ID:14 ConfigID:102 at (2, 1) for player 90000075737
[CheckerBoard_431971945414661] Placed entity 15 at position (2, 6)
[CheckerBoard_431971945414661] Created entity ID:15 ConfigID:101 at (2, 6) for player 90000075737
[CheckerBoard_431971945414661] Generated 5/5 heroes for player 90000075737: 5 placed, 0 in temporary slots
[CheckerBoard_431971945414661] Generated 5 heroes for player 90000075737 in My area
[AutoChessScene_431971945414661] Generated 5 heroes for player 90000075737: 5 placed on board, 0 in temporary slots
[AutoChessScene_431971945414661] Generated 5 new heroes for bot player 90000075737 after buff selection
[BuffManager_431971945414661] Added buff 107 (Buff_107) to player 90000092047
[AutoChessScene_431971945414661] Auto-selected buff 107 for bot player 90000092047
[CheckerBoard_431971945414661] Placed entity 9 at position (3, 3)
[CheckerBoard_431971945414661] Created entity ID:9 ConfigID:101 at (3, 3) for player 90000092047
[CheckerBoard_431971945414661] Placed entity 10 at position (2, 4)
[CheckerBoard_431971945414661] Created entity ID:10 ConfigID:102 at (2, 4) for player 90000092047
[CheckerBoard_431971945414661] Placed entity 11 at position (2, 5)
[CheckerBoard_431971945414661] Created entity ID:11 ConfigID:103 at (2, 5) for player 90000092047
[CheckerBoard_431971945414661] Placed entity 12 at position (3, 5)
[CheckerBoard_431971945414661] Created entity ID:12 ConfigID:102 at (3, 5) for player 90000092047
[CheckerBoard_431971945414661] Placed entity 13 at position (5, 4)
[CheckerBoard_431971945414661] Created entity ID:13 ConfigID:103 at (5, 4) for player 90000092047
[CheckerBoard_431971945414661] Generated 5/5 heroes for player 90000092047: 5 placed, 0 in temporary slots
[CheckerBoard_431971945414661] Generated 5 heroes for player 90000092047 in My area
[AutoChessScene_431971945414661] Generated 5 heroes for player 90000092047: 5 placed on board, 0 in temporary slots
[AutoChessScene_431971945414661] Generated 5 new heroes for bot player 90000092047 after buff selection
[BuffManager_431971945414661] Added buff 101 (Buff_101) to player 90000056864
[AutoChessScene_431971945414661] Auto-selected buff 101 for bot player 90000056864
[CheckerBoard_431971945414661] Placed entity 16 at position (9, 1)
[CheckerBoard_431971945414661] Created entity ID:16 ConfigID:102 at (9, 1) for player 90000056864
[CheckerBoard_431971945414661] Placed entity 17 at position (8, 5)
[CheckerBoard_431971945414661] Created entity ID:17 ConfigID:102 at (8, 5) for player 90000056864
[CheckerBoard_431971945414661] Placed entity 18 at position (7, 6)
[CheckerBoard_431971945414661] Created entity ID:18 ConfigID:101 at (7, 6) for player 90000056864
[CheckerBoard_431971945414661] Placed entity 19 at position (10, 4)
[CheckerBoard_431971945414661] Created entity ID:19 ConfigID:102 at (10, 4) for player 90000056864
[CheckerBoard_431971945414661] Placed entity 20 at position (10, 3)
[CheckerBoard_431971945414661] Created entity ID:20 ConfigID:101 at (10, 3) for player 90000056864
[CheckerBoard_431971945414661] Generated 5/5 heroes for player 90000056864: 5 placed, 0 in temporary slots
[CheckerBoard_431971945414661] Generated 5 heroes for player 90000056864 in Enemy area
[AutoChessScene_431971945414661] Generated 5 heroes for player 90000056864: 5 placed on board, 0 in temporary slots
[AutoChessScene_431971945414661] Generated 5 new heroes for bot player 90000056864 after buff selection
[PlayerManager_431971945414661] Player 90000075737 ready status set to True
[PlayerManager_431971945414661] Player 90000092047 ready status set to True
[PlayerManager_431971945414661] Player 90000056864 ready status set to True
[AutoChessScene_431971945414661] Auto-ready 3 additional bots
[AutoChessScene_431971945414661] Free operation phase started
[BattleService] Updated battle 431971945414661 state to StatePreparation
[AutoChessScene_431971945414661] State change sent to GameServer: StateRoundStart -> StatePreparation (R2)
[BattleStateManager_431971945414661] BattleStateChangedEvent published successfully
[BattleService] ===== RECEIVED SelectBuffer RPC =====
[BattleService] Player 10106021303 is selecting buff 109
[BuffManager_431971945414661] Added buff 109 (Buff_109) to player 10106021303
[CheckerBoard_431971945414661] Placed entity 14 at position (8, 5)
[CheckerBoard_431971945414661] Created entity ID:14 ConfigID:103 at (8, 5) for player 10106021303
[CheckerBoard_431971945414661] Placed entity 15 at position (9, 2)
[CheckerBoard_431971945414661] Created entity ID:15 ConfigID:101 at (9, 2) for player 10106021303
[CheckerBoard_431971945414661] Placed entity 16 at position (8, 1)
[CheckerBoard_431971945414661] Created entity ID:16 ConfigID:102 at (8, 1) for player 10106021303
[CheckerBoard_431971945414661] Placed entity 17 at position (6, 6)
[CheckerBoard_431971945414661] Created entity ID:17 ConfigID:103 at (6, 6) for player 10106021303
[CheckerBoard_431971945414661] Placed entity 18 at position (8, 6)
[CheckerBoard_431971945414661] Created entity ID:18 ConfigID:101 at (8, 6) for player 10106021303
[CheckerBoard_431971945414661] Generated 5/5 heroes for player 10106021303: 5 placed, 0 in temporary slots
[CheckerBoard_431971945414661] Generated 5 heroes for player 10106021303 in Enemy area
[AutoChessScene_431971945414661] Generated 5 heroes for player 10106021303: 5 placed on board, 0 in temporary slots
[AutoChessScene_431971945414661] Player 10106021303 selected buff 109, generated 5 new heroes
[BattleService] SelectBuffer response: Code=0, NewHeroes=5
[AutoChessScene_431971945414661] MergeHero operation: Player 10106021303, From GridID 36 → To GridID 47
[BattleInstance] 431971945414661_1 Processing 1 move operations before merge for player 10106021303
[CheckerBoard_431971945414661] Cannot move: target position (6, 1) is occupied by entity 1
[BattleInstance] 431971945414661_1 Move operation: 43 -> 31, success: False
[CheckerBoard_431971945414661] Merged entity from (6, 6) to (8, 5). Star level increased from 1 to 2
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[AutoChessScene_431971945414661] MergeHero operation: Player 10106021303, From GridID 32 → To GridID 50
[BattleInstance] 431971945414661_1 Processing 2 move operations before merge for player 10106021303
[CheckerBoard_431971945414661] Cannot move: target position (9, 2) is occupied by entity 15
[BattleInstance] 431971945414661_1 Move operation: 32 -> 50, success: False
[CheckerBoard_431971945414661] Cannot move: target position (8, 6) is occupied by entity 18
[BattleInstance] 431971945414661_1 Move operation: 50 -> 48, success: False
[CheckerBoard_431971945414661] Swapped entities between (6, 2) and (9, 2)
[PlayerManager_431971945414661] Player 10106021303 ready status set to True
[PlayerManager_431971945414661] All players are ready!
[AutoChessScene_431971945414661] All players are ready, transitioning to next state
[BattleStateManager_431971945414661] State: StatePreparation -> StateBattleStarting (R2, 1000ms)
[AutoChessScene_431971945414661] Applying battle start buffs for all players
[BuffManager_431971945414661] Applying battle start buff 109 (Buff_109) for player 10106021303
[BuffManager_431971945414661] Applying battle start buff 104 (Buff_104) for player 90000075737
[BuffManager_431971945414661] Applying battle start buff 107 (Buff_107) for player 90000092047
[BuffManager_431971945414661] Applying battle start buff 101 (Buff_101) for player 90000056864
[AutoChessScene_431971945414661] Camp info for player 90000092047: 10 heroes added
[AutoChessScene_431971945414661] Camp info for player 10106021303: 7 heroes added
[AutoChessScene_431971945414661] Created RoundBattleStart request for player 10106021303, Team order: [90000092047, 10106021303], total GridIDs used: 17
[AutoChessScene_431971945414661] Sent RoundBattleStart to Player 10106021303 vs Opponent 90000092047 with 2 teams
[AutoChessScene_431971945414661] Camp info for player 90000075737: 10 heroes added
[AutoChessScene_431971945414661] Camp info for player 90000056864: 10 heroes added
[AutoChessScene_431971945414661] Created RoundBattleStart request for player 90000075737, Team order: [90000075737, 90000056864], total GridIDs used: 20
[AutoChessScene_431971945414661] Sent RoundBattleStart to Player 90000075737 vs Opponent 90000056864 with 2 teams
[AutoChessScene_431971945414661] Camp info for player 90000092047: 10 heroes added
[AutoChessScene_431971945414661] Camp info for player 10106021303: 7 heroes added
[AutoChessScene_431971945414661] Created RoundBattleStart request for player 90000092047, Team order: [90000092047, 10106021303], total GridIDs used: 17
[AutoChessScene_431971945414661] Sent RoundBattleStart to Player 90000092047 vs Opponent 10106021303 with 2 teams
[AutoChessScene_431971945414661] Camp info for player 90000075737: 10 heroes added
[AutoChessScene_431971945414661] Camp info for player 90000056864: 10 heroes added
[AutoChessScene_431971945414661] Created RoundBattleStart request for player 90000056864, Team order: [90000075737, 90000056864], total GridIDs used: 20
[AutoChessScene_431971945414661] Sent RoundBattleStart to Player 90000056864 vs Opponent 90000075737 with 2 teams
[AutoChessScene_431971945414661] Sent RoundBattleStart notifications with seed: 1386749291
[BattleService] Updated battle 431971945414661 state to StateBattleStarting
[AutoChessScene_431971945414661] State change sent to GameServer: StatePreparation -> StateBattleStarting (R2)
[BattleStateManager_431971945414661] BattleStateChangedEvent published successfully
[AutoChessScene_431971945414661] Player 10106021303 set ready status to True
[BattleStateManager_431971945414661] State: StateBattleStarting -> StateBattleInProgress (R2, 65000ms)
[AutoChessScene_431971945414661] Starting all battle instances
[BattleInstance] 431971945414661_1 battle started
[BattleInstance] 431971945414661_2 battle started
[BattleInstanceManager] Started all 2 battle instances
[AutoChessScene_431971945414661] Auto EndBattle for bot 90000075737 vs bot 90000056864, random result: bot 90000075737 wins = True
[AutoChessScene_431971945414661] Player 90000075737 sent EndBattleReq (win: True), instance: 431971945414661_2
[AutoChessScene_431971945414661] Waiting for opponent 90000056864 to send EndBattleReq for instance 431971945414661_2
[AutoChessScene_431971945414661] Player 90000056864 sent EndBattleReq (win: False), instance: 431971945414661_2
[BattleInstance] 431971945414661_2 battle finished, winner: 90000075737, loser: 90000056864
[AutoChessScene_431971945414661] Battle instance 431971945414661_2 completed: Winner 90000075737, Loser 90000056864
[AutoChessScene_431971945414661] Bot 90000092047 vs real player 10106021303, waiting for real player result
[AutoChessScene_431971945414661] Bot vs real player battles will be handled by system timeout (65s)
[BattleService] Updated battle 431971945414661 state to StateBattleInProgress
[AutoChessScene_431971945414661] State change sent to GameServer: StateBattleStarting -> StateBattleInProgress (R2)
[BattleStateManager_431971945414661] BattleStateChangedEvent published successfully
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] Status: 0 waiting (0/0 entered), 1 active
[BattleService] LeaveBattle request received from player 10106021303
[BattleService] Player 10106021303 logout, cleaning up battle 431971945414661
[BattleService] Only one real player in battle 431971945414661, cleaning up entire battle
[BattleService] Starting cleanup for battle 431971945414661
[BattleService] Removed battle state for 431971945414661
[CheckerBoard_431971945414661] Cleared all entities
[BuffManager_431971945414661] Cleared all buffs
[AutoChessScene_431971945414661] Scene resources disposed
[SceneManager] Removed AutoChessScene 431971945414661 from thread management
[BattleService] Cleaned up scene for battle 431971945414661
[BattleService] Cleanup completed for battle 431971945414661
[BattleService] LeaveBattle completed successfully for player 10106021303
[NatsServer] Received publish message on subject: /30001/natsrpc.BattleService/LeaveBattle, no reply needed
