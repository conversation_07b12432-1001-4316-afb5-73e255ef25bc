﻿//*********************************************************
// Framework
// Author:  zhouchen
// Desc:	MemoryPackHelper
// Date  :  Fri Mar 21 11:22:49 AM UTC 2025
//*********************************************************

using System;
using System.IO;
using MemoryPack;

namespace LiteFrame.Framework
{
    public static class MemoryPackHelper
    {
        //! 因为有多态的存在，所以整了这个函数，和MemoryPackRegistHelper对应
        public static byte[] SerializeMessage(Type type, object message)
        {
            return MemoryPackSerializer.Serialize(type, message);
        }

        //! 因为有多态的存在，所以整了这个函数，和MemoryPackRegistHelper对应
        public static object DeserializeMessage(Type type, byte[] bytes)
        {
            return MemoryPackSerializer.Deserialize(type, bytes);
        }

        //! 一般序列化基本类型
        public static byte[] Serialize<T>(T inObj)
        {
            return MemoryPackSerializer.Serialize(inObj);
        }

        //! 一般反序列化基本类型
        public static T Deserialize<T>(byte[] inByteArray)
        {
            return MemoryPackSerializer.Deserialize<T>(inByteArray);
        }

    }
}
