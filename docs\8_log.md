## 日志规范

1. 引用log包 **"liteframe/pkg/log"**
2. 日志等级从低到高：DEBUG，INFO，WARN，ERROR，FATAL
3. 日志等级说明：

| 等级     |      使用情景      | 线上是否输出 | 例子                          |
| :------- |:--------------:| :----------: |:----------------------------|
| DEBUG    |      调试信息      |      否      | 开发期需要方便调试问题                 |
| INFO     | 正常信息，可供必要情况下查询 |      是      | 完成某个任务、领取某个奖励、打了某场战斗等       |
| WARN  |     一般性错误      |      是      | 客户端传来错误索引、完成条件不满足等          |
| ERROR    | 需要修改代码或者配置的错误  |      是      | 所有订单相关的错误、可能引起严重错误、超出预设定范围等 |
| FATAL |    导致异常或者崩溃    |      是      | panic、数据库连接错误、导致服务器崩溃等      |

* 格式 `log.Info("MSG", log.KV(), log.KV()...)`
* 要求
    * **MSG需要简洁，能够快速定位**
    * **存在唯一id需要打出唯一id**
    * **存在error要打出error，使用log.Err()**
    * **多个字段使用log.KV()，error使用log.Err()**
    * **崩溃级别错误需要打出调用堆栈**
* 示例
    ```go
    log.Info("Formation SaveTeam", log.Kv("uid", f.player.Uid()), log.Kv("ft", ft))
    ```
    ```go
    log.Error("debug client listen failed", log.Err(err))
    ```
