//GrpcAddressType:TeamCenter
//GrpcServerType:server,world

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.23.2
// source: v1/teamcenter.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TeamCenterService_TCAskTeamInfoByTeamId_FullMethodName         = "/Aurora.TeamCenter.TeamCenterService/TCAskTeamInfoByTeamId"
	TeamCenterService_TCAskTeamInfoByPlayerGuid_FullMethodName     = "/Aurora.TeamCenter.TeamCenterService/TCAskTeamInfoByPlayerGuid"
	TeamCenterService_TCCreateTeam_FullMethodName                  = "/Aurora.TeamCenter.TeamCenterService/TCCreateTeam"
	TeamCenterService_TCPlayerJoinTeam_FullMethodName              = "/Aurora.TeamCenter.TeamCenterService/TCPlayerJoinTeam"
	TeamCenterService_TCPlayerLeaveTeam_FullMethodName             = "/Aurora.TeamCenter.TeamCenterService/TCPlayerLeaveTeam"
	TeamCenterService_TCRobotJoinTeam_FullMethodName               = "/Aurora.TeamCenter.TeamCenterService/TCRobotJoinTeam"
	TeamCenterService_TCRobotLeaveTeam_FullMethodName              = "/Aurora.TeamCenter.TeamCenterService/TCRobotLeaveTeam"
	TeamCenterService_TCTeamAppointedJobTitle_FullMethodName       = "/Aurora.TeamCenter.TeamCenterService/TCTeamAppointedJobTitle"
	TeamCenterService_TCAskTeamInfoListByTeamIdList_FullMethodName = "/Aurora.TeamCenter.TeamCenterService/TCAskTeamInfoListByTeamIdList"
)

// TeamCenterServiceClient is the client API for TeamCenterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ServiceStart
type TeamCenterServiceClient interface {
	// 通过队伍ID，查询队伍信息
	TCAskTeamInfoByTeamId(ctx context.Context, in *TCAskTeamInfoByTeamIdRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error)
	// 通过玩家guid，查询队伍信息
	TCAskTeamInfoByPlayerGuid(ctx context.Context, in *TCAskTeamInfoByPlayerGuidRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error)
	// 创建队伍
	TCCreateTeam(ctx context.Context, in *TCCreateTeamRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error)
	// 玩家加入队伍
	TCPlayerJoinTeam(ctx context.Context, in *TCPlayerJoinTeamRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error)
	// 玩家离开队伍
	TCPlayerLeaveTeam(ctx context.Context, in *TCPlayerLeaveTeamRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error)
	// 机器人加入队伍
	TCRobotJoinTeam(ctx context.Context, in *TCRobotJoinTeamRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error)
	// 机器人离开队伍
	TCRobotLeaveTeam(ctx context.Context, in *TCRobotLeaveTeamRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error)
	// 队长任命队伍中的成员职位
	TCTeamAppointedJobTitle(ctx context.Context, in *TCTeamAppointedJobTitleRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error)
	// 批量查询队伍数据
	TCAskTeamInfoListByTeamIdList(ctx context.Context, in *TCAskTeamInfoListByTeamIdListRequest, opts ...grpc.CallOption) (*TCTeamInfoListReply, error)
}

type teamCenterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTeamCenterServiceClient(cc grpc.ClientConnInterface) TeamCenterServiceClient {
	return &teamCenterServiceClient{cc}
}

func (c *teamCenterServiceClient) TCAskTeamInfoByTeamId(ctx context.Context, in *TCAskTeamInfoByTeamIdRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TCTeamInfoReply)
	err := c.cc.Invoke(ctx, TeamCenterService_TCAskTeamInfoByTeamId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamCenterServiceClient) TCAskTeamInfoByPlayerGuid(ctx context.Context, in *TCAskTeamInfoByPlayerGuidRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TCTeamInfoReply)
	err := c.cc.Invoke(ctx, TeamCenterService_TCAskTeamInfoByPlayerGuid_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamCenterServiceClient) TCCreateTeam(ctx context.Context, in *TCCreateTeamRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TCTeamInfoReply)
	err := c.cc.Invoke(ctx, TeamCenterService_TCCreateTeam_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamCenterServiceClient) TCPlayerJoinTeam(ctx context.Context, in *TCPlayerJoinTeamRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TCTeamInfoReply)
	err := c.cc.Invoke(ctx, TeamCenterService_TCPlayerJoinTeam_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamCenterServiceClient) TCPlayerLeaveTeam(ctx context.Context, in *TCPlayerLeaveTeamRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TCTeamInfoReply)
	err := c.cc.Invoke(ctx, TeamCenterService_TCPlayerLeaveTeam_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamCenterServiceClient) TCRobotJoinTeam(ctx context.Context, in *TCRobotJoinTeamRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TCTeamInfoReply)
	err := c.cc.Invoke(ctx, TeamCenterService_TCRobotJoinTeam_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamCenterServiceClient) TCRobotLeaveTeam(ctx context.Context, in *TCRobotLeaveTeamRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TCTeamInfoReply)
	err := c.cc.Invoke(ctx, TeamCenterService_TCRobotLeaveTeam_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamCenterServiceClient) TCTeamAppointedJobTitle(ctx context.Context, in *TCTeamAppointedJobTitleRequest, opts ...grpc.CallOption) (*TCTeamInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TCTeamInfoReply)
	err := c.cc.Invoke(ctx, TeamCenterService_TCTeamAppointedJobTitle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *teamCenterServiceClient) TCAskTeamInfoListByTeamIdList(ctx context.Context, in *TCAskTeamInfoListByTeamIdListRequest, opts ...grpc.CallOption) (*TCTeamInfoListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TCTeamInfoListReply)
	err := c.cc.Invoke(ctx, TeamCenterService_TCAskTeamInfoListByTeamIdList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TeamCenterServiceServer is the server API for TeamCenterService service.
// All implementations must embed UnimplementedTeamCenterServiceServer
// for forward compatibility.
//
// ServiceStart
type TeamCenterServiceServer interface {
	// 通过队伍ID，查询队伍信息
	TCAskTeamInfoByTeamId(context.Context, *TCAskTeamInfoByTeamIdRequest) (*TCTeamInfoReply, error)
	// 通过玩家guid，查询队伍信息
	TCAskTeamInfoByPlayerGuid(context.Context, *TCAskTeamInfoByPlayerGuidRequest) (*TCTeamInfoReply, error)
	// 创建队伍
	TCCreateTeam(context.Context, *TCCreateTeamRequest) (*TCTeamInfoReply, error)
	// 玩家加入队伍
	TCPlayerJoinTeam(context.Context, *TCPlayerJoinTeamRequest) (*TCTeamInfoReply, error)
	// 玩家离开队伍
	TCPlayerLeaveTeam(context.Context, *TCPlayerLeaveTeamRequest) (*TCTeamInfoReply, error)
	// 机器人加入队伍
	TCRobotJoinTeam(context.Context, *TCRobotJoinTeamRequest) (*TCTeamInfoReply, error)
	// 机器人离开队伍
	TCRobotLeaveTeam(context.Context, *TCRobotLeaveTeamRequest) (*TCTeamInfoReply, error)
	// 队长任命队伍中的成员职位
	TCTeamAppointedJobTitle(context.Context, *TCTeamAppointedJobTitleRequest) (*TCTeamInfoReply, error)
	// 批量查询队伍数据
	TCAskTeamInfoListByTeamIdList(context.Context, *TCAskTeamInfoListByTeamIdListRequest) (*TCTeamInfoListReply, error)
	mustEmbedUnimplementedTeamCenterServiceServer()
}

// UnimplementedTeamCenterServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTeamCenterServiceServer struct{}

func (UnimplementedTeamCenterServiceServer) TCAskTeamInfoByTeamId(context.Context, *TCAskTeamInfoByTeamIdRequest) (*TCTeamInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TCAskTeamInfoByTeamId not implemented")
}
func (UnimplementedTeamCenterServiceServer) TCAskTeamInfoByPlayerGuid(context.Context, *TCAskTeamInfoByPlayerGuidRequest) (*TCTeamInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TCAskTeamInfoByPlayerGuid not implemented")
}
func (UnimplementedTeamCenterServiceServer) TCCreateTeam(context.Context, *TCCreateTeamRequest) (*TCTeamInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TCCreateTeam not implemented")
}
func (UnimplementedTeamCenterServiceServer) TCPlayerJoinTeam(context.Context, *TCPlayerJoinTeamRequest) (*TCTeamInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TCPlayerJoinTeam not implemented")
}
func (UnimplementedTeamCenterServiceServer) TCPlayerLeaveTeam(context.Context, *TCPlayerLeaveTeamRequest) (*TCTeamInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TCPlayerLeaveTeam not implemented")
}
func (UnimplementedTeamCenterServiceServer) TCRobotJoinTeam(context.Context, *TCRobotJoinTeamRequest) (*TCTeamInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TCRobotJoinTeam not implemented")
}
func (UnimplementedTeamCenterServiceServer) TCRobotLeaveTeam(context.Context, *TCRobotLeaveTeamRequest) (*TCTeamInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TCRobotLeaveTeam not implemented")
}
func (UnimplementedTeamCenterServiceServer) TCTeamAppointedJobTitle(context.Context, *TCTeamAppointedJobTitleRequest) (*TCTeamInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TCTeamAppointedJobTitle not implemented")
}
func (UnimplementedTeamCenterServiceServer) TCAskTeamInfoListByTeamIdList(context.Context, *TCAskTeamInfoListByTeamIdListRequest) (*TCTeamInfoListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TCAskTeamInfoListByTeamIdList not implemented")
}
func (UnimplementedTeamCenterServiceServer) mustEmbedUnimplementedTeamCenterServiceServer() {}
func (UnimplementedTeamCenterServiceServer) testEmbeddedByValue()                           {}

// UnsafeTeamCenterServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TeamCenterServiceServer will
// result in compilation errors.
type UnsafeTeamCenterServiceServer interface {
	mustEmbedUnimplementedTeamCenterServiceServer()
}

func RegisterTeamCenterServiceServer(s grpc.ServiceRegistrar, srv TeamCenterServiceServer) {
	// If the following call pancis, it indicates UnimplementedTeamCenterServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TeamCenterService_ServiceDesc, srv)
}

func _TeamCenterService_TCAskTeamInfoByTeamId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TCAskTeamInfoByTeamIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamCenterServiceServer).TCAskTeamInfoByTeamId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TeamCenterService_TCAskTeamInfoByTeamId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamCenterServiceServer).TCAskTeamInfoByTeamId(ctx, req.(*TCAskTeamInfoByTeamIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TeamCenterService_TCAskTeamInfoByPlayerGuid_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TCAskTeamInfoByPlayerGuidRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamCenterServiceServer).TCAskTeamInfoByPlayerGuid(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TeamCenterService_TCAskTeamInfoByPlayerGuid_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamCenterServiceServer).TCAskTeamInfoByPlayerGuid(ctx, req.(*TCAskTeamInfoByPlayerGuidRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TeamCenterService_TCCreateTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TCCreateTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamCenterServiceServer).TCCreateTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TeamCenterService_TCCreateTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamCenterServiceServer).TCCreateTeam(ctx, req.(*TCCreateTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TeamCenterService_TCPlayerJoinTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TCPlayerJoinTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamCenterServiceServer).TCPlayerJoinTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TeamCenterService_TCPlayerJoinTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamCenterServiceServer).TCPlayerJoinTeam(ctx, req.(*TCPlayerJoinTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TeamCenterService_TCPlayerLeaveTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TCPlayerLeaveTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamCenterServiceServer).TCPlayerLeaveTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TeamCenterService_TCPlayerLeaveTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamCenterServiceServer).TCPlayerLeaveTeam(ctx, req.(*TCPlayerLeaveTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TeamCenterService_TCRobotJoinTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TCRobotJoinTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamCenterServiceServer).TCRobotJoinTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TeamCenterService_TCRobotJoinTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamCenterServiceServer).TCRobotJoinTeam(ctx, req.(*TCRobotJoinTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TeamCenterService_TCRobotLeaveTeam_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TCRobotLeaveTeamRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamCenterServiceServer).TCRobotLeaveTeam(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TeamCenterService_TCRobotLeaveTeam_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamCenterServiceServer).TCRobotLeaveTeam(ctx, req.(*TCRobotLeaveTeamRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TeamCenterService_TCTeamAppointedJobTitle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TCTeamAppointedJobTitleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamCenterServiceServer).TCTeamAppointedJobTitle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TeamCenterService_TCTeamAppointedJobTitle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamCenterServiceServer).TCTeamAppointedJobTitle(ctx, req.(*TCTeamAppointedJobTitleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TeamCenterService_TCAskTeamInfoListByTeamIdList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TCAskTeamInfoListByTeamIdListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamCenterServiceServer).TCAskTeamInfoListByTeamIdList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TeamCenterService_TCAskTeamInfoListByTeamIdList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamCenterServiceServer).TCAskTeamInfoListByTeamIdList(ctx, req.(*TCAskTeamInfoListByTeamIdListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TeamCenterService_ServiceDesc is the grpc.ServiceDesc for TeamCenterService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TeamCenterService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Aurora.TeamCenter.TeamCenterService",
	HandlerType: (*TeamCenterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TCAskTeamInfoByTeamId",
			Handler:    _TeamCenterService_TCAskTeamInfoByTeamId_Handler,
		},
		{
			MethodName: "TCAskTeamInfoByPlayerGuid",
			Handler:    _TeamCenterService_TCAskTeamInfoByPlayerGuid_Handler,
		},
		{
			MethodName: "TCCreateTeam",
			Handler:    _TeamCenterService_TCCreateTeam_Handler,
		},
		{
			MethodName: "TCPlayerJoinTeam",
			Handler:    _TeamCenterService_TCPlayerJoinTeam_Handler,
		},
		{
			MethodName: "TCPlayerLeaveTeam",
			Handler:    _TeamCenterService_TCPlayerLeaveTeam_Handler,
		},
		{
			MethodName: "TCRobotJoinTeam",
			Handler:    _TeamCenterService_TCRobotJoinTeam_Handler,
		},
		{
			MethodName: "TCRobotLeaveTeam",
			Handler:    _TeamCenterService_TCRobotLeaveTeam_Handler,
		},
		{
			MethodName: "TCTeamAppointedJobTitle",
			Handler:    _TeamCenterService_TCTeamAppointedJobTitle_Handler,
		},
		{
			MethodName: "TCAskTeamInfoListByTeamIdList",
			Handler:    _TeamCenterService_TCAskTeamInfoListByTeamIdList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "v1/teamcenter.proto",
}
