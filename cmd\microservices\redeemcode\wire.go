//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"liteframe/internal/microservices/redeemcode/biz"
	"liteframe/internal/microservices/redeemcode/conf"
	"liteframe/internal/microservices/redeemcode/data"
	"liteframe/internal/microservices/redeemcode/registry"
	"liteframe/internal/microservices/redeemcode/server"
	"liteframe/internal/microservices/redeemcode/service"
)

// wireApp init redeemCode application.
func wireApp(*conf.Server, *conf.Data, *conf.Bootstrap, log.Logger, map[string]string) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, registry.ProviderSet, newApp))
}
