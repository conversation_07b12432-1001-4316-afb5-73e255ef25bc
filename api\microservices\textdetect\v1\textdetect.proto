//
// @Description: ddotnet游戏服和kratos交互的GRPC消息定义在这里
//               注：每个service前后请加//ServiceStart和//ServiceEnd，否则不能生成dotnet的C#Service代码
// @Data: Thu Jan  4 14:27:54 CST 2024

//下面这行很重要，是表明你这个Service用于哪个URL的，见游戏服的GrpcAddressType
//GrpcAddressType:TextDetect
//GrpcServerType:all

syntax = "proto3";

package Aurora.PlayerInfoServer;

import "google/api/annotations.proto";

option go_package = "textdetect/api/textdetect/v1;v1";

//ServiceStart
service TextDetect {
rpc DoTextDetect(DoTextDetectRequest) returns (DoTextDetectReply) {
}
}
//ServiceEnd

//Type:Http
enum DetectType
{
//正常
DetectType_Normal = 0;
//禁言
DetectType_JinYan = 1;
}

message TextDetectItem{
uint64 index = 1;
int32 zoneWorldId = 2;
uint64 guid = 3;
DetectType detectType = 4;
string text = 5;
}

message DoTextDetectRequest {
repeated TextDetectItem textDetectItems = 1;
}

enum TextDetectResultType
{
//通过
TextDetectResultType_Pass           = 0;
//嫌疑
TextDetectResultType_XianYi         = 1;
//不通过
TextDetectResultType_NoPass         = 2;
}

enum TextDetectEvilType
{
//通过
TextDetectEvilType_Pass             = 0;
//色情
TextDetectEvilType_Porn             = 100;
//广告
TextDetectEvilType_Advertisement    = 200;
//涉恐
TextDetectEvilType_Terror           = 300;
//违禁
TextDetectEvilType_Prohibit         = 400;
//涉政
TextDetectEvilType_Politics         = 500;
//灌水
TextDetectEvilType_Water            = 700;
//其它
TextDetectEvilType_Other            = 900;
}

message HadesTextDetected{
uint64  Result = 1;
string  RetMsg = 2;
string  TextID = 3;
uint64  EvilType = 4;
}

message HadesResult{
bool Status = 1;
uint64 Id = 2;
HadesTextDetected Json = 3;
}

message TextDetectedItem{
uint64 index = 1;
HadesResult hadesResult = 2;
}

message DoTextDetectReply {
int32 result = 1;
repeated TextDetectedItem textDetectedItems = 2;
}

