[{"ID": 1001, "StartType": 5, "StartParameter": 5, "EndType": -1, "EndParameter": -1, "GuildStartId": 100101, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 1002, "StartType": 2, "StartParameter": 40000, "EndType": 2, "EndParameter": 40000, "GuildStartId": 100201, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 1100, "StartType": 2, "StartParameter": 40001, "EndType": -1, "EndParameter": -1, "GuildStartId": 110001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_CharacterUpgrade"]}, {"ID": 1200, "StartType": 2, "StartParameter": 40002, "EndType": 2, "EndParameter": 40002, "GuildStartId": 120001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_CharacterUpgrade"]}, {"ID": 1300, "StartType": 3, "StartParameter": 1200, "EndType": -1, "EndParameter": -1, "GuildStartId": 130001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_CharacterUpgrade"]}, {"ID": 1400, "StartType": 2, "StartParameter": 40003, "EndType": 2, "EndParameter": 40003, "GuildStartId": 140001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_CharacterUpgrade"]}, {"ID": 1500, "StartType": 2, "StartParameter": 40004, "EndType": 2, "EndParameter": 40004, "GuildStartId": 150001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_CharacterUpgrade"]}, {"ID": 1600, "StartType": 1, "StartParameter": 40004, "EndType": -1, "EndParameter": -1, "GuildStartId": 160001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 1700, "StartType": 1, "StartParameter": 40006, "EndType": -1, "EndParameter": -1, "GuildStartId": 170001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 1800, "StartType": 1, "StartParameter": 40009, "EndType": 7, "EndParameter": 7, "GuildStartId": 180001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 1900, "StartType": 3, "StartParameter": 1800, "EndType": -1, "EndParameter": -1, "GuildStartId": 190001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_Lottery"]}, {"ID": 2000, "StartType": 3, "StartParameter": 1900, "EndType": -1, "EndParameter": -1, "GuildStartId": 200001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_Equip"]}, {"ID": 2100, "StartType": 3, "StartParameter": 2000, "EndType": -1, "EndParameter": -1, "GuildStartId": 210001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_Equip"]}, {"ID": 2200, "StartType": 1, "StartParameter": 40015, "EndType": 14, "EndParameter": 4, "GuildStartId": 220001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 2230, "StartType": 3, "StartParameter": 2200, "EndType": 16, "EndParameter": 1, "GuildStartId": 223001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 2260, "StartType": 3, "StartParameter": 2230, "EndType": 16, "EndParameter": 2, "GuildStartId": 226001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 2300, "StartType": 3, "StartParameter": 2260, "EndType": 14, "EndParameter": -1, "GuildStartId": 230001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 2310, "StartType": 3, "StartParameter": 2300, "EndType": 11, "EndParameter": -1, "GuildStartId": 231001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 2400, "StartType": 3, "StartParameter": 2310, "EndType": 13, "EndParameter": -1, "GuildStartId": 240001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 2410, "StartType": 3, "StartParameter": 2400, "EndType": -1, "EndParameter": -1, "GuildStartId": 241001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 2500, "StartType": 1, "StartParameter": 40020, "EndType": 7, "EndParameter": 7, "GuildStartId": 250001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 2600, "StartType": 3, "StartParameter": 2500, "EndType": -1, "EndParameter": -1, "GuildStartId": 260001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_Lottery"]}, {"ID": 2700, "StartType": 1, "StartParameter": 40029, "EndType": 4, "EndParameter": 2000001, "GuildStartId": 270001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 3000, "StartType": 16, "StartParameter": 7, "EndType": 10, "EndParameter": 1, "GuildStartId": 300001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonMining", "UI_LevelUp", "UI_Island", "UI_Island_DragonDig", "UI_WaitNetWork", "UI_DragonDig", "UI_Dragon_GetPop", "UI_DragonIsland"]}, {"ID": 3100, "StartType": 3, "StartParameter": 3000, "EndType": 10, "EndParameter": 2, "GuildStartId": 310001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonMining", "UI_LevelUp", "UI_Island", "UI_Island_DragonDig", "UI_WaitNetWork", "UI_DragonDig", "UI_Dragon_GetPop", "UI_DragonIsland"]}, {"ID": 3110, "StartType": 3, "StartParameter": 3100, "EndType": 10, "EndParameter": 2, "GuildStartId": 311001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonMining", "UI_LevelUp", "UI_Island", "UI_Island_DragonDig", "UI_WaitNetWork", "UI_DragonDig", "UI_Dragon_GetPop", "UI_DragonIsland"]}, {"ID": 3200, "StartType": 3, "StartParameter": 3110, "EndType": -1, "EndParameter": -1, "GuildStartId": 320001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonMining", "UI_LevelUp", "UI_Island", "UI_Island_DragonDig", "UI_WaitNetWork", "UI_DragonDig", "UI_Dragon_GetPop", "UI_DragonIsland"]}, {"ID": 3300, "StartType": 3, "StartParameter": 3200, "EndType": 11, "EndParameter": 0, "GuildStartId": 330001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 3400, "StartType": 3, "StartParameter": 3300, "EndType": 11, "EndParameter": 0, "GuildStartId": 340001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 3500, "StartType": 3, "StartParameter": 3400, "EndType": 12, "EndParameter": 3, "GuildStartId": 350001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 3600, "StartType": 3, "StartParameter": 3500, "EndType": 13, "EndParameter": -1, "GuildStartId": 360001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 3700, "StartType": 1, "StartParameter": 40063, "EndType": 4, "EndParameter": 3000001, "GuildStartId": 370001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_Instance_RewardPop", "UI_Instance", "UI_Instance_StageChoosePop"]}, {"ID": 3800, "StartType": 4, "StartParameter": 3000001, "EndType": -1, "EndParameter": -1, "GuildStartId": 380001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_Instance_RewardPop", "UI_Instance", "UI_Instance_StageChoosePop"]}, {"ID": 3900, "StartType": 1, "StartParameter": 40055, "EndType": 4, "EndParameter": 6001001, "GuildStartId": 390001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 4000, "StartType": 4, "StartParameter": 6001001, "EndType": -1, "EndParameter": -1, "GuildStartId": 400001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 4300, "StartType": 16, "StartParameter": 52, "EndType": -1, "EndParameter": -1, "GuildStartId": 430001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonIsland"]}, {"ID": 4540, "StartType": 16, "StartParameter": 17, "EndType": -1, "EndParameter": -1, "GuildStartId": 454001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 4560, "StartType": 3, "StartParameter": 4540, "EndType": -1, "EndParameter": -1, "GuildStartId": 456001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig", "UI_GetReward"]}, {"ID": 4580, "StartType": 3, "StartParameter": 4560, "EndType": -1, "EndParameter": -1, "GuildStartId": 458001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 4650, "StartType": 16, "StartParameter": 22, "EndType": -1, "EndParameter": -1, "GuildStartId": 465001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonCompound", "UI_DragonIsland", "UI_DragonDig"]}, {"ID": 4700, "StartType": 1, "StartParameter": 40080, "EndType": -1, "EndParameter": -1, "GuildStartId": 470001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 4800, "StartType": 1, "StartParameter": 40097, "EndType": -1, "EndParameter": -1, "GuildStartId": 480001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 5000, "StartType": 16, "StartParameter": 32, "EndType": -1, "EndParameter": -1, "GuildStartId": 500001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonIsland", "UI_Main_StageMapPop", "UI_DragonFactory_ResDetailPop", "UI_DragonFactory"]}, {"ID": 5010, "StartType": 3, "StartParameter": 5000, "EndType": -1, "EndParameter": -1, "GuildStartId": 501001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonIsland", "UI_Main_StageMapPop", "UI_DragonFactory_ResDetailPop", "UI_DragonFactory"]}, {"ID": 5015, "StartType": 3, "StartParameter": 5010, "EndType": -1, "EndParameter": -1, "GuildStartId": 501501, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_DragonFactory_LvUpPop", "UI_DragonIsland", "UI_Main_StageMapPop", "UI_DragonFactory_ResDetailPop", "UI_DragonFactory"]}, {"ID": 5100, "StartType": 1, "StartParameter": 40130, "EndType": -1, "EndParameter": -1, "GuildStartId": 510001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 5200, "StartType": 1, "StartParameter": 40150, "EndType": -1, "EndParameter": -1, "GuildStartId": 520001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_Lottery"]}, {"ID": 5300, "StartType": 3, "StartParameter": 5200, "EndType": -1, "EndParameter": -1, "GuildStartId": 530001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_Lottery"]}, {"ID": 5400, "StartType": 1, "StartParameter": 40173, "EndType": -1, "EndParameter": -1, "GuildStartId": 540001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 5500, "StartType": 1, "StartParameter": 40187, "EndType": -1, "EndParameter": -1, "GuildStartId": 550001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 5600, "StartType": 1, "StartParameter": 40212, "EndType": -1, "EndParameter": -1, "GuildStartId": 560001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": []}, {"ID": 5700, "StartType": 3, "StartParameter": 5600, "EndType": -1, "EndParameter": -1, "GuildStartId": 570001, "IsInMainScene": 1, "IsGoBack": -1, "StayUI": ["UI_Equip", "UI_Lottery"]}]