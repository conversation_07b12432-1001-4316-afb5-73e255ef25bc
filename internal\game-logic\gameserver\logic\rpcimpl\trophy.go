package rpcimpl

import (
	"context"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/game-logic/gameserver/logic/player"
)

type trophyService struct {
	defaultService
}

func (t *trophyService) ClaimSeasonReward(ctx context.Context, p *player.Player, in *cs.CLClaimSeasonRewardReq) *cs.LCClaimSeasonRewardRsp {
	out := p.Trophy().HandleClaimSeasonReward(in)
	return out
}

func (t *trophyService) SeasonInfo(ctx context.Context, p *player.Player, in *cs.CLSeasonInfoReq) *cs.LCSeasonInfoRsp {
	out := p.Trophy().HandleSyncSeasonInfo(in)
	return out
}
