﻿
using BattleServer.Server;
using LiteFrame.Framework;

namespace LiteFrame.Game
{
    public enum PacketSendType
    {
        None = 0,
        Session = 1,
        MQ = 2,
    }

    public class BasePacketDispatcher : IPacketDispatcher
    {
        public virtual void OnRead(Packet packet, Session session)
        {
        }

        public virtual void OnSend(Packet packet, Session session)
        {
        }

        public void Dispatch(Packet packet, Session session = null)
        {
            if (packet != null)
            {
                try
                {
                    WorkUnitBase targetWorkUnit = SeekTargetWorkUnit(packet, out bool bSeekBreak);
                    if (!bSeekBreak)
                    {
                        if (targetWorkUnit != null)
                        {
                            if (ThreadObject.CurrentWorkUnit == targetWorkUnit)
                            {
                                Session targetSession = SeekTargetSession(packet, session, out bool bNeedSend, out bSeekBreak, out bool bError);
                                if (!bError)
                                {
                                    if (!bSeekBreak)
                                    {
                                        if (bNeedSend)
                                        {
                                            PacketSendType sendType = GetPacketSendType(packet);
                                            if (sendType == PacketSendType.MQ)      //通过消息队列发送
                                            {
                                                //SendByMQ(packet);
                                            }
                                            else
                                            {
                                                SendBySession(targetSession, packet);
                                            }
                                        }
                                        else
                                        {
                                            if (!packet.GetMessage().Precheck || Precheck(targetSession, packet))
                                            {
                                                if (packet.MsgClass == MessageClass.IResponse)
                                                {
                                                    if (targetSession != null)
                                                    {
                                                        string strMethodName = packet.GetMessage().GetType().Name + ".ResponseHandler";
                                                        try
                                                        {
#if OPEN_PERFORMANCE
                                                            {
                                                                PerformanceMonitorManager.EnterMethod(strMethodName);
                                                            }
#endif

                                                            IResponse response = packet.GetMessage() as IResponse;
                                                            targetSession.OnRead(packet.RpcID, response);
                                                        }
                                                        catch (Exception e)
                                                        {
                                                            targetSession.OnExcuteError();
                                                            Log.Error($"{strMethodName} Execute Error! Message:{e.Message}\nStackTrace:\n{e.StackTrace}");
                                                        }
                                                        finally
                                                        {
#if OPEN_PERFORMANCE
                                                            PerformanceMonitorManager.LeaveMethod(strMethodName);
#endif
                                                            Packet.Destroy(packet);
                                                        }
                                                    }
                                                    else
                                                    {
                                                        Log.Error($"Dispatch Packet Fail, Target Session Is Not Found!");
                                                        OnDispatchError(packet);
                                                    }
                                                }
                                                else
                                                {
                                                    string strMethodName = packet.GetMessage().GetType().Name + ".MessageHandler";
                                                    try
                                                    {
#if OPEN_PERFORMANCE
                                                        PerformanceMonitorManager.EnterMethod(strMethodName);
#endif
                                                        MessageHandlerComponent.Instance.Handle(targetSession, packet);
                                                    }
                                                    catch (Exception e)
                                                    {
                                                        targetSession?.OnExcuteError();
                                                        Log.Error($"{strMethodName} Execute Error! Message:{e.Message}\nStackTrace:\n{e.StackTrace}");
                                                    }
                                                    finally
                                                    {
#if OPEN_PERFORMANCE
                                                        PerformanceMonitorManager.LeaveMethod(strMethodName);
#endif
                                                        Packet.Destroy(packet);
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                Log.Error($"Dispatch Packet Fail, IsCanLogic Fail!");
                                                OnDispatchError(packet, (int)FError.CannotLogicErr);
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    OnDispatchError(packet);
                                }
                            }
                            else
                            {
                                targetWorkUnit.PostPacket(packet);
                            }
                        }
                        else
                        {
                            Log.Error($"Dispatch Packet Fail, Target WorkUnit Is Not Found! Packet:{packet.GetMessage().GetType().Name}");
                            OnDispatchError(packet);
                        }
                    }
                }
                catch (Exception e)
                {
                    Log.Error($"Dispatch Packet Error! Message:{e.Message}\nStackTrace:\n{e.StackTrace}");
                    OnDispatchError(packet);
                }
            }
            else
            {
                Log.Error($"Dispatch Packet Fail, Packet Is Null!");
            }
        }

        public void DispatchLater(Packet packet)
        {
            ThreadObject.CurrentWorkUnit.PostPacket(packet);
        }

        public void DiscardPacket(Packet packet)
        {
            Log.Error($"Dispatch Packet Fail, Target WorkUnit Has Shut Down!");
            OnDispatchError(packet);
        }

        protected virtual WorkUnitBase SeekTargetWorkUnit(Packet packet, out bool bSeekBreak)
        {
            bSeekBreak = false;
            return null;
        }

        protected virtual Session SeekTargetSession(Packet packet, Session session, out bool bNeedSend, out bool bSeekBreak, out bool bError)
        {
            bNeedSend = false;
            bSeekBreak = false;
            bError = false;
            return null;
        }

        protected virtual bool Precheck(Session session, Packet packet)
        {
            return true;
        }

        public virtual PacketSendType GetPacketSendType(Packet packet)
        {
            return PacketSendType.Session;
        }

        private void OnDispatchError(Packet packet, int nError = (int)FError.DispatchErr)
        {
            if (packet.MsgClass == MessageClass.IRequest)
            {
                if (packet.RpcID != 0)
                {
                    IRequest request = packet.GetMessage() as IRequest;
                    if (request != null)
                    {
                        IResponse response = MessageIDComponent.Instance.CreateResponse(request);
                        if (response != null)
                        {
                            response.Error = nError;
                            response.Message = $"Dispatch Error, request: {request.GetType().Name} response: {response.GetType().Name}";
                            Packet replyPacket = Packet.Create(response);
                            {
                                replyPacket.RpcID = packet.RpcID;
                                replyPacket.SourceAddress.SetAsNone();
                                replyPacket.TargetAddress.CopyFrom(packet.SourceAddress);
                            }
                            Dispatch(replyPacket);
                        }
                    }
                }
            }

            Packet.Destroy(packet);
        }

        private void SendBySession(Session Session, Packet packet)
        {
            if (Session != null)
            {
                Session.Send(packet);
                Packet.Destroy(packet);
            }
            else
            {
                Log.Error($"Dispatch Packet Fail, Target Session Is Not Found! Packet:{packet.GetMessage().GetType().Name}");
                OnDispatchError(packet);
            }
        }

        //private static void SendByMQ(Packet packet)
        //{
        //    Service mqService = ServiceManager.Instance.GetService(ServiceID.MQ);
        //    if (mqService != null)
        //    {
        //        RedisStreamMQComponent mqComponent = mqService.GetComponent<RedisStreamMQComponent>();
        //        if (mqComponent != null)
        //        {

        //            string key = mqComponent.GenMQKeyByPacket(packet.TargetAddress);
        //            mqComponent.SendPacketToRedisMQ(key, packet);
        //        }
        //    }

        //    Packet.Destroy(packet);
        //}
    }
}
