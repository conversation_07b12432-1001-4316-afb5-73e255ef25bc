package main

import (
	"fmt"
	"os"

	"liteframe/internal/common/constant"
	"liteframe/internal/common/version"
	"liteframe/internal/game-logic/matchserver/server"
	"liteframe/pkg/log"

	"github.com/urfave/cli/v2"
)

func main() {
	app := &cli.App{
		Name:    string(constant.ServiceNameMatchServer),
		Flags:   server.Flags(),
		Action:  mainLoop,
		Version: version.String(),
	}

	err := app.Run(os.Args)
	if err != nil {
		fmt.Println("server run failed!", err)
		log.Error("server run failed!", log.Err(err))
	} else {
		log.Info("server close!")
	}
	log.Close()
}

func mainLoop(c *cli.Context) error {
	s := server.NewServer()

	if err := s.Init(); err != nil {
		return err
	}

	if err := s.Run(); err != nil {
		return err
	}

	return s.Exit()
}
