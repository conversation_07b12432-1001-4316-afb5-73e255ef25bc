package main

import (
	"context"
	"flag"
	"fmt"
	"liteframe/internal/common/constant"
	"liteframe/internal/general-services/announcement/biz"
	"liteframe/internal/general-services/announcement/boot"
	"os"

	"github.com/go-kratos/kratos/contrib/registry/etcd/v2"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	_ "go.uber.org/automaxprocs"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name = constant.ServiceNameAnnouncement
	// Version is the version of the compiled software.
	Version string

	id, _   = os.Hostname()
	PidFile = constant.ServiceNameAnnouncement + ".pid"
)

func newApp(logger log.Logger, hs *http.Server, r *etcd.Registry, meta map[string]string) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(meta),
		kratos.Logger(logger),
		kratos.Server(
			hs,
		),
		kratos.Registrar(r),
		kratos.AfterStart(afterStart),
	)
}

func afterStart(context.Context) error {
	pid := fmt.Sprintf("%d", os.Getpid())
	f, err := os.OpenFile(PidFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		panic(err)
	}
	err = os.WriteFile(PidFile, []byte(pid), 0)
	if err != nil {
		panic(err)
	}
	err = f.Close()
	if err != nil {
		panic(err)
	}
	return nil
}

func main() {
	flag.Parse()

	// 初始化配置
	app, cleanup, err := initApp()
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}

// initApp 初始化应用
func initApp() (*kratos.App, func(), error) {
	// 创建一个基础logger
	baseLogger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", id,
		"service.name", Name,
		"service.version", Version,
	)
	logHelper := log.NewHelper(baseLogger)

	// 创建配置管理器 - 使用boot.go中的NewBootConf初始化
	bootConf := boot.NewBootConf()

	// 加载配置
	bc := bootConf.Run(id, Name)

	// 确保版本号设置正确
	bc.Version = Version

	// 使用配置初始化结构化logger
	logger := boot.NewBootLog(bc).Run()
	logHelper = log.NewHelper(logger)

	logHelper.Infof("Starting service: name=%s, version=%s", Name, Version)

	metaData := map[string]string{}
	app, cleanup, err := wireApp(bc.Server, bc.Data, bc, logger, metaData)
	if err != nil {
		return nil, nil, err
	}

	// 如果已经创建了AnnouncementUsecase，重新初始化配置以支持Redis同步
	if biz.GlobalAnnouncementUsecase != nil {
		bootConf = boot.NewBootConfWithUsecase(biz.GlobalAnnouncementUsecase)
		bc = bootConf.Run(id, Name)
	}

	return app, cleanup, nil
}
