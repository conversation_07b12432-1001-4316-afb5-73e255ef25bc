// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"context"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"liteframe/internal/general-services/announcement/biz"
	"liteframe/internal/general-services/announcement/conf"
	"liteframe/internal/general-services/announcement/data"
	"liteframe/internal/general-services/announcement/registry"
	"liteframe/internal/general-services/announcement/server"
	"liteframe/internal/general-services/announcement/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init announcement application.
func wireApp(confServer *conf.Server, confData *conf.Data, bootstrap *conf.Bootstrap, logger log.Logger, arg map[string]string) (*kratos.App, func(), error) {
	cmdable := data.NewRedisCmd(confData, logger)
	dataData, err := data.NewData(cmdable, logger)
	if err != nil {
		return nil, nil, err
	}
	announcementRepo := data.NewAnnouncementRepo(dataData, logger)
	announcementUsecase := biz.NewAnnouncementUsecase(announcementRepo, logger, bootstrap)
	announcementService := service.NewAnnouncementServiceService(announcementUsecase)
	httpServer := server.NewHTTPServer(confServer, announcementService, logger)
	confRegistry := registry.ProvideRegistry(bootstrap)
	etcdRegistry := registry.NewEtcdRegistry(confRegistry)
	app := newApp(logger, httpServer, etcdRegistry, arg)
	return app, func() {
	}, nil
}

// wire.go:

// 全局变量存储usecase引用，用于配置变更时调用
var (
	globalAnnouncementUsecase *biz.AnnouncementUsecase
)

// SetAnnouncementUsecase 提供用于获取usecase的函数
func SetAnnouncementUsecase(u *biz.AnnouncementUsecase) {
	globalAnnouncementUsecase = u
}

// UpdateAnnouncementConfig 提供用于更新配置的函数
func UpdateAnnouncementConfig(ctx context.Context, bootstrap *conf.Bootstrap) error {
	if biz.GlobalAnnouncementUsecase != nil {
		return biz.GlobalAnnouncementUsecase.UpdateConfig(ctx, bootstrap)
	}
	return nil
}
