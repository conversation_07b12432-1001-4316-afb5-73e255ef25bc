// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"go.etcd.io/etcd/client/v3"
	"liteframe/internal/general-services/director/biz"
	"liteframe/internal/general-services/director/conf"
	"liteframe/internal/general-services/director/registry"
	"liteframe/internal/general-services/director/server"
	"liteframe/internal/general-services/director/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init director application.
func wireApp(confServer *conf.Server, config *conf.Config, confRegistry *conf.Registry, logger log.Logger, client *clientv3.Client, arg map[string]string) (*kratos.App, func(), error) {
	globalDirectorInterface := biz.NewEtcdWatch(config, client, logger)
	directorUsecase := biz.NewDirectorUseCase(logger, globalDirectorInterface)
	directorService := service.NewDirectorService(directorUsecase)
	httpServer := server.NewHTTPServer(confServer, directorService, logger)
	etcdRegistry := registry.NewEtcdRegistry(client, config)
	app := newApp(logger, httpServer, etcdRegistry, arg)
	return app, func() {
	}, nil
}
