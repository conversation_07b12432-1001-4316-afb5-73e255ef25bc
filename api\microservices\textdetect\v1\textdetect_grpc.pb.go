//
// @Description: ddotnet游戏服和kratos交互的GRPC消息定义在这里
//               注：每个service前后请加//ServiceStart和//ServiceEnd，否则不能生成dotnet的C#Service代码
// @Data: Thu Jan  4 14:27:54 CST 2024

//下面这行很重要，是表明你这个Service用于哪个URL的，见游戏服的GrpcAddressType
//GrpcAddressType:TextDetect
//GrpcServerType:all

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.4.0
// - protoc             v4.23.2
// source: v1/textdetect.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.62.0 or later.
const _ = grpc.SupportPackageIsVersion8

const (
	TextDetect_DoTextDetect_FullMethodName = "/Aurora.PlayerInfoServer.TextDetect/DoTextDetect"
)

// TextDetectClient is the client API for TextDetect service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ServiceStart
type TextDetectClient interface {
	DoTextDetect(ctx context.Context, in *DoTextDetectRequest, opts ...grpc.CallOption) (*DoTextDetectReply, error)
}

type textDetectClient struct {
	cc grpc.ClientConnInterface
}

func NewTextDetectClient(cc grpc.ClientConnInterface) TextDetectClient {
	return &textDetectClient{cc}
}

func (c *textDetectClient) DoTextDetect(ctx context.Context, in *DoTextDetectRequest, opts ...grpc.CallOption) (*DoTextDetectReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DoTextDetectReply)
	err := c.cc.Invoke(ctx, TextDetect_DoTextDetect_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TextDetectServer is the server API for TextDetect service.
// All implementations must embed UnimplementedTextDetectServer
// for forward compatibility
//
// ServiceStart
type TextDetectServer interface {
	DoTextDetect(context.Context, *DoTextDetectRequest) (*DoTextDetectReply, error)
	mustEmbedUnimplementedTextDetectServer()
}

// UnimplementedTextDetectServer must be embedded to have forward compatible implementations.
type UnimplementedTextDetectServer struct {
}

func (UnimplementedTextDetectServer) DoTextDetect(context.Context, *DoTextDetectRequest) (*DoTextDetectReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoTextDetect not implemented")
}
func (UnimplementedTextDetectServer) mustEmbedUnimplementedTextDetectServer() {}

// UnsafeTextDetectServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TextDetectServer will
// result in compilation errors.
type UnsafeTextDetectServer interface {
	mustEmbedUnimplementedTextDetectServer()
}

func RegisterTextDetectServer(s grpc.ServiceRegistrar, srv TextDetectServer) {
	s.RegisterService(&TextDetect_ServiceDesc, srv)
}

func _TextDetect_DoTextDetect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DoTextDetectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TextDetectServer).DoTextDetect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TextDetect_DoTextDetect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TextDetectServer).DoTextDetect(ctx, req.(*DoTextDetectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TextDetect_ServiceDesc is the grpc.ServiceDesc for TextDetect service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TextDetect_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Aurora.PlayerInfoServer.TextDetect",
	HandlerType: (*TextDetectServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DoTextDetect",
			Handler:    _TextDetect_DoTextDetect_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "v1/textdetect.proto",
}
