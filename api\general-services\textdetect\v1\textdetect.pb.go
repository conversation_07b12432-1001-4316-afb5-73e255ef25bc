//
// @Description: ddotnet游戏服和kratos交互的GRPC消息定义在这里
//               注：每个service前后请加//ServiceStart和//ServiceEnd，否则不能生成dotnet的C#Service代码
// @Data: Thu Jan  4 14:27:54 CST 2024

//下面这行很重要，是表明你这个Service用于哪个URL的，见游戏服的GrpcAddressType
//GrpcAddressType:TextDetect
//GrpcServerType:all

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v4.23.2
// source: general-services/textdetect/v1/textdetect.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Type:Http
type DetectType int32

const (
	// 正常
	DetectType_DetectType_Normal DetectType = 0
	// 禁言
	DetectType_DetectType_JinYan DetectType = 1
)

// Enum value maps for DetectType.
var (
	DetectType_name = map[int32]string{
		0: "DetectType_Normal",
		1: "DetectType_JinYan",
	}
	DetectType_value = map[string]int32{
		"DetectType_Normal": 0,
		"DetectType_JinYan": 1,
	}
)

func (x DetectType) Enum() *DetectType {
	p := new(DetectType)
	*p = x
	return p
}

func (x DetectType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DetectType) Descriptor() protoreflect.EnumDescriptor {
	return file_general_services_textdetect_v1_textdetect_proto_enumTypes[0].Descriptor()
}

func (DetectType) Type() protoreflect.EnumType {
	return &file_general_services_textdetect_v1_textdetect_proto_enumTypes[0]
}

func (x DetectType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DetectType.Descriptor instead.
func (DetectType) EnumDescriptor() ([]byte, []int) {
	return file_general_services_textdetect_v1_textdetect_proto_rawDescGZIP(), []int{0}
}

type TextDetectResultType int32

const (
	// 通过
	TextDetectResultType_TextDetectResultType_Pass TextDetectResultType = 0
	// 嫌疑
	TextDetectResultType_TextDetectResultType_XianYi TextDetectResultType = 1
	// 不通过
	TextDetectResultType_TextDetectResultType_NoPass TextDetectResultType = 2
	// 易盾错误
	TextDetectResultType_TextDetectResultType_YiDunErr TextDetectResultType = 3
)

// Enum value maps for TextDetectResultType.
var (
	TextDetectResultType_name = map[int32]string{
		0: "TextDetectResultType_Pass",
		1: "TextDetectResultType_XianYi",
		2: "TextDetectResultType_NoPass",
		3: "TextDetectResultType_YiDunErr",
	}
	TextDetectResultType_value = map[string]int32{
		"TextDetectResultType_Pass":     0,
		"TextDetectResultType_XianYi":   1,
		"TextDetectResultType_NoPass":   2,
		"TextDetectResultType_YiDunErr": 3,
	}
)

func (x TextDetectResultType) Enum() *TextDetectResultType {
	p := new(TextDetectResultType)
	*p = x
	return p
}

func (x TextDetectResultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TextDetectResultType) Descriptor() protoreflect.EnumDescriptor {
	return file_general_services_textdetect_v1_textdetect_proto_enumTypes[1].Descriptor()
}

func (TextDetectResultType) Type() protoreflect.EnumType {
	return &file_general_services_textdetect_v1_textdetect_proto_enumTypes[1]
}

func (x TextDetectResultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TextDetectResultType.Descriptor instead.
func (TextDetectResultType) EnumDescriptor() ([]byte, []int) {
	return file_general_services_textdetect_v1_textdetect_proto_rawDescGZIP(), []int{1}
}

type TextDetectEvilType int32

const (
	// 通过
	TextDetectEvilType_TextDetectEvilType_Pass TextDetectEvilType = 0
	// 色情
	TextDetectEvilType_TextDetectEvilType_Porn TextDetectEvilType = 100
	// 广告
	TextDetectEvilType_TextDetectEvilType_Advertisement TextDetectEvilType = 200
	// 涉恐
	TextDetectEvilType_TextDetectEvilType_Terror TextDetectEvilType = 300
	// 违禁
	TextDetectEvilType_TextDetectEvilType_Prohibit TextDetectEvilType = 400
	// 涉政
	TextDetectEvilType_TextDetectEvilType_Politics TextDetectEvilType = 500
	// 灌水
	TextDetectEvilType_TextDetectEvilType_Water TextDetectEvilType = 700
	// 其它
	TextDetectEvilType_TextDetectEvilType_Other TextDetectEvilType = 900
)

// Enum value maps for TextDetectEvilType.
var (
	TextDetectEvilType_name = map[int32]string{
		0:   "TextDetectEvilType_Pass",
		100: "TextDetectEvilType_Porn",
		200: "TextDetectEvilType_Advertisement",
		300: "TextDetectEvilType_Terror",
		400: "TextDetectEvilType_Prohibit",
		500: "TextDetectEvilType_Politics",
		700: "TextDetectEvilType_Water",
		900: "TextDetectEvilType_Other",
	}
	TextDetectEvilType_value = map[string]int32{
		"TextDetectEvilType_Pass":          0,
		"TextDetectEvilType_Porn":          100,
		"TextDetectEvilType_Advertisement": 200,
		"TextDetectEvilType_Terror":        300,
		"TextDetectEvilType_Prohibit":      400,
		"TextDetectEvilType_Politics":      500,
		"TextDetectEvilType_Water":         700,
		"TextDetectEvilType_Other":         900,
	}
)

func (x TextDetectEvilType) Enum() *TextDetectEvilType {
	p := new(TextDetectEvilType)
	*p = x
	return p
}

func (x TextDetectEvilType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TextDetectEvilType) Descriptor() protoreflect.EnumDescriptor {
	return file_general_services_textdetect_v1_textdetect_proto_enumTypes[2].Descriptor()
}

func (TextDetectEvilType) Type() protoreflect.EnumType {
	return &file_general_services_textdetect_v1_textdetect_proto_enumTypes[2]
}

func (x TextDetectEvilType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TextDetectEvilType.Descriptor instead.
func (TextDetectEvilType) EnumDescriptor() ([]byte, []int) {
	return file_general_services_textdetect_v1_textdetect_proto_rawDescGZIP(), []int{2}
}

type TextDetectItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index       uint64     `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	ZoneWorldId int32      `protobuf:"varint,2,opt,name=zoneWorldId,proto3" json:"zoneWorldId,omitempty"`
	Guid        uint64     `protobuf:"varint,3,opt,name=guid,proto3" json:"guid,omitempty"`
	DetectType  DetectType `protobuf:"varint,4,opt,name=detectType,proto3,enum=Aurora.PlayerInfoServer.DetectType" json:"detectType,omitempty"`
	Text        string     `protobuf:"bytes,5,opt,name=text,proto3" json:"text,omitempty"`
}

func (x *TextDetectItem) Reset() {
	*x = TextDetectItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextDetectItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextDetectItem) ProtoMessage() {}

func (x *TextDetectItem) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextDetectItem.ProtoReflect.Descriptor instead.
func (*TextDetectItem) Descriptor() ([]byte, []int) {
	return file_general_services_textdetect_v1_textdetect_proto_rawDescGZIP(), []int{0}
}

func (x *TextDetectItem) GetIndex() uint64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *TextDetectItem) GetZoneWorldId() int32 {
	if x != nil {
		return x.ZoneWorldId
	}
	return 0
}

func (x *TextDetectItem) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *TextDetectItem) GetDetectType() DetectType {
	if x != nil {
		return x.DetectType
	}
	return DetectType_DetectType_Normal
}

func (x *TextDetectItem) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type DoTextDetectRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TextDetectItems []*TextDetectItem `protobuf:"bytes,1,rep,name=textDetectItems,proto3" json:"textDetectItems,omitempty"`
}

func (x *DoTextDetectRequest) Reset() {
	*x = DoTextDetectRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoTextDetectRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoTextDetectRequest) ProtoMessage() {}

func (x *DoTextDetectRequest) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoTextDetectRequest.ProtoReflect.Descriptor instead.
func (*DoTextDetectRequest) Descriptor() ([]byte, []int) {
	return file_general_services_textdetect_v1_textdetect_proto_rawDescGZIP(), []int{1}
}

func (x *DoTextDetectRequest) GetTextDetectItems() []*TextDetectItem {
	if x != nil {
		return x.TextDetectItems
	}
	return nil
}

type HadesTextDetected struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   uint64 `protobuf:"varint,1,opt,name=Result,proto3" json:"Result,omitempty"`
	RetMsg   string `protobuf:"bytes,2,opt,name=RetMsg,proto3" json:"RetMsg,omitempty"`
	TextID   string `protobuf:"bytes,3,opt,name=TextID,proto3" json:"TextID,omitempty"`
	EvilType uint64 `protobuf:"varint,4,opt,name=EvilType,proto3" json:"EvilType,omitempty"`
}

func (x *HadesTextDetected) Reset() {
	*x = HadesTextDetected{}
	if protoimpl.UnsafeEnabled {
		mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HadesTextDetected) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HadesTextDetected) ProtoMessage() {}

func (x *HadesTextDetected) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HadesTextDetected.ProtoReflect.Descriptor instead.
func (*HadesTextDetected) Descriptor() ([]byte, []int) {
	return file_general_services_textdetect_v1_textdetect_proto_rawDescGZIP(), []int{2}
}

func (x *HadesTextDetected) GetResult() uint64 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *HadesTextDetected) GetRetMsg() string {
	if x != nil {
		return x.RetMsg
	}
	return ""
}

func (x *HadesTextDetected) GetTextID() string {
	if x != nil {
		return x.TextID
	}
	return ""
}

func (x *HadesTextDetected) GetEvilType() uint64 {
	if x != nil {
		return x.EvilType
	}
	return 0
}

type HadesResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool               `protobuf:"varint,1,opt,name=Status,proto3" json:"Status,omitempty"`
	Id     uint64             `protobuf:"varint,2,opt,name=Id,proto3" json:"Id,omitempty"`
	Json   *HadesTextDetected `protobuf:"bytes,3,opt,name=Json,proto3" json:"Json,omitempty"`
}

func (x *HadesResult) Reset() {
	*x = HadesResult{}
	if protoimpl.UnsafeEnabled {
		mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HadesResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HadesResult) ProtoMessage() {}

func (x *HadesResult) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HadesResult.ProtoReflect.Descriptor instead.
func (*HadesResult) Descriptor() ([]byte, []int) {
	return file_general_services_textdetect_v1_textdetect_proto_rawDescGZIP(), []int{3}
}

func (x *HadesResult) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *HadesResult) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *HadesResult) GetJson() *HadesTextDetected {
	if x != nil {
		return x.Json
	}
	return nil
}

type TextDetectedItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index       uint64       `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	HadesResult *HadesResult `protobuf:"bytes,2,opt,name=hadesResult,proto3" json:"hadesResult,omitempty"`
}

func (x *TextDetectedItem) Reset() {
	*x = TextDetectedItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TextDetectedItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TextDetectedItem) ProtoMessage() {}

func (x *TextDetectedItem) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TextDetectedItem.ProtoReflect.Descriptor instead.
func (*TextDetectedItem) Descriptor() ([]byte, []int) {
	return file_general_services_textdetect_v1_textdetect_proto_rawDescGZIP(), []int{4}
}

func (x *TextDetectedItem) GetIndex() uint64 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *TextDetectedItem) GetHadesResult() *HadesResult {
	if x != nil {
		return x.HadesResult
	}
	return nil
}

type DoTextDetectReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result            int32               `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	TextDetectedItems []*TextDetectedItem `protobuf:"bytes,2,rep,name=textDetectedItems,proto3" json:"textDetectedItems,omitempty"`
}

func (x *DoTextDetectReply) Reset() {
	*x = DoTextDetectReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DoTextDetectReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DoTextDetectReply) ProtoMessage() {}

func (x *DoTextDetectReply) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_textdetect_v1_textdetect_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DoTextDetectReply.ProtoReflect.Descriptor instead.
func (*DoTextDetectReply) Descriptor() ([]byte, []int) {
	return file_general_services_textdetect_v1_textdetect_proto_rawDescGZIP(), []int{5}
}

func (x *DoTextDetectReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *DoTextDetectReply) GetTextDetectedItems() []*TextDetectedItem {
	if x != nil {
		return x.TextDetectedItems
	}
	return nil
}

var File_general_services_textdetect_v1_textdetect_proto protoreflect.FileDescriptor

var file_general_services_textdetect_v1_textdetect_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x74, 0x65, 0x78, 0x74, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x17, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb5, 0x01, 0x0a, 0x0e, 0x54, 0x65, 0x78,
	0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14, 0x0a, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x20, 0x0a, 0x0b, 0x7a, 0x6f, 0x6e, 0x65, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x7a, 0x6f, 0x6e, 0x65, 0x57, 0x6f, 0x72, 0x6c,
	0x64, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x67, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x67, 0x75, 0x69, 0x64, 0x12, 0x43, 0x0a, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x41, 0x75,
	0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74,
	0x22, 0x68, 0x0a, 0x13, 0x44, 0x6f, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x51, 0x0a, 0x0f, 0x74, 0x65, 0x78, 0x74, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0f, 0x74, 0x65, 0x78, 0x74, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x77, 0x0a, 0x11, 0x48, 0x61,
	0x64, 0x65, 0x73, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12,
	0x16, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x74, 0x4d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x74, 0x4d, 0x73, 0x67, 0x12,
	0x16, 0x0a, 0x06, 0x54, 0x65, 0x78, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x54, 0x65, 0x78, 0x74, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x45, 0x76, 0x69, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x45, 0x76, 0x69, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x75, 0x0a, 0x0b, 0x48, 0x61, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x04, 0x4a, 0x73,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x48, 0x61, 0x64, 0x65, 0x73, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x52, 0x04, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x70, 0x0a, 0x10, 0x54, 0x65,
	0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x14,
	0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x05, 0x69,
	0x6e, 0x64, 0x65, 0x78, 0x12, 0x46, 0x0a, 0x0b, 0x68, 0x61, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x41, 0x75, 0x72, 0x6f,
	0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x48, 0x61, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52,
	0x0b, 0x68, 0x61, 0x64, 0x65, 0x73, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x84, 0x01, 0x0a,
	0x11, 0x44, 0x6f, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x57, 0x0a, 0x11, 0x74, 0x65,
	0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x11, 0x74, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x65, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x73, 0x2a, 0x3a, 0x0a, 0x0a, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4a, 0x69, 0x6e, 0x59, 0x61, 0x6e, 0x10, 0x01, 0x2a,
	0x9a, 0x01, 0x0a, 0x14, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x65, 0x78, 0x74,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x50, 0x61, 0x73, 0x73, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x65, 0x78, 0x74, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f,
	0x58, 0x69, 0x61, 0x6e, 0x59, 0x69, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x65, 0x78, 0x74,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x4e, 0x6f, 0x50, 0x61, 0x73, 0x73, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x54, 0x65, 0x78,
	0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x59, 0x69, 0x44, 0x75, 0x6e, 0x45, 0x72, 0x72, 0x10, 0x03, 0x2a, 0x97, 0x02, 0x0a,
	0x12, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x76, 0x69, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63,
	0x74, 0x45, 0x76, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x61, 0x73, 0x73, 0x10, 0x00,
	0x12, 0x1b, 0x0a, 0x17, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x76,
	0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x6f, 0x72, 0x6e, 0x10, 0x64, 0x12, 0x25, 0x0a,
	0x20, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x76, 0x69, 0x6c, 0x54,
	0x79, 0x70, 0x65, 0x5f, 0x41, 0x64, 0x76, 0x65, 0x72, 0x74, 0x69, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x10, 0xc8, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x45, 0x76, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x54, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x10, 0xac, 0x02, 0x12, 0x20, 0x0a, 0x1b, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65,
	0x63, 0x74, 0x45, 0x76, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x72, 0x6f, 0x68, 0x69,
	0x62, 0x69, 0x74, 0x10, 0x90, 0x03, 0x12, 0x20, 0x0a, 0x1b, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x45, 0x76, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x6f, 0x6c,
	0x69, 0x74, 0x69, 0x63, 0x73, 0x10, 0xf4, 0x03, 0x12, 0x1d, 0x0a, 0x18, 0x54, 0x65, 0x78, 0x74,
	0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x76, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x57,
	0x61, 0x74, 0x65, 0x72, 0x10, 0xbc, 0x05, 0x12, 0x1d, 0x0a, 0x18, 0x54, 0x65, 0x78, 0x74, 0x44,
	0x65, 0x74, 0x65, 0x63, 0x74, 0x45, 0x76, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4f, 0x74,
	0x68, 0x65, 0x72, 0x10, 0x84, 0x07, 0x32, 0x78, 0x0a, 0x0a, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x12, 0x6a, 0x0a, 0x0c, 0x44, 0x6f, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65,
	0x74, 0x65, 0x63, 0x74, 0x12, 0x2c, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44,
	0x6f, 0x54, 0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x6f, 0x54,
	0x65, 0x78, 0x74, 0x44, 0x65, 0x74, 0x65, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x42, 0x21, 0x5a, 0x1f, 0x74, 0x65, 0x78, 0x74, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x64, 0x65, 0x74, 0x65, 0x63, 0x74, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_general_services_textdetect_v1_textdetect_proto_rawDescOnce sync.Once
	file_general_services_textdetect_v1_textdetect_proto_rawDescData = file_general_services_textdetect_v1_textdetect_proto_rawDesc
)

func file_general_services_textdetect_v1_textdetect_proto_rawDescGZIP() []byte {
	file_general_services_textdetect_v1_textdetect_proto_rawDescOnce.Do(func() {
		file_general_services_textdetect_v1_textdetect_proto_rawDescData = protoimpl.X.CompressGZIP(file_general_services_textdetect_v1_textdetect_proto_rawDescData)
	})
	return file_general_services_textdetect_v1_textdetect_proto_rawDescData
}

var file_general_services_textdetect_v1_textdetect_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_general_services_textdetect_v1_textdetect_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_general_services_textdetect_v1_textdetect_proto_goTypes = []any{
	(DetectType)(0),             // 0: Aurora.PlayerInfoServer.DetectType
	(TextDetectResultType)(0),   // 1: Aurora.PlayerInfoServer.TextDetectResultType
	(TextDetectEvilType)(0),     // 2: Aurora.PlayerInfoServer.TextDetectEvilType
	(*TextDetectItem)(nil),      // 3: Aurora.PlayerInfoServer.TextDetectItem
	(*DoTextDetectRequest)(nil), // 4: Aurora.PlayerInfoServer.DoTextDetectRequest
	(*HadesTextDetected)(nil),   // 5: Aurora.PlayerInfoServer.HadesTextDetected
	(*HadesResult)(nil),         // 6: Aurora.PlayerInfoServer.HadesResult
	(*TextDetectedItem)(nil),    // 7: Aurora.PlayerInfoServer.TextDetectedItem
	(*DoTextDetectReply)(nil),   // 8: Aurora.PlayerInfoServer.DoTextDetectReply
}
var file_general_services_textdetect_v1_textdetect_proto_depIdxs = []int32{
	0, // 0: Aurora.PlayerInfoServer.TextDetectItem.detectType:type_name -> Aurora.PlayerInfoServer.DetectType
	3, // 1: Aurora.PlayerInfoServer.DoTextDetectRequest.textDetectItems:type_name -> Aurora.PlayerInfoServer.TextDetectItem
	5, // 2: Aurora.PlayerInfoServer.HadesResult.Json:type_name -> Aurora.PlayerInfoServer.HadesTextDetected
	6, // 3: Aurora.PlayerInfoServer.TextDetectedItem.hadesResult:type_name -> Aurora.PlayerInfoServer.HadesResult
	7, // 4: Aurora.PlayerInfoServer.DoTextDetectReply.textDetectedItems:type_name -> Aurora.PlayerInfoServer.TextDetectedItem
	4, // 5: Aurora.PlayerInfoServer.TextDetect.DoTextDetect:input_type -> Aurora.PlayerInfoServer.DoTextDetectRequest
	8, // 6: Aurora.PlayerInfoServer.TextDetect.DoTextDetect:output_type -> Aurora.PlayerInfoServer.DoTextDetectReply
	6, // [6:7] is the sub-list for method output_type
	5, // [5:6] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_general_services_textdetect_v1_textdetect_proto_init() }
func file_general_services_textdetect_v1_textdetect_proto_init() {
	if File_general_services_textdetect_v1_textdetect_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_general_services_textdetect_v1_textdetect_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*TextDetectItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_general_services_textdetect_v1_textdetect_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*DoTextDetectRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_general_services_textdetect_v1_textdetect_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*HadesTextDetected); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_general_services_textdetect_v1_textdetect_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*HadesResult); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_general_services_textdetect_v1_textdetect_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*TextDetectedItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_general_services_textdetect_v1_textdetect_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*DoTextDetectReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_general_services_textdetect_v1_textdetect_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_general_services_textdetect_v1_textdetect_proto_goTypes,
		DependencyIndexes: file_general_services_textdetect_v1_textdetect_proto_depIdxs,
		EnumInfos:         file_general_services_textdetect_v1_textdetect_proto_enumTypes,
		MessageInfos:      file_general_services_textdetect_v1_textdetect_proto_msgTypes,
	}.Build()
	File_general_services_textdetect_v1_textdetect_proto = out.File
	file_general_services_textdetect_v1_textdetect_proto_rawDesc = nil
	file_general_services_textdetect_v1_textdetect_proto_goTypes = nil
	file_general_services_textdetect_v1_textdetect_proto_depIdxs = nil
}
