//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"liteframe/internal/general-services/charlist/biz"
	"liteframe/internal/general-services/charlist/conf"
	"liteframe/internal/general-services/charlist/data"
	"liteframe/internal/general-services/charlist/registry"
	"liteframe/internal/general-services/charlist/server"
	"liteframe/internal/general-services/charlist/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init charlist application.
func wireApp(*conf.Server, *conf.Bootstrap, log.Logger) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, registry.ProviderSet, newApp))
}
