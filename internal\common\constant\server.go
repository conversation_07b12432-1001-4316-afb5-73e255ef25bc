package constant

import "path"

const (
	ServiceNameWebServer    = "webserver"
	ServiceNameGameServer   = "gameserver"
	ServiceNameBattleServer = "battleserver"
	ServiceNameMatchServer  = "matchserver"
	ServiceNamePayServer    = "payserver"
	ServiceNameTextDetect   = "textdetect"
	ServiceNameChat         = "chat"
	ServiceNameFriend       = "friend"
	ServiceNameRedeemCode   = "redeemcode"
	ServiceNamePlayerInfo   = "playerinfo"
	ServiceNameRank         = "rank"
	ServiceNameDirector     = "director"
	ServiceNameServerList   = "serverlist"
	ServiceNameAnnouncement = "announcement"
	ServiceNameCharList     = "charlist"
)

const (
	OnlineNum   = "online"
	RegisterNum = "register"
)

const (
	Service      = "server"
	Microservice = "microservices"
	Config       = "config"
	Register     = "register"
	Vms          = "vms"
	GMConfig     = "gmconfig"
	Version      = "version"
)

var (
	//ServerConfigPrefix   = path.Join(Service, Config)
	ServerRegisterPrefix = path.Join(Service, Register)
	MicroRegisterPrefix  = path.Join(Microservice, Register)
	MicroConfigPrefix    = path.Join(Microservice, Config)
)

var ( // etcd config
	ServerBlackRpc = path.Join(Service, "blackrpc")          // RPC黑名单
	LogLevel       = "loglevel"                              // 日志等级
	RpcHook        = path.Join(Service, GMConfig, "rpchook") // rpc热更
)

var (
	VmsVersion       = path.Join(Vms, "version")        // vms版本列表
	VmsVersionOnline = path.Join(Vms, "version_online") // vms上线版本
	VmsAnnounce      = path.Join(Vms, "announce")       // vms公告
	VmsConfig        = path.Join(Vms, "config")         // vms配置
)

const (
	WebsocketSecKey   = "Sec-Gateway-Key"
	WebSocketSecValue = "7dc44d7772da91e6df0e4b54b8c5ea25"
)
