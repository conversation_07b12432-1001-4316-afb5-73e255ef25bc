//GrpcAddressType:RedeemCode
//GrpcServerType:server
syntax = "proto3";

package Aurora.PlayerInfoServer;

import "google/api/annotations.proto";

option go_package = "gameserver/api/redeemcode/v1;v1";


//ServiceStart
service RedeemCode
{
  // 通过GMT平台添加一批新礼包码
  rpc AddBatchRedeemCode(RedeemCodeBatchInfo) returns (AddBatchRedeemCodeRes)
  {
    option (google.api.http) = {
      post : "/gmt/redeemcode/add",
      body : "*",
    };
  }
  // 根据批次ID查询礼包码Info
  rpc GetBatchRedeemCode(CodeBatchReq) returns (RedeemCodeBatchInfo) {
    option (google.api.http) = {
      get: "/gmt/redeemcode/get/{BatchId}"
    };
  }
  // 根据批次ID删除礼包码
  rpc DeleteBatchRedeemCode(CodeBatchReq) returns (DeleteCodeBatchRes) {
    option (google.api.http) = {
      delete: "/gmt/redeemcode/delete/{BatchId}"
    };
  }
  // 接收游戏服的请求 校验礼包码 返回道具map
  rpc GetRedeemCodeItems(GetRedeemCodeRewardReq) returns (GetRedeemCodeItemsRep)
  {
  }
  // 旧逻辑 校验礼包码 返回掉落包ID
rpc GetRedeemCodeReward(GetRedeemCodeRewardReq) returns (GetRedeemCodeRewardReply)
{
}
}
//ServiceEnd

message RedeemCodeBatchInfo {
  string BatchId = 1;               // 批次Id
  string CodeBatchName = 2;         // 批次名
  int32 CreateNum = 3;		          // 生成礼包码的个数
  int32 ExchangeMaxNum = 4;         // 每个礼包码最大可兑换次数
  int32 UserExchangeMaxNum = 5; 	  // 每个用户在此批次能用几个礼包码
  int32 RoleLevel = 6;		          // 玩家等级限制
  int64 ValidityStartTime = 7;      // 有效期开始时间
  int64 ExpirationTime = 8;	        // 兑换截止时间
  int64 CreateRoleStartTime = 9;    // 角色创建时间限制
  int64 CreateRoleEndTime = 10;
  map<int32, int32> Items = 11;   // 道具ID -> 数量
  repeated string RedeemCodes = 12; // 礼包码 可不传 不传则随机生成
  string CodeBatchNote = 13; 	      // 注释
}

message AddRedeemCodeResult {
  string RedeemCodeId = 1;  // 礼包码
  int32 Result = 2; // 添加礼包码结果
}

message AddBatchRedeemCodeRes {
  repeated string RedeemCodeId = 1;  // 礼包码数组
  int32 Result = 2; // 添加礼包码结果
}

message CodeBatchReq{
  string BatchId = 1;
}

message DeleteCodeBatchRes{
  string BatchId = 1;
  int32 Result = 2; //删除结果
}
message GetRedeemCodeRewardReq
{
  string RedeemCodeId = 1;	    // 礼包码Id
  uint64 PlayerGuid = 2;	      // 玩家Id
  int64 PlayerCreateTime = 3; 	// 玩家创建时间
  uint32 PlayerLevel = 4;		    // 玩家等级
  uint64 ServerId = 5;        	// 服务器Id
}

message GetRedeemCodeRewardReply
{
RedeemCodeResultType Result = 1;
int32 DropPackID = 2;
}

message GetRedeemCodeItemsRep {
  RedeemCodeResultType Result = 1;
  map<int32, int32> Items = 2; // 道具ID -> 数量
}

enum RedeemCodeType
{
ePersonOnlyCode = 0;
eGlobalOnlyCode = 1;
}

enum RedeemCodeResultType
{
  eCanReward = 0;
  eHaveReward = 1;          // 使用次数超过上限
  eRedeemCodeNotOpen = 2;   // 未到开放时间
  eRedeemCodeExpired = 3;   // 已过期
  eRedeemCodeRedisError = 4;// redis错误
  eRedeemCodeError = 5;     // 礼包码错误
  eUserExchangeToMax = 6;   // 玩家在此批次使用礼包码次数达到上限
  eLevelTooLow = 7;         // 玩家等级不足
  eRoleCreateTimeError = 8; // 玩家创建时间不在限定时间内
}
