package def

const (
	// MsgId_Actor_System_Begin ~ MsgId_Actor_System_End Actor 系統消息段
	MsgId_Actor_System_Begin = 1
	MsgId_Actor_System_End   = 100

	// MsgId_Game_Begin ~ MsgId_Game_End client 到 game 的逻辑消息段 // 定义在common/rpc_def.go
	MsgId_Game_Begin = 1000
	MsgId_Game_End   = 1999

	// MsgId_Actor_Begin ~ MsgId_Actor_End gameserver actor 消息段
	MsgId_Actor_Begin         = 10000
	MsgId_Actor_ServerTick    = 10000 // 服务 tick
	MsgId_Actor_Second        = 10001
	MsgId_Actor_FiveSecond    = 10002
	MsgId_Actor_TenSecond     = 10003
	MsgId_Actor_Monitor       = 10004 // 系统监控
	MsgId_Actor_InitPlayer    = 10099 // 初始化玩家数据
	MsgId_Actor_LoadSimple    = 11001 // 获取玩家基本数据
	MsgId_Actor_PlayerOffline = 11002 // 玩家下线通知
	MsgId_Actor_GM            = 11003
	MsgId_Actor_AsyncSaveUser = 11004 // 玩家数据保存
	MsgId_Actor_PlayerOnline  = 11005 // 登录成功
	MsgId_Actor_PlayerExit    = 11006 // 删除玩家同步保存

	MsgId_Actor_SyncConfig = 11007 // 同步 Actor  更新配置

	MsgId_Actor_GMT        = 11008 // GMT
	MsgId_Actor_CrossDay   = 11009 // 跨天通知
	MsgId_Actor_CrossWeek  = 11010 // 跨周通知
	MsgId_Actor_CrossMonth = 11011 // 跨月通知

	MsgId_Actor_PlayerSystem_Exit_Status = 11012 // 获取 player system 退出状态
	MsgId_Actor_FindDuplicateNames       = 11013 // 玩家数据保存
	MsgId_Actor_ChangeNames              = 11014 // 玩家数据保存

	MsgId_Actor_Player2GuildSystem         = 11015 // 玩家消息到公会系统
	MsgId_Actor_GuildSystem2Player         = 11016 // 公会系统消息到玩家
	MsgId_Actor_GuildSystem2PlayerSystem   = 11017 // 公会系统消息到玩家系统
	MsgId_Actor_PlayerSystem2GuildSystem   = 11018 // 玩家系统消息到公会系统
	MsgId_Actor_PlayerSystem2Player        = 11019 // 玩家系统消息到玩家
	MsgId_Actor_PaymentSystem2PlayerSystem = 11020 // 支付系统消息到玩家系统
	MsgId_Actor_PlayerSystem2PaymentSystem = 11021 // 玩家系统消息到支付系统
	MsgId_Actor_PaymentSystem2Player       = 11022 // 支付系统消息到玩家
	MsgId_Actor_Player2PaymentSystem       = 11023 // 玩家消息到支付系统
	MsgId_Actor_Pay_Callback               = 11024 // 支付通知回调

	MsgId_Actor_GlobalMail2PlayerSystem = 11025 // 全局邮件系统消息到玩家系统
	MsgId_Actor_PlayerSystem2GlobalMail = 11026 // 玩家系统消息到全局邮件系统
	MsgId_Actor_Player2GlobalMail       = 11027 // 玩家消息到全局邮件系统
	MsgId_Actor_GlobalMail2Player       = 11028 // 全局邮件系统消息到玩家
	MsgId_Actor_Survey_Callback         = 11029 // 问卷

	// Arena系统消息ID
	MsgId_Actor_Player2ArenaSystem = 11030 // 玩家消息到竞技场系统
	MsgId_Actor_ArenaSystem2Player = 11031 // 竞技场系统消息到玩家

	// 举报系统消息ID
	MsgId_Actor_Player2TipOffSystem = 11032 // 玩家消息到举报系统
	MsgId_Actor_TipOffSystem2Player = 11033 // 举报系统消息到玩家

	// 赛季Buff系统消息ID
	MsgId_Actor_Player2SeasonBuffSystem       = 11034 // 玩家消息到赛季buff系统
	MsgId_Actor_SeasonBuffSystem2Player       = 11035 // 赛季buff系统消息到玩家
	MsgId_Actor_SeasonBuffSystem2PlayerSystem = 11036 // 赛季buff系统消息到玩家系统

	// 赛季系统消息ID
	MsgId_Actor_SeasonSystem2PlayerSystem = 11037 // 赛季系统消息到玩家系统
	MsgId_Actor_PlayerSystem2Season       = 11038 // 玩家系统消息到赛季系统

	MsgId_Actor_Player_MatchResult         = 12000 // 匹配结果
	MsgId_Actor_Player_BattleStart         = 12001 // 战斗开始
	MsgId_Actor_Player_RoundStart          = 12002 // 回合开始
	MsgId_Actor_Player_RoundEnd            = 12003 // 回合结束
	MsgId_Actor_Player_BattleEnd           = 12004 // 战斗结束
	MsgId_Actor_Player_RoomDestroy_Notify  = 12005 // 房间销毁
	MsgId_Actor_Player_BattleResult        = 12006 //战斗结果
	MsgId_Actor_Player_BattleReady_Timeout = 12007 // 准备超时

	// add actor id before this line!!!
	MsgId_Actor_StopGame = 10999 // 停服
	MsgId_Actor_End      = 20000

	// MsgId_Mongo_Begin ~ MsgId_Mongo_End gameserver mongo 消息段
	MsgId_Mongo_Begin                 = 90000
	MsgId_Mongo_LoadOrCreateUser      = 90001
	MsgId_Mongo_SaveUser              = 90002
	MsgId_Mongo_LoadUser              = 90003
	MsgId_Mongo_LoadData              = 90004
	MsgId_Mongo_SaveData              = 90005
	MsgId_Mongo_DeleteData            = 90006
	MsgId_Mongo_LoadOrCreateUid       = 90007 //
	MsgId_Mongo_LoadUidByAccount      = 90008 // 用账号获取 uid
	MsgId_Mongo_LoadGuildData         = 90009
	MsgId_Mongo_SaveGuildData         = 90010
	MsgId_Mongo_DeleteGuildData       = 90011
	MsgId_Mongo_LoadGlobalMail        = 90012 // 加载全局邮件
	MsgId_Mongo_SaveGlobalMail        = 90013 // 存储全局邮件
	MsgId_Mongo_LoadPaymentData       = 90014
	MsgId_Mongo_SavePaymentData       = 90015
	MsgId_Mongo_LoadUserSnapDataBatch = 90016
	MsgId_Mongo_LoadAllAccountMap     = 90017 // 加载所有账号和uid映射
	MsgId_Mongo_LoadArenaSystem       = 90018 // 加载竞技场系统
	MsgId_Mongo_SaveArenaSystem       = 90019 // 存储竞技场系统

	MsgId_Actor_Update_User_Snap_Data = 90101 // 更新玩家快照数据
	MsgId_Actor_Get_User_Snap_Data    = 90102 // 获取玩家快照数据
	MsgId_Actor_Set_Name_If_Absent    = 90103 // 尝试给玩家改新名

	MsgId_Mongo_LoadTipOffData = 90104 // 加载举报数据
	MsgId_Mongo_SaveTipOffData = 90105 // 存储举报数据

	MsgId_Mongo_LoadSeasonBuffData = 90106 // 加载赛季buff数据
	MsgId_Mongo_SaveSeasonBuffData = 90107 // 存储赛季buff数据

	MsgId_Mongo_Ping = 99990 // mongo ping
	MsgId_Mongo_End  = 99999
)
