using LiteFrame.Framework;
using Newtonsoft.Json;
using System.Collections.Generic;

public class PlayMode
{
    public int ID;
    public int Type;
    public int Scene;
    public int PreDuration;
    public int BattleDuration;
    public int[] BuffDuration;
    public int SpeedUpTime;
    public int[] BuffRound;
    public int[] BuffList;
    public int[][] PlayerHP;
    public int[][] AddHero;
    public int CheckTimes;
}


public class PlayModeManager
{
    private Dictionary<int, PlayMode> dict = new Dictionary<int, PlayMode>();


    public void Init()
    {
        var configFilePath = $"../configs/table/PlayMode.json";

        // 检查文件是否存在
        if (!File.Exists(configFilePath))
        {
            Log.Error($"配置文件不存在: {configFilePath}");
            return;
        }

        // 读取JSON文件内容
        string jsonContent = File.ReadAllText(configFilePath);
        var data = JsonConvert.DeserializeObject<List<PlayMode>>(jsonContent);

        foreach (PlayMode config in data)
        {
            this.dict.Add(config.ID, config);
        }
    }

    public PlayMode Get(int id)
    {
        if (this.dict.TryGetValue(id, out PlayMode item))
            return item;
        Log.Error($"配置找不到，配置表名: {nameof(PlayMode)}，配置id: {id}");
        return null;
    }

    public bool TryGetValue(int id, out PlayMode item)
    {
        return this.dict.TryGetValue(id, out item);
    }

    public bool Contain(int id)
    {
        return this.dict.ContainsKey(id);
    }

    public Dictionary<int, PlayMode> GetAll()
    {
        return this.dict;
    }

    public PlayMode GetOne()
    {
        if (this.dict == null || this.dict.Count <= 0)
        {
            return null;
        }
        return this.dict.Values.GetEnumerator().Current;
    }
}