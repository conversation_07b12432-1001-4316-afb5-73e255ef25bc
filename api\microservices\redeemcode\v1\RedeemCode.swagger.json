{"swagger": "2.0", "info": {"title": "microservices/redeemcode/v1/RedeemCode.proto", "version": "version not set"}, "tags": [{"name": "RedeemCode"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/gmt/redeemcode/add": {"post": {"summary": "通过GMT平台添加一批新礼包码", "operationId": "RedeemCode_AddBatchRedeemCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerAddBatchRedeemCodeRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerRedeemCodeBatchInfo"}}], "tags": ["RedeemCode"]}}, "/gmt/redeemcode/delete/{BatchId}": {"delete": {"summary": "根据批次ID删除礼包码", "operationId": "RedeemCode_DeleteBatchRedeemCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerDeleteCodeBatchRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "BatchId", "in": "path", "required": true, "type": "string"}], "tags": ["RedeemCode"]}}, "/gmt/redeemcode/get/{BatchId}": {"get": {"summary": "根据批次ID查询礼包码Info", "operationId": "RedeemCode_GetBatchRedeemCode", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerRedeemCodeBatchInfo"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "BatchId", "in": "path", "required": true, "type": "string"}], "tags": ["RedeemCode"]}}}, "definitions": {"PlayerInfoServerAddBatchRedeemCodeRes": {"type": "object", "properties": {"RedeemCodeId": {"type": "array", "items": {"type": "string"}, "title": "礼包码数组"}, "Result": {"type": "integer", "format": "int32", "title": "添加礼包码结果"}}}, "PlayerInfoServerDeleteCodeBatchRes": {"type": "object", "properties": {"BatchId": {"type": "string"}, "Result": {"type": "integer", "format": "int32", "title": "删除结果"}}}, "PlayerInfoServerGetRedeemCodeItemsRep": {"type": "object", "properties": {"Result": {"$ref": "#/definitions/PlayerInfoServerRedeemCodeResultType"}, "Items": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "title": "道具ID -> 数量"}}}, "PlayerInfoServerGetRedeemCodeRewardReply": {"type": "object", "properties": {"Result": {"$ref": "#/definitions/PlayerInfoServerRedeemCodeResultType"}, "DropPackID": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerRedeemCodeBatchInfo": {"type": "object", "properties": {"BatchId": {"type": "string", "title": "批次Id"}, "CodeBatchName": {"type": "string", "title": "批次名"}, "CreateNum": {"type": "integer", "format": "int32", "title": "生成礼包码的个数"}, "ExchangeMaxNum": {"type": "integer", "format": "int32", "title": "每个礼包码最大可兑换次数"}, "UserExchangeMaxNum": {"type": "integer", "format": "int32", "title": "每个用户在此批次能用几个礼包码"}, "RoleLevel": {"type": "integer", "format": "int32", "title": "玩家等级限制"}, "ValidityStartTime": {"type": "string", "format": "int64", "title": "有效期开始时间"}, "ExpirationTime": {"type": "string", "format": "int64", "title": "兑换截止时间"}, "CreateRoleStartTime": {"type": "string", "format": "int64", "title": "角色创建时间限制"}, "CreateRoleEndTime": {"type": "string", "format": "int64"}, "Items": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "title": "道具ID -> 数量"}, "RedeemCodes": {"type": "array", "items": {"type": "string"}, "title": "礼包码 可不传 不传则随机生成"}, "CodeBatchNote": {"type": "string", "title": "注释"}}}, "PlayerInfoServerRedeemCodeResultType": {"type": "string", "enum": ["eCanReward", "eHaveReward", "eRedeemCodeNotOpen", "eRedeemCodeExpired", "eRedeemCodeRedisError", "eRedeemCodeError", "eUserExchangeToMax", "eLevelTooLow", "eRoleCreateTimeError"], "default": "eCanReward", "title": "- eHaveReward: 使用次数超过上限\n - eRedeemCodeNotOpen: 未到开放时间\n - eRedeemCodeExpired: 已过期\n - eRedeemCodeRedisError: redis错误\n - eRedeemCodeError: 礼包码错误\n - eUserExchangeToMax: 玩家在此批次使用礼包码次数达到上限\n - eLevelTooLow: 玩家等级不足\n - eRoleCreateTimeError: 玩家创建时间不在限定时间内"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}