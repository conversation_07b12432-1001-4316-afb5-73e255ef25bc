//下面这行很重要，是表明你这个Service用于哪个URL的，见游戏服的GrpcAddressType
//GrpcAddressType:ChatLocalServer
//GrpcAddressType:ChatZoneServer
//GrpcAddressType:ChatGlobalServer
//GrpcServerType:all

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.19.1
// source: microservices/chat/v1/chat.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	v1 "liteframe/api/microservices/playerinfo/v1"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Type:ClientWebSocket
type WebSocketMessageIDWebSc int32

const (
	WebSocketMessageIDWebSc_ChatMessage_ID           WebSocketMessageIDWebSc = 0
	WebSocketMessageIDWebSc_RequestOfflineMessage_ID WebSocketMessageIDWebSc = 1
	WebSocketMessageIDWebSc_OperNoticeProxy          WebSocketMessageIDWebSc = 2
)

// Enum value maps for WebSocketMessageIDWebSc.
var (
	WebSocketMessageIDWebSc_name = map[int32]string{
		0: "ChatMessage_ID",
		1: "RequestOfflineMessage_ID",
		2: "OperNoticeProxy",
	}
	WebSocketMessageIDWebSc_value = map[string]int32{
		"ChatMessage_ID":           0,
		"RequestOfflineMessage_ID": 1,
		"OperNoticeProxy":          2,
	}
)

func (x WebSocketMessageIDWebSc) Enum() *WebSocketMessageIDWebSc {
	p := new(WebSocketMessageIDWebSc)
	*p = x
	return p
}

func (x WebSocketMessageIDWebSc) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WebSocketMessageIDWebSc) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_chat_v1_chat_proto_enumTypes[0].Descriptor()
}

func (WebSocketMessageIDWebSc) Type() protoreflect.EnumType {
	return &file_microservices_chat_v1_chat_proto_enumTypes[0]
}

func (x WebSocketMessageIDWebSc) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WebSocketMessageIDWebSc.Descriptor instead.
func (WebSocketMessageIDWebSc) EnumDescriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{0}
}

// Type:ClientWebSocket
type DispatchOperNoticeInfoEnumType int32

const (
	DispatchOperNoticeInfoEnumType_none             DispatchOperNoticeInfoEnumType = 0
	DispatchOperNoticeInfoEnumType_tips             DispatchOperNoticeInfoEnumType = 1 // 提示 参数传字典
	DispatchOperNoticeInfoEnumType_tips_Param       DispatchOperNoticeInfoEnumType = 2 // 提示 参数传字典   123|param1|param2
	DispatchOperNoticeInfoEnumType_tips_guidGetName DispatchOperNoticeInfoEnumType = 3 // 提示 参数传字典  123|guid
	// friend  1001 --- 2000
	DispatchOperNoticeInfoEnumType_Friend_start                   DispatchOperNoticeInfoEnumType = 1000
	DispatchOperNoticeInfoEnumType_Friend_AddFriendPlayer         DispatchOperNoticeInfoEnumType = 1001
	DispatchOperNoticeInfoEnumType_Friend_DelFriendPlayer         DispatchOperNoticeInfoEnumType = 1002
	DispatchOperNoticeInfoEnumType_Friend_GroupBaseInfo           DispatchOperNoticeInfoEnumType = 1003
	DispatchOperNoticeInfoEnumType_Friend_InviteJoinGroupList     DispatchOperNoticeInfoEnumType = 1004
	DispatchOperNoticeInfoEnumType_Friend_UpdateSelfGroupListInfo DispatchOperNoticeInfoEnumType = 1005
	DispatchOperNoticeInfoEnumType_Friend_ApplyFriend             DispatchOperNoticeInfoEnumType = 1006 //好友申请
	DispatchOperNoticeInfoEnumType_Friend_ApproveFriendSingle     DispatchOperNoticeInfoEnumType = 1007 //同意一个好友
	DispatchOperNoticeInfoEnumType_Friend_ApproveFriendAll        DispatchOperNoticeInfoEnumType = 1008 //同意所有好友
	DispatchOperNoticeInfoEnumType_Friend_RemoveFriendSingle      DispatchOperNoticeInfoEnumType = 1009 //拒绝单个好友
	DispatchOperNoticeInfoEnumType_Friend_RemoveFriendAll         DispatchOperNoticeInfoEnumType = 1010 //拒绝所有好友
	DispatchOperNoticeInfoEnumType_Friend_DeleteFriend            DispatchOperNoticeInfoEnumType = 1011 //删除好友
	DispatchOperNoticeInfoEnumType_Friend_end                     DispatchOperNoticeInfoEnumType = 2000
	// 调查问卷  2001
	DispatchOperNoticeInfoEnumType_Questionnaire_AskList DispatchOperNoticeInfoEnumType = 2001
)

// Enum value maps for DispatchOperNoticeInfoEnumType.
var (
	DispatchOperNoticeInfoEnumType_name = map[int32]string{
		0:    "none",
		1:    "tips",
		2:    "tips_Param",
		3:    "tips_guidGetName",
		1000: "Friend_start",
		1001: "Friend_AddFriendPlayer",
		1002: "Friend_DelFriendPlayer",
		1003: "Friend_GroupBaseInfo",
		1004: "Friend_InviteJoinGroupList",
		1005: "Friend_UpdateSelfGroupListInfo",
		1006: "Friend_ApplyFriend",
		1007: "Friend_ApproveFriendSingle",
		1008: "Friend_ApproveFriendAll",
		1009: "Friend_RemoveFriendSingle",
		1010: "Friend_RemoveFriendAll",
		1011: "Friend_DeleteFriend",
		2000: "Friend_end",
		2001: "Questionnaire_AskList",
	}
	DispatchOperNoticeInfoEnumType_value = map[string]int32{
		"none":                           0,
		"tips":                           1,
		"tips_Param":                     2,
		"tips_guidGetName":               3,
		"Friend_start":                   1000,
		"Friend_AddFriendPlayer":         1001,
		"Friend_DelFriendPlayer":         1002,
		"Friend_GroupBaseInfo":           1003,
		"Friend_InviteJoinGroupList":     1004,
		"Friend_UpdateSelfGroupListInfo": 1005,
		"Friend_ApplyFriend":             1006,
		"Friend_ApproveFriendSingle":     1007,
		"Friend_ApproveFriendAll":        1008,
		"Friend_RemoveFriendSingle":      1009,
		"Friend_RemoveFriendAll":         1010,
		"Friend_DeleteFriend":            1011,
		"Friend_end":                     2000,
		"Questionnaire_AskList":          2001,
	}
)

func (x DispatchOperNoticeInfoEnumType) Enum() *DispatchOperNoticeInfoEnumType {
	p := new(DispatchOperNoticeInfoEnumType)
	*p = x
	return p
}

func (x DispatchOperNoticeInfoEnumType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DispatchOperNoticeInfoEnumType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_chat_v1_chat_proto_enumTypes[1].Descriptor()
}

func (DispatchOperNoticeInfoEnumType) Type() protoreflect.EnumType {
	return &file_microservices_chat_v1_chat_proto_enumTypes[1]
}

func (x DispatchOperNoticeInfoEnumType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DispatchOperNoticeInfoEnumType.Descriptor instead.
func (DispatchOperNoticeInfoEnumType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{1}
}

// Type:ClientWebSocket
// Type:ServerWebSocket
type MessageChannelType int32

const (
	MessageChannelType_MessageType_Invalid     MessageChannelType = 0
	MessageChannelType_MessageType_World       MessageChannelType = 1  //LocalServer,ZoneServer
	MessageChannelType_MessageType_Team        MessageChannelType = 2  //GlobalServer
	MessageChannelType_MessageType_Raid        MessageChannelType = 3  //GlobalServer
	MessageChannelType_MessageType_Guild       MessageChannelType = 4  //LocalServer
	MessageChannelType_MessageType_Near        MessageChannelType = 5  //GlobalServer
	MessageChannelType_MessageType_Speaker     MessageChannelType = 6  //LocalServer,ZoneServer
	MessageChannelType_MessageType_PrivateChat MessageChannelType = 7  //GlobalServer
	MessageChannelType_MessageType_GroupChat   MessageChannelType = 8  //GlobalServer
	MessageChannelType_MessageType_Recruit     MessageChannelType = 9  //LocalServer,ZoneServer
	MessageChannelType_MessageType_System      MessageChannelType = 10 //LocalServer,ZoneServer,GlobalServer
	MessageChannelType_MessageType_Scene       MessageChannelType = 11 //GlobalServer
	MessageChannelType_MessageType_GongGao     MessageChannelType = 12 //LocalServer,ZoneServer,GlobalServer
)

// Enum value maps for MessageChannelType.
var (
	MessageChannelType_name = map[int32]string{
		0:  "MessageType_Invalid",
		1:  "MessageType_World",
		2:  "MessageType_Team",
		3:  "MessageType_Raid",
		4:  "MessageType_Guild",
		5:  "MessageType_Near",
		6:  "MessageType_Speaker",
		7:  "MessageType_PrivateChat",
		8:  "MessageType_GroupChat",
		9:  "MessageType_Recruit",
		10: "MessageType_System",
		11: "MessageType_Scene",
		12: "MessageType_GongGao",
	}
	MessageChannelType_value = map[string]int32{
		"MessageType_Invalid":     0,
		"MessageType_World":       1,
		"MessageType_Team":        2,
		"MessageType_Raid":        3,
		"MessageType_Guild":       4,
		"MessageType_Near":        5,
		"MessageType_Speaker":     6,
		"MessageType_PrivateChat": 7,
		"MessageType_GroupChat":   8,
		"MessageType_Recruit":     9,
		"MessageType_System":      10,
		"MessageType_Scene":       11,
		"MessageType_GongGao":     12,
	}
)

func (x MessageChannelType) Enum() *MessageChannelType {
	p := new(MessageChannelType)
	*p = x
	return p
}

func (x MessageChannelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageChannelType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_chat_v1_chat_proto_enumTypes[2].Descriptor()
}

func (MessageChannelType) Type() protoreflect.EnumType {
	return &file_microservices_chat_v1_chat_proto_enumTypes[2]
}

func (x MessageChannelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageChannelType.Descriptor instead.
func (MessageChannelType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{2}
}

// Type:ClientWebSocket
// Type:ServerWebSocket
type MessageSubType int32

const (
	MessageSubType_MessageSubType_Invalid        MessageSubType = 0
	MessageSubType_MessageSubType_Broadcast      MessageSubType = 1
	MessageSubType_MessageSubType_Private        MessageSubType = 2
	MessageSubType_MessageSubType_Team           MessageSubType = 3
	MessageSubType_MessageSubType_Guild          MessageSubType = 4
	MessageSubType_MessageSubType_Near           MessageSubType = 5
	MessageSubType_MessageSubType_Scene          MessageSubType = 6
	MessageSubType_MessageSubType_Broadcast_Save MessageSubType = 7
)

// Enum value maps for MessageSubType.
var (
	MessageSubType_name = map[int32]string{
		0: "MessageSubType_Invalid",
		1: "MessageSubType_Broadcast",
		2: "MessageSubType_Private",
		3: "MessageSubType_Team",
		4: "MessageSubType_Guild",
		5: "MessageSubType_Near",
		6: "MessageSubType_Scene",
		7: "MessageSubType_Broadcast_Save",
	}
	MessageSubType_value = map[string]int32{
		"MessageSubType_Invalid":        0,
		"MessageSubType_Broadcast":      1,
		"MessageSubType_Private":        2,
		"MessageSubType_Team":           3,
		"MessageSubType_Guild":          4,
		"MessageSubType_Near":           5,
		"MessageSubType_Scene":          6,
		"MessageSubType_Broadcast_Save": 7,
	}
)

func (x MessageSubType) Enum() *MessageSubType {
	p := new(MessageSubType)
	*p = x
	return p
}

func (x MessageSubType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MessageSubType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_chat_v1_chat_proto_enumTypes[3].Descriptor()
}

func (MessageSubType) Type() protoreflect.EnumType {
	return &file_microservices_chat_v1_chat_proto_enumTypes[3]
}

func (x MessageSubType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MessageSubType.Descriptor instead.
func (MessageSubType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{3}
}

// Type:ClientWebSocket
// Type:ServerWebSocket
type TargetChatServer int32

const (
	TargetChatServer_TargetServer_Invalid TargetChatServer = 0
	// 本服
	TargetChatServer_TargetServer_LocalServer TargetChatServer = 1
	// 区服
	TargetChatServer_TargerServer_ZoneServer TargetChatServer = 2
	// 跨服
	TargetChatServer_TargetServer_GlobalServer TargetChatServer = 3
	TargetChatServer_TargetServer_Proxy        TargetChatServer = 10
)

// Enum value maps for TargetChatServer.
var (
	TargetChatServer_name = map[int32]string{
		0:  "TargetServer_Invalid",
		1:  "TargetServer_LocalServer",
		2:  "TargerServer_ZoneServer",
		3:  "TargetServer_GlobalServer",
		10: "TargetServer_Proxy",
	}
	TargetChatServer_value = map[string]int32{
		"TargetServer_Invalid":      0,
		"TargetServer_LocalServer":  1,
		"TargerServer_ZoneServer":   2,
		"TargetServer_GlobalServer": 3,
		"TargetServer_Proxy":        10,
	}
)

func (x TargetChatServer) Enum() *TargetChatServer {
	p := new(TargetChatServer)
	*p = x
	return p
}

func (x TargetChatServer) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetChatServer) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_chat_v1_chat_proto_enumTypes[4].Descriptor()
}

func (TargetChatServer) Type() protoreflect.EnumType {
	return &file_microservices_chat_v1_chat_proto_enumTypes[4]
}

func (x TargetChatServer) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetChatServer.Descriptor instead.
func (TargetChatServer) EnumDescriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{4}
}

// Type:ClientWebSocket
// Type:ServerWebSocket
type ChatChannelType int32

const (
	ChatChannelType_ChatType_Invalid     ChatChannelType = 0
	ChatChannelType_ChatType_Message     ChatChannelType = 1
	ChatChannelType_ChatType_BigEmoji    ChatChannelType = 2
	ChatChannelType_ChatType_RedEnvelope ChatChannelType = 3
	ChatChannelType_ChatType_Voice       ChatChannelType = 4
)

// Enum value maps for ChatChannelType.
var (
	ChatChannelType_name = map[int32]string{
		0: "ChatType_Invalid",
		1: "ChatType_Message",
		2: "ChatType_BigEmoji",
		3: "ChatType_RedEnvelope",
		4: "ChatType_Voice",
	}
	ChatChannelType_value = map[string]int32{
		"ChatType_Invalid":     0,
		"ChatType_Message":     1,
		"ChatType_BigEmoji":    2,
		"ChatType_RedEnvelope": 3,
		"ChatType_Voice":       4,
	}
)

func (x ChatChannelType) Enum() *ChatChannelType {
	p := new(ChatChannelType)
	*p = x
	return p
}

func (x ChatChannelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatChannelType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_chat_v1_chat_proto_enumTypes[5].Descriptor()
}

func (ChatChannelType) Type() protoreflect.EnumType {
	return &file_microservices_chat_v1_chat_proto_enumTypes[5]
}

func (x ChatChannelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatChannelType.Descriptor instead.
func (ChatChannelType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{5}
}

// Type:ClientWebSocket
// Type:ServerWebSocket
type ChatShareItemType int32

const (
	ChatShareItemType_ShareType_Item            ChatShareItemType = 0
	ChatShareItemType_ShareType_Pet             ChatShareItemType = 1
	ChatShareItemType_ShareType_Mount           ChatShareItemType = 2
	ChatShareItemType_ShareType_TagFriend       ChatShareItemType = 3
	ChatShareItemType_ShareType_Coordinate      ChatShareItemType = 4
	ChatShareItemType_ShareType_FormatClientStr ChatShareItemType = 5
	ChatShareItemType_ShareType_InviteGuild     ChatShareItemType = 6
	ChatShareItemType_ShareType_TeamUp          ChatShareItemType = 7
)

// Enum value maps for ChatShareItemType.
var (
	ChatShareItemType_name = map[int32]string{
		0: "ShareType_Item",
		1: "ShareType_Pet",
		2: "ShareType_Mount",
		3: "ShareType_TagFriend",
		4: "ShareType_Coordinate",
		5: "ShareType_FormatClientStr",
		6: "ShareType_InviteGuild",
		7: "ShareType_TeamUp",
	}
	ChatShareItemType_value = map[string]int32{
		"ShareType_Item":            0,
		"ShareType_Pet":             1,
		"ShareType_Mount":           2,
		"ShareType_TagFriend":       3,
		"ShareType_Coordinate":      4,
		"ShareType_FormatClientStr": 5,
		"ShareType_InviteGuild":     6,
		"ShareType_TeamUp":          7,
	}
)

func (x ChatShareItemType) Enum() *ChatShareItemType {
	p := new(ChatShareItemType)
	*p = x
	return p
}

func (x ChatShareItemType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatShareItemType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_chat_v1_chat_proto_enumTypes[6].Descriptor()
}

func (ChatShareItemType) Type() protoreflect.EnumType {
	return &file_microservices_chat_v1_chat_proto_enumTypes[6]
}

func (x ChatShareItemType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatShareItemType.Descriptor instead.
func (ChatShareItemType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{6}
}

// Type:ClientWebSocket
// Type:ServerWebSocket
type ChatPlayerInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Level         int32                  `protobuf:"varint,2,opt,name=level,proto3" json:"level,omitempty"`
	IconID        int32                  `protobuf:"varint,3,opt,name=iconID,proto3" json:"iconID,omitempty"`
	IconUrl       string                 `protobuf:"bytes,4,opt,name=iconUrl,proto3" json:"iconUrl,omitempty"`
	IconFrameID   int32                  `protobuf:"varint,5,opt,name=iconFrameID,proto3" json:"iconFrameID,omitempty"`
	PetID         int32                  `protobuf:"varint,6,opt,name=petID,proto3" json:"petID,omitempty"`
	GuildName     string                 `protobuf:"bytes,7,opt,name=guildName,proto3" json:"guildName,omitempty"`
	ServerID      int32                  `protobuf:"varint,8,opt,name=serverID,proto3" json:"serverID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatPlayerInfo) Reset() {
	*x = ChatPlayerInfo{}
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatPlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatPlayerInfo) ProtoMessage() {}

func (x *ChatPlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatPlayerInfo.ProtoReflect.Descriptor instead.
func (*ChatPlayerInfo) Descriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{0}
}

func (x *ChatPlayerInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ChatPlayerInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *ChatPlayerInfo) GetIconID() int32 {
	if x != nil {
		return x.IconID
	}
	return 0
}

func (x *ChatPlayerInfo) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *ChatPlayerInfo) GetIconFrameID() int32 {
	if x != nil {
		return x.IconFrameID
	}
	return 0
}

func (x *ChatPlayerInfo) GetPetID() int32 {
	if x != nil {
		return x.PetID
	}
	return 0
}

func (x *ChatPlayerInfo) GetGuildName() string {
	if x != nil {
		return x.GuildName
	}
	return ""
}

func (x *ChatPlayerInfo) GetServerID() int32 {
	if x != nil {
		return x.ServerID
	}
	return 0
}

// Type:ClientWebSocket
// Type:ServerWebSocket
type ChatShareItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ShareType     ChatShareItemType      `protobuf:"varint,1,opt,name=shareType,proto3,enum=Aurora.PlayerInfoServer.ChatShareItemType" json:"shareType,omitempty"`
	Guid          uint64                 `protobuf:"varint,2,opt,name=guid,proto3" json:"guid,omitempty"`
	Info          []byte                 `protobuf:"bytes,3,opt,name=info,proto3" json:"info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChatShareItem) Reset() {
	*x = ChatShareItem{}
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatShareItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatShareItem) ProtoMessage() {}

func (x *ChatShareItem) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatShareItem.ProtoReflect.Descriptor instead.
func (*ChatShareItem) Descriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{1}
}

func (x *ChatShareItem) GetShareType() ChatShareItemType {
	if x != nil {
		return x.ShareType
	}
	return ChatShareItemType_ShareType_Item
}

func (x *ChatShareItem) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *ChatShareItem) GetInfo() []byte {
	if x != nil {
		return x.Info
	}
	return nil
}

// Type:ClientWebSocket
// Type:ServerWebSocket
// WebSocketMessageIDWebSc.ChatMessage_ID
type ChatMessage struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TimeStamp        int64                  `protobuf:"varint,1,opt,name=timeStamp,proto3" json:"timeStamp,omitempty"`
	MsgId            int32                  `protobuf:"varint,2,opt,name=msgId,proto3" json:"msgId,omitempty"`
	ZoneWorldId      int32                  `protobuf:"varint,3,opt,name=zoneWorldId,proto3" json:"zoneWorldId,omitempty"`
	SenderGuid       uint64                 `protobuf:"varint,4,opt,name=senderGuid,proto3" json:"senderGuid,omitempty"`
	SenderPlayerInfo *v1.PlayerBaseInfo     `protobuf:"bytes,5,opt,name=senderPlayerInfo,proto3" json:"senderPlayerInfo,omitempty"`
	MessageType      MessageChannelType     `protobuf:"varint,6,opt,name=messageType,proto3,enum=Aurora.PlayerInfoServer.MessageChannelType" json:"messageType,omitempty"`
	MessageSubType   MessageSubType         `protobuf:"varint,7,opt,name=messageSubType,proto3,enum=Aurora.PlayerInfoServer.MessageSubType" json:"messageSubType,omitempty"`
	TargetChatServer TargetChatServer       `protobuf:"varint,8,opt,name=targetChatServer,proto3,enum=Aurora.PlayerInfoServer.TargetChatServer" json:"targetChatServer,omitempty"`
	ChannelType      ChatChannelType        `protobuf:"varint,10,opt,name=channelType,proto3,enum=Aurora.PlayerInfoServer.ChatChannelType" json:"channelType,omitempty"`
	ParamId1         uint64                 `protobuf:"varint,11,opt,name=paramId1,proto3" json:"paramId1,omitempty"`
	ParamId2         int64                  `protobuf:"varint,12,opt,name=paramId2,proto3" json:"paramId2,omitempty"`
	Message          string                 `protobuf:"bytes,13,opt,name=message,proto3" json:"message,omitempty"`
	ItemList         []*ChatShareItem       `protobuf:"bytes,14,rep,name=itemList,proto3" json:"itemList,omitempty"`
	HeadFrameId      int32                  `protobuf:"varint,15,opt,name=headFrameId,proto3" json:"headFrameId,omitempty"`
	HeadIconId       string                 `protobuf:"bytes,16,opt,name=headIconId,proto3" json:"headIconId,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ChatMessage) Reset() {
	*x = ChatMessage{}
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChatMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChatMessage) ProtoMessage() {}

func (x *ChatMessage) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChatMessage.ProtoReflect.Descriptor instead.
func (*ChatMessage) Descriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{2}
}

func (x *ChatMessage) GetTimeStamp() int64 {
	if x != nil {
		return x.TimeStamp
	}
	return 0
}

func (x *ChatMessage) GetMsgId() int32 {
	if x != nil {
		return x.MsgId
	}
	return 0
}

func (x *ChatMessage) GetZoneWorldId() int32 {
	if x != nil {
		return x.ZoneWorldId
	}
	return 0
}

func (x *ChatMessage) GetSenderGuid() uint64 {
	if x != nil {
		return x.SenderGuid
	}
	return 0
}

func (x *ChatMessage) GetSenderPlayerInfo() *v1.PlayerBaseInfo {
	if x != nil {
		return x.SenderPlayerInfo
	}
	return nil
}

func (x *ChatMessage) GetMessageType() MessageChannelType {
	if x != nil {
		return x.MessageType
	}
	return MessageChannelType_MessageType_Invalid
}

func (x *ChatMessage) GetMessageSubType() MessageSubType {
	if x != nil {
		return x.MessageSubType
	}
	return MessageSubType_MessageSubType_Invalid
}

func (x *ChatMessage) GetTargetChatServer() TargetChatServer {
	if x != nil {
		return x.TargetChatServer
	}
	return TargetChatServer_TargetServer_Invalid
}

func (x *ChatMessage) GetChannelType() ChatChannelType {
	if x != nil {
		return x.ChannelType
	}
	return ChatChannelType_ChatType_Invalid
}

func (x *ChatMessage) GetParamId1() uint64 {
	if x != nil {
		return x.ParamId1
	}
	return 0
}

func (x *ChatMessage) GetParamId2() int64 {
	if x != nil {
		return x.ParamId2
	}
	return 0
}

func (x *ChatMessage) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ChatMessage) GetItemList() []*ChatShareItem {
	if x != nil {
		return x.ItemList
	}
	return nil
}

func (x *ChatMessage) GetHeadFrameId() int32 {
	if x != nil {
		return x.HeadFrameId
	}
	return 0
}

func (x *ChatMessage) GetHeadIconId() string {
	if x != nil {
		return x.HeadIconId
	}
	return ""
}

type SendChatMessageRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ChatMessage    *ChatMessage           `protobuf:"bytes,1,opt,name=chatMessage,proto3" json:"chatMessage,omitempty"`
	ReceiverGuids  []uint64               `protobuf:"varint,2,rep,packed,name=receiverGuids,proto3" json:"receiverGuids,omitempty"`
	NeedTextDetect bool                   `protobuf:"varint,3,opt,name=NeedTextDetect,proto3" json:"NeedTextDetect,omitempty"`
	SaveTime       int64                  `protobuf:"varint,4,opt,name=SaveTime,proto3" json:"SaveTime,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SendChatMessageRequest) Reset() {
	*x = SendChatMessageRequest{}
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendChatMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendChatMessageRequest) ProtoMessage() {}

func (x *SendChatMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendChatMessageRequest.ProtoReflect.Descriptor instead.
func (*SendChatMessageRequest) Descriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{3}
}

func (x *SendChatMessageRequest) GetChatMessage() *ChatMessage {
	if x != nil {
		return x.ChatMessage
	}
	return nil
}

func (x *SendChatMessageRequest) GetReceiverGuids() []uint64 {
	if x != nil {
		return x.ReceiverGuids
	}
	return nil
}

func (x *SendChatMessageRequest) GetNeedTextDetect() bool {
	if x != nil {
		return x.NeedTextDetect
	}
	return false
}

func (x *SendChatMessageRequest) GetSaveTime() int64 {
	if x != nil {
		return x.SaveTime
	}
	return 0
}

// The response message containing the message
type SendChatMessageReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendChatMessageReply) Reset() {
	*x = SendChatMessageReply{}
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendChatMessageReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendChatMessageReply) ProtoMessage() {}

func (x *SendChatMessageReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendChatMessageReply.ProtoReflect.Descriptor instead.
func (*SendChatMessageReply) Descriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{4}
}

func (x *SendChatMessageReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type G2G_ChatMessage struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ChatMessage    *ChatMessage           `protobuf:"bytes,1,opt,name=chatMessage,proto3" json:"chatMessage,omitempty"`
	ReceiverGuids  []uint64               `protobuf:"varint,2,rep,packed,name=receiverGuids,proto3" json:"receiverGuids,omitempty"`
	NeedTextDetect bool                   `protobuf:"varint,3,opt,name=NeedTextDetect,proto3" json:"NeedTextDetect,omitempty"`
	SaveTime       int64                  `protobuf:"varint,4,opt,name=SaveTime,proto3" json:"SaveTime,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *G2G_ChatMessage) Reset() {
	*x = G2G_ChatMessage{}
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *G2G_ChatMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*G2G_ChatMessage) ProtoMessage() {}

func (x *G2G_ChatMessage) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use G2G_ChatMessage.ProtoReflect.Descriptor instead.
func (*G2G_ChatMessage) Descriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{5}
}

func (x *G2G_ChatMessage) GetChatMessage() *ChatMessage {
	if x != nil {
		return x.ChatMessage
	}
	return nil
}

func (x *G2G_ChatMessage) GetReceiverGuids() []uint64 {
	if x != nil {
		return x.ReceiverGuids
	}
	return nil
}

func (x *G2G_ChatMessage) GetNeedTextDetect() bool {
	if x != nil {
		return x.NeedTextDetect
	}
	return false
}

func (x *G2G_ChatMessage) GetSaveTime() int64 {
	if x != nil {
		return x.SaveTime
	}
	return 0
}

// Type:ClientWebSocket
// WebSocketMessageIDWebSc.RequestOfflineMessage_ID
type RequestOfflineMessage struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	MessageType      MessageChannelType     `protobuf:"varint,1,opt,name=messageType,proto3,enum=Aurora.PlayerInfoServer.MessageChannelType" json:"messageType,omitempty"`
	MessageSubType   MessageSubType         `protobuf:"varint,2,opt,name=messageSubType,proto3,enum=Aurora.PlayerInfoServer.MessageSubType" json:"messageSubType,omitempty"`
	TargetChatServer TargetChatServer       `protobuf:"varint,3,opt,name=targetChatServer,proto3,enum=Aurora.PlayerInfoServer.TargetChatServer" json:"targetChatServer,omitempty"`
	ParamId1         uint64                 `protobuf:"varint,4,opt,name=paramId1,proto3" json:"paramId1,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *RequestOfflineMessage) Reset() {
	*x = RequestOfflineMessage{}
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestOfflineMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestOfflineMessage) ProtoMessage() {}

func (x *RequestOfflineMessage) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestOfflineMessage.ProtoReflect.Descriptor instead.
func (*RequestOfflineMessage) Descriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{6}
}

func (x *RequestOfflineMessage) GetMessageType() MessageChannelType {
	if x != nil {
		return x.MessageType
	}
	return MessageChannelType_MessageType_Invalid
}

func (x *RequestOfflineMessage) GetMessageSubType() MessageSubType {
	if x != nil {
		return x.MessageSubType
	}
	return MessageSubType_MessageSubType_Invalid
}

func (x *RequestOfflineMessage) GetTargetChatServer() TargetChatServer {
	if x != nil {
		return x.TargetChatServer
	}
	return TargetChatServer_TargetServer_Invalid
}

func (x *RequestOfflineMessage) GetParamId1() uint64 {
	if x != nil {
		return x.ParamId1
	}
	return 0
}

// Type:ClientWebSocket
// WebSocketMessageIDWebSc.OperNoticeProxy
type DispatchOperNoticeInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TargetGuid    uint64                 `protobuf:"varint,1,opt,name=TargetGuid,proto3" json:"TargetGuid,omitempty"`
	OperType      int32                  `protobuf:"varint,2,opt,name=OperType,proto3" json:"OperType,omitempty"`
	OperParamStr  string                 `protobuf:"bytes,10,opt,name=OperParamStr,proto3" json:"OperParamStr,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DispatchOperNoticeInfo) Reset() {
	*x = DispatchOperNoticeInfo{}
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DispatchOperNoticeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatchOperNoticeInfo) ProtoMessage() {}

func (x *DispatchOperNoticeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatchOperNoticeInfo.ProtoReflect.Descriptor instead.
func (*DispatchOperNoticeInfo) Descriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{7}
}

func (x *DispatchOperNoticeInfo) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *DispatchOperNoticeInfo) GetOperType() int32 {
	if x != nil {
		return x.OperType
	}
	return 0
}

func (x *DispatchOperNoticeInfo) GetOperParamStr() string {
	if x != nil {
		return x.OperParamStr
	}
	return ""
}

type DispatchOperNoticeReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=Result,proto3" json:"Result,omitempty"`
	OperType      int32                  `protobuf:"varint,2,opt,name=OperType,proto3" json:"OperType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DispatchOperNoticeReply) Reset() {
	*x = DispatchOperNoticeReply{}
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DispatchOperNoticeReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DispatchOperNoticeReply) ProtoMessage() {}

func (x *DispatchOperNoticeReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_chat_v1_chat_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DispatchOperNoticeReply.ProtoReflect.Descriptor instead.
func (*DispatchOperNoticeReply) Descriptor() ([]byte, []int) {
	return file_microservices_chat_v1_chat_proto_rawDescGZIP(), []int{8}
}

func (x *DispatchOperNoticeReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *DispatchOperNoticeReply) GetOperType() int32 {
	if x != nil {
		return x.OperType
	}
	return 0
}

var File_microservices_chat_v1_chat_proto protoreflect.FileDescriptor

const file_microservices_chat_v1_chat_proto_rawDesc = "" +
	"\n" +
	" microservices/chat/v1/chat.proto\x12\x17Aurora.PlayerInfoServer\x1a\x1cgoogle/api/annotations.proto\x1a2microservices/playerinfo/v1/playerinfostruct.proto\"\xde\x01\n" +
	"\x0eChatPlayerInfo\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05level\x18\x02 \x01(\x05R\x05level\x12\x16\n" +
	"\x06iconID\x18\x03 \x01(\x05R\x06iconID\x12\x18\n" +
	"\aiconUrl\x18\x04 \x01(\tR\aiconUrl\x12 \n" +
	"\viconFrameID\x18\x05 \x01(\x05R\viconFrameID\x12\x14\n" +
	"\x05petID\x18\x06 \x01(\x05R\x05petID\x12\x1c\n" +
	"\tguildName\x18\a \x01(\tR\tguildName\x12\x1a\n" +
	"\bserverID\x18\b \x01(\x05R\bserverID\"\x81\x01\n" +
	"\rChatShareItem\x12H\n" +
	"\tshareType\x18\x01 \x01(\x0e2*.Aurora.PlayerInfoServer.ChatShareItemTypeR\tshareType\x12\x12\n" +
	"\x04guid\x18\x02 \x01(\x04R\x04guid\x12\x12\n" +
	"\x04info\x18\x03 \x01(\fR\x04info\"\xf3\x05\n" +
	"\vChatMessage\x12\x1c\n" +
	"\ttimeStamp\x18\x01 \x01(\x03R\ttimeStamp\x12\x14\n" +
	"\x05msgId\x18\x02 \x01(\x05R\x05msgId\x12 \n" +
	"\vzoneWorldId\x18\x03 \x01(\x05R\vzoneWorldId\x12\x1e\n" +
	"\n" +
	"senderGuid\x18\x04 \x01(\x04R\n" +
	"senderGuid\x12S\n" +
	"\x10senderPlayerInfo\x18\x05 \x01(\v2'.Aurora.PlayerInfoServer.PlayerBaseInfoR\x10senderPlayerInfo\x12M\n" +
	"\vmessageType\x18\x06 \x01(\x0e2+.Aurora.PlayerInfoServer.MessageChannelTypeR\vmessageType\x12O\n" +
	"\x0emessageSubType\x18\a \x01(\x0e2'.Aurora.PlayerInfoServer.MessageSubTypeR\x0emessageSubType\x12U\n" +
	"\x10targetChatServer\x18\b \x01(\x0e2).Aurora.PlayerInfoServer.TargetChatServerR\x10targetChatServer\x12J\n" +
	"\vchannelType\x18\n" +
	" \x01(\x0e2(.Aurora.PlayerInfoServer.ChatChannelTypeR\vchannelType\x12\x1a\n" +
	"\bparamId1\x18\v \x01(\x04R\bparamId1\x12\x1a\n" +
	"\bparamId2\x18\f \x01(\x03R\bparamId2\x12\x18\n" +
	"\amessage\x18\r \x01(\tR\amessage\x12B\n" +
	"\bitemList\x18\x0e \x03(\v2&.Aurora.PlayerInfoServer.ChatShareItemR\bitemList\x12 \n" +
	"\vheadFrameId\x18\x0f \x01(\x05R\vheadFrameId\x12\x1e\n" +
	"\n" +
	"headIconId\x18\x10 \x01(\tR\n" +
	"headIconId\"\xca\x01\n" +
	"\x16SendChatMessageRequest\x12F\n" +
	"\vchatMessage\x18\x01 \x01(\v2$.Aurora.PlayerInfoServer.ChatMessageR\vchatMessage\x12$\n" +
	"\rreceiverGuids\x18\x02 \x03(\x04R\rreceiverGuids\x12&\n" +
	"\x0eNeedTextDetect\x18\x03 \x01(\bR\x0eNeedTextDetect\x12\x1a\n" +
	"\bSaveTime\x18\x04 \x01(\x03R\bSaveTime\".\n" +
	"\x14SendChatMessageReply\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\"\xc3\x01\n" +
	"\x0fG2G_ChatMessage\x12F\n" +
	"\vchatMessage\x18\x01 \x01(\v2$.Aurora.PlayerInfoServer.ChatMessageR\vchatMessage\x12$\n" +
	"\rreceiverGuids\x18\x02 \x03(\x04R\rreceiverGuids\x12&\n" +
	"\x0eNeedTextDetect\x18\x03 \x01(\bR\x0eNeedTextDetect\x12\x1a\n" +
	"\bSaveTime\x18\x04 \x01(\x03R\bSaveTime\"\xaa\x02\n" +
	"\x15RequestOfflineMessage\x12M\n" +
	"\vmessageType\x18\x01 \x01(\x0e2+.Aurora.PlayerInfoServer.MessageChannelTypeR\vmessageType\x12O\n" +
	"\x0emessageSubType\x18\x02 \x01(\x0e2'.Aurora.PlayerInfoServer.MessageSubTypeR\x0emessageSubType\x12U\n" +
	"\x10targetChatServer\x18\x03 \x01(\x0e2).Aurora.PlayerInfoServer.TargetChatServerR\x10targetChatServer\x12\x1a\n" +
	"\bparamId1\x18\x04 \x01(\x04R\bparamId1\"x\n" +
	"\x16DispatchOperNoticeInfo\x12\x1e\n" +
	"\n" +
	"TargetGuid\x18\x01 \x01(\x04R\n" +
	"TargetGuid\x12\x1a\n" +
	"\bOperType\x18\x02 \x01(\x05R\bOperType\x12\"\n" +
	"\fOperParamStr\x18\n" +
	" \x01(\tR\fOperParamStr\"M\n" +
	"\x17DispatchOperNoticeReply\x12\x16\n" +
	"\x06Result\x18\x01 \x01(\x05R\x06Result\x12\x1a\n" +
	"\bOperType\x18\x02 \x01(\x05R\bOperType*`\n" +
	"\x17WebSocketMessageIDWebSc\x12\x12\n" +
	"\x0eChatMessage_ID\x10\x00\x12\x1c\n" +
	"\x18RequestOfflineMessage_ID\x10\x01\x12\x13\n" +
	"\x0fOperNoticeProxy\x10\x02*\xe4\x03\n" +
	"\x1eDispatchOperNoticeInfoEnumType\x12\b\n" +
	"\x04none\x10\x00\x12\b\n" +
	"\x04tips\x10\x01\x12\x0e\n" +
	"\n" +
	"tips_Param\x10\x02\x12\x14\n" +
	"\x10tips_guidGetName\x10\x03\x12\x11\n" +
	"\fFriend_start\x10\xe8\a\x12\x1b\n" +
	"\x16Friend_AddFriendPlayer\x10\xe9\a\x12\x1b\n" +
	"\x16Friend_DelFriendPlayer\x10\xea\a\x12\x19\n" +
	"\x14Friend_GroupBaseInfo\x10\xeb\a\x12\x1f\n" +
	"\x1aFriend_InviteJoinGroupList\x10\xec\a\x12#\n" +
	"\x1eFriend_UpdateSelfGroupListInfo\x10\xed\a\x12\x17\n" +
	"\x12Friend_ApplyFriend\x10\xee\a\x12\x1f\n" +
	"\x1aFriend_ApproveFriendSingle\x10\xef\a\x12\x1c\n" +
	"\x17Friend_ApproveFriendAll\x10\xf0\a\x12\x1e\n" +
	"\x19Friend_RemoveFriendSingle\x10\xf1\a\x12\x1b\n" +
	"\x16Friend_RemoveFriendAll\x10\xf2\a\x12\x18\n" +
	"\x13Friend_DeleteFriend\x10\xf3\a\x12\x0f\n" +
	"\n" +
	"Friend_end\x10\xd0\x0f\x12\x1a\n" +
	"\x15Questionnaire_AskList\x10\xd1\x0f*\xcf\x02\n" +
	"\x12MessageChannelType\x12\x17\n" +
	"\x13MessageType_Invalid\x10\x00\x12\x15\n" +
	"\x11MessageType_World\x10\x01\x12\x14\n" +
	"\x10MessageType_Team\x10\x02\x12\x14\n" +
	"\x10MessageType_Raid\x10\x03\x12\x15\n" +
	"\x11MessageType_Guild\x10\x04\x12\x14\n" +
	"\x10MessageType_Near\x10\x05\x12\x17\n" +
	"\x13MessageType_Speaker\x10\x06\x12\x1b\n" +
	"\x17MessageType_PrivateChat\x10\a\x12\x19\n" +
	"\x15MessageType_GroupChat\x10\b\x12\x17\n" +
	"\x13MessageType_Recruit\x10\t\x12\x16\n" +
	"\x12MessageType_System\x10\n" +
	"\x12\x15\n" +
	"\x11MessageType_Scene\x10\v\x12\x17\n" +
	"\x13MessageType_GongGao\x10\f*\xef\x01\n" +
	"\x0eMessageSubType\x12\x1a\n" +
	"\x16MessageSubType_Invalid\x10\x00\x12\x1c\n" +
	"\x18MessageSubType_Broadcast\x10\x01\x12\x1a\n" +
	"\x16MessageSubType_Private\x10\x02\x12\x17\n" +
	"\x13MessageSubType_Team\x10\x03\x12\x18\n" +
	"\x14MessageSubType_Guild\x10\x04\x12\x17\n" +
	"\x13MessageSubType_Near\x10\x05\x12\x18\n" +
	"\x14MessageSubType_Scene\x10\x06\x12!\n" +
	"\x1dMessageSubType_Broadcast_Save\x10\a*\x9e\x01\n" +
	"\x10TargetChatServer\x12\x18\n" +
	"\x14TargetServer_Invalid\x10\x00\x12\x1c\n" +
	"\x18TargetServer_LocalServer\x10\x01\x12\x1b\n" +
	"\x17TargerServer_ZoneServer\x10\x02\x12\x1d\n" +
	"\x19TargetServer_GlobalServer\x10\x03\x12\x16\n" +
	"\x12TargetServer_Proxy\x10\n" +
	"*\x82\x01\n" +
	"\x0fChatChannelType\x12\x14\n" +
	"\x10ChatType_Invalid\x10\x00\x12\x14\n" +
	"\x10ChatType_Message\x10\x01\x12\x15\n" +
	"\x11ChatType_BigEmoji\x10\x02\x12\x18\n" +
	"\x14ChatType_RedEnvelope\x10\x03\x12\x12\n" +
	"\x0eChatType_Voice\x10\x04*\xd2\x01\n" +
	"\x11ChatShareItemType\x12\x12\n" +
	"\x0eShareType_Item\x10\x00\x12\x11\n" +
	"\rShareType_Pet\x10\x01\x12\x13\n" +
	"\x0fShareType_Mount\x10\x02\x12\x17\n" +
	"\x13ShareType_TagFriend\x10\x03\x12\x18\n" +
	"\x14ShareType_Coordinate\x10\x04\x12\x1d\n" +
	"\x19ShareType_FormatClientStr\x10\x05\x12\x19\n" +
	"\x15ShareType_InviteGuild\x10\x06\x12\x14\n" +
	"\x10ShareType_TeamUp\x10\a2\xeb\x02\n" +
	"\vChatService\x12s\n" +
	"\x0fSendChatMessage\x12/.Aurora.PlayerInfoServer.SendChatMessageRequest\x1a-.Aurora.PlayerInfoServer.SendChatMessageReply\"\x00\x12o\n" +
	"\x12BrocastChatMessage\x12(.Aurora.PlayerInfoServer.G2G_ChatMessage\x1a-.Aurora.PlayerInfoServer.SendChatMessageReply\"\x00\x12v\n" +
	"\x0fOperNoticeProxy\x12/.Aurora.PlayerInfoServer.DispatchOperNoticeInfo\x1a0.Aurora.PlayerInfoServer.DispatchOperNoticeReply\"\x00B(Z&liteframe/api/microservices/chat/v1;v1b\x06proto3"

var (
	file_microservices_chat_v1_chat_proto_rawDescOnce sync.Once
	file_microservices_chat_v1_chat_proto_rawDescData []byte
)

func file_microservices_chat_v1_chat_proto_rawDescGZIP() []byte {
	file_microservices_chat_v1_chat_proto_rawDescOnce.Do(func() {
		file_microservices_chat_v1_chat_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_microservices_chat_v1_chat_proto_rawDesc), len(file_microservices_chat_v1_chat_proto_rawDesc)))
	})
	return file_microservices_chat_v1_chat_proto_rawDescData
}

var file_microservices_chat_v1_chat_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_microservices_chat_v1_chat_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_microservices_chat_v1_chat_proto_goTypes = []any{
	(WebSocketMessageIDWebSc)(0),        // 0: Aurora.PlayerInfoServer.WebSocketMessageIDWebSc
	(DispatchOperNoticeInfoEnumType)(0), // 1: Aurora.PlayerInfoServer.DispatchOperNoticeInfoEnumType
	(MessageChannelType)(0),             // 2: Aurora.PlayerInfoServer.MessageChannelType
	(MessageSubType)(0),                 // 3: Aurora.PlayerInfoServer.MessageSubType
	(TargetChatServer)(0),               // 4: Aurora.PlayerInfoServer.TargetChatServer
	(ChatChannelType)(0),                // 5: Aurora.PlayerInfoServer.ChatChannelType
	(ChatShareItemType)(0),              // 6: Aurora.PlayerInfoServer.ChatShareItemType
	(*ChatPlayerInfo)(nil),              // 7: Aurora.PlayerInfoServer.ChatPlayerInfo
	(*ChatShareItem)(nil),               // 8: Aurora.PlayerInfoServer.ChatShareItem
	(*ChatMessage)(nil),                 // 9: Aurora.PlayerInfoServer.ChatMessage
	(*SendChatMessageRequest)(nil),      // 10: Aurora.PlayerInfoServer.SendChatMessageRequest
	(*SendChatMessageReply)(nil),        // 11: Aurora.PlayerInfoServer.SendChatMessageReply
	(*G2G_ChatMessage)(nil),             // 12: Aurora.PlayerInfoServer.G2G_ChatMessage
	(*RequestOfflineMessage)(nil),       // 13: Aurora.PlayerInfoServer.RequestOfflineMessage
	(*DispatchOperNoticeInfo)(nil),      // 14: Aurora.PlayerInfoServer.DispatchOperNoticeInfo
	(*DispatchOperNoticeReply)(nil),     // 15: Aurora.PlayerInfoServer.DispatchOperNoticeReply
	(*v1.PlayerBaseInfo)(nil),           // 16: Aurora.PlayerInfoServer.PlayerBaseInfo
}
var file_microservices_chat_v1_chat_proto_depIdxs = []int32{
	6,  // 0: Aurora.PlayerInfoServer.ChatShareItem.shareType:type_name -> Aurora.PlayerInfoServer.ChatShareItemType
	16, // 1: Aurora.PlayerInfoServer.ChatMessage.senderPlayerInfo:type_name -> Aurora.PlayerInfoServer.PlayerBaseInfo
	2,  // 2: Aurora.PlayerInfoServer.ChatMessage.messageType:type_name -> Aurora.PlayerInfoServer.MessageChannelType
	3,  // 3: Aurora.PlayerInfoServer.ChatMessage.messageSubType:type_name -> Aurora.PlayerInfoServer.MessageSubType
	4,  // 4: Aurora.PlayerInfoServer.ChatMessage.targetChatServer:type_name -> Aurora.PlayerInfoServer.TargetChatServer
	5,  // 5: Aurora.PlayerInfoServer.ChatMessage.channelType:type_name -> Aurora.PlayerInfoServer.ChatChannelType
	8,  // 6: Aurora.PlayerInfoServer.ChatMessage.itemList:type_name -> Aurora.PlayerInfoServer.ChatShareItem
	9,  // 7: Aurora.PlayerInfoServer.SendChatMessageRequest.chatMessage:type_name -> Aurora.PlayerInfoServer.ChatMessage
	9,  // 8: Aurora.PlayerInfoServer.G2G_ChatMessage.chatMessage:type_name -> Aurora.PlayerInfoServer.ChatMessage
	2,  // 9: Aurora.PlayerInfoServer.RequestOfflineMessage.messageType:type_name -> Aurora.PlayerInfoServer.MessageChannelType
	3,  // 10: Aurora.PlayerInfoServer.RequestOfflineMessage.messageSubType:type_name -> Aurora.PlayerInfoServer.MessageSubType
	4,  // 11: Aurora.PlayerInfoServer.RequestOfflineMessage.targetChatServer:type_name -> Aurora.PlayerInfoServer.TargetChatServer
	10, // 12: Aurora.PlayerInfoServer.ChatService.SendChatMessage:input_type -> Aurora.PlayerInfoServer.SendChatMessageRequest
	12, // 13: Aurora.PlayerInfoServer.ChatService.BrocastChatMessage:input_type -> Aurora.PlayerInfoServer.G2G_ChatMessage
	14, // 14: Aurora.PlayerInfoServer.ChatService.OperNoticeProxy:input_type -> Aurora.PlayerInfoServer.DispatchOperNoticeInfo
	11, // 15: Aurora.PlayerInfoServer.ChatService.SendChatMessage:output_type -> Aurora.PlayerInfoServer.SendChatMessageReply
	11, // 16: Aurora.PlayerInfoServer.ChatService.BrocastChatMessage:output_type -> Aurora.PlayerInfoServer.SendChatMessageReply
	15, // 17: Aurora.PlayerInfoServer.ChatService.OperNoticeProxy:output_type -> Aurora.PlayerInfoServer.DispatchOperNoticeReply
	15, // [15:18] is the sub-list for method output_type
	12, // [12:15] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_microservices_chat_v1_chat_proto_init() }
func file_microservices_chat_v1_chat_proto_init() {
	if File_microservices_chat_v1_chat_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_microservices_chat_v1_chat_proto_rawDesc), len(file_microservices_chat_v1_chat_proto_rawDesc)),
			NumEnums:      7,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_microservices_chat_v1_chat_proto_goTypes,
		DependencyIndexes: file_microservices_chat_v1_chat_proto_depIdxs,
		EnumInfos:         file_microservices_chat_v1_chat_proto_enumTypes,
		MessageInfos:      file_microservices_chat_v1_chat_proto_msgTypes,
	}.Build()
	File_microservices_chat_v1_chat_proto = out.File
	file_microservices_chat_v1_chat_proto_goTypes = nil
	file_microservices_chat_v1_chat_proto_depIdxs = nil
}
