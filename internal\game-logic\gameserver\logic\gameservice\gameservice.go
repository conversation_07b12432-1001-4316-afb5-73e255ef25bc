package gameservice

import (
	"context"
	"fmt"
	"liteframe/internal/common/constant/def"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/natsrpc"
	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
)

type GameService struct {
}

func NewGameService() *GameService {
	return &GameService{}
}

func (l *GameService) Auth(ctx context.Context, req *natsrpc.AuthReq) (*natsrpc.AuthResp, error) {
	log.Info("Auth request received",
		log.Kv("uuid", req.Uuid),
		log.Kv("token", req.Token))
	resp := &natsrpc.AuthResp{}

	msg := &actor.Message{
		Id: uint32(def.MsgId_Gate_PrepareLogin),
		Data: &game_def.PrepareLoginRequest{
			Account: req.Uuid,
			Token:   req.Token,
		},
	}
	actorRsp := actor.SyncRequest(actor_def.SystemPID, actor_def.PlayerSystemPID, msg)
	if actorRsp.Err != nil {
		resp.Code = 1
		resp.Message = actorRsp.Err.Error()
		log.Error("Auth failed", log.Kv("uuid", req.Uuid), log.Err(actorRsp.Err))
		return resp, fmt.Errorf("prepare login request failed: %s", actorRsp.Err.Error())
	}

	return resp, nil
}

func (g *GameService) MatchResult(ctx context.Context, request *natsrpc.MatchResultRequest) (*natsrpc.MatchResultResponse, error) {
	log.Info("MatchResult request received",
		log.Kv("uid", request.Uid),
		log.Kv("battleId", request.BattleId),
		log.Kv("serverId", request.ServerId),
		log.Kv("success", request.Success),
		log.Kv("createTime", request.CreateTime))

	if err := actor.Send(actor_def.SystemPID, actor_def.PlayerSystemPID, &actor.Message{
		Id:   def.MsgId_Actor_Player_MatchResult,
		Uid:  request.Uid,
		Data: request,
	}); err != nil {
		log.Error("Send match result failed", log.Kv("uid", request.Uid), log.Err(err))
	}

	return &natsrpc.MatchResultResponse{Code: int32(error_code.ERROR_OK)}, nil // TODO: 不需要返回
}

func (g *GameService) RoundStart(ctx context.Context, request *natsrpc.RoundStartReq) (*natsrpc.RoundStartResp, error) {
	log.Info("RoundStart request received",
		log.Kv("uid", request.Uid),
		log.Kv("playerBoards", request.PlayerBoards),
		log.Kv("buff", request.Buffers))

	if err := actor.Send(actor_def.SystemPID, actor_def.PlayerSystemPID, &actor.Message{
		Id:   def.MsgId_Actor_Player_BattleStart,
		Uid:  request.Uid,
		Data: request,
	}); err != nil {
		log.Error("Send round start failed", log.Kv("uid", request.Uid), log.Err(err))
	}

	return &natsrpc.RoundStartResp{Code: int32(error_code.ERROR_OK)}, nil // TODO: 不需要返回
}

func (g *GameService) RoundBattleStart(ctx context.Context, request *natsrpc.RoundBattleStartReq) (*natsrpc.RoundBattleStartResp, error) {
	log.Info("RoundBattleStart request received",
		log.Kv("uid", request.Uid),
		log.Kv("battleId", request.BattleId),
		log.Kv("seed", request.Seed),
		log.Kv("team", request.Team))

	if err := actor.Send(actor_def.SystemPID, actor_def.PlayerSystemPID, &actor.Message{
		Id:   def.MsgId_Actor_Player_RoundStart,
		Uid:  request.Uid,
		Data: request,
	}); err != nil {
		log.Error("Send round battle start failed", log.Kv("uid", request.Uid), log.Err(err))
	}

	return &natsrpc.RoundBattleStartResp{Code: int32(error_code.ERROR_OK)}, nil
}

func (g *GameService) RoundBattleEnd(ctx context.Context, request *natsrpc.RoundBattleEndReq) (*natsrpc.RoundBattleEndResp, error) {
	log.Info("RoundBattleEnd request received",
		log.Kv("uid", request.Uid),
		log.Kv("winUid", request.WinUid),
		log.Kv("loseUid", request.LoseUid))

	if err := actor.Send(actor_def.SystemPID, actor_def.PlayerSystemPID, &actor.Message{
		Id:   def.MsgId_Actor_Player_RoundEnd,
		Uid:  request.Uid,
		Data: request,
	}); err != nil {
		log.Error("Send round battle end failed", log.Kv("uid", request.Uid), log.Err(err))
	}

	return &natsrpc.RoundBattleEndResp{Code: int32(error_code.ERROR_OK)}, nil
}

func (g *GameService) BattleEnd(ctx context.Context, request *natsrpc.BattleEndReq) (*natsrpc.BattleEndResp, error) {
	log.Info("BattleEnd request received",
		log.Kv("uid", request.Uid),
		log.Kv("battle_id", request.BattleId),
		log.Kv("rank", request.Rank),
		log.Kv("win_streak", request.WinStreak),
		log.Kv("heroes", request.Heros))

	if err := actor.Send(actor_def.SystemPID, actor_def.PlayerSystemPID, &actor.Message{
		Id:   def.MsgId_Actor_Player_BattleEnd,
		Uid:  request.Uid,
		Data: request,
	}); err != nil {
		log.Error("Send battle end failed", log.Kv("uid", request.Uid), log.Err(err))
	}

	return &natsrpc.BattleEndResp{Code: int32(error_code.ERROR_OK)}, nil
}

func (g *GameService) OnBattleStateChanged(ctx context.Context, request *natsrpc.BattleStateChangeReq) (*natsrpc.BattleStateChangeResp, error) {
	log.Info("OnBattleStateChanged",
		log.Kv("battleId", request.BattleId),
		log.Kv("state", request.State),
		log.Kv("remainTimeMs", request.RemainTimeMs),
		log.Kv("roundCount", request.RoundCount))

	// TODO: 处理战斗状态变化逻辑

	return &natsrpc.BattleStateChangeResp{Code: int32(error_code.ERROR_OK)}, nil
}
