//GrpcAddressType:TeamCenter
//GrpcServerType:server,world

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.2
// 	protoc        v4.23.2
// source: v1/teamcenter.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Type:Http
// Type:Inner
type ETeamJobTitle int32

const (
	ETeamJobTitle_eInvalid   ETeamJobTitle = 0
	ETeamJobTitle_eLeader    ETeamJobTitle = 1
	ETeamJobTitle_eSubLeader ETeamJobTitle = 2
	ETeamJobTitle_eMmeber    ETeamJobTitle = 3
)

// Enum value maps for ETeamJobTitle.
var (
	ETeamJobTitle_name = map[int32]string{
		0: "eInvalid",
		1: "eLeader",
		2: "eSubLeader",
		3: "eMmeber",
	}
	ETeamJobTitle_value = map[string]int32{
		"eInvalid":   0,
		"eLeader":    1,
		"eSubLeader": 2,
		"eMmeber":    3,
	}
)

func (x ETeamJobTitle) Enum() *ETeamJobTitle {
	p := new(ETeamJobTitle)
	*p = x
	return p
}

func (x ETeamJobTitle) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ETeamJobTitle) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_teamcenter_proto_enumTypes[0].Descriptor()
}

func (ETeamJobTitle) Type() protoreflect.EnumType {
	return &file_v1_teamcenter_proto_enumTypes[0]
}

func (x ETeamJobTitle) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ETeamJobTitle.Descriptor instead.
func (ETeamJobTitle) EnumDescriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{0}
}

// Type:Http
// Type:Inner
type ENUM_TEAMCENTER_OP int32

const (
	ENUM_TEAMCENTER_OP_None                                                   ENUM_TEAMCENTER_OP = 0
	ENUM_TEAMCENTER_OP_Suc                                                    ENUM_TEAMCENTER_OP = 1
	ENUM_TEAMCENTER_OP_saveTCTeamMemberToRedis_Suc                            ENUM_TEAMCENTER_OP = 10
	ENUM_TEAMCENTER_OP_saveTCTeamMemberToRedis_Redis_Faild                    ENUM_TEAMCENTER_OP = 11
	ENUM_TEAMCENTER_OP_saveTCTeamInfoToRedis_Suc                              ENUM_TEAMCENTER_OP = 20
	ENUM_TEAMCENTER_OP_saveTCTeamInfoToRedis_JsonMarshal_Error                ENUM_TEAMCENTER_OP = 21
	ENUM_TEAMCENTER_OP_saveTCTeamInfoToRedis_Redis_Error                      ENUM_TEAMCENTER_OP = 22
	ENUM_TEAMCENTER_OP_saveTCTeamInfoToRedis_saveTCTeamMember_Error           ENUM_TEAMCENTER_OP = 23
	ENUM_TEAMCENTER_OP_loadTCTeamInfoFromRedis_Suc                            ENUM_TEAMCENTER_OP = 30
	ENUM_TEAMCENTER_OP_loadTCTeamInfoFromRedis_TeamIdInvalid                  ENUM_TEAMCENTER_OP = 31
	ENUM_TEAMCENTER_OP_loadTCTeamInfoFromRedis_RedisError                     ENUM_TEAMCENTER_OP = 32
	ENUM_TEAMCENTER_OP_loadTCTeamInfoFromRedis_JsonUnmarshal_Error            ENUM_TEAMCENTER_OP = 33
	ENUM_TEAMCENTER_OP_deleteTCTeamInfoFromRedis_Suc                          ENUM_TEAMCENTER_OP = 40
	ENUM_TEAMCENTER_OP_deleteTCTeamInfoFromRedis_Redis_Error                  ENUM_TEAMCENTER_OP = 41
	ENUM_TEAMCENTER_OP_deleteTCTeamInfoFromRedis_DeleteTeamMemberInfo_Error   ENUM_TEAMCENTER_OP = 42
	ENUM_TEAMCENTER_OP_getPlayerTeamIdFromRedis_Suc                           ENUM_TEAMCENTER_OP = 50
	ENUM_TEAMCENTER_OP_getPlayerTeamIdFromRedis_Redis_Error                   ENUM_TEAMCENTER_OP = 51
	ENUM_TEAMCENTER_OP_TCAskTeamInfoByTeamIdRp_Suc                            ENUM_TEAMCENTER_OP = 100
	ENUM_TEAMCENTER_OP_TCAskTeamInfoByTeamIdRp_OverTime                       ENUM_TEAMCENTER_OP = 101
	ENUM_TEAMCENTER_OP_TCAskTeamInfoByTeamIdRp_AcqLockFaild                   ENUM_TEAMCENTER_OP = 102
	ENUM_TEAMCENTER_OP_TCAskTeamInfoByPlayerGuidRp_Suc                        ENUM_TEAMCENTER_OP = 200
	ENUM_TEAMCENTER_OP_TCAskTeamInfoByPlayerGuidRp_OverTime                   ENUM_TEAMCENTER_OP = 201
	ENUM_TEAMCENTER_OP_TCAskTeamInfoByPlayerGuidRp_AcqLockFaild_Player        ENUM_TEAMCENTER_OP = 202
	ENUM_TEAMCENTER_OP_TCAskTeamInfoByPlayerGuidRp_AcqLockFaild_Team          ENUM_TEAMCENTER_OP = 203
	ENUM_TEAMCENTER_OP_CreateTeam_Suc                                         ENUM_TEAMCENTER_OP = 300
	ENUM_TEAMCENTER_OP_CreateTeam_GetNewTeamId_Faild                          ENUM_TEAMCENTER_OP = 301
	ENUM_TEAMCENTER_OP_CreateTeam_SaveTeamInfo_JsonMarshal_Error              ENUM_TEAMCENTER_OP = 302
	ENUM_TEAMCENTER_OP_CreateTeam_SaveTeamInfo_Redis_Error                    ENUM_TEAMCENTER_OP = 303
	ENUM_TEAMCENTER_OP_CreateTeam_SavePlayerTeamId_Redis_Error                ENUM_TEAMCENTER_OP = 304
	ENUM_TEAMCENTER_OP_CreateTeam_CheckPlayerTeamId_PlayerHasTeam             ENUM_TEAMCENTER_OP = 305
	ENUM_TEAMCENTER_OP_CreateTeam_CheckPlayerTeamId_GenTeamLockKey_Faild      ENUM_TEAMCENTER_OP = 306
	ENUM_TEAMCENTER_OP_CreateTeam_CheckPlayerTeamId_LockKey_AcquireLock_Faild ENUM_TEAMCENTER_OP = 307
	ENUM_TEAMCENTER_OP_JoinTeam_Suc                                           ENUM_TEAMCENTER_OP = 400
	ENUM_TEAMCENTER_OP_JoinTeam_CheckPlayerTeamId_Error                       ENUM_TEAMCENTER_OP = 401
	ENUM_TEAMCENTER_OP_JoinTeam_CheckPlayerTeamId_PlayerHasTeam               ENUM_TEAMCENTER_OP = 402
	ENUM_TEAMCENTER_OP_JoinTeam_CheckPlayerTeamId_newTeamMemListEmpty         ENUM_TEAMCENTER_OP = 403
	ENUM_TEAMCENTER_OP_JoinTeam_CheckTeam_GetTeamInfoFaild                    ENUM_TEAMCENTER_OP = 404
	ENUM_TEAMCENTER_OP_JoinTeam_CheckTeam_PlayerExistence                     ENUM_TEAMCENTER_OP = 405
	ENUM_TEAMCENTER_OP_JoinTeam_CheckTeam_TeamTypeInvalid                     ENUM_TEAMCENTER_OP = 406
	ENUM_TEAMCENTER_OP_JoinTeam_CheckTeam_TeamFull                            ENUM_TEAMCENTER_OP = 407
	ENUM_TEAMCENTER_OP_JoinTeam_CheckTeam_OpPlayerInvalid                     ENUM_TEAMCENTER_OP = 408
	ENUM_TEAMCENTER_OP_JoinTeam_CheckTeam_TeamIdInvalid                       ENUM_TEAMCENTER_OP = 409
	ENUM_TEAMCENTER_OP_JoinTeam_CheckTeam_Team_Lock_Faild                     ENUM_TEAMCENTER_OP = 410
	ENUM_TEAMCENTER_OP_LeaveTeam_Suc                                          ENUM_TEAMCENTER_OP = 500
	ENUM_TEAMCENTER_OP_LeaveTeam_Team_Lock_Faild                              ENUM_TEAMCENTER_OP = 501
	ENUM_TEAMCENTER_OP_AppointedJobTitle_Suc                                  ENUM_TEAMCENTER_OP = 600
	ENUM_TEAMCENTER_OP_AppointedJobTitle_OpPlayer_Error                       ENUM_TEAMCENTER_OP = 601
	ENUM_TEAMCENTER_OP_AppointedJobTitle_OpPlayer_TeamIdInvalid               ENUM_TEAMCENTER_OP = 602
	ENUM_TEAMCENTER_OP_AppointedJobTitle_LeaderPlayer_Error                   ENUM_TEAMCENTER_OP = 603
	ENUM_TEAMCENTER_OP_AppointedJobTitle_LeaderPlayer_TeamIdInvalid           ENUM_TEAMCENTER_OP = 604
	ENUM_TEAMCENTER_OP_AppointedJobTitle_TeamIdInvalid                        ENUM_TEAMCENTER_OP = 605
	ENUM_TEAMCENTER_OP_AppointedJobTitle_TeamLeader_Nil                       ENUM_TEAMCENTER_OP = 606
	ENUM_TEAMCENTER_OP_AppointedJobTitle_TeamLeaderGuid_Dif                   ENUM_TEAMCENTER_OP = 607
	ENUM_TEAMCENTER_OP_AppointedJobTitle_OpPlayer_NoTeam                      ENUM_TEAMCENTER_OP = 608
	ENUM_TEAMCENTER_OP_AppointedJobTitle_InvalidJobTitle                      ENUM_TEAMCENTER_OP = 609
	ENUM_TEAMCENTER_OP_AppointedJobTitle_Team_Lock_Faild                      ENUM_TEAMCENTER_OP = 610
	ENUM_TEAMCENTER_OP_AppointedJobTitle_OpPlayer_Lock_Faild                  ENUM_TEAMCENTER_OP = 611
	ENUM_TEAMCENTER_OP_AppointedJobTitle_LeaderPlayer_Lock_Faild              ENUM_TEAMCENTER_OP = 612
	ENUM_TEAMCENTER_OP_TCAskTeamInfoListByTeamIdList_Suc                      ENUM_TEAMCENTER_OP = 700
	ENUM_TEAMCENTER_OP_TCAskTeamInfoListByTeamIdList_Load_Error               ENUM_TEAMCENTER_OP = 701
	ENUM_TEAMCENTER_OP_TCAskTeamInfoListByTeamIdList_OverTime                 ENUM_TEAMCENTER_OP = 702
	ENUM_TEAMCENTER_OP_TCAskTeamInfoListByTeamIdList_AcqLockFaild             ENUM_TEAMCENTER_OP = 703
	ENUM_TEAMCENTER_OP_RobotJoinTeam_Suc                                      ENUM_TEAMCENTER_OP = 800
	ENUM_TEAMCENTER_OP_RobotJoinTeam_CheckTeam_GetTeamInfoFaild               ENUM_TEAMCENTER_OP = 801
	ENUM_TEAMCENTER_OP_RobotJoinTeam_CheckTeam_TeamTypeInvalid                ENUM_TEAMCENTER_OP = 802
	ENUM_TEAMCENTER_OP_RobotJoinTeam_CheckTeam_TeamFull                       ENUM_TEAMCENTER_OP = 803
	ENUM_TEAMCENTER_OP_RobotJoinTeam_CheckTeam_OpPlayerInvalid                ENUM_TEAMCENTER_OP = 804
	ENUM_TEAMCENTER_OP_RobotJoinTeam_CheckTeam_TeamIdInvalid                  ENUM_TEAMCENTER_OP = 805
	ENUM_TEAMCENTER_OP_RobotJoinTeam_CheckTeam_Team_Lock_Faild                ENUM_TEAMCENTER_OP = 806
	ENUM_TEAMCENTER_OP_RobotJoinTeam_CheckPlayerTeamId_newTeamMemListEmpty    ENUM_TEAMCENTER_OP = 807
	ENUM_TEAMCENTER_OP_RobotLeaveTeam_Suc                                     ENUM_TEAMCENTER_OP = 900
	ENUM_TEAMCENTER_OP_RobotLeaveTeam_Team_Lock_Faild                         ENUM_TEAMCENTER_OP = 901
	ENUM_TEAMCENTER_OP_RobotLeaveTeam_TeamID_Invalid                          ENUM_TEAMCENTER_OP = 902
)

// Enum value maps for ENUM_TEAMCENTER_OP.
var (
	ENUM_TEAMCENTER_OP_name = map[int32]string{
		0:   "None",
		1:   "Suc",
		10:  "saveTCTeamMemberToRedis_Suc",
		11:  "saveTCTeamMemberToRedis_Redis_Faild",
		20:  "saveTCTeamInfoToRedis_Suc",
		21:  "saveTCTeamInfoToRedis_JsonMarshal_Error",
		22:  "saveTCTeamInfoToRedis_Redis_Error",
		23:  "saveTCTeamInfoToRedis_saveTCTeamMember_Error",
		30:  "loadTCTeamInfoFromRedis_Suc",
		31:  "loadTCTeamInfoFromRedis_TeamIdInvalid",
		32:  "loadTCTeamInfoFromRedis_RedisError",
		33:  "loadTCTeamInfoFromRedis_JsonUnmarshal_Error",
		40:  "deleteTCTeamInfoFromRedis_Suc",
		41:  "deleteTCTeamInfoFromRedis_Redis_Error",
		42:  "deleteTCTeamInfoFromRedis_DeleteTeamMemberInfo_Error",
		50:  "getPlayerTeamIdFromRedis_Suc",
		51:  "getPlayerTeamIdFromRedis_Redis_Error",
		100: "TCAskTeamInfoByTeamIdRp_Suc",
		101: "TCAskTeamInfoByTeamIdRp_OverTime",
		102: "TCAskTeamInfoByTeamIdRp_AcqLockFaild",
		200: "TCAskTeamInfoByPlayerGuidRp_Suc",
		201: "TCAskTeamInfoByPlayerGuidRp_OverTime",
		202: "TCAskTeamInfoByPlayerGuidRp_AcqLockFaild_Player",
		203: "TCAskTeamInfoByPlayerGuidRp_AcqLockFaild_Team",
		300: "CreateTeam_Suc",
		301: "CreateTeam_GetNewTeamId_Faild",
		302: "CreateTeam_SaveTeamInfo_JsonMarshal_Error",
		303: "CreateTeam_SaveTeamInfo_Redis_Error",
		304: "CreateTeam_SavePlayerTeamId_Redis_Error",
		305: "CreateTeam_CheckPlayerTeamId_PlayerHasTeam",
		306: "CreateTeam_CheckPlayerTeamId_GenTeamLockKey_Faild",
		307: "CreateTeam_CheckPlayerTeamId_LockKey_AcquireLock_Faild",
		400: "JoinTeam_Suc",
		401: "JoinTeam_CheckPlayerTeamId_Error",
		402: "JoinTeam_CheckPlayerTeamId_PlayerHasTeam",
		403: "JoinTeam_CheckPlayerTeamId_newTeamMemListEmpty",
		404: "JoinTeam_CheckTeam_GetTeamInfoFaild",
		405: "JoinTeam_CheckTeam_PlayerExistence",
		406: "JoinTeam_CheckTeam_TeamTypeInvalid",
		407: "JoinTeam_CheckTeam_TeamFull",
		408: "JoinTeam_CheckTeam_OpPlayerInvalid",
		409: "JoinTeam_CheckTeam_TeamIdInvalid",
		410: "JoinTeam_CheckTeam_Team_Lock_Faild",
		500: "LeaveTeam_Suc",
		501: "LeaveTeam_Team_Lock_Faild",
		600: "AppointedJobTitle_Suc",
		601: "AppointedJobTitle_OpPlayer_Error",
		602: "AppointedJobTitle_OpPlayer_TeamIdInvalid",
		603: "AppointedJobTitle_LeaderPlayer_Error",
		604: "AppointedJobTitle_LeaderPlayer_TeamIdInvalid",
		605: "AppointedJobTitle_TeamIdInvalid",
		606: "AppointedJobTitle_TeamLeader_Nil",
		607: "AppointedJobTitle_TeamLeaderGuid_Dif",
		608: "AppointedJobTitle_OpPlayer_NoTeam",
		609: "AppointedJobTitle_InvalidJobTitle",
		610: "AppointedJobTitle_Team_Lock_Faild",
		611: "AppointedJobTitle_OpPlayer_Lock_Faild",
		612: "AppointedJobTitle_LeaderPlayer_Lock_Faild",
		700: "TCAskTeamInfoListByTeamIdList_Suc",
		701: "TCAskTeamInfoListByTeamIdList_Load_Error",
		702: "TCAskTeamInfoListByTeamIdList_OverTime",
		703: "TCAskTeamInfoListByTeamIdList_AcqLockFaild",
		800: "RobotJoinTeam_Suc",
		801: "RobotJoinTeam_CheckTeam_GetTeamInfoFaild",
		802: "RobotJoinTeam_CheckTeam_TeamTypeInvalid",
		803: "RobotJoinTeam_CheckTeam_TeamFull",
		804: "RobotJoinTeam_CheckTeam_OpPlayerInvalid",
		805: "RobotJoinTeam_CheckTeam_TeamIdInvalid",
		806: "RobotJoinTeam_CheckTeam_Team_Lock_Faild",
		807: "RobotJoinTeam_CheckPlayerTeamId_newTeamMemListEmpty",
		900: "RobotLeaveTeam_Suc",
		901: "RobotLeaveTeam_Team_Lock_Faild",
		902: "RobotLeaveTeam_TeamID_Invalid",
	}
	ENUM_TEAMCENTER_OP_value = map[string]int32{
		"None":                                0,
		"Suc":                                 1,
		"saveTCTeamMemberToRedis_Suc":         10,
		"saveTCTeamMemberToRedis_Redis_Faild": 11,
		"saveTCTeamInfoToRedis_Suc":           20,
		"saveTCTeamInfoToRedis_JsonMarshal_Error":                21,
		"saveTCTeamInfoToRedis_Redis_Error":                      22,
		"saveTCTeamInfoToRedis_saveTCTeamMember_Error":           23,
		"loadTCTeamInfoFromRedis_Suc":                            30,
		"loadTCTeamInfoFromRedis_TeamIdInvalid":                  31,
		"loadTCTeamInfoFromRedis_RedisError":                     32,
		"loadTCTeamInfoFromRedis_JsonUnmarshal_Error":            33,
		"deleteTCTeamInfoFromRedis_Suc":                          40,
		"deleteTCTeamInfoFromRedis_Redis_Error":                  41,
		"deleteTCTeamInfoFromRedis_DeleteTeamMemberInfo_Error":   42,
		"getPlayerTeamIdFromRedis_Suc":                           50,
		"getPlayerTeamIdFromRedis_Redis_Error":                   51,
		"TCAskTeamInfoByTeamIdRp_Suc":                            100,
		"TCAskTeamInfoByTeamIdRp_OverTime":                       101,
		"TCAskTeamInfoByTeamIdRp_AcqLockFaild":                   102,
		"TCAskTeamInfoByPlayerGuidRp_Suc":                        200,
		"TCAskTeamInfoByPlayerGuidRp_OverTime":                   201,
		"TCAskTeamInfoByPlayerGuidRp_AcqLockFaild_Player":        202,
		"TCAskTeamInfoByPlayerGuidRp_AcqLockFaild_Team":          203,
		"CreateTeam_Suc":                                         300,
		"CreateTeam_GetNewTeamId_Faild":                          301,
		"CreateTeam_SaveTeamInfo_JsonMarshal_Error":              302,
		"CreateTeam_SaveTeamInfo_Redis_Error":                    303,
		"CreateTeam_SavePlayerTeamId_Redis_Error":                304,
		"CreateTeam_CheckPlayerTeamId_PlayerHasTeam":             305,
		"CreateTeam_CheckPlayerTeamId_GenTeamLockKey_Faild":      306,
		"CreateTeam_CheckPlayerTeamId_LockKey_AcquireLock_Faild": 307,
		"JoinTeam_Suc":                                        400,
		"JoinTeam_CheckPlayerTeamId_Error":                    401,
		"JoinTeam_CheckPlayerTeamId_PlayerHasTeam":            402,
		"JoinTeam_CheckPlayerTeamId_newTeamMemListEmpty":      403,
		"JoinTeam_CheckTeam_GetTeamInfoFaild":                 404,
		"JoinTeam_CheckTeam_PlayerExistence":                  405,
		"JoinTeam_CheckTeam_TeamTypeInvalid":                  406,
		"JoinTeam_CheckTeam_TeamFull":                         407,
		"JoinTeam_CheckTeam_OpPlayerInvalid":                  408,
		"JoinTeam_CheckTeam_TeamIdInvalid":                    409,
		"JoinTeam_CheckTeam_Team_Lock_Faild":                  410,
		"LeaveTeam_Suc":                                       500,
		"LeaveTeam_Team_Lock_Faild":                           501,
		"AppointedJobTitle_Suc":                               600,
		"AppointedJobTitle_OpPlayer_Error":                    601,
		"AppointedJobTitle_OpPlayer_TeamIdInvalid":            602,
		"AppointedJobTitle_LeaderPlayer_Error":                603,
		"AppointedJobTitle_LeaderPlayer_TeamIdInvalid":        604,
		"AppointedJobTitle_TeamIdInvalid":                     605,
		"AppointedJobTitle_TeamLeader_Nil":                    606,
		"AppointedJobTitle_TeamLeaderGuid_Dif":                607,
		"AppointedJobTitle_OpPlayer_NoTeam":                   608,
		"AppointedJobTitle_InvalidJobTitle":                   609,
		"AppointedJobTitle_Team_Lock_Faild":                   610,
		"AppointedJobTitle_OpPlayer_Lock_Faild":               611,
		"AppointedJobTitle_LeaderPlayer_Lock_Faild":           612,
		"TCAskTeamInfoListByTeamIdList_Suc":                   700,
		"TCAskTeamInfoListByTeamIdList_Load_Error":            701,
		"TCAskTeamInfoListByTeamIdList_OverTime":              702,
		"TCAskTeamInfoListByTeamIdList_AcqLockFaild":          703,
		"RobotJoinTeam_Suc":                                   800,
		"RobotJoinTeam_CheckTeam_GetTeamInfoFaild":            801,
		"RobotJoinTeam_CheckTeam_TeamTypeInvalid":             802,
		"RobotJoinTeam_CheckTeam_TeamFull":                    803,
		"RobotJoinTeam_CheckTeam_OpPlayerInvalid":             804,
		"RobotJoinTeam_CheckTeam_TeamIdInvalid":               805,
		"RobotJoinTeam_CheckTeam_Team_Lock_Faild":             806,
		"RobotJoinTeam_CheckPlayerTeamId_newTeamMemListEmpty": 807,
		"RobotLeaveTeam_Suc":                                  900,
		"RobotLeaveTeam_Team_Lock_Faild":                      901,
		"RobotLeaveTeam_TeamID_Invalid":                       902,
	}
)

func (x ENUM_TEAMCENTER_OP) Enum() *ENUM_TEAMCENTER_OP {
	p := new(ENUM_TEAMCENTER_OP)
	*p = x
	return p
}

func (x ENUM_TEAMCENTER_OP) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ENUM_TEAMCENTER_OP) Descriptor() protoreflect.EnumDescriptor {
	return file_v1_teamcenter_proto_enumTypes[1].Descriptor()
}

func (ENUM_TEAMCENTER_OP) Type() protoreflect.EnumType {
	return &file_v1_teamcenter_proto_enumTypes[1]
}

func (x ENUM_TEAMCENTER_OP) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ENUM_TEAMCENTER_OP.Descriptor instead.
func (ENUM_TEAMCENTER_OP) EnumDescriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{1}
}

// Type:Http
// Type:Inner
type TCTeamMember struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TeamId        uint64                 `protobuf:"varint,1,opt,name=TeamId,proto3" json:"TeamId,omitempty"`
	PlayerGuid    uint64                 `protobuf:"varint,2,opt,name=PlayerGuid,proto3" json:"PlayerGuid,omitempty"`
	PlayerIndex   int32                  `protobuf:"varint,3,opt,name=PlayerIndex,proto3" json:"PlayerIndex,omitempty"`
	PlayerTitle   int32                  `protobuf:"varint,4,opt,name=PlayerTitle,proto3" json:"PlayerTitle,omitempty"`
	PlayerType    int32                  `protobuf:"varint,5,opt,name=PlayerType,proto3" json:"PlayerType,omitempty"`
	ZoneWorldID   int32                  `protobuf:"varint,6,opt,name=ZoneWorldID,proto3" json:"ZoneWorldID,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TCTeamMember) Reset() {
	*x = TCTeamMember{}
	mi := &file_v1_teamcenter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCTeamMember) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCTeamMember) ProtoMessage() {}

func (x *TCTeamMember) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCTeamMember.ProtoReflect.Descriptor instead.
func (*TCTeamMember) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{0}
}

func (x *TCTeamMember) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *TCTeamMember) GetPlayerGuid() uint64 {
	if x != nil {
		return x.PlayerGuid
	}
	return 0
}

func (x *TCTeamMember) GetPlayerIndex() int32 {
	if x != nil {
		return x.PlayerIndex
	}
	return 0
}

func (x *TCTeamMember) GetPlayerTitle() int32 {
	if x != nil {
		return x.PlayerTitle
	}
	return 0
}

func (x *TCTeamMember) GetPlayerType() int32 {
	if x != nil {
		return x.PlayerType
	}
	return 0
}

func (x *TCTeamMember) GetZoneWorldID() int32 {
	if x != nil {
		return x.ZoneWorldID
	}
	return 0
}

// Type:Http
// Type:Inner
type TCTeamInfo struct {
	state           protoimpl.MessageState  `protogen:"open.v1"`
	TeamId          uint64                  `protobuf:"varint,1,opt,name=TeamId,proto3" json:"TeamId,omitempty"`
	TeamType        int32                   `protobuf:"varint,2,opt,name=TeamType,proto3" json:"TeamType,omitempty"`
	LeaderIndex     int32                   `protobuf:"varint,3,opt,name=LeaderIndex,proto3" json:"LeaderIndex,omitempty"`
	TCTeamMemberDic map[int64]*TCTeamMember `protobuf:"bytes,4,rep,name=TCTeamMemberDic,proto3" json:"TCTeamMemberDic,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *TCTeamInfo) Reset() {
	*x = TCTeamInfo{}
	mi := &file_v1_teamcenter_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCTeamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCTeamInfo) ProtoMessage() {}

func (x *TCTeamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCTeamInfo.ProtoReflect.Descriptor instead.
func (*TCTeamInfo) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{1}
}

func (x *TCTeamInfo) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *TCTeamInfo) GetTeamType() int32 {
	if x != nil {
		return x.TeamType
	}
	return 0
}

func (x *TCTeamInfo) GetLeaderIndex() int32 {
	if x != nil {
		return x.LeaderIndex
	}
	return 0
}

func (x *TCTeamInfo) GetTCTeamMemberDic() map[int64]*TCTeamMember {
	if x != nil {
		return x.TCTeamMemberDic
	}
	return nil
}

// Type:Inner
// Target:W2S
type TCTeamInfoReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OpState       int32                  `protobuf:"varint,1,opt,name=opState,proto3" json:"opState,omitempty"`
	TCTeamInfoEle *TCTeamInfo            `protobuf:"bytes,2,opt,name=TCTeamInfoEle,proto3" json:"TCTeamInfoEle,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TCTeamInfoReply) Reset() {
	*x = TCTeamInfoReply{}
	mi := &file_v1_teamcenter_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCTeamInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCTeamInfoReply) ProtoMessage() {}

func (x *TCTeamInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCTeamInfoReply.ProtoReflect.Descriptor instead.
func (*TCTeamInfoReply) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{2}
}

func (x *TCTeamInfoReply) GetOpState() int32 {
	if x != nil {
		return x.OpState
	}
	return 0
}

func (x *TCTeamInfoReply) GetTCTeamInfoEle() *TCTeamInfo {
	if x != nil {
		return x.TCTeamInfoEle
	}
	return nil
}

// Type:Inner
// Target:W2S
type TCTeamInfoListReply struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	OpState           int32                  `protobuf:"varint,1,opt,name=opState,proto3" json:"opState,omitempty"`
	TCTeamInfoEleList []*TCTeamInfo          `protobuf:"bytes,2,rep,name=TCTeamInfoEleList,proto3" json:"TCTeamInfoEleList,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TCTeamInfoListReply) Reset() {
	*x = TCTeamInfoListReply{}
	mi := &file_v1_teamcenter_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCTeamInfoListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCTeamInfoListReply) ProtoMessage() {}

func (x *TCTeamInfoListReply) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCTeamInfoListReply.ProtoReflect.Descriptor instead.
func (*TCTeamInfoListReply) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{3}
}

func (x *TCTeamInfoListReply) GetOpState() int32 {
	if x != nil {
		return x.OpState
	}
	return 0
}

func (x *TCTeamInfoListReply) GetTCTeamInfoEleList() []*TCTeamInfo {
	if x != nil {
		return x.TCTeamInfoEleList
	}
	return nil
}

// Type:Http
// Type:Inner
// Target:S2W
// Response W2S_TCTeamInfoReply
type TCAskTeamInfoByTeamIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TeamId        uint64                 `protobuf:"varint,1,opt,name=teamId,proto3" json:"teamId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TCAskTeamInfoByTeamIdRequest) Reset() {
	*x = TCAskTeamInfoByTeamIdRequest{}
	mi := &file_v1_teamcenter_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCAskTeamInfoByTeamIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCAskTeamInfoByTeamIdRequest) ProtoMessage() {}

func (x *TCAskTeamInfoByTeamIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCAskTeamInfoByTeamIdRequest.ProtoReflect.Descriptor instead.
func (*TCAskTeamInfoByTeamIdRequest) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{4}
}

func (x *TCAskTeamInfoByTeamIdRequest) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

// Type:Http
// Type:Inner
// Target:S2W
// Response W2S_TCTeamInfoReply
type TCAskTeamInfoByPlayerGuidRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlayerGuid    uint64                 `protobuf:"varint,1,opt,name=playerGuid,proto3" json:"playerGuid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TCAskTeamInfoByPlayerGuidRequest) Reset() {
	*x = TCAskTeamInfoByPlayerGuidRequest{}
	mi := &file_v1_teamcenter_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCAskTeamInfoByPlayerGuidRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCAskTeamInfoByPlayerGuidRequest) ProtoMessage() {}

func (x *TCAskTeamInfoByPlayerGuidRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCAskTeamInfoByPlayerGuidRequest.ProtoReflect.Descriptor instead.
func (*TCAskTeamInfoByPlayerGuidRequest) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{5}
}

func (x *TCAskTeamInfoByPlayerGuidRequest) GetPlayerGuid() uint64 {
	if x != nil {
		return x.PlayerGuid
	}
	return 0
}

// Type:Http
// Type:Inner
// Target:S2W
// Response W2S_TCTeamInfoReply
type TCCreateTeamRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TCTeamInfoEle *TCTeamInfo            `protobuf:"bytes,1,opt,name=TCTeamInfoEle,proto3" json:"TCTeamInfoEle,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TCCreateTeamRequest) Reset() {
	*x = TCCreateTeamRequest{}
	mi := &file_v1_teamcenter_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCCreateTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCCreateTeamRequest) ProtoMessage() {}

func (x *TCCreateTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCCreateTeamRequest.ProtoReflect.Descriptor instead.
func (*TCCreateTeamRequest) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{6}
}

func (x *TCCreateTeamRequest) GetTCTeamInfoEle() *TCTeamInfo {
	if x != nil {
		return x.TCTeamInfoEle
	}
	return nil
}

// Type:Http
// Type:Inner
// Target:S2W
// Response W2S_TCTeamInfoReply
type TCPlayerJoinTeamRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TeamId           uint64                 `protobuf:"varint,1,opt,name=TeamId,proto3" json:"TeamId,omitempty"`
	TeamMaxCount     int32                  `protobuf:"varint,2,opt,name=TeamMaxCount,proto3" json:"TeamMaxCount,omitempty"`
	TeamType         int32                  `protobuf:"varint,3,opt,name=TeamType,proto3" json:"TeamType,omitempty"`
	OpPlayerGuid     uint64                 `protobuf:"varint,4,opt,name=opPlayerGuid,proto3" json:"opPlayerGuid,omitempty"`
	TCTeamMemberList []*TCTeamMember        `protobuf:"bytes,5,rep,name=TCTeamMemberList,proto3" json:"TCTeamMemberList,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *TCPlayerJoinTeamRequest) Reset() {
	*x = TCPlayerJoinTeamRequest{}
	mi := &file_v1_teamcenter_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCPlayerJoinTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCPlayerJoinTeamRequest) ProtoMessage() {}

func (x *TCPlayerJoinTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCPlayerJoinTeamRequest.ProtoReflect.Descriptor instead.
func (*TCPlayerJoinTeamRequest) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{7}
}

func (x *TCPlayerJoinTeamRequest) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *TCPlayerJoinTeamRequest) GetTeamMaxCount() int32 {
	if x != nil {
		return x.TeamMaxCount
	}
	return 0
}

func (x *TCPlayerJoinTeamRequest) GetTeamType() int32 {
	if x != nil {
		return x.TeamType
	}
	return 0
}

func (x *TCPlayerJoinTeamRequest) GetOpPlayerGuid() uint64 {
	if x != nil {
		return x.OpPlayerGuid
	}
	return 0
}

func (x *TCPlayerJoinTeamRequest) GetTCTeamMemberList() []*TCTeamMember {
	if x != nil {
		return x.TCTeamMemberList
	}
	return nil
}

// Type:Http
// Type:Inner
// Target:S2W
// Response W2S_TCTeamInfoReply
type TCPlayerLeaveTeamRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TeamId           uint64                 `protobuf:"varint,1,opt,name=TeamId,proto3" json:"TeamId,omitempty"`
	TeamMaxCount     int32                  `protobuf:"varint,2,opt,name=TeamMaxCount,proto3" json:"TeamMaxCount,omitempty"`
	TCTeamMemberList []*TCTeamMember        `protobuf:"bytes,3,rep,name=TCTeamMemberList,proto3" json:"TCTeamMemberList,omitempty"` // 这个list中的玩家都必须在同一个队伍中
	NewLeaderGuid    uint64                 `protobuf:"varint,4,opt,name=newLeaderGuid,proto3" json:"newLeaderGuid,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *TCPlayerLeaveTeamRequest) Reset() {
	*x = TCPlayerLeaveTeamRequest{}
	mi := &file_v1_teamcenter_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCPlayerLeaveTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCPlayerLeaveTeamRequest) ProtoMessage() {}

func (x *TCPlayerLeaveTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCPlayerLeaveTeamRequest.ProtoReflect.Descriptor instead.
func (*TCPlayerLeaveTeamRequest) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{8}
}

func (x *TCPlayerLeaveTeamRequest) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *TCPlayerLeaveTeamRequest) GetTeamMaxCount() int32 {
	if x != nil {
		return x.TeamMaxCount
	}
	return 0
}

func (x *TCPlayerLeaveTeamRequest) GetTCTeamMemberList() []*TCTeamMember {
	if x != nil {
		return x.TCTeamMemberList
	}
	return nil
}

func (x *TCPlayerLeaveTeamRequest) GetNewLeaderGuid() uint64 {
	if x != nil {
		return x.NewLeaderGuid
	}
	return 0
}

// Type:Http
// Type:Inner
// Target:S2W
// Response W2S_TCTeamInfoReply
type TCRobotJoinTeamRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TeamId           uint64                 `protobuf:"varint,1,opt,name=TeamId,proto3" json:"TeamId,omitempty"`
	TeamMaxCount     int32                  `protobuf:"varint,2,opt,name=TeamMaxCount,proto3" json:"TeamMaxCount,omitempty"`
	TeamType         int32                  `protobuf:"varint,3,opt,name=TeamType,proto3" json:"TeamType,omitempty"`
	OpPlayerGuid     uint64                 `protobuf:"varint,4,opt,name=opPlayerGuid,proto3" json:"opPlayerGuid,omitempty"`
	TCTeamMemberList []*TCTeamMember        `protobuf:"bytes,5,rep,name=TCTeamMemberList,proto3" json:"TCTeamMemberList,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *TCRobotJoinTeamRequest) Reset() {
	*x = TCRobotJoinTeamRequest{}
	mi := &file_v1_teamcenter_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCRobotJoinTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCRobotJoinTeamRequest) ProtoMessage() {}

func (x *TCRobotJoinTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCRobotJoinTeamRequest.ProtoReflect.Descriptor instead.
func (*TCRobotJoinTeamRequest) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{9}
}

func (x *TCRobotJoinTeamRequest) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *TCRobotJoinTeamRequest) GetTeamMaxCount() int32 {
	if x != nil {
		return x.TeamMaxCount
	}
	return 0
}

func (x *TCRobotJoinTeamRequest) GetTeamType() int32 {
	if x != nil {
		return x.TeamType
	}
	return 0
}

func (x *TCRobotJoinTeamRequest) GetOpPlayerGuid() uint64 {
	if x != nil {
		return x.OpPlayerGuid
	}
	return 0
}

func (x *TCRobotJoinTeamRequest) GetTCTeamMemberList() []*TCTeamMember {
	if x != nil {
		return x.TCTeamMemberList
	}
	return nil
}

// Type:Http
// Type:Inner
// Target:S2W
// Response W2S_TCTeamInfoReply
type TCRobotLeaveTeamRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TeamId           uint64                 `protobuf:"varint,1,opt,name=TeamId,proto3" json:"TeamId,omitempty"`
	TeamMaxCount     int32                  `protobuf:"varint,2,opt,name=TeamMaxCount,proto3" json:"TeamMaxCount,omitempty"`
	TCTeamMemberList []*TCTeamMember        `protobuf:"bytes,3,rep,name=TCTeamMemberList,proto3" json:"TCTeamMemberList,omitempty"` // 这个list中的玩家都必须在同一个队伍中
	NewLeaderGuid    uint64                 `protobuf:"varint,4,opt,name=newLeaderGuid,proto3" json:"newLeaderGuid,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *TCRobotLeaveTeamRequest) Reset() {
	*x = TCRobotLeaveTeamRequest{}
	mi := &file_v1_teamcenter_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCRobotLeaveTeamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCRobotLeaveTeamRequest) ProtoMessage() {}

func (x *TCRobotLeaveTeamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCRobotLeaveTeamRequest.ProtoReflect.Descriptor instead.
func (*TCRobotLeaveTeamRequest) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{10}
}

func (x *TCRobotLeaveTeamRequest) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *TCRobotLeaveTeamRequest) GetTeamMaxCount() int32 {
	if x != nil {
		return x.TeamMaxCount
	}
	return 0
}

func (x *TCRobotLeaveTeamRequest) GetTCTeamMemberList() []*TCTeamMember {
	if x != nil {
		return x.TCTeamMemberList
	}
	return nil
}

func (x *TCRobotLeaveTeamRequest) GetNewLeaderGuid() uint64 {
	if x != nil {
		return x.NewLeaderGuid
	}
	return 0
}

// Type:Http
// Type:Inner
// Target:S2W
// Response W2S_TCTeamInfoReply
type TCTeamAppointedJobTitleRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	TeamId           uint64                 `protobuf:"varint,1,opt,name=TeamId,proto3" json:"TeamId,omitempty"`
	CurLeaderGuid    uint64                 `protobuf:"varint,2,opt,name=curLeaderGuid,proto3" json:"curLeaderGuid,omitempty"`
	OpPlayerGuid     uint64                 `protobuf:"varint,3,opt,name=opPlayerGuid,proto3" json:"opPlayerGuid,omitempty"`
	OpPlayerJobTitle int32                  `protobuf:"varint,4,opt,name=opPlayerJobTitle,proto3" json:"opPlayerJobTitle,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *TCTeamAppointedJobTitleRequest) Reset() {
	*x = TCTeamAppointedJobTitleRequest{}
	mi := &file_v1_teamcenter_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCTeamAppointedJobTitleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCTeamAppointedJobTitleRequest) ProtoMessage() {}

func (x *TCTeamAppointedJobTitleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCTeamAppointedJobTitleRequest.ProtoReflect.Descriptor instead.
func (*TCTeamAppointedJobTitleRequest) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{11}
}

func (x *TCTeamAppointedJobTitleRequest) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *TCTeamAppointedJobTitleRequest) GetCurLeaderGuid() uint64 {
	if x != nil {
		return x.CurLeaderGuid
	}
	return 0
}

func (x *TCTeamAppointedJobTitleRequest) GetOpPlayerGuid() uint64 {
	if x != nil {
		return x.OpPlayerGuid
	}
	return 0
}

func (x *TCTeamAppointedJobTitleRequest) GetOpPlayerJobTitle() int32 {
	if x != nil {
		return x.OpPlayerJobTitle
	}
	return 0
}

// Type:Http
// Type:Inner
// Target:S2W
// Response W2S_TCTeamInfoListReply
type TCAskTeamInfoListByTeamIdListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TeamIdList    []uint64               `protobuf:"varint,1,rep,packed,name=TeamIdList,proto3" json:"TeamIdList,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TCAskTeamInfoListByTeamIdListRequest) Reset() {
	*x = TCAskTeamInfoListByTeamIdListRequest{}
	mi := &file_v1_teamcenter_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TCAskTeamInfoListByTeamIdListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCAskTeamInfoListByTeamIdListRequest) ProtoMessage() {}

func (x *TCAskTeamInfoListByTeamIdListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_v1_teamcenter_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCAskTeamInfoListByTeamIdListRequest.ProtoReflect.Descriptor instead.
func (*TCAskTeamInfoListByTeamIdListRequest) Descriptor() ([]byte, []int) {
	return file_v1_teamcenter_proto_rawDescGZIP(), []int{12}
}

func (x *TCAskTeamInfoListByTeamIdListRequest) GetTeamIdList() []uint64 {
	if x != nil {
		return x.TeamIdList
	}
	return nil
}

var File_v1_teamcenter_proto protoreflect.FileDescriptor

var file_v1_teamcenter_proto_rawDesc = []byte{
	0x0a, 0x13, 0x76, 0x31, 0x2f, 0x74, 0x65, 0x61, 0x6d, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65,
	0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcc, 0x01, 0x0a, 0x0c, 0x54, 0x43, 0x54, 0x65, 0x61,
	0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12,
	0x20, 0x0a, 0x0b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x12, 0x20, 0x0a, 0x0b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x5a, 0x6f, 0x6e, 0x65, 0x57, 0x6f, 0x72, 0x6c, 0x64,
	0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x5a, 0x6f, 0x6e, 0x65, 0x57, 0x6f,
	0x72, 0x6c, 0x64, 0x49, 0x44, 0x22, 0xa5, 0x02, 0x0a, 0x0a, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x54, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x54, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x4c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x4c,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x5c, 0x0a, 0x0f, 0x54, 0x43,
	0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x69, 0x63, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61,
	0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x44,
	0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0f, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x69, 0x63, 0x1a, 0x63, 0x0a, 0x14, 0x54, 0x43, 0x54, 0x65,
	0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x44, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x35, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x70, 0x0a,
	0x0f, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x07, 0x6f, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x0d, 0x54, 0x43,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0d, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6c, 0x65, 0x22,
	0x7c, 0x0a, 0x13, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x70, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x6f, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x4b, 0x0a, 0x11, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6c,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x41, 0x75,
	0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x54, 0x43, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x36, 0x0a,
	0x1c, 0x54, 0x43, 0x41, 0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a,
	0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x74,
	0x65, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x42, 0x0a, 0x20, 0x54, 0x43, 0x41, 0x73, 0x6b, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75,
	0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x22, 0x5a, 0x0a, 0x13, 0x54, 0x43, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x43, 0x0a, 0x0d, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61,
	0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x45, 0x6c, 0x65, 0x22, 0xe2, 0x01, 0x0a, 0x17, 0x54, 0x43, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x54, 0x65, 0x61,
	0x6d, 0x4d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x6f, 0x70, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12, 0x4b, 0x0a,
	0x10, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61,
	0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65,
	0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x10, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc9, 0x01, 0x0a, 0x18, 0x54,
	0x43, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x54, 0x65, 0x61, 0x6d,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x61, 0x78, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x10, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65,
	0x72, 0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x10,
	0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x24, 0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x47, 0x75, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x6e, 0x65, 0x77, 0x4c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x22, 0xe1, 0x01, 0x0a, 0x16, 0x54, 0x43, 0x52, 0x6f, 0x62,
	0x6f, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x54, 0x65, 0x61,
	0x6d, 0x4d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0c, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0c, 0x6f, 0x70, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12, 0x4b, 0x0a,
	0x10, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61,
	0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65,
	0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x10, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc8, 0x01, 0x0a, 0x17, 0x54,
	0x43, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x61, 0x78, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x61, 0x78, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x10, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62,
	0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x10, 0x54,
	0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12,
	0x24, 0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x6e, 0x65, 0x77, 0x4c, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x47, 0x75, 0x69, 0x64, 0x22, 0xae, 0x01, 0x0a, 0x1e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d,
	0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x47, 0x75, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x4c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0c, 0x6f, 0x70,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12, 0x2a, 0x0a, 0x10, 0x6f, 0x70,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x6f, 0x70, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4a, 0x6f,
	0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x22, 0x46, 0x0a, 0x24, 0x54, 0x43, 0x41, 0x73, 0x6b, 0x54,
	0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x54, 0x65, 0x61,
	0x6d, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e,
	0x0a, 0x0a, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x0a, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x2a, 0x47,
	0x0a, 0x0d, 0x45, 0x54, 0x65, 0x61, 0x6d, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x0c, 0x0a, 0x08, 0x65, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x65, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x65, 0x53,
	0x75, 0x62, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x65, 0x4d,
	0x6d, 0x65, 0x62, 0x65, 0x72, 0x10, 0x03, 0x2a, 0x9c, 0x17, 0x0a, 0x12, 0x45, 0x4e, 0x55, 0x4d,
	0x5f, 0x54, 0x45, 0x41, 0x4d, 0x43, 0x45, 0x4e, 0x54, 0x45, 0x52, 0x5f, 0x4f, 0x50, 0x12, 0x08,
	0x0a, 0x04, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x75, 0x63, 0x10,
	0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x73, 0x61, 0x76, 0x65, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x4d,
	0x65, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x6f, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x53, 0x75, 0x63,
	0x10, 0x0a, 0x12, 0x27, 0x0a, 0x23, 0x73, 0x61, 0x76, 0x65, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d,
	0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x54, 0x6f, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x52, 0x65,
	0x64, 0x69, 0x73, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x10, 0x0b, 0x12, 0x1d, 0x0a, 0x19, 0x73,
	0x61, 0x76, 0x65, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x6f, 0x52,
	0x65, 0x64, 0x69, 0x73, 0x5f, 0x53, 0x75, 0x63, 0x10, 0x14, 0x12, 0x2b, 0x0a, 0x27, 0x73, 0x61,
	0x76, 0x65, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x6f, 0x52, 0x65,
	0x64, 0x69, 0x73, 0x5f, 0x4a, 0x73, 0x6f, 0x6e, 0x4d, 0x61, 0x72, 0x73, 0x68, 0x61, 0x6c, 0x5f,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x15, 0x12, 0x25, 0x0a, 0x21, 0x73, 0x61, 0x76, 0x65, 0x54,
	0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x6f, 0x52, 0x65, 0x64, 0x69, 0x73,
	0x5f, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x16, 0x12, 0x30,
	0x0a, 0x2c, 0x73, 0x61, 0x76, 0x65, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f,
	0x54, 0x6f, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x73, 0x61, 0x76, 0x65, 0x54, 0x43, 0x54, 0x65,
	0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x17,
	0x12, 0x1f, 0x0a, 0x1b, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x53, 0x75, 0x63, 0x10,
	0x1e, 0x12, 0x29, 0x0a, 0x25, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x54, 0x65, 0x61,
	0x6d, 0x49, 0x64, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x1f, 0x12, 0x26, 0x0a, 0x22,
	0x6c, 0x6f, 0x61, 0x64, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x72,
	0x6f, 0x6d, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x52, 0x65, 0x64, 0x69, 0x73, 0x45, 0x72, 0x72,
	0x6f, 0x72, 0x10, 0x20, 0x12, 0x2f, 0x0a, 0x2b, 0x6c, 0x6f, 0x61, 0x64, 0x54, 0x43, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f,
	0x4a, 0x73, 0x6f, 0x6e, 0x55, 0x6e, 0x6d, 0x61, 0x72, 0x73, 0x68, 0x61, 0x6c, 0x5f, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x10, 0x21, 0x12, 0x21, 0x0a, 0x1d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54,
	0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x64,
	0x69, 0x73, 0x5f, 0x53, 0x75, 0x63, 0x10, 0x28, 0x12, 0x29, 0x0a, 0x25, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x72, 0x6f, 0x6d,
	0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x10, 0x29, 0x12, 0x38, 0x0a, 0x34, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x43, 0x54,
	0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x64, 0x69, 0x73,
	0x5f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x5f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x2a, 0x12, 0x20, 0x0a,
	0x1c, 0x67, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64,
	0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x53, 0x75, 0x63, 0x10, 0x32, 0x12,
	0x28, 0x0a, 0x24, 0x67, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x64, 0x46, 0x72, 0x6f, 0x6d, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x52, 0x65, 0x64, 0x69,
	0x73, 0x5f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x33, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x43, 0x41,
	0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x64, 0x52, 0x70, 0x5f, 0x53, 0x75, 0x63, 0x10, 0x64, 0x12, 0x24, 0x0a, 0x20, 0x54, 0x43,
	0x41, 0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x65, 0x61,
	0x6d, 0x49, 0x64, 0x52, 0x70, 0x5f, 0x4f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x10, 0x65,
	0x12, 0x28, 0x0a, 0x24, 0x54, 0x43, 0x41, 0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x79, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x52, 0x70, 0x5f, 0x41, 0x63, 0x71, 0x4c,
	0x6f, 0x63, 0x6b, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x10, 0x66, 0x12, 0x24, 0x0a, 0x1f, 0x54, 0x43,
	0x41, 0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x52, 0x70, 0x5f, 0x53, 0x75, 0x63, 0x10, 0xc8, 0x01,
	0x12, 0x29, 0x0a, 0x24, 0x54, 0x43, 0x41, 0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x42, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x52, 0x70, 0x5f,
	0x4f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x10, 0xc9, 0x01, 0x12, 0x34, 0x0a, 0x2f, 0x54,
	0x43, 0x41, 0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x52, 0x70, 0x5f, 0x41, 0x63, 0x71, 0x4c, 0x6f,
	0x63, 0x6b, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x10, 0xca,
	0x01, 0x12, 0x32, 0x0a, 0x2d, 0x54, 0x43, 0x41, 0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x42, 0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x52, 0x70,
	0x5f, 0x41, 0x63, 0x71, 0x4c, 0x6f, 0x63, 0x6b, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x5f, 0x54, 0x65,
	0x61, 0x6d, 0x10, 0xcb, 0x01, 0x12, 0x13, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x65, 0x61, 0x6d, 0x5f, 0x53, 0x75, 0x63, 0x10, 0xac, 0x02, 0x12, 0x22, 0x0a, 0x1d, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x77, 0x54,
	0x65, 0x61, 0x6d, 0x49, 0x64, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x10, 0xad, 0x02, 0x12, 0x2e,
	0x0a, 0x29, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x53, 0x61, 0x76,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x5f, 0x4a, 0x73, 0x6f, 0x6e, 0x4d, 0x61,
	0x72, 0x73, 0x68, 0x61, 0x6c, 0x5f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0xae, 0x02, 0x12, 0x28,
	0x0a, 0x23, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x53, 0x61, 0x76,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x5f, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0xaf, 0x02, 0x12, 0x2c, 0x0a, 0x27, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x53, 0x61, 0x76, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x5f, 0x52, 0x65, 0x64, 0x69, 0x73, 0x5f, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x10, 0xb0, 0x02, 0x12, 0x2f, 0x0a, 0x2a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x48, 0x61, 0x73,
	0x54, 0x65, 0x61, 0x6d, 0x10, 0xb1, 0x02, 0x12, 0x36, 0x0a, 0x31, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x5f, 0x47, 0x65, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x4c,
	0x6f, 0x63, 0x6b, 0x4b, 0x65, 0x79, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x10, 0xb2, 0x02, 0x12,
	0x3b, 0x0a, 0x36, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x5f,
	0x4c, 0x6f, 0x63, 0x6b, 0x4b, 0x65, 0x79, 0x5f, 0x41, 0x63, 0x71, 0x75, 0x69, 0x72, 0x65, 0x4c,
	0x6f, 0x63, 0x6b, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x10, 0xb3, 0x02, 0x12, 0x11, 0x0a, 0x0c,
	0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x53, 0x75, 0x63, 0x10, 0x90, 0x03, 0x12,
	0x25, 0x0a, 0x20, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x5f, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x10, 0x91, 0x03, 0x12, 0x2d, 0x0a, 0x28, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65,
	0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x64, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x48, 0x61, 0x73, 0x54, 0x65,
	0x61, 0x6d, 0x10, 0x92, 0x03, 0x12, 0x33, 0x0a, 0x2e, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61,
	0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x54, 0x65, 0x61,
	0x6d, 0x49, 0x64, 0x5f, 0x6e, 0x65, 0x77, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x10, 0x93, 0x03, 0x12, 0x28, 0x0a, 0x23, 0x4a, 0x6f,
	0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d,
	0x5f, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x61, 0x69, 0x6c,
	0x64, 0x10, 0x94, 0x03, 0x12, 0x27, 0x0a, 0x22, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d,
	0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x45, 0x78, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x10, 0x95, 0x03, 0x12, 0x27, 0x0a,
	0x22, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54,
	0x65, 0x61, 0x6d, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x10, 0x96, 0x03, 0x12, 0x20, 0x0a, 0x1b, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65,
	0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x54, 0x65, 0x61,
	0x6d, 0x46, 0x75, 0x6c, 0x6c, 0x10, 0x97, 0x03, 0x12, 0x27, 0x0a, 0x22, 0x4a, 0x6f, 0x69, 0x6e,
	0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x4f,
	0x70, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x98,
	0x03, 0x12, 0x25, 0x0a, 0x20, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x49, 0x6e,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x99, 0x03, 0x12, 0x27, 0x0a, 0x22, 0x4a, 0x6f, 0x69, 0x6e,
	0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x54,
	0x65, 0x61, 0x6d, 0x5f, 0x4c, 0x6f, 0x63, 0x6b, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x10, 0x9a,
	0x03, 0x12, 0x12, 0x0a, 0x0d, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x53,
	0x75, 0x63, 0x10, 0xf4, 0x03, 0x12, 0x1e, 0x0a, 0x19, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x54, 0x65,
	0x61, 0x6d, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x4c, 0x6f, 0x63, 0x6b, 0x5f, 0x46, 0x61, 0x69,
	0x6c, 0x64, 0x10, 0xf5, 0x03, 0x12, 0x1a, 0x0a, 0x15, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x53, 0x75, 0x63, 0x10, 0xd8,
	0x04, 0x12, 0x25, 0x0a, 0x20, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x65, 0x64, 0x4a, 0x6f,
	0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x4f, 0x70, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0xd9, 0x04, 0x12, 0x2d, 0x0a, 0x28, 0x41, 0x70, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x4f, 0x70,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x49, 0x6e, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x10, 0xda, 0x04, 0x12, 0x29, 0x0a, 0x24, 0x41, 0x70, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x4c, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10,
	0xdb, 0x04, 0x12, 0x31, 0x0a, 0x2c, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x65, 0x64, 0x4a,
	0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x49, 0x6e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x10, 0xdc, 0x04, 0x12, 0x24, 0x0a, 0x1f, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x49,
	0x64, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0xdd, 0x04, 0x12, 0x25, 0x0a, 0x20, 0x41,
	0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x5f, 0x54, 0x65, 0x61, 0x6d, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x4e, 0x69, 0x6c, 0x10,
	0xde, 0x04, 0x12, 0x29, 0x0a, 0x24, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x65, 0x64, 0x4a,
	0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x4c, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x5f, 0x44, 0x69, 0x66, 0x10, 0xdf, 0x04, 0x12, 0x26, 0x0a,
	0x21, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x5f, 0x4f, 0x70, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x5f, 0x4e, 0x6f, 0x54, 0x65,
	0x61, 0x6d, 0x10, 0xe0, 0x04, 0x12, 0x26, 0x0a, 0x21, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x49, 0x6e, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x10, 0xe1, 0x04, 0x12, 0x26, 0x0a,
	0x21, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x4c, 0x6f, 0x63, 0x6b, 0x5f, 0x46, 0x61, 0x69,
	0x6c, 0x64, 0x10, 0xe2, 0x04, 0x12, 0x2a, 0x0a, 0x25, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x4f, 0x70, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x5f, 0x4c, 0x6f, 0x63, 0x6b, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x10, 0xe3,
	0x04, 0x12, 0x2e, 0x0a, 0x29, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x65, 0x64, 0x4a, 0x6f,
	0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x4c, 0x65, 0x61, 0x64, 0x65, 0x72, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x5f, 0x4c, 0x6f, 0x63, 0x6b, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x10, 0xe4,
	0x04, 0x12, 0x26, 0x0a, 0x21, 0x54, 0x43, 0x41, 0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e,
	0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x4c, 0x69,
	0x73, 0x74, 0x5f, 0x53, 0x75, 0x63, 0x10, 0xbc, 0x05, 0x12, 0x2d, 0x0a, 0x28, 0x54, 0x43, 0x41,
	0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x5f, 0x4c, 0x6f, 0x61, 0x64, 0x5f,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0xbd, 0x05, 0x12, 0x2b, 0x0a, 0x26, 0x54, 0x43, 0x41, 0x73,
	0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x54,
	0x65, 0x61, 0x6d, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x5f, 0x4f, 0x76, 0x65, 0x72, 0x54, 0x69,
	0x6d, 0x65, 0x10, 0xbe, 0x05, 0x12, 0x2f, 0x0a, 0x2a, 0x54, 0x43, 0x41, 0x73, 0x6b, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x5f, 0x41, 0x63, 0x71, 0x4c, 0x6f, 0x63, 0x6b, 0x46, 0x61,
	0x69, 0x6c, 0x64, 0x10, 0xbf, 0x05, 0x12, 0x16, 0x0a, 0x11, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4a,
	0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x53, 0x75, 0x63, 0x10, 0xa0, 0x06, 0x12, 0x2d,
	0x0a, 0x28, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x5f,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x47, 0x65, 0x74, 0x54, 0x65, 0x61,
	0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x10, 0xa1, 0x06, 0x12, 0x2c, 0x0a,
	0x27, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0xa2, 0x06, 0x12, 0x25, 0x0a, 0x20, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x46, 0x75, 0x6c, 0x6c, 0x10,
	0xa3, 0x06, 0x12, 0x2c, 0x0a, 0x27, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x54,
	0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x4f, 0x70,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0xa4, 0x06,
	0x12, 0x2a, 0x0a, 0x25, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61,
	0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x64, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0xa5, 0x06, 0x12, 0x2c, 0x0a, 0x27,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x4c, 0x6f, 0x63,
	0x6b, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x10, 0xa6, 0x06, 0x12, 0x38, 0x0a, 0x33, 0x52, 0x6f,
	0x62, 0x6f, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x5f, 0x6e, 0x65,
	0x77, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x10, 0xa7, 0x06, 0x12, 0x17, 0x0a, 0x12, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4c, 0x65, 0x61,
	0x76, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x53, 0x75, 0x63, 0x10, 0x84, 0x07, 0x12, 0x23, 0x0a,
	0x1e, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x5f,
	0x54, 0x65, 0x61, 0x6d, 0x5f, 0x4c, 0x6f, 0x63, 0x6b, 0x5f, 0x46, 0x61, 0x69, 0x6c, 0x64, 0x10,
	0x85, 0x07, 0x12, 0x22, 0x0a, 0x1d, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4c, 0x65, 0x61, 0x76, 0x65,
	0x54, 0x65, 0x61, 0x6d, 0x5f, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x44, 0x5f, 0x49, 0x6e, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x10, 0x86, 0x07, 0x32, 0xb0, 0x08, 0x0a, 0x11, 0x54, 0x65, 0x61, 0x6d, 0x43,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x8e, 0x01, 0x0a,
	0x15, 0x54, 0x43, 0x41, 0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x2f, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e,
	0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x41, 0x73, 0x6b,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61,
	0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x20, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x1a, 0x3a, 0x01, 0x2a, 0x22, 0x15, 0x2f, 0x76, 0x31, 0x2f, 0x54, 0x65, 0x61, 0x6d,
	0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x42, 0x79, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x9a, 0x01,
	0x0a, 0x19, 0x54, 0x43, 0x41, 0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x79, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12, 0x33, 0x2e, 0x41, 0x75,
	0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x54, 0x43, 0x41, 0x73, 0x6b, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x79, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22,
	0x19, 0x2f, 0x76, 0x31, 0x2f, 0x54, 0x65, 0x61, 0x6d, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x42, 0x79,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x12, 0x5c, 0x0a, 0x0c, 0x54, 0x43,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x26, 0x2e, 0x41, 0x75, 0x72,
	0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54,
	0x43, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x22, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d,
	0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x10, 0x54, 0x43, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x2a, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72,
	0x2e, 0x54, 0x43, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61,
	0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54,
	0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x66,
	0x0a, 0x11, 0x54, 0x43, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x54,
	0x65, 0x61, 0x6d, 0x12, 0x2b, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61,
	0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x4c, 0x65, 0x61, 0x76, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x62, 0x0a, 0x0f, 0x54, 0x43, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x29, 0x2e, 0x41, 0x75, 0x72, 0x6f,
	0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4a, 0x6f, 0x69, 0x6e, 0x54, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65,
	0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x10, 0x54, 0x43,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x54, 0x65, 0x61, 0x6d, 0x12, 0x2a,
	0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x54, 0x43, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x4c, 0x65, 0x61, 0x76, 0x65, 0x54,
	0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x41, 0x75, 0x72,
	0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54,
	0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x12, 0x72, 0x0a, 0x17, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x65, 0x64, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x41, 0x75,
	0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e,
	0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x41, 0x70, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x65, 0x64, 0x4a,
	0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22,
	0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x00, 0x12, 0x82, 0x01, 0x0a, 0x1d, 0x54, 0x43, 0x41, 0x73, 0x6b, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x37, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e,
	0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x41, 0x73, 0x6b,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x79, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x64, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x26, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x43, 0x65, 0x6e,
	0x74, 0x65, 0x72, 0x2e, 0x54, 0x43, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x42, 0x21, 0x5a, 0x1f, 0x67, 0x61, 0x6d,
	0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x54, 0x65, 0x61, 0x6d,
	0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_v1_teamcenter_proto_rawDescOnce sync.Once
	file_v1_teamcenter_proto_rawDescData = file_v1_teamcenter_proto_rawDesc
)

func file_v1_teamcenter_proto_rawDescGZIP() []byte {
	file_v1_teamcenter_proto_rawDescOnce.Do(func() {
		file_v1_teamcenter_proto_rawDescData = protoimpl.X.CompressGZIP(file_v1_teamcenter_proto_rawDescData)
	})
	return file_v1_teamcenter_proto_rawDescData
}

var file_v1_teamcenter_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_v1_teamcenter_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_v1_teamcenter_proto_goTypes = []any{
	(ETeamJobTitle)(0),                           // 0: Aurora.TeamCenter.ETeamJobTitle
	(ENUM_TEAMCENTER_OP)(0),                      // 1: Aurora.TeamCenter.ENUM_TEAMCENTER_OP
	(*TCTeamMember)(nil),                         // 2: Aurora.TeamCenter.TCTeamMember
	(*TCTeamInfo)(nil),                           // 3: Aurora.TeamCenter.TCTeamInfo
	(*TCTeamInfoReply)(nil),                      // 4: Aurora.TeamCenter.TCTeamInfoReply
	(*TCTeamInfoListReply)(nil),                  // 5: Aurora.TeamCenter.TCTeamInfoListReply
	(*TCAskTeamInfoByTeamIdRequest)(nil),         // 6: Aurora.TeamCenter.TCAskTeamInfoByTeamIdRequest
	(*TCAskTeamInfoByPlayerGuidRequest)(nil),     // 7: Aurora.TeamCenter.TCAskTeamInfoByPlayerGuidRequest
	(*TCCreateTeamRequest)(nil),                  // 8: Aurora.TeamCenter.TCCreateTeamRequest
	(*TCPlayerJoinTeamRequest)(nil),              // 9: Aurora.TeamCenter.TCPlayerJoinTeamRequest
	(*TCPlayerLeaveTeamRequest)(nil),             // 10: Aurora.TeamCenter.TCPlayerLeaveTeamRequest
	(*TCRobotJoinTeamRequest)(nil),               // 11: Aurora.TeamCenter.TCRobotJoinTeamRequest
	(*TCRobotLeaveTeamRequest)(nil),              // 12: Aurora.TeamCenter.TCRobotLeaveTeamRequest
	(*TCTeamAppointedJobTitleRequest)(nil),       // 13: Aurora.TeamCenter.TCTeamAppointedJobTitleRequest
	(*TCAskTeamInfoListByTeamIdListRequest)(nil), // 14: Aurora.TeamCenter.TCAskTeamInfoListByTeamIdListRequest
	nil, // 15: Aurora.TeamCenter.TCTeamInfo.TCTeamMemberDicEntry
}
var file_v1_teamcenter_proto_depIdxs = []int32{
	15, // 0: Aurora.TeamCenter.TCTeamInfo.TCTeamMemberDic:type_name -> Aurora.TeamCenter.TCTeamInfo.TCTeamMemberDicEntry
	3,  // 1: Aurora.TeamCenter.TCTeamInfoReply.TCTeamInfoEle:type_name -> Aurora.TeamCenter.TCTeamInfo
	3,  // 2: Aurora.TeamCenter.TCTeamInfoListReply.TCTeamInfoEleList:type_name -> Aurora.TeamCenter.TCTeamInfo
	3,  // 3: Aurora.TeamCenter.TCCreateTeamRequest.TCTeamInfoEle:type_name -> Aurora.TeamCenter.TCTeamInfo
	2,  // 4: Aurora.TeamCenter.TCPlayerJoinTeamRequest.TCTeamMemberList:type_name -> Aurora.TeamCenter.TCTeamMember
	2,  // 5: Aurora.TeamCenter.TCPlayerLeaveTeamRequest.TCTeamMemberList:type_name -> Aurora.TeamCenter.TCTeamMember
	2,  // 6: Aurora.TeamCenter.TCRobotJoinTeamRequest.TCTeamMemberList:type_name -> Aurora.TeamCenter.TCTeamMember
	2,  // 7: Aurora.TeamCenter.TCRobotLeaveTeamRequest.TCTeamMemberList:type_name -> Aurora.TeamCenter.TCTeamMember
	2,  // 8: Aurora.TeamCenter.TCTeamInfo.TCTeamMemberDicEntry.value:type_name -> Aurora.TeamCenter.TCTeamMember
	6,  // 9: Aurora.TeamCenter.TeamCenterService.TCAskTeamInfoByTeamId:input_type -> Aurora.TeamCenter.TCAskTeamInfoByTeamIdRequest
	7,  // 10: Aurora.TeamCenter.TeamCenterService.TCAskTeamInfoByPlayerGuid:input_type -> Aurora.TeamCenter.TCAskTeamInfoByPlayerGuidRequest
	8,  // 11: Aurora.TeamCenter.TeamCenterService.TCCreateTeam:input_type -> Aurora.TeamCenter.TCCreateTeamRequest
	9,  // 12: Aurora.TeamCenter.TeamCenterService.TCPlayerJoinTeam:input_type -> Aurora.TeamCenter.TCPlayerJoinTeamRequest
	10, // 13: Aurora.TeamCenter.TeamCenterService.TCPlayerLeaveTeam:input_type -> Aurora.TeamCenter.TCPlayerLeaveTeamRequest
	11, // 14: Aurora.TeamCenter.TeamCenterService.TCRobotJoinTeam:input_type -> Aurora.TeamCenter.TCRobotJoinTeamRequest
	12, // 15: Aurora.TeamCenter.TeamCenterService.TCRobotLeaveTeam:input_type -> Aurora.TeamCenter.TCRobotLeaveTeamRequest
	13, // 16: Aurora.TeamCenter.TeamCenterService.TCTeamAppointedJobTitle:input_type -> Aurora.TeamCenter.TCTeamAppointedJobTitleRequest
	14, // 17: Aurora.TeamCenter.TeamCenterService.TCAskTeamInfoListByTeamIdList:input_type -> Aurora.TeamCenter.TCAskTeamInfoListByTeamIdListRequest
	4,  // 18: Aurora.TeamCenter.TeamCenterService.TCAskTeamInfoByTeamId:output_type -> Aurora.TeamCenter.TCTeamInfoReply
	4,  // 19: Aurora.TeamCenter.TeamCenterService.TCAskTeamInfoByPlayerGuid:output_type -> Aurora.TeamCenter.TCTeamInfoReply
	4,  // 20: Aurora.TeamCenter.TeamCenterService.TCCreateTeam:output_type -> Aurora.TeamCenter.TCTeamInfoReply
	4,  // 21: Aurora.TeamCenter.TeamCenterService.TCPlayerJoinTeam:output_type -> Aurora.TeamCenter.TCTeamInfoReply
	4,  // 22: Aurora.TeamCenter.TeamCenterService.TCPlayerLeaveTeam:output_type -> Aurora.TeamCenter.TCTeamInfoReply
	4,  // 23: Aurora.TeamCenter.TeamCenterService.TCRobotJoinTeam:output_type -> Aurora.TeamCenter.TCTeamInfoReply
	4,  // 24: Aurora.TeamCenter.TeamCenterService.TCRobotLeaveTeam:output_type -> Aurora.TeamCenter.TCTeamInfoReply
	4,  // 25: Aurora.TeamCenter.TeamCenterService.TCTeamAppointedJobTitle:output_type -> Aurora.TeamCenter.TCTeamInfoReply
	5,  // 26: Aurora.TeamCenter.TeamCenterService.TCAskTeamInfoListByTeamIdList:output_type -> Aurora.TeamCenter.TCTeamInfoListReply
	18, // [18:27] is the sub-list for method output_type
	9,  // [9:18] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_v1_teamcenter_proto_init() }
func file_v1_teamcenter_proto_init() {
	if File_v1_teamcenter_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_v1_teamcenter_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_v1_teamcenter_proto_goTypes,
		DependencyIndexes: file_v1_teamcenter_proto_depIdxs,
		EnumInfos:         file_v1_teamcenter_proto_enumTypes,
		MessageInfos:      file_v1_teamcenter_proto_msgTypes,
	}.Build()
	File_v1_teamcenter_proto = out.File
	file_v1_teamcenter_proto_rawDesc = nil
	file_v1_teamcenter_proto_goTypes = nil
	file_v1_teamcenter_proto_depIdxs = nil
}
