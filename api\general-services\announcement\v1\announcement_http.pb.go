// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.28.1
// source: general-services/announcement/v1/announcement.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAnnouncementAddAnnouncementItemData = "/message.v1.Announcement/AddAnnouncementItemData"
const OperationAnnouncementDelAnnouncementItemData = "/message.v1.Announcement/DelAnnouncementItemData"
const OperationAnnouncementGetAnnouncementListData = "/message.v1.Announcement/GetAnnouncementListData"

type AnnouncementHTTPServer interface {
	AddAnnouncementItemData(context.Context, *NoticeItem) (*GetAnnouncementListReply, error)
	DelAnnouncementItemData(context.Context, *GetAnnouncementListReq) (*GetAnnouncementListReply, error)
	GetAnnouncementListData(context.Context, *GetAnnouncementListReq) (*GetAnnouncementListReply, error)
}

func RegisterAnnouncementHTTPServer(s *http.Server, srv AnnouncementHTTPServer) {
	r := s.Route("/")
	r.GET("/announcement/api/announcement/getannouncement", _Announcement_GetAnnouncementListData0_HTTP_Handler(srv))
	r.POST("/announcement/api/announcement/addannouncement", _Announcement_AddAnnouncementItemData0_HTTP_Handler(srv))
	r.GET("/announcement/api/announcement/delannouncement/{noticeid}", _Announcement_DelAnnouncementItemData0_HTTP_Handler(srv))
}

func _Announcement_GetAnnouncementListData0_HTTP_Handler(srv AnnouncementHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAnnouncementListReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnnouncementGetAnnouncementListData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetAnnouncementListData(ctx, req.(*GetAnnouncementListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAnnouncementListReply)
		return ctx.Result(200, reply)
	}
}

func _Announcement_AddAnnouncementItemData0_HTTP_Handler(srv AnnouncementHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in NoticeItem
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnnouncementAddAnnouncementItemData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddAnnouncementItemData(ctx, req.(*NoticeItem))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAnnouncementListReply)
		return ctx.Result(200, reply)
	}
}

func _Announcement_DelAnnouncementItemData0_HTTP_Handler(srv AnnouncementHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetAnnouncementListReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAnnouncementDelAnnouncementItemData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DelAnnouncementItemData(ctx, req.(*GetAnnouncementListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetAnnouncementListReply)
		return ctx.Result(200, reply)
	}
}

type AnnouncementHTTPClient interface {
	AddAnnouncementItemData(ctx context.Context, req *NoticeItem, opts ...http.CallOption) (rsp *GetAnnouncementListReply, err error)
	DelAnnouncementItemData(ctx context.Context, req *GetAnnouncementListReq, opts ...http.CallOption) (rsp *GetAnnouncementListReply, err error)
	GetAnnouncementListData(ctx context.Context, req *GetAnnouncementListReq, opts ...http.CallOption) (rsp *GetAnnouncementListReply, err error)
}

type AnnouncementHTTPClientImpl struct {
	cc *http.Client
}

func NewAnnouncementHTTPClient(client *http.Client) AnnouncementHTTPClient {
	return &AnnouncementHTTPClientImpl{client}
}

func (c *AnnouncementHTTPClientImpl) AddAnnouncementItemData(ctx context.Context, in *NoticeItem, opts ...http.CallOption) (*GetAnnouncementListReply, error) {
	var out GetAnnouncementListReply
	pattern := "/announcement/api/announcement/addannouncement"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationAnnouncementAddAnnouncementItemData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AnnouncementHTTPClientImpl) DelAnnouncementItemData(ctx context.Context, in *GetAnnouncementListReq, opts ...http.CallOption) (*GetAnnouncementListReply, error) {
	var out GetAnnouncementListReply
	pattern := "/announcement/api/announcement/delannouncement/{noticeid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAnnouncementDelAnnouncementItemData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *AnnouncementHTTPClientImpl) GetAnnouncementListData(ctx context.Context, in *GetAnnouncementListReq, opts ...http.CallOption) (*GetAnnouncementListReply, error) {
	var out GetAnnouncementListReply
	pattern := "/announcement/api/announcement/getannouncement"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAnnouncementGetAnnouncementListData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
