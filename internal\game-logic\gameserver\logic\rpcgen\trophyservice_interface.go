// Code generated by rpcparse. DO NOT EDIT.

package rpcgen

import (
	"context"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/game-logic/gameserver/logic/player"
)

type TrophyServiceInterface interface {
	RPCHandler
	// claimseasonreward
	ClaimSeasonReward(ctx context.Context, sender *player.Player, in *cs.CLClaimSeasonRewardReq) (out *cs.LCClaimSeasonRewardRsp)
	// seasoninfo
	SeasonInfo(ctx context.Context, sender *player.Player, in *cs.CLSeasonInfoReq) (out *cs.LCSeasonInfoRsp)
}
