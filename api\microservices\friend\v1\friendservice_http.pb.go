// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.19.1
// source: microservices/friend/v1/friendservice.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationFriendserviceAddFriendApply = "/Aurora.PlayerInfoServer.Friendservice/AddFriendApply"
const OperationFriendserviceAddFriendPlayer = "/Aurora.PlayerInfoServer.Friendservice/AddFriendPlayer"
const OperationFriendserviceApproveFriendApply = "/Aurora.PlayerInfoServer.Friendservice/ApproveFriendApply"
const OperationFriendserviceDelFriendPlayer = "/Aurora.PlayerInfoServer.Friendservice/DelFriendPlayer"
const OperationFriendserviceGetFriendApplyList = "/Aurora.PlayerInfoServer.Friendservice/GetFriendApplyList"
const OperationFriendserviceGetFriendPlayerList = "/Aurora.PlayerInfoServer.Friendservice/GetFriendPlayerList"
const OperationFriendserviceGetGroupList = "/Aurora.PlayerInfoServer.Friendservice/GetGroupList"
const OperationFriendserviceGetGroupPlayerListByGroupId = "/Aurora.PlayerInfoServer.Friendservice/GetGroupPlayerListByGroupId"
const OperationFriendserviceGetMyGroupInviteJoinGroupListRep = "/Aurora.PlayerInfoServer.Friendservice/GetMyGroupInviteJoinGroupListRep"
const OperationFriendserviceRemoveFriendApply = "/Aurora.PlayerInfoServer.Friendservice/RemoveFriendApply"
const OperationFriendserviceSetFriendBaseInfoRep = "/Aurora.PlayerInfoServer.Friendservice/SetFriendBaseInfoRep"
const OperationFriendserviceSetFriendPlayer = "/Aurora.PlayerInfoServer.Friendservice/SetFriendPlayer"

type FriendserviceHTTPServer interface {
	AddFriendApply(context.Context, *AddFriendApplyInfo) (*AddFriendApplyResult, error)
	AddFriendPlayer(context.Context, *AddFriendPlayerInfo) (*AddFriendResult, error)
	ApproveFriendApply(context.Context, *ApproveFriendApplyInfo) (*ApproveFriendApplyResult, error)
	DelFriendPlayer(context.Context, *DelFriendPlayerInfo) (*DelFriendResult, error)
	GetFriendApplyList(context.Context, *GetFriendApplyListInfo) (*GetFriendApplyListResult, error)
	GetFriendPlayerList(context.Context, *GetFriendPlayerListInfo) (*GetFriendPlayerListInfoResult, error)
	GetGroupList(context.Context, *GetGroupListInfo) (*GetGroupListResult, error)
	GetGroupPlayerListByGroupId(context.Context, *GetGroupPlayerListByGroupIdInfo) (*GetGroupPlayerListByGroupIdResult, error)
	GetMyGroupInviteJoinGroupListRep(context.Context, *GetMyGroupInviteJoinGroupList) (*GetMyGroupInviteJoinGroupListResult, error)
	RemoveFriendApply(context.Context, *RemoveFriendApplyInfo) (*RemoveFriendApplyResult, error)
	SetFriendBaseInfoRep(context.Context, *SetFriendBaseInfo) (*SetFriendBaseInfoResult, error)
	SetFriendPlayer(context.Context, *SetFriendPlayerInfo) (*SetFriendResult, error)
}

func RegisterFriendserviceHTTPServer(s *http.Server, srv FriendserviceHTTPServer) {
	r := s.Route("/")
	r.POST("/gameserver/api/friendservice/addfriendapply", _Friendservice_AddFriendApply0_HTTP_Handler(srv))
	r.POST("/gameserver/api/friendservice/approvefriendapply", _Friendservice_ApproveFriendApply0_HTTP_Handler(srv))
	r.POST("/gameserver/api/friendservice/removefriendapply", _Friendservice_RemoveFriendApply0_HTTP_Handler(srv))
	r.POST("/gameserver/api/friendservice/getfriendapplylist", _Friendservice_GetFriendApplyList0_HTTP_Handler(srv))
	r.POST("/gameserver/api/friendservice/addfriendplayer", _Friendservice_AddFriendPlayer0_HTTP_Handler(srv))
	r.POST("/gameserver/api/friendservice/delfriendplayer", _Friendservice_DelFriendPlayer0_HTTP_Handler(srv))
	r.POST("/gameserver/api/friendservice/setfriendplayer", _Friendservice_SetFriendPlayer0_HTTP_Handler(srv))
	r.POST("/gameserver/api/friendservice/getfriendplayerlist", _Friendservice_GetFriendPlayerList0_HTTP_Handler(srv))
	r.POST("/gameserver/api/friendservice/getgrouplist", _Friendservice_GetGroupList0_HTTP_Handler(srv))
	r.POST("/gameserver/api/friendservice/getgrouplistbygroupid", _Friendservice_GetGroupPlayerListByGroupId0_HTTP_Handler(srv))
	r.POST("/gameserver/api/friendservice/setfriendbaseinfo", _Friendservice_SetFriendBaseInfoRep0_HTTP_Handler(srv))
	r.POST("/gameserver/api/friendservice/getmygroupinvitejoingrouplist", _Friendservice_GetMyGroupInviteJoinGroupListRep0_HTTP_Handler(srv))
}

func _Friendservice_AddFriendApply0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddFriendApplyInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceAddFriendApply)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddFriendApply(ctx, req.(*AddFriendApplyInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddFriendApplyResult)
		return ctx.Result(200, reply)
	}
}

func _Friendservice_ApproveFriendApply0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in ApproveFriendApplyInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceApproveFriendApply)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.ApproveFriendApply(ctx, req.(*ApproveFriendApplyInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*ApproveFriendApplyResult)
		return ctx.Result(200, reply)
	}
}

func _Friendservice_RemoveFriendApply0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RemoveFriendApplyInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceRemoveFriendApply)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RemoveFriendApply(ctx, req.(*RemoveFriendApplyInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RemoveFriendApplyResult)
		return ctx.Result(200, reply)
	}
}

func _Friendservice_GetFriendApplyList0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetFriendApplyListInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceGetFriendApplyList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetFriendApplyList(ctx, req.(*GetFriendApplyListInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetFriendApplyListResult)
		return ctx.Result(200, reply)
	}
}

func _Friendservice_AddFriendPlayer0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in AddFriendPlayerInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceAddFriendPlayer)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.AddFriendPlayer(ctx, req.(*AddFriendPlayerInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*AddFriendResult)
		return ctx.Result(200, reply)
	}
}

func _Friendservice_DelFriendPlayer0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in DelFriendPlayerInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceDelFriendPlayer)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.DelFriendPlayer(ctx, req.(*DelFriendPlayerInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*DelFriendResult)
		return ctx.Result(200, reply)
	}
}

func _Friendservice_SetFriendPlayer0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetFriendPlayerInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceSetFriendPlayer)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetFriendPlayer(ctx, req.(*SetFriendPlayerInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SetFriendResult)
		return ctx.Result(200, reply)
	}
}

func _Friendservice_GetFriendPlayerList0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetFriendPlayerListInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceGetFriendPlayerList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetFriendPlayerList(ctx, req.(*GetFriendPlayerListInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetFriendPlayerListInfoResult)
		return ctx.Result(200, reply)
	}
}

func _Friendservice_GetGroupList0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGroupListInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceGetGroupList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGroupList(ctx, req.(*GetGroupListInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetGroupListResult)
		return ctx.Result(200, reply)
	}
}

func _Friendservice_GetGroupPlayerListByGroupId0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetGroupPlayerListByGroupIdInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceGetGroupPlayerListByGroupId)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetGroupPlayerListByGroupId(ctx, req.(*GetGroupPlayerListByGroupIdInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetGroupPlayerListByGroupIdResult)
		return ctx.Result(200, reply)
	}
}

func _Friendservice_SetFriendBaseInfoRep0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SetFriendBaseInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceSetFriendBaseInfoRep)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetFriendBaseInfoRep(ctx, req.(*SetFriendBaseInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SetFriendBaseInfoResult)
		return ctx.Result(200, reply)
	}
}

func _Friendservice_GetMyGroupInviteJoinGroupListRep0_HTTP_Handler(srv FriendserviceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetMyGroupInviteJoinGroupList
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationFriendserviceGetMyGroupInviteJoinGroupListRep)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetMyGroupInviteJoinGroupListRep(ctx, req.(*GetMyGroupInviteJoinGroupList))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetMyGroupInviteJoinGroupListResult)
		return ctx.Result(200, reply)
	}
}

type FriendserviceHTTPClient interface {
	AddFriendApply(ctx context.Context, req *AddFriendApplyInfo, opts ...http.CallOption) (rsp *AddFriendApplyResult, err error)
	AddFriendPlayer(ctx context.Context, req *AddFriendPlayerInfo, opts ...http.CallOption) (rsp *AddFriendResult, err error)
	ApproveFriendApply(ctx context.Context, req *ApproveFriendApplyInfo, opts ...http.CallOption) (rsp *ApproveFriendApplyResult, err error)
	DelFriendPlayer(ctx context.Context, req *DelFriendPlayerInfo, opts ...http.CallOption) (rsp *DelFriendResult, err error)
	GetFriendApplyList(ctx context.Context, req *GetFriendApplyListInfo, opts ...http.CallOption) (rsp *GetFriendApplyListResult, err error)
	GetFriendPlayerList(ctx context.Context, req *GetFriendPlayerListInfo, opts ...http.CallOption) (rsp *GetFriendPlayerListInfoResult, err error)
	GetGroupList(ctx context.Context, req *GetGroupListInfo, opts ...http.CallOption) (rsp *GetGroupListResult, err error)
	GetGroupPlayerListByGroupId(ctx context.Context, req *GetGroupPlayerListByGroupIdInfo, opts ...http.CallOption) (rsp *GetGroupPlayerListByGroupIdResult, err error)
	GetMyGroupInviteJoinGroupListRep(ctx context.Context, req *GetMyGroupInviteJoinGroupList, opts ...http.CallOption) (rsp *GetMyGroupInviteJoinGroupListResult, err error)
	RemoveFriendApply(ctx context.Context, req *RemoveFriendApplyInfo, opts ...http.CallOption) (rsp *RemoveFriendApplyResult, err error)
	SetFriendBaseInfoRep(ctx context.Context, req *SetFriendBaseInfo, opts ...http.CallOption) (rsp *SetFriendBaseInfoResult, err error)
	SetFriendPlayer(ctx context.Context, req *SetFriendPlayerInfo, opts ...http.CallOption) (rsp *SetFriendResult, err error)
}

type FriendserviceHTTPClientImpl struct {
	cc *http.Client
}

func NewFriendserviceHTTPClient(client *http.Client) FriendserviceHTTPClient {
	return &FriendserviceHTTPClientImpl{client}
}

func (c *FriendserviceHTTPClientImpl) AddFriendApply(ctx context.Context, in *AddFriendApplyInfo, opts ...http.CallOption) (*AddFriendApplyResult, error) {
	var out AddFriendApplyResult
	pattern := "/gameserver/api/friendservice/addfriendapply"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceAddFriendApply))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FriendserviceHTTPClientImpl) AddFriendPlayer(ctx context.Context, in *AddFriendPlayerInfo, opts ...http.CallOption) (*AddFriendResult, error) {
	var out AddFriendResult
	pattern := "/gameserver/api/friendservice/addfriendplayer"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceAddFriendPlayer))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FriendserviceHTTPClientImpl) ApproveFriendApply(ctx context.Context, in *ApproveFriendApplyInfo, opts ...http.CallOption) (*ApproveFriendApplyResult, error) {
	var out ApproveFriendApplyResult
	pattern := "/gameserver/api/friendservice/approvefriendapply"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceApproveFriendApply))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FriendserviceHTTPClientImpl) DelFriendPlayer(ctx context.Context, in *DelFriendPlayerInfo, opts ...http.CallOption) (*DelFriendResult, error) {
	var out DelFriendResult
	pattern := "/gameserver/api/friendservice/delfriendplayer"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceDelFriendPlayer))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FriendserviceHTTPClientImpl) GetFriendApplyList(ctx context.Context, in *GetFriendApplyListInfo, opts ...http.CallOption) (*GetFriendApplyListResult, error) {
	var out GetFriendApplyListResult
	pattern := "/gameserver/api/friendservice/getfriendapplylist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceGetFriendApplyList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FriendserviceHTTPClientImpl) GetFriendPlayerList(ctx context.Context, in *GetFriendPlayerListInfo, opts ...http.CallOption) (*GetFriendPlayerListInfoResult, error) {
	var out GetFriendPlayerListInfoResult
	pattern := "/gameserver/api/friendservice/getfriendplayerlist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceGetFriendPlayerList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FriendserviceHTTPClientImpl) GetGroupList(ctx context.Context, in *GetGroupListInfo, opts ...http.CallOption) (*GetGroupListResult, error) {
	var out GetGroupListResult
	pattern := "/gameserver/api/friendservice/getgrouplist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceGetGroupList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FriendserviceHTTPClientImpl) GetGroupPlayerListByGroupId(ctx context.Context, in *GetGroupPlayerListByGroupIdInfo, opts ...http.CallOption) (*GetGroupPlayerListByGroupIdResult, error) {
	var out GetGroupPlayerListByGroupIdResult
	pattern := "/gameserver/api/friendservice/getgrouplistbygroupid"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceGetGroupPlayerListByGroupId))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FriendserviceHTTPClientImpl) GetMyGroupInviteJoinGroupListRep(ctx context.Context, in *GetMyGroupInviteJoinGroupList, opts ...http.CallOption) (*GetMyGroupInviteJoinGroupListResult, error) {
	var out GetMyGroupInviteJoinGroupListResult
	pattern := "/gameserver/api/friendservice/getmygroupinvitejoingrouplist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceGetMyGroupInviteJoinGroupListRep))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FriendserviceHTTPClientImpl) RemoveFriendApply(ctx context.Context, in *RemoveFriendApplyInfo, opts ...http.CallOption) (*RemoveFriendApplyResult, error) {
	var out RemoveFriendApplyResult
	pattern := "/gameserver/api/friendservice/removefriendapply"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceRemoveFriendApply))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FriendserviceHTTPClientImpl) SetFriendBaseInfoRep(ctx context.Context, in *SetFriendBaseInfo, opts ...http.CallOption) (*SetFriendBaseInfoResult, error) {
	var out SetFriendBaseInfoResult
	pattern := "/gameserver/api/friendservice/setfriendbaseinfo"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceSetFriendBaseInfoRep))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *FriendserviceHTTPClientImpl) SetFriendPlayer(ctx context.Context, in *SetFriendPlayerInfo, opts ...http.CallOption) (*SetFriendResult, error) {
	var out SetFriendResult
	pattern := "/gameserver/api/friendservice/setfriendplayer"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationFriendserviceSetFriendPlayer))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
