//GrpcAddressType:TeamCenter
//GrpcServerType:server,world

syntax = "proto3";

package Aurora.TeamCenter;

import "google/api/annotations.proto";

option go_package = "gameserver/api/TeamCenter/v1;v1";

//ServiceStart
service TeamCenterService {
// 通过队伍ID，查询队伍信息
rpc TCAskTeamInfoByTeamId(TCAskTeamInfoByTeamIdRequest) returns (TCTeamInfoReply) {
option (google.api.http) = {
post: "/v1/Teaminfo/ByTeamId"
body: "*"
};
}

// 通过玩家guid，查询队伍信息
rpc TCAskTeamInfoByPlayerGuid(TCAskTeamInfoByPlayerGuidRequest) returns (TCTeamInfoReply) {
option (google.api.http) = {
post: "/v1/Teaminfo/ByPlayerGuid"
body: "*"
};
}

// 创建队伍
rpc TCCreateTeam(TCCreateTeamRequest) returns (TCTeamInfoReply) {
}

// 玩家加入队伍
rpc TCPlayerJoinTeam(TCPlayerJoinTeamRequest) returns (TCTeamInfoReply) {
}


// 玩家离开队伍
rpc TCPlayerLeaveTeam(TCPlayerLeaveTeamRequest) returns (TCTeamInfoReply) {
}

// 机器人加入队伍
rpc TCRobotJoinTeam(TCRobotJoinTeamRequest) returns (TCTeamInfoReply) {
}

// 机器人离开队伍
rpc TCRobotLeaveTeam(TCRobotLeaveTeamRequest) returns (TCTeamInfoReply) {
}

// 队长任命队伍中的成员职位
rpc TCTeamAppointedJobTitle(TCTeamAppointedJobTitleRequest) returns (TCTeamInfoReply) {
}

// 批量查询队伍数据
rpc TCAskTeamInfoListByTeamIdList(TCAskTeamInfoListByTeamIdListRequest) returns (TCTeamInfoListReply) {
}
}
//ServiceEnd


//StructStart

//Type:Http
//Type:Inner
enum ETeamJobTitle
{
eInvalid = 0;
eLeader = 1;
eSubLeader = 2;
eMmeber = 3;
}

//Type:Http
//Type:Inner
enum ENUM_TEAMCENTER_OP
{
None=0;
Suc=1;
saveTCTeamMemberToRedis_Suc = 10;
saveTCTeamMemberToRedis_Redis_Faild = 11;

saveTCTeamInfoToRedis_Suc= 20;
saveTCTeamInfoToRedis_JsonMarshal_Error = 21;
saveTCTeamInfoToRedis_Redis_Error = 22;
saveTCTeamInfoToRedis_saveTCTeamMember_Error = 23;

loadTCTeamInfoFromRedis_Suc = 30;
loadTCTeamInfoFromRedis_TeamIdInvalid = 31;
loadTCTeamInfoFromRedis_RedisError = 32;
loadTCTeamInfoFromRedis_JsonUnmarshal_Error = 33;

deleteTCTeamInfoFromRedis_Suc = 40;
deleteTCTeamInfoFromRedis_Redis_Error = 41;
deleteTCTeamInfoFromRedis_DeleteTeamMemberInfo_Error = 42;

getPlayerTeamIdFromRedis_Suc = 50;
getPlayerTeamIdFromRedis_Redis_Error = 51;

TCAskTeamInfoByTeamIdRp_Suc = 100;
TCAskTeamInfoByTeamIdRp_OverTime = 101;
TCAskTeamInfoByTeamIdRp_AcqLockFaild = 102;

TCAskTeamInfoByPlayerGuidRp_Suc = 200;
TCAskTeamInfoByPlayerGuidRp_OverTime = 201;
TCAskTeamInfoByPlayerGuidRp_AcqLockFaild_Player = 202;
TCAskTeamInfoByPlayerGuidRp_AcqLockFaild_Team = 203;

CreateTeam_Suc = 300;
CreateTeam_GetNewTeamId_Faild = 301;
CreateTeam_SaveTeamInfo_JsonMarshal_Error = 302;
CreateTeam_SaveTeamInfo_Redis_Error = 303;
CreateTeam_SavePlayerTeamId_Redis_Error = 304;
CreateTeam_CheckPlayerTeamId_PlayerHasTeam = 305;
CreateTeam_CheckPlayerTeamId_GenTeamLockKey_Faild = 306;
CreateTeam_CheckPlayerTeamId_LockKey_AcquireLock_Faild = 307;

JoinTeam_Suc = 400;
JoinTeam_CheckPlayerTeamId_Error = 401;
JoinTeam_CheckPlayerTeamId_PlayerHasTeam = 402;
JoinTeam_CheckPlayerTeamId_newTeamMemListEmpty = 403;
JoinTeam_CheckTeam_GetTeamInfoFaild = 404;
JoinTeam_CheckTeam_PlayerExistence = 405;
JoinTeam_CheckTeam_TeamTypeInvalid = 406;
JoinTeam_CheckTeam_TeamFull = 407;
JoinTeam_CheckTeam_OpPlayerInvalid = 408;
JoinTeam_CheckTeam_TeamIdInvalid = 409;
JoinTeam_CheckTeam_Team_Lock_Faild = 410;

LeaveTeam_Suc = 500;
LeaveTeam_Team_Lock_Faild = 501;

AppointedJobTitle_Suc = 600;
AppointedJobTitle_OpPlayer_Error = 601;
AppointedJobTitle_OpPlayer_TeamIdInvalid = 602;
AppointedJobTitle_LeaderPlayer_Error = 603;
AppointedJobTitle_LeaderPlayer_TeamIdInvalid = 604;
AppointedJobTitle_TeamIdInvalid = 605;
AppointedJobTitle_TeamLeader_Nil = 606;
AppointedJobTitle_TeamLeaderGuid_Dif = 607;
AppointedJobTitle_OpPlayer_NoTeam = 608;
AppointedJobTitle_InvalidJobTitle = 609;
AppointedJobTitle_Team_Lock_Faild = 610;
AppointedJobTitle_OpPlayer_Lock_Faild = 611;
AppointedJobTitle_LeaderPlayer_Lock_Faild = 612;

TCAskTeamInfoListByTeamIdList_Suc = 700;
TCAskTeamInfoListByTeamIdList_Load_Error = 701;
TCAskTeamInfoListByTeamIdList_OverTime = 702;
TCAskTeamInfoListByTeamIdList_AcqLockFaild = 703;

RobotJoinTeam_Suc = 800;
RobotJoinTeam_CheckTeam_GetTeamInfoFaild = 801;
RobotJoinTeam_CheckTeam_TeamTypeInvalid = 802;
RobotJoinTeam_CheckTeam_TeamFull = 803;
RobotJoinTeam_CheckTeam_OpPlayerInvalid = 804;
RobotJoinTeam_CheckTeam_TeamIdInvalid = 805;
RobotJoinTeam_CheckTeam_Team_Lock_Faild = 806;
RobotJoinTeam_CheckPlayerTeamId_newTeamMemListEmpty = 807;

RobotLeaveTeam_Suc = 900;
RobotLeaveTeam_Team_Lock_Faild = 901;
RobotLeaveTeam_TeamID_Invalid = 902;

}

//Type:Http
//Type:Inner
message TCTeamMember
{
uint64 TeamId = 1;
uint64 PlayerGuid = 2;
int32 PlayerIndex = 3;
int32 PlayerTitle = 4;
int32 PlayerType = 5;
int32 ZoneWorldID = 6;
}


//Type:Http
//Type:Inner
message TCTeamInfo
{
uint64 TeamId = 1;
int32 TeamType = 2;
int32 LeaderIndex = 3;
map<int64,TCTeamMember> TCTeamMemberDic = 4;
}

//StructEnd

// 请求消息定义

//Type:Inner
//Target:W2S
message TCTeamInfoReply  //IResponse
{
int32 opState = 1;
TCTeamInfo TCTeamInfoEle = 2;
}

//Type:Inner
//Target:W2S
message TCTeamInfoListReply  //IResponse
{
int32 opState = 1;
repeated TCTeamInfo TCTeamInfoEleList = 2;
}

//Type:Http
//Type:Inner
//Target:S2W
//Response W2S_TCTeamInfoReply
message TCAskTeamInfoByTeamIdRequest  //IRequest
{
uint64 teamId = 1;
}

//Type:Http
//Type:Inner
//Target:S2W
//Response W2S_TCTeamInfoReply
message TCAskTeamInfoByPlayerGuidRequest  //IRequest
{
uint64 playerGuid = 1;
}

//Type:Http
//Type:Inner
//Target:S2W
//Response W2S_TCTeamInfoReply
message TCCreateTeamRequest  //IRequest
{
TCTeamInfo TCTeamInfoEle = 1;
}


//Type:Http
//Type:Inner
//Target:S2W
//Response W2S_TCTeamInfoReply
message TCPlayerJoinTeamRequest  //IRequest
{
uint64 TeamId = 1;
int32 TeamMaxCount = 2;
int32 TeamType = 3;
uint64 opPlayerGuid = 4;
repeated TCTeamMember TCTeamMemberList = 5;
}

//Type:Http
//Type:Inner
//Target:S2W
//Response W2S_TCTeamInfoReply
message TCPlayerLeaveTeamRequest  //IRequest
{
uint64 TeamId = 1;
int32 TeamMaxCount = 2;
repeated TCTeamMember TCTeamMemberList = 3;   // 这个list中的玩家都必须在同一个队伍中
uint64 newLeaderGuid = 4;
}


//Type:Http
//Type:Inner
//Target:S2W
//Response W2S_TCTeamInfoReply
message TCRobotJoinTeamRequest  //IRequest
{
uint64 TeamId = 1;
int32 TeamMaxCount = 2;
int32 TeamType = 3;
uint64 opPlayerGuid = 4;
repeated TCTeamMember TCTeamMemberList = 5;
}

//Type:Http
//Type:Inner
//Target:S2W
//Response W2S_TCTeamInfoReply
message TCRobotLeaveTeamRequest  //IRequest
{
uint64 TeamId = 1;
int32 TeamMaxCount = 2;
repeated TCTeamMember TCTeamMemberList = 3;   // 这个list中的玩家都必须在同一个队伍中
uint64 newLeaderGuid = 4;
}

//Type:Http
//Type:Inner
//Target:S2W
//Response W2S_TCTeamInfoReply
message TCTeamAppointedJobTitleRequest  //IRequest
{
uint64 TeamId = 1;
uint64 curLeaderGuid = 2;
uint64 opPlayerGuid = 3;
int32 opPlayerJobTitle = 4;
}

//Type:Http
//Type:Inner
//Target:S2W
//Response W2S_TCTeamInfoListReply
message TCAskTeamInfoListByTeamIdListRequest  //IRequest
{
repeated uint64 TeamIdList = 1;
}
