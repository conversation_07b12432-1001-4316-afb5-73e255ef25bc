{"swagger": "2.0", "info": {"title": "v1/teamcenter.proto", "version": "version not set"}, "tags": [{"name": "TeamCenterService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/Teaminfo/ByPlayerGuid": {"post": {"summary": "通过玩家guid，查询队伍信息", "operationId": "TeamCenterService_TCAskTeamInfoByPlayerGuid", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/TeamCenterTCTeamInfoReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/TeamCenterTCAskTeamInfoByPlayerGuidRequest"}}], "tags": ["TeamCenterService"]}}, "/v1/Teaminfo/ByTeamId": {"post": {"summary": "通过队伍ID，查询队伍信息", "operationId": "TeamCenterService_TCAskTeamInfoByTeamId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/TeamCenterTCTeamInfoReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/TeamCenterTCAskTeamInfoByTeamIdRequest"}}], "tags": ["TeamCenterService"]}}}, "definitions": {"TeamCenterTCAskTeamInfoByPlayerGuidRequest": {"type": "object", "properties": {"playerGuid": {"type": "string", "format": "uint64"}}, "title": "Type:Http\nType:Inner\nTarget:S2W\nResponse W2S_TCTeamInfoReply"}, "TeamCenterTCAskTeamInfoByTeamIdRequest": {"type": "object", "properties": {"teamId": {"type": "string", "format": "uint64"}}, "title": "Type:Http\nType:Inner\nTarget:S2W\nResponse W2S_TCTeamInfoReply"}, "TeamCenterTCTeamInfo": {"type": "object", "properties": {"TeamId": {"type": "string", "format": "uint64"}, "TeamType": {"type": "integer", "format": "int32"}, "LeaderIndex": {"type": "integer", "format": "int32"}, "TCTeamMemberDic": {"type": "object", "additionalProperties": {"$ref": "#/definitions/TeamCenterTCTeamMember"}}}, "title": "Type:Http\nType:Inner"}, "TeamCenterTCTeamInfoListReply": {"type": "object", "properties": {"opState": {"type": "integer", "format": "int32"}, "TCTeamInfoEleList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/TeamCenterTCTeamInfo"}}}, "title": "Type:Inner\nTarget:W2S"}, "TeamCenterTCTeamInfoReply": {"type": "object", "properties": {"opState": {"type": "integer", "format": "int32"}, "TCTeamInfoEle": {"$ref": "#/definitions/TeamCenterTCTeamInfo"}}, "title": "Type:Inner\nTarget:W2S"}, "TeamCenterTCTeamMember": {"type": "object", "properties": {"TeamId": {"type": "string", "format": "uint64"}, "PlayerGuid": {"type": "string", "format": "uint64"}, "PlayerIndex": {"type": "integer", "format": "int32"}, "PlayerTitle": {"type": "integer", "format": "int32"}, "PlayerType": {"type": "integer", "format": "int32"}, "ZoneWorldID": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}