package player

import (
	"context"
	rankv1 "liteframe/api/microservices/rank/v1"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/common/table"
	"liteframe/internal/common/table/table_data"
	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/pkg/log"
	"time"
)

// Trophy 奖杯模块
type Trophy struct {
	player                *Player
	db                    *dbstruct.Trophy
	lastMatchTrophyChange int32 // 运行时缓存：上次战斗的奖杯变动值，用于对战庇佑功能
}

func NewTrophy(p *Player) *Trophy {
	return &Trophy{
		player: p,
	}
}

// InitDB 初始化模块数据
func (t *Trophy) InitDB(db *dbstruct.UserDB) {
	if db.Game == nil {
		db.Game = &dbstruct.GameDB{}
	}
	if db.Game.Trophy == nil {
		initialTrophy := 0
		//initialTrophy := table.GetMainRankScoreInitial() // TODO

		db.Game.Trophy = &dbstruct.Trophy{
			CurrentTrophy:        int32(initialTrophy),
			CurrentWinStreak:     0,
			ClaimedRewards:       make([]int32, 0),
			CurrentSeasonId:      1, // 默认第一赛季
			ClaimedSeasonRewards: make([]int32, 0),
			SeasonHistory:        make([]*public.SeasonTrophyInfo, 0),
			SupplyTimes:          0,
			BlessingTimes:        0,
			LastDailyResetTime:   0,
		}
	}
	t.db = db.Game.Trophy

	// 检查每日重置
	t.checkDailyReset()

	log.Info("Trophy InitDB", log.Kv("player_id", t.player.Uid()), log.Kv("current_trophy", t.db.CurrentTrophy))
}

// OnCrossDay 跨天处理
func (t *Trophy) OnCrossDay(natural bool, nowUnix int64) {
	t.checkDailyReset()
}

// checkDailyReset 检查每日重置
func (t *Trophy) checkDailyReset() {
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayUnix := today.Unix()

	if t.db.LastDailyResetTime < todayUnix {
		// 重置每日次数
		t.db.SupplyTimes = 0
		t.db.BlessingTimes = 0
		t.db.LastDailyResetTime = todayUnix

		log.Info("Trophy daily reset", log.Kv("player_id", t.player.Uid()))
	}
}

// GetCurrentTrophy 获取当前奖杯数
func (t *Trophy) GetCurrentTrophy() int32 {
	return t.db.CurrentTrophy
}

// GetCurrentWinStreak 获取当前连胜数
func (t *Trophy) GetCurrentWinStreak() int32 {
	return t.db.CurrentWinStreak
}

// GetCurrentRankId 根据当前奖杯数获取段位ID
func (t *Trophy) GetCurrentRankId() int32 {
	currentTrophy := t.db.CurrentTrophy
	var rankId int32 = 1

	// 遍历段位表，找到当前奖杯数对应的段位
	table.GetTable().TableMainRank.Foreach(func(rank *table_data.TableMainRank) bool {
		if currentTrophy >= rank.ScoreRank {
			rankId = rank.ID
		} else {
			return true // 找到第一个不满足的段位，停止遍历
		}
		return false
	})

	return rankId
}

// GetRemainingSupplyTimes 获取对战补给剩余次数
func (t *Trophy) GetRemainingSupplyTimes() int32 {
	maxTimes := int32(10) // TODO
	//maxTimes := table.GetMainBattleDailyWinTimes()
	return maxTimes - t.db.SupplyTimes
}

// GetRemainingBlessingTimes 获取对战庇佑剩余次数
func (t *Trophy) GetRemainingBlessingTimes() int32 {
	maxTimes := int32(3) // 默认值
	//maxTimes := table.GetMainBattleDailyFailTimes() // TODO
	return maxTimes - t.db.BlessingTimes
}

// ProcessBattleEnd 处理战斗结束结算
func (t *Trophy) ProcessBattleEnd(rank int32, winStreak int32, finalLineup []*public.PBBattleHeroInfo) *cs.LCBattleEndNotify {
	// 记录结算前的奖杯数
	trophyBefore := t.db.CurrentTrophy

	// 根据段位和排名计算奖杯变化
	trophyChange := t.calculateTrophyChange(rank)
	trophyAfter := trophyBefore + trophyChange

	// 确保奖杯数不为负
	if trophyAfter < 0 {
		trophyAfter = 0
	}

	// 更新数据库
	t.db.CurrentTrophy = trophyAfter
	t.db.CurrentWinStreak = winStreak

	// 缓存本次奖杯变动，用于对战庇佑功能
	t.lastMatchTrophyChange = trophyChange

	// 发放战斗奖励
	t.grantBattleRewards(rank)

	t.syncRank()
	// 构造并返回结算通知
	notify := &cs.LCBattleEndNotify{
		Rank:          rank,
		Heros:         finalLineup,
		WinStreak:     winStreak,
		TrophyBefore:  trophyBefore,
		TrophyAfter:   trophyAfter,
		SupplyTimes:   t.GetRemainingSupplyTimes(),
		BlessingTimes: t.GetRemainingBlessingTimes(),
	}

	log.Info("Battle end processed",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("rank", rank),
		log.Kv("trophy_before", trophyBefore),
		log.Kv("trophy_after", trophyAfter),
		log.Kv("trophy_change", trophyChange),
		log.Kv("win_streak", winStreak))

	return notify
}

// calculateTrophyChange 根据排名计算奖杯变化
func (t *Trophy) calculateTrophyChange(rank int32) int32 {
	// 获取当前段位
	rankId := t.GetCurrentRankId()
	rankConfig := table.GetTable().TableMainRank.GetById(rankId)
	if rankConfig == nil {
		log.Error("Rank config not found", log.Kv("rank_id", rankId))
		return 0
	}

	// 根据排名获取奖杯变化（排名从1开始，数组从0开始）
	if rank >= 1 && rank <= int32(len(rankConfig.ScoreBattle)) {
		return rankConfig.ScoreBattle[rank-1]
	}

	log.Error("Invalid rank for trophy calculation", log.Kv("rank", rank), log.Kv("rank_id", rankId))
	return 0
}

// grantBattleRewards 发放战斗奖励
func (t *Trophy) grantBattleRewards(rank int32) {
	// 获取当前段位
	rankId := t.GetCurrentRankId()
	rankConfig := table.GetTable().TableMainRank.GetById(rankId)
	if rankConfig == nil {
		log.Error("Rank config not found for battle rewards", log.Kv("rank_id", rankId))
		return
	}

	// 发放战斗奖励
	if len(rankConfig.RewardBattle) > 0 {
		for _, reward := range rankConfig.RewardBattle {
			if len(reward) >= 2 {
				itemId := reward[0]
				count := reward[1]
				t.player.AddItem(itemId, count, game_def.ADD_ITEM_TROPHY)
			}
		}

		log.Info("Battle rewards granted",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("rank", rank),
			log.Kv("rank_id", rankId),
			log.Kv("rewards", rankConfig.RewardBattle))
	}
}

// ClaimAdReward 领取额外广告奖励
func (t *Trophy) ClaimAdReward(ctx context.Context, req *cs.CLClaimAdRewardReq) *cs.LCClaimAdRewardRsp {
	rsp := &cs.LCClaimAdRewardRsp{
		Code: int32(error_code.ERROR_OK),
		Type: req.Type,
	}

	switch req.Type {
	case public.AdRewardType_BATTLE_SUPPLY_DROP:
		// 对战补给
		if t.GetRemainingSupplyTimes() <= 0 {
			rsp.Code = int32(error_code.ERROR_PARAMS)
			log.Error("Supply times exhausted", log.Kv("player_id", t.player.Uid()))
			return rsp
		}

		// 检查是否有奖杯增长
		if t.lastMatchTrophyChange <= 0 {
			rsp.Code = int32(error_code.ERROR_PARAMS)
			log.Error("No trophy gain for supply reward", log.Kv("player_id", t.player.Uid()))
			return rsp
		}

		// 发放补给奖励
		t.grantSupplyReward(rsp)
		t.db.SupplyTimes++

	case public.AdRewardType_BATTLE_BLESSING:
		// 对战庇佑
		if t.GetRemainingBlessingTimes() <= 0 {
			rsp.Code = int32(error_code.ERROR_PARAMS)
			log.Error("Blessing times exhausted", log.Kv("player_id", t.player.Uid()))
			return rsp
		}

		// 检查是否有奖杯扣除
		if t.lastMatchTrophyChange >= 0 {
			rsp.Code = int32(error_code.ERROR_PARAMS)
			log.Error("No trophy loss for blessing", log.Kv("player_id", t.player.Uid()))
			return rsp
		}

		// 恢复奖杯
		t.db.CurrentTrophy -= t.lastMatchTrophyChange // 减去负数等于加上正数
		rsp.CurrentTrophy = t.db.CurrentTrophy
		t.db.BlessingTimes++

		log.Info("Trophy restored by blessing",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("restored_amount", -t.lastMatchTrophyChange),
			log.Kv("current_trophy", t.db.CurrentTrophy))

	default:
		rsp.Code = int32(error_code.ERROR_PARAMS)
		log.Error("Invalid ad reward type", log.Kv("type", req.Type))
	}

	return rsp
}

// grantSupplyReward 发放补给奖励
func (t *Trophy) grantSupplyReward(rsp *cs.LCClaimAdRewardRsp) {
	// 获取当前段位
	rankId := t.GetCurrentRankId()
	rankConfig := table.GetTable().TableMainRank.GetById(rankId)
	if rankConfig == nil {
		log.Error("Rank config not found for supply reward", log.Kv("rank_id", rankId))
		return
	}

	// 发放每日奖励
	if len(rankConfig.RewardDaily) > 0 {
		for _, reward := range rankConfig.RewardDaily {
			if len(reward) >= 2 {
				itemId := reward[0]
				count := reward[1]
				t.player.AddItem(itemId, count, game_def.ADD_ITEM_TROPHY)

				// 添加到响应中
				rsp.ItemInfo = append(rsp.ItemInfo, &public.PBDropItemDataInfo{
					ItemId:    itemId,
					ItemCount: count,
				})
			}
		}

		log.Info("Supply reward granted",
			log.Kv("player_id", t.player.Uid()),
			log.Kv("rank_id", rankId),
			log.Kv("rewards", rankConfig.RewardDaily))
	}
}

// IsRewardClaimed 检查段位奖励是否已领取
func (t *Trophy) IsRewardClaimed(rankId int32) bool {
	for _, claimedId := range t.db.ClaimedRewards {
		if claimedId == rankId {
			return true
		}
	}
	return false
}

// IsSeasonRewardClaimed 检查赛季段位奖励是否已领取
func (t *Trophy) IsSeasonRewardClaimed(rankId int32) bool {
	for _, claimedId := range t.db.ClaimedSeasonRewards {
		if claimedId == rankId {
			return true
		}
	}
	return false
}

// ClaimRankReward 领取段位奖励
func (t *Trophy) ClaimRankReward(rankId int32) error_code.Code {
	// 检查段位是否达到
	if t.db.CurrentTrophy < t.getRequiredTrophyForRank(rankId) {
		return error_code.ERROR_PARAMS
	}

	// 检查是否已领取
	if t.IsRewardClaimed(rankId) {
		return error_code.ERROR_PARAMS
	}

	// 获取奖励配置
	rankConfig := table.GetTable().TableMainRank.GetById(rankId)
	if rankConfig == nil {
		return error_code.ERROR_PARAMS
	}

	// 发放奖励
	if len(rankConfig.Reward) > 0 {
		for _, reward := range rankConfig.Reward {
			if len(reward) >= 2 {
				itemId := reward[0]
				count := reward[1]
				t.player.AddItem(itemId, count, game_def.ADD_ITEM_TROPHY)
			}
		}
	}

	// 记录已领取
	t.db.ClaimedRewards = append(t.db.ClaimedRewards, rankId)

	log.Info("Rank reward claimed",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("rank_id", rankId),
		log.Kv("rewards", rankConfig.Reward))

	return error_code.ERROR_OK
}

// getRequiredTrophyForRank 获取段位所需奖杯数
func (t *Trophy) getRequiredTrophyForRank(rankId int32) int32 {
	rankConfig := table.GetTable().TableMainRank.GetById(rankId)
	if rankConfig == nil {
		return 0
	}
	return rankConfig.ScoreRank
}

// GetSeasonHistory 获取赛季历史
func (t *Trophy) GetSeasonHistory() []*public.SeasonTrophyInfo {
	return t.db.SeasonHistory
}

// ResetSeason 重置赛季（由SeasonSystem调用）
func (t *Trophy) ResetSeason(oldSeasonId, newSeasonId int32) {
	// 读取上赛季最终奖杯
	oldFinalTrophy := t.db.CurrentTrophy

	// 保存上赛季数据到历史记录
	if oldSeasonId > 0 {
		seasonHistory := &public.SeasonTrophyInfo{
			SeasonId:    oldSeasonId,
			FinalTrophy: oldFinalTrophy,
		}
		t.db.SeasonHistory = append(t.db.SeasonHistory, seasonHistory)
	}

	// 根据重置表计算新的奖杯数
	newTrophy := t.calculateSeasonResetTrophy()

	// 更新赛季数据
	t.db.CurrentSeasonId = newSeasonId
	t.db.CurrentTrophy = newTrophy
	t.db.ClaimedSeasonRewards = make([]int32, 0) // 清空赛季奖励记录

	// 推送赛季重置通知给客户端
	notify := &cs.LCSeasonResetNotify{
		OldSeasonId:            oldSeasonId,
		NewSeasonId:            newSeasonId,
		FinalTrophyLastSeason:  oldFinalTrophy,
		InitialTrophyNewSeason: newTrophy,
	}

	// 通过玩家发送通知
	t.player.SeasonResetNotify(notify)

	log.Info("Season reset with notification",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("old_season_id", oldSeasonId),
		log.Kv("new_season_id", newSeasonId),
		log.Kv("old_final_trophy", oldFinalTrophy),
		log.Kv("new_trophy", newTrophy))
}

// calculateSeasonResetTrophy 计算赛季重置后的奖杯数
func (t *Trophy) calculateSeasonResetTrophy() int32 {
	currentTrophy := t.db.CurrentTrophy

	// 查找对应的重置配置
	var resetTrophy int32 = currentTrophy
	table.GetTable().TableMainRank.Foreach(func(rank *table_data.TableMainRank) bool {
		if currentTrophy >= rank.ScoreRank && rank.ScoreReset > 0 {
			resetTrophy = rank.ScoreReset
		}
		return false
	})

	return resetTrophy
}

// HandleSyncSeasonInfo 处理赛季信息同步请求
func (t *Trophy) HandleSyncSeasonInfo(req *cs.CLSeasonInfoReq) *cs.LCSeasonInfoRsp {
	rsp := &cs.LCSeasonInfoRsp{
		Code: int32(error_code.ERROR_OK),
	}

	// 填充核心数据
	rsp.Trophy = t.db.CurrentTrophy
	rsp.CurrentWinStreak = t.db.CurrentWinStreak
	rsp.CurrentSeasonId = t.db.CurrentSeasonId

	// 填充普通段位奖励状态
	rsp.RankRewardStates = make([]*public.PBRankRewardState, 0)
	table.GetTable().TableMainRank.Foreach(func(rank *table_data.TableMainRank) bool {
		if rank.ScoreType == 1 { // 普通积分段位
			state := &public.PBRankRewardState{
				RankId:    rank.ID,
				IsClaimed: t.IsRewardClaimed(rank.ID),
			}
			rsp.RankRewardStates = append(rsp.RankRewardStates, state)
		}
		return false
	})

	// 填充赛季段位奖励状态
	rsp.SeasonRankRewardStates = make([]*public.PBRankRewardState, 0)
	table.GetTable().TableMainRank.Foreach(func(rank *table_data.TableMainRank) bool {
		if rank.ScoreType == 2 { // 赛季积分段位
			state := &public.PBRankRewardState{
				RankId:    rank.ID,
				IsClaimed: t.IsSeasonRewardClaimed(rank.ID),
			}
			rsp.SeasonRankRewardStates = append(rsp.SeasonRankRewardStates, state)
		}
		return false
	})

	// 填充赛季历史
	rsp.SeasonHistory = t.db.SeasonHistory

	log.Info("Season info synced",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("current_trophy", rsp.Trophy),
		log.Kv("current_season_id", rsp.CurrentSeasonId))

	return rsp
}

// HandleClaimSeasonReward 处理赛季奖励领取请求
func (t *Trophy) HandleClaimSeasonReward(req *cs.CLClaimSeasonRewardReq) *cs.LCClaimSeasonRewardRsp {
	rsp := &cs.LCClaimSeasonRewardRsp{}

	// 根据 req.Type 判断是领取普通段位奖励还是赛季段位奖励
	if req.Type == 1 { // 普通段位奖励
		result := t.ClaimRankReward(req.Id)
		rsp.Code = int32(result)
	} else if req.Type == 2 { // 赛季段位奖励
		result := t.ClaimSeasonRankReward(req.Id)
		rsp.Code = int32(result)
	} else {
		rsp.Code = int32(error_code.ERROR_PARAMS)
	}

	log.Info("Season reward claim processed",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("type", req.Type),
		log.Kv("rank_id", req.Id),
		log.Kv("result", rsp.Code))

	return rsp
}

// ClaimSeasonRankReward 领取赛季段位奖励
func (t *Trophy) ClaimSeasonRankReward(rankId int32) error_code.Code {
	// 检查段位是否达到
	if t.db.CurrentTrophy < t.getRequiredTrophyForRank(rankId) {
		return error_code.ERROR_PARAMS
	}

	// 检查是否已领取
	if t.IsSeasonRewardClaimed(rankId) {
		return error_code.ERROR_PARAMS
	}

	// 获取奖励配置
	rankConfig := table.GetTable().TableMainRank.GetById(rankId)
	if rankConfig == nil || rankConfig.ScoreType != 2 {
		return error_code.ERROR_PARAMS
	}

	// 发放奖励
	if len(rankConfig.RewardSeason) > 0 {
		for _, reward := range rankConfig.RewardSeason {
			if len(reward) >= 2 {
				itemId := reward[0]
				count := reward[1]
				t.player.AddItem(itemId, count, game_def.ADD_ITEM_TROPHY)
			}
		}
	}

	// 记录已领取
	t.db.ClaimedSeasonRewards = append(t.db.ClaimedSeasonRewards, rankId)

	log.Info("Season rank reward claimed",
		log.Kv("player_id", t.player.Uid()),
		log.Kv("rank_id", rankId),
		log.Kv("rewards", rankConfig.RewardSeason))

	return error_code.ERROR_OK
}

// HandleSeasonReset 处理来自PlayerSystem的赛季重置消息
func (t *Trophy) HandleSeasonReset(req *cs.SeasonResetReq) {
	t.ResetSeason(req.OldId, req.NewId)
}

func (t *Trophy) syncRank() {
	req := &rankv1.SyncRoleInfoRequest{
		Info: &rankv1.RankRoleInfo{
			Uid:        t.player.Uid(),
			Kserver:    int32(global.ServerId),
			Name:       t.player.Name(),
			Level:      int32(t.player.Level()),
			FightPoint: int64(t.db.CurrentTrophy),
		},
	}
	grpcMgr.GetRankService().SyncRoleInfo(context.Background(), req)

	updateReq := &rankv1.UpdateRoleRankRequest{
		Uid:     t.player.Uid(),
		Kserver: int32(global.ServerId),
		Info:    make([]*rankv1.RankValueInfo, 0),
	}
	updateReq.Info = append(updateReq.Info, &rankv1.RankValueInfo{
		RankType:  int32(rankv1.RankType_FightPoint),
		RankValue: int64(t.db.CurrentTrophy),
	})
	grpcMgr.GetRankService().UpdateRoleRankInfo(context.Background(), updateReq)
}
