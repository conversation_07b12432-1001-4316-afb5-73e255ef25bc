//
// @Author:zhouchen
// @Description: main函数
// @Data: Created in 21:10 2023/6/6

package main

import (
	"context"
	"flag"
	"fmt"
	"liteframe/internal/common/constant"
	"liteframe/internal/microservices/rank/boot"
	"os"

	"github.com/go-kratos/kratos/contrib/registry/etcd/v2"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	_ "go.uber.org/automaxprocs"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name = constant.ServiceNameRank
	// Version is the version of the compiled software.
	Version       string
	ServerVersion string
	ClientVersion string

	id, _   = os.Hostname()
	PidFile = constant.ServiceNameRank + ".pid"
)

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server, r *etcd.Registry, meta map[string]string) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(meta),
		kratos.Logger(logger),
		kratos.Server(
			gs,
			hs,
		),
		kratos.Registrar(r),
		kratos.AfterStart(afterStart),
	)
}

func afterStart(context.Context) error {
	pid := fmt.Sprintf("%d", os.Getpid())
	f, err := os.OpenFile(PidFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		panic(err)
	}
	err = os.WriteFile(PidFile, []byte(pid), 0)
	if err != nil {
		panic(err)
	}
	err = f.Close()
	if err != nil {
		panic(err)
	}
	return nil
}

func main() {
	flag.Parse()

	// 初始化配置
	bf := boot.NewBootConf()
	bc := bf.Run(id, Name)
	defer bf.Stop()

	bc.Server.Id = id
	bc.Server.Name = Name
	bc.Server.Version = Version

	// 初始化log
	logger := boot.NewBootLog(bc).Run()

	log.Info("RankService Init, Version:", Version)

	metaData := map[string]string{}
	metaData["ServerVersion"] = ServerVersion
	metaData["ClientVersion"] = ClientVersion

	app, cleanup, err := wireApp(bc.Server, bc.Data, bc, logger, metaData)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
