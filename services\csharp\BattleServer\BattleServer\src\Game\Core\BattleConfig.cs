using System;
using System.Collections.Generic;
using System.Linq;

namespace BattleServer.Game.Core
{
    /// <summary>
    /// 战斗配置 - 集中管理所有战斗相关的常量和配置
    /// </summary>
    public static class BattleConfig
    {
        private static PlayMode _playModeConfig;

        /// <summary>
        /// 初始化配置（从PlayMode表读取）
        /// </summary>
        public static void Initialize(int playModeId = 1)
        {
            _playModeConfig = TableManager.Instance.playModeManager.Get(playModeId);
            if (_playModeConfig == null)
            {
                LiteFrame.Framework.Log.Error($"PlayMode config not found for ID: {playModeId}");
                throw new InvalidOperationException($"Critical error: PlayMode config not found for ID: {playModeId}. Server cannot start without valid configuration.");
            }

            LiteFrame.Framework.Log.Info($"BattleConfig initialized with PlayMode ID: {playModeId}, Type: {_playModeConfig.Type}, Scene: {_playModeConfig.Scene}");
        }

        /// <summary>
        /// 获取玩法类型
        /// </summary>
        public static int GetPlayModeType()
        {
            return _playModeConfig?.Type ?? 1;
        }

        /// <summary>
        /// 获取战斗场景资源ID
        /// </summary>
        public static int GetBattleSceneId()
        {
            return _playModeConfig?.Scene ?? 1001;
        }

        /// <summary>
        /// 时间配置 (毫秒) - 从PlayMode表读取
        /// </summary>
        public static class Timing
        {
            // 统一缓冲时间 - 用于同步服务器与客户端动画时间
            public const int BufferTime = 5; // 5秒缓冲时间

            // 主要时间配置 - 从PlayMode表读取，统一添加5秒缓冲
            public static int RoundPrepareTime => ((_playModeConfig?.PreDuration ?? 60) + BufferTime) * 1000;
            // 战斗时间 = 配置时间 + 5秒缓冲时间）
            public static int BattleTime => ((_playModeConfig?.BattleDuration ?? 60) + BufferTime) * 1000;
            // Buff选择时间 = 配置时间 + 5秒缓冲时间
            public static int BuffSelectionTime => ((_playModeConfig?.BuffDuration?[0] ?? 20) + BufferTime) * 1000;
            public static int SpeedUpTime => (_playModeConfig?.SpeedUpTime ?? 10) * 1000;

            // 瞬间状态时间配置 - 这些状态应该很快完成
            public const int BattleStartingTime = 1000;     // 1秒 - 战斗开始提示时间
            public const int RoundSettlementTime = 5000;    // 5秒 - 回合结算展示时间（等待客户端发送EnterBattle确认）
            public const int EliminationCheckTime = 1000;   // 1秒 - 淘汰检查展示时间（已废弃，合并到RoundSettlement）

            // 网络延时缓冲时间
            public const int NetworkDelayBuffer = 2000;     // 2秒 - 网络延时缓冲
        }

        /// <summary>
        /// 棋盘配置
        /// </summary>
        public static class Board
        {
            public const int RowCount = 10;                 // 棋盘行数
            public const int ColumnCount = 6;               // 棋盘列数
            public const int MyAreaStart = 1;               // 我方区域起始行
            public const int MyAreaEnd = 5;                 // 我方区域结束行
            public const int EnemyAreaStart = 6;            // 敌方区域起始行
            public const int EnemyAreaEnd = 10;             // 敌方区域结束行
        }

        /// <summary>
        /// 玩家配置 - 从PlayMode表读取
        /// </summary>
        public static class Player
        {
            public const int MaxPlayers = 8;                // 最大玩家数
            public const int MinPlayers = 2;                // 最小玩家数
            public const int MaxHealth = 10;                // 最大血量

            /// <summary>
            /// 根据奖杯数获取玩家血量
            /// 配置格式：[[0,999,3],[1000,100000,4]] 表示0-999杯3血，1000-100000杯4血
            /// 超过上限取上限血量
            /// </summary>
            public static int GetHealthByTrophy(int trophyCount)
            {
                if (_playModeConfig?.PlayerHP == null) return 3;

                int maxHealth = 3; // 默认血量

                foreach (var tier in _playModeConfig.PlayerHP)
                {
                    if (tier.Length >= 3)
                    {
                        // 如果奖杯数在当前区间内，直接返回
                        if (trophyCount >= tier[0] && trophyCount <= tier[1])
                        {
                            return tier[2];
                        }
                        // 记录最高血量（用于超过上限的情况）
                        if (tier[2] > maxHealth)
                        {
                            maxHealth = tier[2];
                        }
                    }
                }

                // 超过所有配置上限时，返回最高血量
                return maxHealth;
            }

            /// <summary>
            /// 获取所有玩家中的最高血量（策划要求：取最高杯段玩家血量）
            /// </summary>
            public static int GetMaxHealthFromTrophies(Dictionary<long, int> playerTrophies)
            {
                if (playerTrophies == null || playerTrophies.Count == 0) return 3;

                int maxHealth = 3;
                foreach (var trophy in playerTrophies.Values)
                {
                    int health = GetHealthByTrophy(trophy);
                    if (health > maxHealth) maxHealth = health;
                }
                return maxHealth;
            }
        }

        /// <summary>
        /// 实体配置
        /// </summary>
        public static class Entity
        {
            public const int MaxStarLevel = 3;              // 最大星级
            public const int MinStarLevel = 1;              // 最小星级
        }

        /// <summary>
        /// Buff配置 - 从PlayMode表读取
        /// </summary>
        public static class Buff
        {
            public const int OptionsPerRound = 3;           // 每回合Buff选项数量

            /// <summary>
            /// 获取Buff选择回合列表
            /// </summary>
            public static int[] GetBuffRounds()
            {
                return _playModeConfig?.BuffRound ?? new int[] { 2, 4, 6 };
            }

            /// <summary>
            /// 获取Buff库列表
            /// </summary>
            public static int[] GetBuffList()
            {
                return _playModeConfig?.BuffList ?? new int[] { 101, 102, 103, 104, 105, 106, 107, 108, 109, 110 };
            }

            /// <summary>
            /// 检查指定回合是否有Buff选择
            /// </summary>
            public static bool HasBuffSelection(int roundCount)
            {
                var buffRounds = GetBuffRounds();
                return buffRounds.Contains(roundCount);
            }
        }

        /// <summary>
        /// 游戏规则配置 - 从PlayMode表读取
        /// </summary>
        public static class GameRules
        {
            public const int HealthLossPerDefeat = 1;       // 每次失败扣血量
            public const int HealthLossPerDraw = 1;         // 平局扣血量

            /// <summary>
            /// 获取指定回合的英雄生成数量
            /// </summary>
            public static int GetHeroCountForRound(int roundCount)
            {
                if (_playModeConfig?.AddHero == null) return 5;

                foreach (var heroConfig in _playModeConfig.AddHero)
                {
                    if (heroConfig.Length >= 4 && roundCount >= heroConfig[0] && roundCount <= heroConfig[1])
                    {
                        return heroConfig[2]; // 返回英雄数量
                    }
                }
                return 5; // 默认数量
            }

            /// <summary>
            /// 获取单角色复选最大次数
            /// </summary>
            public static int GetCheckTimes()
            {
                return _playModeConfig?.CheckTimes ?? 4;
            }
        }
    }
}
