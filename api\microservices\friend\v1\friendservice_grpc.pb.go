//GrpcAddressType:FriendServer
//GrpcServerType:all

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.19.1
// source: microservices/friend/v1/friendservice.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Friendservice_AddFriendApply_FullMethodName                   = "/Aurora.PlayerInfoServer.Friendservice/AddFriendApply"
	Friendservice_ApproveFriendApply_FullMethodName               = "/Aurora.PlayerInfoServer.Friendservice/ApproveFriendApply"
	Friendservice_RemoveFriendApply_FullMethodName                = "/Aurora.PlayerInfoServer.Friendservice/RemoveFriendApply"
	Friendservice_GetFriendApplyList_FullMethodName               = "/Aurora.PlayerInfoServer.Friendservice/GetFriendApplyList"
	Friendservice_AddFriendPlayer_FullMethodName                  = "/Aurora.PlayerInfoServer.Friendservice/AddFriendPlayer"
	Friendservice_DelFriendPlayer_FullMethodName                  = "/Aurora.PlayerInfoServer.Friendservice/DelFriendPlayer"
	Friendservice_SetFriendPlayer_FullMethodName                  = "/Aurora.PlayerInfoServer.Friendservice/SetFriendPlayer"
	Friendservice_GetFriendPlayerList_FullMethodName              = "/Aurora.PlayerInfoServer.Friendservice/GetFriendPlayerList"
	Friendservice_CreateFriendGroup_FullMethodName                = "/Aurora.PlayerInfoServer.Friendservice/CreateFriendGroup"
	Friendservice_SetFriendGroup_FullMethodName                   = "/Aurora.PlayerInfoServer.Friendservice/SetFriendGroup"
	Friendservice_DelFriendGroup_FullMethodName                   = "/Aurora.PlayerInfoServer.Friendservice/DelFriendGroup"
	Friendservice_AddPlayerFriendGroup_FullMethodName             = "/Aurora.PlayerInfoServer.Friendservice/AddPlayerFriendGroup"
	Friendservice_DelPlayerFriendGroup_FullMethodName             = "/Aurora.PlayerInfoServer.Friendservice/DelPlayerFriendGroup"
	Friendservice_SetFriendRemarks_FullMethodName                 = "/Aurora.PlayerInfoServer.Friendservice/SetFriendRemarks"
	Friendservice_SetSelfAttention_FullMethodName                 = "/Aurora.PlayerInfoServer.Friendservice/SetSelfAttention"
	Friendservice_SetGroupBase_FullMethodName                     = "/Aurora.PlayerInfoServer.Friendservice/SetGroupBase"
	Friendservice_ApplyJoinGroup_FullMethodName                   = "/Aurora.PlayerInfoServer.Friendservice/ApplyJoinGroup"
	Friendservice_DelGroupPlayer_FullMethodName                   = "/Aurora.PlayerInfoServer.Friendservice/DelGroupPlayer"
	Friendservice_PlayerInviteJoinGroup_FullMethodName            = "/Aurora.PlayerInfoServer.Friendservice/PlayerInviteJoinGroup"
	Friendservice_GetGroupList_FullMethodName                     = "/Aurora.PlayerInfoServer.Friendservice/GetGroupList"
	Friendservice_GetGroupPlayerListByGroupId_FullMethodName      = "/Aurora.PlayerInfoServer.Friendservice/GetGroupPlayerListByGroupId"
	Friendservice_SetFriendBaseInfoRep_FullMethodName             = "/Aurora.PlayerInfoServer.Friendservice/SetFriendBaseInfoRep"
	Friendservice_GetMyGroupInviteJoinGroupListRep_FullMethodName = "/Aurora.PlayerInfoServer.Friendservice/GetMyGroupInviteJoinGroupListRep"
)

// FriendserviceClient is the client API for Friendservice service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ServiceStart
type FriendserviceClient interface {
	AddFriendApply(ctx context.Context, in *AddFriendApplyInfo, opts ...grpc.CallOption) (*AddFriendApplyResult, error)
	ApproveFriendApply(ctx context.Context, in *ApproveFriendApplyInfo, opts ...grpc.CallOption) (*ApproveFriendApplyResult, error)
	RemoveFriendApply(ctx context.Context, in *RemoveFriendApplyInfo, opts ...grpc.CallOption) (*RemoveFriendApplyResult, error)
	GetFriendApplyList(ctx context.Context, in *GetFriendApplyListInfo, opts ...grpc.CallOption) (*GetFriendApplyListResult, error)
	AddFriendPlayer(ctx context.Context, in *AddFriendPlayerInfo, opts ...grpc.CallOption) (*AddFriendResult, error)
	DelFriendPlayer(ctx context.Context, in *DelFriendPlayerInfo, opts ...grpc.CallOption) (*DelFriendResult, error)
	SetFriendPlayer(ctx context.Context, in *SetFriendPlayerInfo, opts ...grpc.CallOption) (*SetFriendResult, error)
	GetFriendPlayerList(ctx context.Context, in *GetFriendPlayerListInfo, opts ...grpc.CallOption) (*GetFriendPlayerListInfoResult, error)
	CreateFriendGroup(ctx context.Context, in *CreateFriendGroupInfo, opts ...grpc.CallOption) (*CreateFriendGroupResult, error)
	SetFriendGroup(ctx context.Context, in *SetFriendGroupInfo, opts ...grpc.CallOption) (*SetFriendGroupResult, error)
	DelFriendGroup(ctx context.Context, in *DelFriendGroupInfo, opts ...grpc.CallOption) (*DelFriendGroupResult, error)
	AddPlayerFriendGroup(ctx context.Context, in *AddPlayerFriendGroupInfo, opts ...grpc.CallOption) (*AddPlayerFriendGroupResult, error)
	DelPlayerFriendGroup(ctx context.Context, in *AddPlayerFriendGroupInfo, opts ...grpc.CallOption) (*AddPlayerFriendGroupResult, error)
	SetFriendRemarks(ctx context.Context, in *SetFriendRemarksInfo, opts ...grpc.CallOption) (*SetFriendRemarksResult, error)
	SetSelfAttention(ctx context.Context, in *SetSelfAttentionInfo, opts ...grpc.CallOption) (*SetSelfAttentionResult, error)
	SetGroupBase(ctx context.Context, in *GroupBaseInfo, opts ...grpc.CallOption) (*CreateGroupResult, error)
	ApplyJoinGroup(ctx context.Context, in *ApplyGroupListInfo, opts ...grpc.CallOption) (*ApplyGroupListResult, error)
	DelGroupPlayer(ctx context.Context, in *DelGroupPlayerInfo, opts ...grpc.CallOption) (*DelGroupPlayerResult, error)
	PlayerInviteJoinGroup(ctx context.Context, in *PlayerInviteJoinGroupInfo, opts ...grpc.CallOption) (*PlayerInviteJoinGroupResult, error)
	GetGroupList(ctx context.Context, in *GetGroupListInfo, opts ...grpc.CallOption) (*GetGroupListResult, error)
	GetGroupPlayerListByGroupId(ctx context.Context, in *GetGroupPlayerListByGroupIdInfo, opts ...grpc.CallOption) (*GetGroupPlayerListByGroupIdResult, error)
	SetFriendBaseInfoRep(ctx context.Context, in *SetFriendBaseInfo, opts ...grpc.CallOption) (*SetFriendBaseInfoResult, error)
	GetMyGroupInviteJoinGroupListRep(ctx context.Context, in *GetMyGroupInviteJoinGroupList, opts ...grpc.CallOption) (*GetMyGroupInviteJoinGroupListResult, error)
}

type friendserviceClient struct {
	cc grpc.ClientConnInterface
}

func NewFriendserviceClient(cc grpc.ClientConnInterface) FriendserviceClient {
	return &friendserviceClient{cc}
}

func (c *friendserviceClient) AddFriendApply(ctx context.Context, in *AddFriendApplyInfo, opts ...grpc.CallOption) (*AddFriendApplyResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddFriendApplyResult)
	err := c.cc.Invoke(ctx, Friendservice_AddFriendApply_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) ApproveFriendApply(ctx context.Context, in *ApproveFriendApplyInfo, opts ...grpc.CallOption) (*ApproveFriendApplyResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApproveFriendApplyResult)
	err := c.cc.Invoke(ctx, Friendservice_ApproveFriendApply_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) RemoveFriendApply(ctx context.Context, in *RemoveFriendApplyInfo, opts ...grpc.CallOption) (*RemoveFriendApplyResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveFriendApplyResult)
	err := c.cc.Invoke(ctx, Friendservice_RemoveFriendApply_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) GetFriendApplyList(ctx context.Context, in *GetFriendApplyListInfo, opts ...grpc.CallOption) (*GetFriendApplyListResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFriendApplyListResult)
	err := c.cc.Invoke(ctx, Friendservice_GetFriendApplyList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) AddFriendPlayer(ctx context.Context, in *AddFriendPlayerInfo, opts ...grpc.CallOption) (*AddFriendResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddFriendResult)
	err := c.cc.Invoke(ctx, Friendservice_AddFriendPlayer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) DelFriendPlayer(ctx context.Context, in *DelFriendPlayerInfo, opts ...grpc.CallOption) (*DelFriendResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DelFriendResult)
	err := c.cc.Invoke(ctx, Friendservice_DelFriendPlayer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) SetFriendPlayer(ctx context.Context, in *SetFriendPlayerInfo, opts ...grpc.CallOption) (*SetFriendResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetFriendResult)
	err := c.cc.Invoke(ctx, Friendservice_SetFriendPlayer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) GetFriendPlayerList(ctx context.Context, in *GetFriendPlayerListInfo, opts ...grpc.CallOption) (*GetFriendPlayerListInfoResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFriendPlayerListInfoResult)
	err := c.cc.Invoke(ctx, Friendservice_GetFriendPlayerList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) CreateFriendGroup(ctx context.Context, in *CreateFriendGroupInfo, opts ...grpc.CallOption) (*CreateFriendGroupResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateFriendGroupResult)
	err := c.cc.Invoke(ctx, Friendservice_CreateFriendGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) SetFriendGroup(ctx context.Context, in *SetFriendGroupInfo, opts ...grpc.CallOption) (*SetFriendGroupResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetFriendGroupResult)
	err := c.cc.Invoke(ctx, Friendservice_SetFriendGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) DelFriendGroup(ctx context.Context, in *DelFriendGroupInfo, opts ...grpc.CallOption) (*DelFriendGroupResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DelFriendGroupResult)
	err := c.cc.Invoke(ctx, Friendservice_DelFriendGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) AddPlayerFriendGroup(ctx context.Context, in *AddPlayerFriendGroupInfo, opts ...grpc.CallOption) (*AddPlayerFriendGroupResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddPlayerFriendGroupResult)
	err := c.cc.Invoke(ctx, Friendservice_AddPlayerFriendGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) DelPlayerFriendGroup(ctx context.Context, in *AddPlayerFriendGroupInfo, opts ...grpc.CallOption) (*AddPlayerFriendGroupResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddPlayerFriendGroupResult)
	err := c.cc.Invoke(ctx, Friendservice_DelPlayerFriendGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) SetFriendRemarks(ctx context.Context, in *SetFriendRemarksInfo, opts ...grpc.CallOption) (*SetFriendRemarksResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetFriendRemarksResult)
	err := c.cc.Invoke(ctx, Friendservice_SetFriendRemarks_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) SetSelfAttention(ctx context.Context, in *SetSelfAttentionInfo, opts ...grpc.CallOption) (*SetSelfAttentionResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetSelfAttentionResult)
	err := c.cc.Invoke(ctx, Friendservice_SetSelfAttention_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) SetGroupBase(ctx context.Context, in *GroupBaseInfo, opts ...grpc.CallOption) (*CreateGroupResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateGroupResult)
	err := c.cc.Invoke(ctx, Friendservice_SetGroupBase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) ApplyJoinGroup(ctx context.Context, in *ApplyGroupListInfo, opts ...grpc.CallOption) (*ApplyGroupListResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ApplyGroupListResult)
	err := c.cc.Invoke(ctx, Friendservice_ApplyJoinGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) DelGroupPlayer(ctx context.Context, in *DelGroupPlayerInfo, opts ...grpc.CallOption) (*DelGroupPlayerResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DelGroupPlayerResult)
	err := c.cc.Invoke(ctx, Friendservice_DelGroupPlayer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) PlayerInviteJoinGroup(ctx context.Context, in *PlayerInviteJoinGroupInfo, opts ...grpc.CallOption) (*PlayerInviteJoinGroupResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PlayerInviteJoinGroupResult)
	err := c.cc.Invoke(ctx, Friendservice_PlayerInviteJoinGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) GetGroupList(ctx context.Context, in *GetGroupListInfo, opts ...grpc.CallOption) (*GetGroupListResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGroupListResult)
	err := c.cc.Invoke(ctx, Friendservice_GetGroupList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) GetGroupPlayerListByGroupId(ctx context.Context, in *GetGroupPlayerListByGroupIdInfo, opts ...grpc.CallOption) (*GetGroupPlayerListByGroupIdResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGroupPlayerListByGroupIdResult)
	err := c.cc.Invoke(ctx, Friendservice_GetGroupPlayerListByGroupId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) SetFriendBaseInfoRep(ctx context.Context, in *SetFriendBaseInfo, opts ...grpc.CallOption) (*SetFriendBaseInfoResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetFriendBaseInfoResult)
	err := c.cc.Invoke(ctx, Friendservice_SetFriendBaseInfoRep_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *friendserviceClient) GetMyGroupInviteJoinGroupListRep(ctx context.Context, in *GetMyGroupInviteJoinGroupList, opts ...grpc.CallOption) (*GetMyGroupInviteJoinGroupListResult, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMyGroupInviteJoinGroupListResult)
	err := c.cc.Invoke(ctx, Friendservice_GetMyGroupInviteJoinGroupListRep_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// FriendserviceServer is the server API for Friendservice service.
// All implementations must embed UnimplementedFriendserviceServer
// for forward compatibility.
//
// ServiceStart
type FriendserviceServer interface {
	AddFriendApply(context.Context, *AddFriendApplyInfo) (*AddFriendApplyResult, error)
	ApproveFriendApply(context.Context, *ApproveFriendApplyInfo) (*ApproveFriendApplyResult, error)
	RemoveFriendApply(context.Context, *RemoveFriendApplyInfo) (*RemoveFriendApplyResult, error)
	GetFriendApplyList(context.Context, *GetFriendApplyListInfo) (*GetFriendApplyListResult, error)
	AddFriendPlayer(context.Context, *AddFriendPlayerInfo) (*AddFriendResult, error)
	DelFriendPlayer(context.Context, *DelFriendPlayerInfo) (*DelFriendResult, error)
	SetFriendPlayer(context.Context, *SetFriendPlayerInfo) (*SetFriendResult, error)
	GetFriendPlayerList(context.Context, *GetFriendPlayerListInfo) (*GetFriendPlayerListInfoResult, error)
	CreateFriendGroup(context.Context, *CreateFriendGroupInfo) (*CreateFriendGroupResult, error)
	SetFriendGroup(context.Context, *SetFriendGroupInfo) (*SetFriendGroupResult, error)
	DelFriendGroup(context.Context, *DelFriendGroupInfo) (*DelFriendGroupResult, error)
	AddPlayerFriendGroup(context.Context, *AddPlayerFriendGroupInfo) (*AddPlayerFriendGroupResult, error)
	DelPlayerFriendGroup(context.Context, *AddPlayerFriendGroupInfo) (*AddPlayerFriendGroupResult, error)
	SetFriendRemarks(context.Context, *SetFriendRemarksInfo) (*SetFriendRemarksResult, error)
	SetSelfAttention(context.Context, *SetSelfAttentionInfo) (*SetSelfAttentionResult, error)
	SetGroupBase(context.Context, *GroupBaseInfo) (*CreateGroupResult, error)
	ApplyJoinGroup(context.Context, *ApplyGroupListInfo) (*ApplyGroupListResult, error)
	DelGroupPlayer(context.Context, *DelGroupPlayerInfo) (*DelGroupPlayerResult, error)
	PlayerInviteJoinGroup(context.Context, *PlayerInviteJoinGroupInfo) (*PlayerInviteJoinGroupResult, error)
	GetGroupList(context.Context, *GetGroupListInfo) (*GetGroupListResult, error)
	GetGroupPlayerListByGroupId(context.Context, *GetGroupPlayerListByGroupIdInfo) (*GetGroupPlayerListByGroupIdResult, error)
	SetFriendBaseInfoRep(context.Context, *SetFriendBaseInfo) (*SetFriendBaseInfoResult, error)
	GetMyGroupInviteJoinGroupListRep(context.Context, *GetMyGroupInviteJoinGroupList) (*GetMyGroupInviteJoinGroupListResult, error)
	mustEmbedUnimplementedFriendserviceServer()
}

// UnimplementedFriendserviceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedFriendserviceServer struct{}

func (UnimplementedFriendserviceServer) AddFriendApply(context.Context, *AddFriendApplyInfo) (*AddFriendApplyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddFriendApply not implemented")
}
func (UnimplementedFriendserviceServer) ApproveFriendApply(context.Context, *ApproveFriendApplyInfo) (*ApproveFriendApplyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApproveFriendApply not implemented")
}
func (UnimplementedFriendserviceServer) RemoveFriendApply(context.Context, *RemoveFriendApplyInfo) (*RemoveFriendApplyResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveFriendApply not implemented")
}
func (UnimplementedFriendserviceServer) GetFriendApplyList(context.Context, *GetFriendApplyListInfo) (*GetFriendApplyListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFriendApplyList not implemented")
}
func (UnimplementedFriendserviceServer) AddFriendPlayer(context.Context, *AddFriendPlayerInfo) (*AddFriendResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddFriendPlayer not implemented")
}
func (UnimplementedFriendserviceServer) DelFriendPlayer(context.Context, *DelFriendPlayerInfo) (*DelFriendResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelFriendPlayer not implemented")
}
func (UnimplementedFriendserviceServer) SetFriendPlayer(context.Context, *SetFriendPlayerInfo) (*SetFriendResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetFriendPlayer not implemented")
}
func (UnimplementedFriendserviceServer) GetFriendPlayerList(context.Context, *GetFriendPlayerListInfo) (*GetFriendPlayerListInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFriendPlayerList not implemented")
}
func (UnimplementedFriendserviceServer) CreateFriendGroup(context.Context, *CreateFriendGroupInfo) (*CreateFriendGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFriendGroup not implemented")
}
func (UnimplementedFriendserviceServer) SetFriendGroup(context.Context, *SetFriendGroupInfo) (*SetFriendGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetFriendGroup not implemented")
}
func (UnimplementedFriendserviceServer) DelFriendGroup(context.Context, *DelFriendGroupInfo) (*DelFriendGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelFriendGroup not implemented")
}
func (UnimplementedFriendserviceServer) AddPlayerFriendGroup(context.Context, *AddPlayerFriendGroupInfo) (*AddPlayerFriendGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPlayerFriendGroup not implemented")
}
func (UnimplementedFriendserviceServer) DelPlayerFriendGroup(context.Context, *AddPlayerFriendGroupInfo) (*AddPlayerFriendGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelPlayerFriendGroup not implemented")
}
func (UnimplementedFriendserviceServer) SetFriendRemarks(context.Context, *SetFriendRemarksInfo) (*SetFriendRemarksResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetFriendRemarks not implemented")
}
func (UnimplementedFriendserviceServer) SetSelfAttention(context.Context, *SetSelfAttentionInfo) (*SetSelfAttentionResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetSelfAttention not implemented")
}
func (UnimplementedFriendserviceServer) SetGroupBase(context.Context, *GroupBaseInfo) (*CreateGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetGroupBase not implemented")
}
func (UnimplementedFriendserviceServer) ApplyJoinGroup(context.Context, *ApplyGroupListInfo) (*ApplyGroupListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyJoinGroup not implemented")
}
func (UnimplementedFriendserviceServer) DelGroupPlayer(context.Context, *DelGroupPlayerInfo) (*DelGroupPlayerResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelGroupPlayer not implemented")
}
func (UnimplementedFriendserviceServer) PlayerInviteJoinGroup(context.Context, *PlayerInviteJoinGroupInfo) (*PlayerInviteJoinGroupResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlayerInviteJoinGroup not implemented")
}
func (UnimplementedFriendserviceServer) GetGroupList(context.Context, *GetGroupListInfo) (*GetGroupListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupList not implemented")
}
func (UnimplementedFriendserviceServer) GetGroupPlayerListByGroupId(context.Context, *GetGroupPlayerListByGroupIdInfo) (*GetGroupPlayerListByGroupIdResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupPlayerListByGroupId not implemented")
}
func (UnimplementedFriendserviceServer) SetFriendBaseInfoRep(context.Context, *SetFriendBaseInfo) (*SetFriendBaseInfoResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetFriendBaseInfoRep not implemented")
}
func (UnimplementedFriendserviceServer) GetMyGroupInviteJoinGroupListRep(context.Context, *GetMyGroupInviteJoinGroupList) (*GetMyGroupInviteJoinGroupListResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMyGroupInviteJoinGroupListRep not implemented")
}
func (UnimplementedFriendserviceServer) mustEmbedUnimplementedFriendserviceServer() {}
func (UnimplementedFriendserviceServer) testEmbeddedByValue()                       {}

// UnsafeFriendserviceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to FriendserviceServer will
// result in compilation errors.
type UnsafeFriendserviceServer interface {
	mustEmbedUnimplementedFriendserviceServer()
}

func RegisterFriendserviceServer(s grpc.ServiceRegistrar, srv FriendserviceServer) {
	// If the following call pancis, it indicates UnimplementedFriendserviceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Friendservice_ServiceDesc, srv)
}

func _Friendservice_AddFriendApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFriendApplyInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).AddFriendApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_AddFriendApply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).AddFriendApply(ctx, req.(*AddFriendApplyInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_ApproveFriendApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApproveFriendApplyInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).ApproveFriendApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_ApproveFriendApply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).ApproveFriendApply(ctx, req.(*ApproveFriendApplyInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_RemoveFriendApply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveFriendApplyInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).RemoveFriendApply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_RemoveFriendApply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).RemoveFriendApply(ctx, req.(*RemoveFriendApplyInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_GetFriendApplyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFriendApplyListInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).GetFriendApplyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_GetFriendApplyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).GetFriendApplyList(ctx, req.(*GetFriendApplyListInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_AddFriendPlayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddFriendPlayerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).AddFriendPlayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_AddFriendPlayer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).AddFriendPlayer(ctx, req.(*AddFriendPlayerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_DelFriendPlayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFriendPlayerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).DelFriendPlayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_DelFriendPlayer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).DelFriendPlayer(ctx, req.(*DelFriendPlayerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_SetFriendPlayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetFriendPlayerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).SetFriendPlayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_SetFriendPlayer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).SetFriendPlayer(ctx, req.(*SetFriendPlayerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_GetFriendPlayerList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFriendPlayerListInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).GetFriendPlayerList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_GetFriendPlayerList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).GetFriendPlayerList(ctx, req.(*GetFriendPlayerListInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_CreateFriendGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFriendGroupInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).CreateFriendGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_CreateFriendGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).CreateFriendGroup(ctx, req.(*CreateFriendGroupInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_SetFriendGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetFriendGroupInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).SetFriendGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_SetFriendGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).SetFriendGroup(ctx, req.(*SetFriendGroupInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_DelFriendGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelFriendGroupInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).DelFriendGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_DelFriendGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).DelFriendGroup(ctx, req.(*DelFriendGroupInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_AddPlayerFriendGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPlayerFriendGroupInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).AddPlayerFriendGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_AddPlayerFriendGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).AddPlayerFriendGroup(ctx, req.(*AddPlayerFriendGroupInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_DelPlayerFriendGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPlayerFriendGroupInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).DelPlayerFriendGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_DelPlayerFriendGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).DelPlayerFriendGroup(ctx, req.(*AddPlayerFriendGroupInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_SetFriendRemarks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetFriendRemarksInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).SetFriendRemarks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_SetFriendRemarks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).SetFriendRemarks(ctx, req.(*SetFriendRemarksInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_SetSelfAttention_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetSelfAttentionInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).SetSelfAttention(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_SetSelfAttention_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).SetSelfAttention(ctx, req.(*SetSelfAttentionInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_SetGroupBase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupBaseInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).SetGroupBase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_SetGroupBase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).SetGroupBase(ctx, req.(*GroupBaseInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_ApplyJoinGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyGroupListInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).ApplyJoinGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_ApplyJoinGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).ApplyJoinGroup(ctx, req.(*ApplyGroupListInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_DelGroupPlayer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DelGroupPlayerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).DelGroupPlayer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_DelGroupPlayer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).DelGroupPlayer(ctx, req.(*DelGroupPlayerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_PlayerInviteJoinGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlayerInviteJoinGroupInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).PlayerInviteJoinGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_PlayerInviteJoinGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).PlayerInviteJoinGroup(ctx, req.(*PlayerInviteJoinGroupInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_GetGroupList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupListInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).GetGroupList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_GetGroupList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).GetGroupList(ctx, req.(*GetGroupListInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_GetGroupPlayerListByGroupId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupPlayerListByGroupIdInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).GetGroupPlayerListByGroupId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_GetGroupPlayerListByGroupId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).GetGroupPlayerListByGroupId(ctx, req.(*GetGroupPlayerListByGroupIdInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_SetFriendBaseInfoRep_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetFriendBaseInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).SetFriendBaseInfoRep(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_SetFriendBaseInfoRep_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).SetFriendBaseInfoRep(ctx, req.(*SetFriendBaseInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Friendservice_GetMyGroupInviteJoinGroupListRep_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMyGroupInviteJoinGroupList)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(FriendserviceServer).GetMyGroupInviteJoinGroupListRep(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Friendservice_GetMyGroupInviteJoinGroupListRep_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(FriendserviceServer).GetMyGroupInviteJoinGroupListRep(ctx, req.(*GetMyGroupInviteJoinGroupList))
	}
	return interceptor(ctx, in, info, handler)
}

// Friendservice_ServiceDesc is the grpc.ServiceDesc for Friendservice service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Friendservice_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Aurora.PlayerInfoServer.Friendservice",
	HandlerType: (*FriendserviceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddFriendApply",
			Handler:    _Friendservice_AddFriendApply_Handler,
		},
		{
			MethodName: "ApproveFriendApply",
			Handler:    _Friendservice_ApproveFriendApply_Handler,
		},
		{
			MethodName: "RemoveFriendApply",
			Handler:    _Friendservice_RemoveFriendApply_Handler,
		},
		{
			MethodName: "GetFriendApplyList",
			Handler:    _Friendservice_GetFriendApplyList_Handler,
		},
		{
			MethodName: "AddFriendPlayer",
			Handler:    _Friendservice_AddFriendPlayer_Handler,
		},
		{
			MethodName: "DelFriendPlayer",
			Handler:    _Friendservice_DelFriendPlayer_Handler,
		},
		{
			MethodName: "SetFriendPlayer",
			Handler:    _Friendservice_SetFriendPlayer_Handler,
		},
		{
			MethodName: "GetFriendPlayerList",
			Handler:    _Friendservice_GetFriendPlayerList_Handler,
		},
		{
			MethodName: "CreateFriendGroup",
			Handler:    _Friendservice_CreateFriendGroup_Handler,
		},
		{
			MethodName: "SetFriendGroup",
			Handler:    _Friendservice_SetFriendGroup_Handler,
		},
		{
			MethodName: "DelFriendGroup",
			Handler:    _Friendservice_DelFriendGroup_Handler,
		},
		{
			MethodName: "AddPlayerFriendGroup",
			Handler:    _Friendservice_AddPlayerFriendGroup_Handler,
		},
		{
			MethodName: "DelPlayerFriendGroup",
			Handler:    _Friendservice_DelPlayerFriendGroup_Handler,
		},
		{
			MethodName: "SetFriendRemarks",
			Handler:    _Friendservice_SetFriendRemarks_Handler,
		},
		{
			MethodName: "SetSelfAttention",
			Handler:    _Friendservice_SetSelfAttention_Handler,
		},
		{
			MethodName: "SetGroupBase",
			Handler:    _Friendservice_SetGroupBase_Handler,
		},
		{
			MethodName: "ApplyJoinGroup",
			Handler:    _Friendservice_ApplyJoinGroup_Handler,
		},
		{
			MethodName: "DelGroupPlayer",
			Handler:    _Friendservice_DelGroupPlayer_Handler,
		},
		{
			MethodName: "PlayerInviteJoinGroup",
			Handler:    _Friendservice_PlayerInviteJoinGroup_Handler,
		},
		{
			MethodName: "GetGroupList",
			Handler:    _Friendservice_GetGroupList_Handler,
		},
		{
			MethodName: "GetGroupPlayerListByGroupId",
			Handler:    _Friendservice_GetGroupPlayerListByGroupId_Handler,
		},
		{
			MethodName: "SetFriendBaseInfoRep",
			Handler:    _Friendservice_SetFriendBaseInfoRep_Handler,
		},
		{
			MethodName: "GetMyGroupInviteJoinGroupListRep",
			Handler:    _Friendservice_GetMyGroupInviteJoinGroupListRep_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "microservices/friend/v1/friendservice.proto",
}
