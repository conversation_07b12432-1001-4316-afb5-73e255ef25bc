//GrpcAddressType:PlayerInfoServer
//GrpcServerType:all

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.1
// source: microservices/playerinfo/v1/playerinfostruct.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// StructStart
// Type:Http
// Type:Inner
type ENUM_PlayerInfo int32

const (
	ENUM_PlayerInfo_None              ENUM_PlayerInfo = 0
	ENUM_PlayerInfo_PlayerBaseInfoPis ENUM_PlayerInfo = 1
	ENUM_PlayerInfo_BaseAttrPis       ENUM_PlayerInfo = 2
	ENUM_PlayerInfo_BagAttrPis        ENUM_PlayerInfo = 4
	ENUM_PlayerInfo_CharBaseAttrPis   ENUM_PlayerInfo = 8
	ENUM_PlayerInfo_PetPis            ENUM_PlayerInfo = 16
	ENUM_PlayerInfo_TeamPis           ENUM_PlayerInfo = 32
	ENUM_PlayerInfo_CharGuildAttrPis  ENUM_PlayerInfo = 64
	ENUM_PlayerInfo_BloodVesselPis    ENUM_PlayerInfo = 128
)

// Enum value maps for ENUM_PlayerInfo.
var (
	ENUM_PlayerInfo_name = map[int32]string{
		0:   "None",
		1:   "PlayerBaseInfoPis",
		2:   "BaseAttrPis",
		4:   "BagAttrPis",
		8:   "CharBaseAttrPis",
		16:  "PetPis",
		32:  "TeamPis",
		64:  "CharGuildAttrPis",
		128: "BloodVesselPis",
	}
	ENUM_PlayerInfo_value = map[string]int32{
		"None":              0,
		"PlayerBaseInfoPis": 1,
		"BaseAttrPis":       2,
		"BagAttrPis":        4,
		"CharBaseAttrPis":   8,
		"PetPis":            16,
		"TeamPis":           32,
		"CharGuildAttrPis":  64,
		"BloodVesselPis":    128,
	}
)

func (x ENUM_PlayerInfo) Enum() *ENUM_PlayerInfo {
	p := new(ENUM_PlayerInfo)
	*p = x
	return p
}

func (x ENUM_PlayerInfo) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ENUM_PlayerInfo) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_enumTypes[0].Descriptor()
}

func (ENUM_PlayerInfo) Type() protoreflect.EnumType {
	return &file_microservices_playerinfo_v1_playerinfostruct_proto_enumTypes[0]
}

func (x ENUM_PlayerInfo) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ENUM_PlayerInfo.Descriptor instead.
func (ENUM_PlayerInfo) EnumDescriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{0}
}

// Type:Http
// Type:Inner
type PlayerBaseInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FightPoint            int64              `protobuf:"varint,1,opt,name=FightPoint,proto3" json:"FightPoint,omitempty"`
	HenshinPetId          int32              `protobuf:"varint,2,opt,name=HenshinPetId,proto3" json:"HenshinPetId,omitempty"`
	Level                 int32              `protobuf:"varint,3,opt,name=Level,proto3" json:"Level,omitempty"`
	Name                  string             `protobuf:"bytes,4,opt,name=Name,proto3" json:"Name,omitempty"`
	SceneId               int32              `protobuf:"varint,5,opt,name=SceneId,proto3" json:"SceneId,omitempty"`
	Sex                   int32              `protobuf:"varint,6,opt,name=Sex,proto3" json:"Sex,omitempty"`
	UnitType              int32              `protobuf:"varint,7,opt,name=UnitType,proto3" json:"UnitType,omitempty"`
	ForbiddenLoginEndTime uint64             `protobuf:"varint,8,opt,name=ForbiddenLoginEndTime,proto3" json:"ForbiddenLoginEndTime,omitempty"`
	Guid                  uint64             `protobuf:"varint,9,opt,name=Guid,proto3" json:"Guid,omitempty"`
	ServerNum             uint32             `protobuf:"varint,10,opt,name=ServerNum,proto3" json:"ServerNum,omitempty"`
	ZoneWorldID           uint32             `protobuf:"varint,11,opt,name=ZoneWorldID,proto3" json:"ZoneWorldID,omitempty"`
	HenshinPet            *PetInfoAttr_PISct `protobuf:"bytes,12,opt,name=HenshinPet,proto3" json:"HenshinPet,omitempty"`
	PetID                 int32              `protobuf:"varint,13,opt,name=PetID,proto3" json:"PetID,omitempty"`
	SelfTeamIndexId       int32              `protobuf:"varint,14,opt,name=SelfTeamIndexId,proto3" json:"SelfTeamIndexId,omitempty"`
	TeamId                uint64             `protobuf:"varint,15,opt,name=TeamId,proto3" json:"TeamId,omitempty"`
	TeamJobTitle          int32              `protobuf:"varint,16,opt,name=TeamJobTitle,proto3" json:"TeamJobTitle,omitempty"`
	TeamMemCount          int32              `protobuf:"varint,17,opt,name=TeamMemCount,proto3" json:"TeamMemCount,omitempty"`
	TeamPlayerCount       int32              `protobuf:"varint,18,opt,name=TeamPlayerCount,proto3" json:"TeamPlayerCount,omitempty"`
	TeamType              int32              `protobuf:"varint,19,opt,name=TeamType,proto3" json:"TeamType,omitempty"`
	GuildGuid             uint64             `protobuf:"varint,20,opt,name=GuildGuid,proto3" json:"GuildGuid,omitempty"`
	GuildName             string             `protobuf:"bytes,21,opt,name=GuildName,proto3" json:"GuildName,omitempty"`
	HeadIcon              int32              `protobuf:"varint,22,opt,name=HeadIcon,proto3" json:"HeadIcon,omitempty"`
	HeadFrame             int32              `protobuf:"varint,23,opt,name=HeadFrame,proto3" json:"HeadFrame,omitempty"`
	IsOnline              bool               `protobuf:"varint,24,opt,name=IsOnline,proto3" json:"IsOnline,omitempty"`
	LastLoginTime         int64              `protobuf:"varint,25,opt,name=LastLoginTime,proto3" json:"LastLoginTime,omitempty"`
}

func (x *PlayerBaseInfo) Reset() {
	*x = PlayerBaseInfo{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerBaseInfo) ProtoMessage() {}

func (x *PlayerBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerBaseInfo.ProtoReflect.Descriptor instead.
func (*PlayerBaseInfo) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{0}
}

func (x *PlayerBaseInfo) GetFightPoint() int64 {
	if x != nil {
		return x.FightPoint
	}
	return 0
}

func (x *PlayerBaseInfo) GetHenshinPetId() int32 {
	if x != nil {
		return x.HenshinPetId
	}
	return 0
}

func (x *PlayerBaseInfo) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PlayerBaseInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PlayerBaseInfo) GetSceneId() int32 {
	if x != nil {
		return x.SceneId
	}
	return 0
}

func (x *PlayerBaseInfo) GetSex() int32 {
	if x != nil {
		return x.Sex
	}
	return 0
}

func (x *PlayerBaseInfo) GetUnitType() int32 {
	if x != nil {
		return x.UnitType
	}
	return 0
}

func (x *PlayerBaseInfo) GetForbiddenLoginEndTime() uint64 {
	if x != nil {
		return x.ForbiddenLoginEndTime
	}
	return 0
}

func (x *PlayerBaseInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *PlayerBaseInfo) GetServerNum() uint32 {
	if x != nil {
		return x.ServerNum
	}
	return 0
}

func (x *PlayerBaseInfo) GetZoneWorldID() uint32 {
	if x != nil {
		return x.ZoneWorldID
	}
	return 0
}

func (x *PlayerBaseInfo) GetHenshinPet() *PetInfoAttr_PISct {
	if x != nil {
		return x.HenshinPet
	}
	return nil
}

func (x *PlayerBaseInfo) GetPetID() int32 {
	if x != nil {
		return x.PetID
	}
	return 0
}

func (x *PlayerBaseInfo) GetSelfTeamIndexId() int32 {
	if x != nil {
		return x.SelfTeamIndexId
	}
	return 0
}

func (x *PlayerBaseInfo) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *PlayerBaseInfo) GetTeamJobTitle() int32 {
	if x != nil {
		return x.TeamJobTitle
	}
	return 0
}

func (x *PlayerBaseInfo) GetTeamMemCount() int32 {
	if x != nil {
		return x.TeamMemCount
	}
	return 0
}

func (x *PlayerBaseInfo) GetTeamPlayerCount() int32 {
	if x != nil {
		return x.TeamPlayerCount
	}
	return 0
}

func (x *PlayerBaseInfo) GetTeamType() int32 {
	if x != nil {
		return x.TeamType
	}
	return 0
}

func (x *PlayerBaseInfo) GetGuildGuid() uint64 {
	if x != nil {
		return x.GuildGuid
	}
	return 0
}

func (x *PlayerBaseInfo) GetGuildName() string {
	if x != nil {
		return x.GuildName
	}
	return ""
}

func (x *PlayerBaseInfo) GetHeadIcon() int32 {
	if x != nil {
		return x.HeadIcon
	}
	return 0
}

func (x *PlayerBaseInfo) GetHeadFrame() int32 {
	if x != nil {
		return x.HeadFrame
	}
	return 0
}

func (x *PlayerBaseInfo) GetIsOnline() bool {
	if x != nil {
		return x.IsOnline
	}
	return false
}

func (x *PlayerBaseInfo) GetLastLoginTime() int64 {
	if x != nil {
		return x.LastLoginTime
	}
	return 0
}

// Type:Http
// Type:Inner
type ItemAttr_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GUID uint64 `protobuf:"varint,1,opt,name=GUID,proto3" json:"GUID,omitempty"`
}

func (x *ItemAttr_PISct) Reset() {
	*x = ItemAttr_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ItemAttr_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ItemAttr_PISct) ProtoMessage() {}

func (x *ItemAttr_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ItemAttr_PISct.ProtoReflect.Descriptor instead.
func (*ItemAttr_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{1}
}

func (x *ItemAttr_PISct) GetGUID() uint64 {
	if x != nil {
		return x.GUID
	}
	return 0
}

// Type:Http
// Type:Inner
type BaseAttr_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharModelID int32  `protobuf:"varint,1,opt,name=CharModelID,proto3" json:"CharModelID,omitempty"`
	Level       int32  `protobuf:"varint,2,opt,name=Level,proto3" json:"Level,omitempty"`
	Name        string `protobuf:"bytes,3,opt,name=Name,proto3" json:"Name,omitempty"`
	SceneId     int32  `protobuf:"varint,4,opt,name=SceneId,proto3" json:"SceneId,omitempty"`
	Sex         int32  `protobuf:"varint,5,opt,name=Sex,proto3" json:"Sex,omitempty"`
}

func (x *BaseAttr_PISct) Reset() {
	*x = BaseAttr_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaseAttr_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseAttr_PISct) ProtoMessage() {}

func (x *BaseAttr_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseAttr_PISct.ProtoReflect.Descriptor instead.
func (*BaseAttr_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{2}
}

func (x *BaseAttr_PISct) GetCharModelID() int32 {
	if x != nil {
		return x.CharModelID
	}
	return 0
}

func (x *BaseAttr_PISct) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *BaseAttr_PISct) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *BaseAttr_PISct) GetSceneId() int32 {
	if x != nil {
		return x.SceneId
	}
	return 0
}

func (x *BaseAttr_PISct) GetSex() int32 {
	if x != nil {
		return x.Sex
	}
	return 0
}

// Type:Http
// Type:Inner
type BagAttr_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BankLockState  []bool                    `protobuf:"varint,1,rep,packed,name=BankLockState,proto3" json:"BankLockState,omitempty"`
	EquipItemAttrs map[int64]*ItemAttr_PISct `protobuf:"bytes,2,rep,name=EquipItemAttrs,proto3" json:"EquipItemAttrs,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *BagAttr_PISct) Reset() {
	*x = BagAttr_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BagAttr_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BagAttr_PISct) ProtoMessage() {}

func (x *BagAttr_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BagAttr_PISct.ProtoReflect.Descriptor instead.
func (*BagAttr_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{3}
}

func (x *BagAttr_PISct) GetBankLockState() []bool {
	if x != nil {
		return x.BankLockState
	}
	return nil
}

func (x *BagAttr_PISct) GetEquipItemAttrs() map[int64]*ItemAttr_PISct {
	if x != nil {
		return x.EquipItemAttrs
	}
	return nil
}

// Type:Http
// Type:Inner
type AccountGuid_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Guid        uint64 `protobuf:"varint,1,opt,name=Guid,proto3" json:"Guid,omitempty"`
	ZoneWorldId int32  `protobuf:"varint,2,opt,name=ZoneWorldId,proto3" json:"ZoneWorldId,omitempty"`
}

func (x *AccountGuid_PISct) Reset() {
	*x = AccountGuid_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountGuid_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountGuid_PISct) ProtoMessage() {}

func (x *AccountGuid_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountGuid_PISct.ProtoReflect.Descriptor instead.
func (*AccountGuid_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{4}
}

func (x *AccountGuid_PISct) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *AccountGuid_PISct) GetZoneWorldId() int32 {
	if x != nil {
		return x.ZoneWorldId
	}
	return 0
}

// Type:Http
// Type:Inner
type CharBaseAttr_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharFaceMake          *FaceMakeInfo_PISct `protobuf:"bytes,1,opt,name=CharFaceMake,proto3" json:"CharFaceMake,omitempty"`
	CreateTime            uint64              `protobuf:"varint,2,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	ForbiddenLoginEndTime uint64              `protobuf:"varint,3,opt,name=ForbiddenLoginEndTime,proto3" json:"ForbiddenLoginEndTime,omitempty"`
	Guid                  uint64              `protobuf:"varint,4,opt,name=Guid,proto3" json:"Guid,omitempty"`
	ServerNum             uint32              `protobuf:"varint,5,opt,name=ServerNum,proto3" json:"ServerNum,omitempty"`
	ZoneWorldID           uint32              `protobuf:"varint,6,opt,name=ZoneWorldID,proto3" json:"ZoneWorldID,omitempty"`
}

func (x *CharBaseAttr_PISct) Reset() {
	*x = CharBaseAttr_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CharBaseAttr_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CharBaseAttr_PISct) ProtoMessage() {}

func (x *CharBaseAttr_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CharBaseAttr_PISct.ProtoReflect.Descriptor instead.
func (*CharBaseAttr_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{5}
}

func (x *CharBaseAttr_PISct) GetCharFaceMake() *FaceMakeInfo_PISct {
	if x != nil {
		return x.CharFaceMake
	}
	return nil
}

func (x *CharBaseAttr_PISct) GetCreateTime() uint64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *CharBaseAttr_PISct) GetForbiddenLoginEndTime() uint64 {
	if x != nil {
		return x.ForbiddenLoginEndTime
	}
	return 0
}

func (x *CharBaseAttr_PISct) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *CharBaseAttr_PISct) GetServerNum() uint32 {
	if x != nil {
		return x.ServerNum
	}
	return 0
}

func (x *CharBaseAttr_PISct) GetZoneWorldID() uint32 {
	if x != nil {
		return x.ZoneWorldID
	}
	return 0
}

// Type:Http
// Type:Inner
type Pet_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CombatPet  *PetFightInfo_PISct `protobuf:"bytes,1,opt,name=CombatPet,proto3" json:"CombatPet,omitempty"`
	HenshinPet *PetInfoAttr_PISct  `protobuf:"bytes,2,opt,name=HenshinPet,proto3" json:"HenshinPet,omitempty"`
}

func (x *Pet_PISct) Reset() {
	*x = Pet_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pet_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pet_PISct) ProtoMessage() {}

func (x *Pet_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pet_PISct.ProtoReflect.Descriptor instead.
func (*Pet_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{6}
}

func (x *Pet_PISct) GetCombatPet() *PetFightInfo_PISct {
	if x != nil {
		return x.CombatPet
	}
	return nil
}

func (x *Pet_PISct) GetHenshinPet() *PetInfoAttr_PISct {
	if x != nil {
		return x.HenshinPet
	}
	return nil
}

// Type:Http
// Type:Inner
type PetInfoAttr_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BasicAttrList map[int64]int32               `protobuf:"bytes,1,rep,name=BasicAttrList,proto3" json:"BasicAttrList,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	CombatModelID int32                         `protobuf:"varint,2,opt,name=CombatModelID,proto3" json:"CombatModelID,omitempty"`
	EvolveLevel   int32                         `protobuf:"varint,3,opt,name=EvolveLevel,proto3" json:"EvolveLevel,omitempty"`
	FightPoint    int32                         `protobuf:"varint,4,opt,name=FightPoint,proto3" json:"FightPoint,omitempty"`
	Flair         map[int64]*PetFlairInfo_PISct `protobuf:"bytes,5,rep,name=Flair,proto3" json:"Flair,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	GeniusCount   int32                         `protobuf:"varint,6,opt,name=GeniusCount,proto3" json:"GeniusCount,omitempty"`
	GeniusList    []*PetGeniusInfo_PISct        `protobuf:"bytes,7,rep,name=GeniusList,proto3" json:"GeniusList,omitempty"`
	GUID          uint64                        `protobuf:"varint,8,opt,name=GUID,proto3" json:"GUID,omitempty"`
	GWTHRate      float32                       `protobuf:"fixed32,9,opt,name=GWTHRate,proto3" json:"GWTHRate,omitempty"`
	IsFightPet    int32                         `protobuf:"varint,10,opt,name=IsFightPet,proto3" json:"IsFightPet,omitempty"`
	IsLocked      bool                          `protobuf:"varint,11,opt,name=IsLocked,proto3" json:"IsLocked,omitempty"`
	Level         int32                         `protobuf:"varint,12,opt,name=Level,proto3" json:"Level,omitempty"`
	LevelEXP      int64                         `protobuf:"varint,13,opt,name=LevelEXP,proto3" json:"LevelEXP,omitempty"`
	Name          string                        `protobuf:"bytes,14,opt,name=Name,proto3" json:"Name,omitempty"`
	PetID         int32                         `protobuf:"varint,15,opt,name=PetID,proto3" json:"PetID,omitempty"`
	Quality       int32                         `protobuf:"varint,16,opt,name=Quality,proto3" json:"Quality,omitempty"`
	SkillBookList []*PetSkillBookInfo_PISct     `protobuf:"bytes,17,rep,name=SkillBookList,proto3" json:"SkillBookList,omitempty"`
	StarLevel     int32                         `protobuf:"varint,18,opt,name=StarLevel,proto3" json:"StarLevel,omitempty"`
	XDValueAGI    int32                         `protobuf:"varint,19,opt,name=XDValueAGI,proto3" json:"XDValueAGI,omitempty"`
	XDValueCON    int32                         `protobuf:"varint,20,opt,name=XDValueCON,proto3" json:"XDValueCON,omitempty"`
	XDValueINT    int32                         `protobuf:"varint,21,opt,name=XDValueINT,proto3" json:"XDValueINT,omitempty"`
	XDValueSTR    int32                         `protobuf:"varint,22,opt,name=XDValueSTR,proto3" json:"XDValueSTR,omitempty"`
	XianDan       int32                         `protobuf:"varint,23,opt,name=XianDan,proto3" json:"XianDan,omitempty"`
}

func (x *PetInfoAttr_PISct) Reset() {
	*x = PetInfoAttr_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetInfoAttr_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetInfoAttr_PISct) ProtoMessage() {}

func (x *PetInfoAttr_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetInfoAttr_PISct.ProtoReflect.Descriptor instead.
func (*PetInfoAttr_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{7}
}

func (x *PetInfoAttr_PISct) GetBasicAttrList() map[int64]int32 {
	if x != nil {
		return x.BasicAttrList
	}
	return nil
}

func (x *PetInfoAttr_PISct) GetCombatModelID() int32 {
	if x != nil {
		return x.CombatModelID
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetEvolveLevel() int32 {
	if x != nil {
		return x.EvolveLevel
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetFightPoint() int32 {
	if x != nil {
		return x.FightPoint
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetFlair() map[int64]*PetFlairInfo_PISct {
	if x != nil {
		return x.Flair
	}
	return nil
}

func (x *PetInfoAttr_PISct) GetGeniusCount() int32 {
	if x != nil {
		return x.GeniusCount
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetGeniusList() []*PetGeniusInfo_PISct {
	if x != nil {
		return x.GeniusList
	}
	return nil
}

func (x *PetInfoAttr_PISct) GetGUID() uint64 {
	if x != nil {
		return x.GUID
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetGWTHRate() float32 {
	if x != nil {
		return x.GWTHRate
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetIsFightPet() int32 {
	if x != nil {
		return x.IsFightPet
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetIsLocked() bool {
	if x != nil {
		return x.IsLocked
	}
	return false
}

func (x *PetInfoAttr_PISct) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetLevelEXP() int64 {
	if x != nil {
		return x.LevelEXP
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PetInfoAttr_PISct) GetPetID() int32 {
	if x != nil {
		return x.PetID
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetQuality() int32 {
	if x != nil {
		return x.Quality
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetSkillBookList() []*PetSkillBookInfo_PISct {
	if x != nil {
		return x.SkillBookList
	}
	return nil
}

func (x *PetInfoAttr_PISct) GetStarLevel() int32 {
	if x != nil {
		return x.StarLevel
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetXDValueAGI() int32 {
	if x != nil {
		return x.XDValueAGI
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetXDValueCON() int32 {
	if x != nil {
		return x.XDValueCON
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetXDValueINT() int32 {
	if x != nil {
		return x.XDValueINT
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetXDValueSTR() int32 {
	if x != nil {
		return x.XDValueSTR
	}
	return 0
}

func (x *PetInfoAttr_PISct) GetXianDan() int32 {
	if x != nil {
		return x.XianDan
	}
	return 0
}

// Type:Http
// Type:Inner
type PetSkillBookInfo_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level       int32 `protobuf:"varint,1,opt,name=Level,proto3" json:"Level,omitempty"`
	SkillBookID int32 `protobuf:"varint,2,opt,name=SkillBookID,proto3" json:"SkillBookID,omitempty"`
	Slot        int32 `protobuf:"varint,3,opt,name=Slot,proto3" json:"Slot,omitempty"`
}

func (x *PetSkillBookInfo_PISct) Reset() {
	*x = PetSkillBookInfo_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetSkillBookInfo_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetSkillBookInfo_PISct) ProtoMessage() {}

func (x *PetSkillBookInfo_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetSkillBookInfo_PISct.ProtoReflect.Descriptor instead.
func (*PetSkillBookInfo_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{8}
}

func (x *PetSkillBookInfo_PISct) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *PetSkillBookInfo_PISct) GetSkillBookID() int32 {
	if x != nil {
		return x.SkillBookID
	}
	return 0
}

func (x *PetSkillBookInfo_PISct) GetSlot() int32 {
	if x != nil {
		return x.Slot
	}
	return 0
}

// Type:Http
// Type:Inner
type PetFightInfo_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PetInfo *PetInfoAttr_PISct `protobuf:"bytes,1,opt,name=PetInfo,proto3" json:"PetInfo,omitempty"`
}

func (x *PetFightInfo_PISct) Reset() {
	*x = PetFightInfo_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetFightInfo_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetFightInfo_PISct) ProtoMessage() {}

func (x *PetFightInfo_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetFightInfo_PISct.ProtoReflect.Descriptor instead.
func (*PetFightInfo_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{9}
}

func (x *PetFightInfo_PISct) GetPetInfo() *PetInfoAttr_PISct {
	if x != nil {
		return x.PetInfo
	}
	return nil
}

// Type:Http
// Type:Inner
type PetFlairInfo_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AllAddValue int32 `protobuf:"varint,1,opt,name=AllAddValue,proto3" json:"AllAddValue,omitempty"`
	BaseValue   int32 `protobuf:"varint,2,opt,name=BaseValue,proto3" json:"BaseValue,omitempty"`
}

func (x *PetFlairInfo_PISct) Reset() {
	*x = PetFlairInfo_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetFlairInfo_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetFlairInfo_PISct) ProtoMessage() {}

func (x *PetFlairInfo_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetFlairInfo_PISct.ProtoReflect.Descriptor instead.
func (*PetFlairInfo_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{10}
}

func (x *PetFlairInfo_PISct) GetAllAddValue() int32 {
	if x != nil {
		return x.AllAddValue
	}
	return 0
}

func (x *PetFlairInfo_PISct) GetBaseValue() int32 {
	if x != nil {
		return x.BaseValue
	}
	return 0
}

// Type:Http
// Type:Inner
type PetGeniusInfo_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amend float32 `protobuf:"fixed32,1,opt,name=Amend,proto3" json:"Amend,omitempty"`
	ID    int32   `protobuf:"varint,2,opt,name=ID,proto3" json:"ID,omitempty"`
}

func (x *PetGeniusInfo_PISct) Reset() {
	*x = PetGeniusInfo_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PetGeniusInfo_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PetGeniusInfo_PISct) ProtoMessage() {}

func (x *PetGeniusInfo_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PetGeniusInfo_PISct.ProtoReflect.Descriptor instead.
func (*PetGeniusInfo_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{11}
}

func (x *PetGeniusInfo_PISct) GetAmend() float32 {
	if x != nil {
		return x.Amend
	}
	return 0
}

func (x *PetGeniusInfo_PISct) GetID() int32 {
	if x != nil {
		return x.ID
	}
	return 0
}

// Type:Http
// Type:Inner
type FaceMakeInfo_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FmDic map[int64]*DyeData_PISct `protobuf:"bytes,1,rep,name=FmDic,proto3" json:"FmDic,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *FaceMakeInfo_PISct) Reset() {
	*x = FaceMakeInfo_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaceMakeInfo_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaceMakeInfo_PISct) ProtoMessage() {}

func (x *FaceMakeInfo_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaceMakeInfo_PISct.ProtoReflect.Descriptor instead.
func (*FaceMakeInfo_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{12}
}

func (x *FaceMakeInfo_PISct) GetFmDic() map[int64]*DyeData_PISct {
	if x != nil {
		return x.FmDic
	}
	return nil
}

// Type:Http
// Type:Inner
type Team_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SelfTeamIndexId int32  `protobuf:"varint,1,opt,name=SelfTeamIndexId,proto3" json:"SelfTeamIndexId,omitempty"`
	TeamFollowState int32  `protobuf:"varint,2,opt,name=TeamFollowState,proto3" json:"TeamFollowState,omitempty"`
	TeamId          uint64 `protobuf:"varint,3,opt,name=TeamId,proto3" json:"TeamId,omitempty"`
	TeamJobTitle    int32  `protobuf:"varint,4,opt,name=TeamJobTitle,proto3" json:"TeamJobTitle,omitempty"`
	TeamMemCount    int32  `protobuf:"varint,5,opt,name=TeamMemCount,proto3" json:"TeamMemCount,omitempty"`
	TeamPlayerCount int32  `protobuf:"varint,6,opt,name=TeamPlayerCount,proto3" json:"TeamPlayerCount,omitempty"`
	TeamTarget      int32  `protobuf:"varint,7,opt,name=TeamTarget,proto3" json:"TeamTarget,omitempty"`
	TeamType        int32  `protobuf:"varint,8,opt,name=TeamType,proto3" json:"TeamType,omitempty"`
}

func (x *Team_PISct) Reset() {
	*x = Team_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Team_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Team_PISct) ProtoMessage() {}

func (x *Team_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Team_PISct.ProtoReflect.Descriptor instead.
func (*Team_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{13}
}

func (x *Team_PISct) GetSelfTeamIndexId() int32 {
	if x != nil {
		return x.SelfTeamIndexId
	}
	return 0
}

func (x *Team_PISct) GetTeamFollowState() int32 {
	if x != nil {
		return x.TeamFollowState
	}
	return 0
}

func (x *Team_PISct) GetTeamId() uint64 {
	if x != nil {
		return x.TeamId
	}
	return 0
}

func (x *Team_PISct) GetTeamJobTitle() int32 {
	if x != nil {
		return x.TeamJobTitle
	}
	return 0
}

func (x *Team_PISct) GetTeamMemCount() int32 {
	if x != nil {
		return x.TeamMemCount
	}
	return 0
}

func (x *Team_PISct) GetTeamPlayerCount() int32 {
	if x != nil {
		return x.TeamPlayerCount
	}
	return 0
}

func (x *Team_PISct) GetTeamTarget() int32 {
	if x != nil {
		return x.TeamTarget
	}
	return 0
}

func (x *Team_PISct) GetTeamType() int32 {
	if x != nil {
		return x.TeamType
	}
	return 0
}

// Type:Http
// Type:Inner
type CharGuildAttr_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GuildGuid   uint64 `protobuf:"varint,1,opt,name=GuildGuid,proto3" json:"GuildGuid,omitempty"`
	GuildLevel  uint32 `protobuf:"varint,2,opt,name=GuildLevel,proto3" json:"GuildLevel,omitempty"`
	GuildName   string `protobuf:"bytes,3,opt,name=GuildName,proto3" json:"GuildName,omitempty"`
	GuildPostId int32  `protobuf:"varint,4,opt,name=GuildPostId,proto3" json:"GuildPostId,omitempty"`
}

func (x *CharGuildAttr_PISct) Reset() {
	*x = CharGuildAttr_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CharGuildAttr_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CharGuildAttr_PISct) ProtoMessage() {}

func (x *CharGuildAttr_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CharGuildAttr_PISct.ProtoReflect.Descriptor instead.
func (*CharGuildAttr_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{14}
}

func (x *CharGuildAttr_PISct) GetGuildGuid() uint64 {
	if x != nil {
		return x.GuildGuid
	}
	return 0
}

func (x *CharGuildAttr_PISct) GetGuildLevel() uint32 {
	if x != nil {
		return x.GuildLevel
	}
	return 0
}

func (x *CharGuildAttr_PISct) GetGuildName() string {
	if x != nil {
		return x.GuildName
	}
	return ""
}

func (x *CharGuildAttr_PISct) GetGuildPostId() int32 {
	if x != nil {
		return x.GuildPostId
	}
	return 0
}

// Type:Http
// Type:Inner
type BloodVessel_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BigNode    int32     `protobuf:"varint,1,opt,name=BigNode,proto3" json:"BigNode,omitempty"`
	FightPoint []float64 `protobuf:"fixed64,2,rep,packed,name=FightPoint,proto3" json:"FightPoint,omitempty"`
}

func (x *BloodVessel_PISct) Reset() {
	*x = BloodVessel_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BloodVessel_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BloodVessel_PISct) ProtoMessage() {}

func (x *BloodVessel_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BloodVessel_PISct.ProtoReflect.Descriptor instead.
func (*BloodVessel_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{15}
}

func (x *BloodVessel_PISct) GetBigNode() int32 {
	if x != nil {
		return x.BigNode
	}
	return 0
}

func (x *BloodVessel_PISct) GetFightPoint() []float64 {
	if x != nil {
		return x.FightPoint
	}
	return nil
}

// Type:Http
// Type:Inner
type DyeData_PISct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FaceData map[int64]int32 `protobuf:"bytes,1,rep,name=FaceData,proto3" json:"FaceData,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *DyeData_PISct) Reset() {
	*x = DyeData_PISct{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DyeData_PISct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DyeData_PISct) ProtoMessage() {}

func (x *DyeData_PISct) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DyeData_PISct.ProtoReflect.Descriptor instead.
func (*DyeData_PISct) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{16}
}

func (x *DyeData_PISct) GetFaceData() map[int64]int32 {
	if x != nil {
		return x.FaceData
	}
	return nil
}

// Type:Http
// Type:Inner
type PlayerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerBaseInfo_PI *PlayerBaseInfo      `protobuf:"bytes,1,opt,name=PlayerBaseInfo_PI,json=PlayerBaseInfoPI,proto3" json:"PlayerBaseInfo_PI,omitempty"`
	BaseAttr_PI       *BaseAttr_PISct      `protobuf:"bytes,2,opt,name=BaseAttr_PI,json=BaseAttrPI,proto3" json:"BaseAttr_PI,omitempty"`
	BagAttr_PI        *BagAttr_PISct       `protobuf:"bytes,3,opt,name=BagAttr_PI,json=BagAttrPI,proto3" json:"BagAttr_PI,omitempty"`
	CharBaseAttr_PI   *CharBaseAttr_PISct  `protobuf:"bytes,4,opt,name=CharBaseAttr_PI,json=CharBaseAttrPI,proto3" json:"CharBaseAttr_PI,omitempty"`
	Pet_PI            *Pet_PISct           `protobuf:"bytes,5,opt,name=Pet_PI,json=PetPI,proto3" json:"Pet_PI,omitempty"`
	Team_PI           *Team_PISct          `protobuf:"bytes,6,opt,name=Team_PI,json=TeamPI,proto3" json:"Team_PI,omitempty"`
	CharGuildAttr_PI  *CharGuildAttr_PISct `protobuf:"bytes,7,opt,name=CharGuildAttr_PI,json=CharGuildAttrPI,proto3" json:"CharGuildAttr_PI,omitempty"`
	BloodVessel_PI    *BloodVessel_PISct   `protobuf:"bytes,8,opt,name=BloodVessel_PI,json=BloodVesselPI,proto3" json:"BloodVessel_PI,omitempty"`
}

func (x *PlayerInfo) Reset() {
	*x = PlayerInfo{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerInfo) ProtoMessage() {}

func (x *PlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerInfo.ProtoReflect.Descriptor instead.
func (*PlayerInfo) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{17}
}

func (x *PlayerInfo) GetPlayerBaseInfo_PI() *PlayerBaseInfo {
	if x != nil {
		return x.PlayerBaseInfo_PI
	}
	return nil
}

func (x *PlayerInfo) GetBaseAttr_PI() *BaseAttr_PISct {
	if x != nil {
		return x.BaseAttr_PI
	}
	return nil
}

func (x *PlayerInfo) GetBagAttr_PI() *BagAttr_PISct {
	if x != nil {
		return x.BagAttr_PI
	}
	return nil
}

func (x *PlayerInfo) GetCharBaseAttr_PI() *CharBaseAttr_PISct {
	if x != nil {
		return x.CharBaseAttr_PI
	}
	return nil
}

func (x *PlayerInfo) GetPet_PI() *Pet_PISct {
	if x != nil {
		return x.Pet_PI
	}
	return nil
}

func (x *PlayerInfo) GetTeam_PI() *Team_PISct {
	if x != nil {
		return x.Team_PI
	}
	return nil
}

func (x *PlayerInfo) GetCharGuildAttr_PI() *CharGuildAttr_PISct {
	if x != nil {
		return x.CharGuildAttr_PI
	}
	return nil
}

func (x *PlayerInfo) GetBloodVessel_PI() *BloodVessel_PISct {
	if x != nil {
		return x.BloodVessel_PI
	}
	return nil
}

// 请求消息定义
// Type:Inner
// Target:S2W
type SyncPlayerInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountGuid *AccountGuid_PISct `protobuf:"bytes,1,opt,name=AccountGuid,proto3" json:"AccountGuid,omitempty"`
	EPlayerInfo uint64             `protobuf:"varint,2,opt,name=ePlayerInfo,proto3" json:"ePlayerInfo,omitempty"` // 类型实际是 ENUM_PlayerInfo
	PlayerInfo  *PlayerInfo        `protobuf:"bytes,3,opt,name=PlayerInfo,proto3" json:"PlayerInfo,omitempty"`
}

func (x *SyncPlayerInfoRequest) Reset() {
	*x = SyncPlayerInfoRequest{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncPlayerInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPlayerInfoRequest) ProtoMessage() {}

func (x *SyncPlayerInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPlayerInfoRequest.ProtoReflect.Descriptor instead.
func (*SyncPlayerInfoRequest) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{18}
}

func (x *SyncPlayerInfoRequest) GetAccountGuid() *AccountGuid_PISct {
	if x != nil {
		return x.AccountGuid
	}
	return nil
}

func (x *SyncPlayerInfoRequest) GetEPlayerInfo() uint64 {
	if x != nil {
		return x.EPlayerInfo
	}
	return 0
}

func (x *SyncPlayerInfoRequest) GetPlayerInfo() *PlayerInfo {
	if x != nil {
		return x.PlayerInfo
	}
	return nil
}

// Type:Inner
// Target:W2S
type SyncPlayerInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *SyncPlayerInfoReply) Reset() {
	*x = SyncPlayerInfoReply{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncPlayerInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncPlayerInfoReply) ProtoMessage() {}

func (x *SyncPlayerInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncPlayerInfoReply.ProtoReflect.Descriptor instead.
func (*SyncPlayerInfoReply) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{19}
}

func (x *SyncPlayerInfoReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

// Type:Http
// Type:Inner
// Target:S2W
// Response W2S_GetPlayerInfoReply
type GetPlayerInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountGuid *AccountGuid_PISct `protobuf:"bytes,1,opt,name=AccountGuid,proto3" json:"AccountGuid,omitempty"`
	EPlayerInfo uint64             `protobuf:"varint,2,opt,name=ePlayerInfo,proto3" json:"ePlayerInfo,omitempty"` // 类型实际是 ENUM_PlayerInfo
}

func (x *GetPlayerInfoRequest) Reset() {
	*x = GetPlayerInfoRequest{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlayerInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerInfoRequest) ProtoMessage() {}

func (x *GetPlayerInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerInfoRequest.ProtoReflect.Descriptor instead.
func (*GetPlayerInfoRequest) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{20}
}

func (x *GetPlayerInfoRequest) GetAccountGuid() *AccountGuid_PISct {
	if x != nil {
		return x.AccountGuid
	}
	return nil
}

func (x *GetPlayerInfoRequest) GetEPlayerInfo() uint64 {
	if x != nil {
		return x.EPlayerInfo
	}
	return 0
}

// Type:Http
// Type:Inner
// Target:W2S
type GetPlayerInfoReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerInfo  *PlayerInfo `protobuf:"bytes,1,opt,name=PlayerInfo,proto3" json:"PlayerInfo,omitempty"`
	EPlayerInfo uint64      `protobuf:"varint,2,opt,name=ePlayerInfo,proto3" json:"ePlayerInfo,omitempty"`
}

func (x *GetPlayerInfoReply) Reset() {
	*x = GetPlayerInfoReply{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlayerInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerInfoReply) ProtoMessage() {}

func (x *GetPlayerInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerInfoReply.ProtoReflect.Descriptor instead.
func (*GetPlayerInfoReply) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{21}
}

func (x *GetPlayerInfoReply) GetPlayerInfo() *PlayerInfo {
	if x != nil {
		return x.PlayerInfo
	}
	return nil
}

func (x *GetPlayerInfoReply) GetEPlayerInfo() uint64 {
	if x != nil {
		return x.EPlayerInfo
	}
	return 0
}

// Type:Http
// Type:Inner
type PlayerInfoAskUnit struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountGuid *AccountGuid_PISct `protobuf:"bytes,1,opt,name=AccountGuid,proto3" json:"AccountGuid,omitempty"`
	EPlayerInfo uint64             `protobuf:"varint,2,opt,name=ePlayerInfo,proto3" json:"ePlayerInfo,omitempty"` // 类型实际是 ENUM_PlayerInfo
}

func (x *PlayerInfoAskUnit) Reset() {
	*x = PlayerInfoAskUnit{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerInfoAskUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerInfoAskUnit) ProtoMessage() {}

func (x *PlayerInfoAskUnit) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerInfoAskUnit.ProtoReflect.Descriptor instead.
func (*PlayerInfoAskUnit) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{22}
}

func (x *PlayerInfoAskUnit) GetAccountGuid() *AccountGuid_PISct {
	if x != nil {
		return x.AccountGuid
	}
	return nil
}

func (x *PlayerInfoAskUnit) GetEPlayerInfo() uint64 {
	if x != nil {
		return x.EPlayerInfo
	}
	return 0
}

// Type:Http
// Type:Inner
// Target:S2W
// Response W2S_GetPlayerInfoListReply
type GetPlayerInfoListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerInfoAskUnitList []*PlayerInfoAskUnit `protobuf:"bytes,1,rep,name=playerInfoAskUnitList,proto3" json:"playerInfoAskUnitList,omitempty"`
}

func (x *GetPlayerInfoListRequest) Reset() {
	*x = GetPlayerInfoListRequest{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlayerInfoListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerInfoListRequest) ProtoMessage() {}

func (x *GetPlayerInfoListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerInfoListRequest.ProtoReflect.Descriptor instead.
func (*GetPlayerInfoListRequest) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{23}
}

func (x *GetPlayerInfoListRequest) GetPlayerInfoAskUnitList() []*PlayerInfoAskUnit {
	if x != nil {
		return x.PlayerInfoAskUnitList
	}
	return nil
}

// Type:Http
// Type:Inner
// Target:W2S
type GetPlayerInfoListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PlayerInfoList  []*PlayerInfo `protobuf:"bytes,1,rep,name=PlayerInfoList,proto3" json:"PlayerInfoList,omitempty"`
	EPlayerInfoList []uint64      `protobuf:"varint,2,rep,packed,name=ePlayerInfoList,proto3" json:"ePlayerInfoList,omitempty"`
}

func (x *GetPlayerInfoListReply) Reset() {
	*x = GetPlayerInfoListReply{}
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlayerInfoListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlayerInfoListReply) ProtoMessage() {}

func (x *GetPlayerInfoListReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlayerInfoListReply.ProtoReflect.Descriptor instead.
func (*GetPlayerInfoListReply) Descriptor() ([]byte, []int) {
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP(), []int{24}
}

func (x *GetPlayerInfoListReply) GetPlayerInfoList() []*PlayerInfo {
	if x != nil {
		return x.PlayerInfoList
	}
	return nil
}

func (x *GetPlayerInfoListReply) GetEPlayerInfoList() []uint64 {
	if x != nil {
		return x.EPlayerInfoList
	}
	return nil
}

var File_microservices_playerinfo_v1_playerinfostruct_proto protoreflect.FileDescriptor

var file_microservices_playerinfo_v1_playerinfostruct_proto_rawDesc = []byte{
	0x0a, 0x32, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xba, 0x06, 0x0a, 0x0e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1e,
	0x0a, 0x0a, 0x46, 0x69, 0x67, 0x68, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0a, 0x46, 0x69, 0x67, 0x68, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x22,
	0x0a, 0x0c, 0x48, 0x65, 0x6e, 0x73, 0x68, 0x69, 0x6e, 0x50, 0x65, 0x74, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x48, 0x65, 0x6e, 0x73, 0x68, 0x69, 0x6e, 0x50, 0x65, 0x74,
	0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x53,
	0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x53, 0x65, 0x78, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x03, 0x53, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x55, 0x6e, 0x69, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x55, 0x6e, 0x69, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65,
	0x6e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x15, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x47, 0x75,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x47, 0x75, 0x69, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b,
	0x5a, 0x6f, 0x6e, 0x65, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x49, 0x44, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0b, 0x5a, 0x6f, 0x6e, 0x65, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x49, 0x44, 0x12, 0x4a,
	0x0a, 0x0a, 0x48, 0x65, 0x6e, 0x73, 0x68, 0x69, 0x6e, 0x50, 0x65, 0x74, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x0a,
	0x48, 0x65, 0x6e, 0x73, 0x68, 0x69, 0x6e, 0x50, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x50, 0x65,
	0x74, 0x49, 0x44, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x50, 0x65, 0x74, 0x49, 0x44,
	0x12, 0x28, 0x0a, 0x0f, 0x53, 0x65, 0x6c, 0x66, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x65,
	0x78, 0x49, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x53, 0x65, 0x6c, 0x66, 0x54,
	0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x54, 0x65, 0x61, 0x6d,
	0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x4a, 0x6f,
	0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65,
	0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x54, 0x65,
	0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x54, 0x65,
	0x61, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0f, 0x54, 0x65, 0x61, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1c, 0x0a, 0x09, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x47, 0x75, 0x69, 0x64, 0x18, 0x14, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x09, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x47, 0x75, 0x69, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08,
	0x48, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x48, 0x65, 0x61, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x48, 0x65, 0x61, 0x64,
	0x46, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x48, 0x65, 0x61,
	0x64, 0x46, 0x72, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x73, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x18, 0x18, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x49, 0x73, 0x4f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x4c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54,
	0x69, 0x6d, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x4c, 0x61, 0x73, 0x74, 0x4c,
	0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x24, 0x0a, 0x0e, 0x49, 0x74, 0x65, 0x6d,
	0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x47, 0x55,
	0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x47, 0x55, 0x49, 0x44, 0x22, 0x88,
	0x01, 0x0a, 0x0e, 0x42, 0x61, 0x73, 0x65, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x53, 0x63,
	0x74, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x43, 0x68, 0x61, 0x72, 0x4d, 0x6f, 0x64, 0x65,
	0x6c, 0x49, 0x44, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x53, 0x65, 0x78, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x53, 0x65, 0x78, 0x22, 0x85, 0x02, 0x0a, 0x0d, 0x42, 0x61,
	0x67, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x24, 0x0a, 0x0d, 0x42,
	0x61, 0x6e, 0x6b, 0x4c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x08, 0x52, 0x0d, 0x42, 0x61, 0x6e, 0x6b, 0x4c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x62, 0x0a, 0x0e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x74,
	0x74, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x41, 0x75, 0x72, 0x6f,
	0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x67, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x53, 0x63,
	0x74, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x45, 0x71, 0x75, 0x69, 0x70, 0x49, 0x74, 0x65, 0x6d,
	0x41, 0x74, 0x74, 0x72, 0x73, 0x1a, 0x6a, 0x0a, 0x13, 0x45, 0x71, 0x75, 0x69, 0x70, 0x49, 0x74,
	0x65, 0x6d, 0x41, 0x74, 0x74, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3d,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x41, 0x74, 0x74, 0x72,
	0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x49, 0x0a, 0x11, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x75, 0x69, 0x64,
	0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x47, 0x75, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x47, 0x75, 0x69, 0x64, 0x12, 0x20, 0x0a, 0x0b, 0x5a, 0x6f,
	0x6e, 0x65, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x5a, 0x6f, 0x6e, 0x65, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x49, 0x64, 0x22, 0x8f, 0x02, 0x0a,
	0x12, 0x43, 0x68, 0x61, 0x72, 0x42, 0x61, 0x73, 0x65, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49,
	0x53, 0x63, 0x74, 0x12, 0x4f, 0x0a, 0x0c, 0x43, 0x68, 0x61, 0x72, 0x46, 0x61, 0x63, 0x65, 0x4d,
	0x61, 0x6b, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x41, 0x75, 0x72, 0x6f,
	0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x0c, 0x43, 0x68, 0x61, 0x72, 0x46, 0x61, 0x63, 0x65,
	0x4d, 0x61, 0x6b, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65,
	0x6e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x15, 0x46, 0x6f, 0x72, 0x62, 0x69, 0x64, 0x64, 0x65, 0x6e, 0x4c, 0x6f,
	0x67, 0x69, 0x6e, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x47, 0x75,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x04, 0x47, 0x75, 0x69, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x09, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0b,
	0x5a, 0x6f, 0x6e, 0x65, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0b, 0x5a, 0x6f, 0x6e, 0x65, 0x57, 0x6f, 0x72, 0x6c, 0x64, 0x49, 0x44, 0x22, 0xa2,
	0x01, 0x0a, 0x09, 0x50, 0x65, 0x74, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x49, 0x0a, 0x09,
	0x43, 0x6f, 0x6d, 0x62, 0x61, 0x74, 0x50, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2b, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x74, 0x46, 0x69, 0x67,
	0x68, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x09, 0x43, 0x6f,
	0x6d, 0x62, 0x61, 0x74, 0x50, 0x65, 0x74, 0x12, 0x4a, 0x0a, 0x0a, 0x48, 0x65, 0x6e, 0x73, 0x68,
	0x69, 0x6e, 0x50, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x41, 0x75,
	0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x74, 0x74,
	0x72, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x0a, 0x48, 0x65, 0x6e, 0x73, 0x68, 0x69, 0x6e,
	0x50, 0x65, 0x74, 0x22, 0xb7, 0x08, 0x0a, 0x11, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x41,
	0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x63, 0x0a, 0x0d, 0x42, 0x61, 0x73,
	0x69, 0x63, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3d, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x2e, 0x42, 0x61, 0x73,
	0x69, 0x63, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0d, 0x42, 0x61, 0x73, 0x69, 0x63, 0x41, 0x74, 0x74, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x24,
	0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x62, 0x61, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x49, 0x44, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x43, 0x6f, 0x6d, 0x62, 0x61, 0x74, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x45, 0x76, 0x6f, 0x6c, 0x76, 0x65, 0x4c, 0x65,
	0x76, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x45, 0x76, 0x6f, 0x6c, 0x76,
	0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x46, 0x69, 0x67, 0x68, 0x74, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x46, 0x69, 0x67, 0x68,
	0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x05, 0x46, 0x6c, 0x61, 0x69, 0x72, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x53, 0x63,
	0x74, 0x2e, 0x46, 0x6c, 0x61, 0x69, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x46, 0x6c,
	0x61, 0x69, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x47, 0x65, 0x6e, 0x69, 0x75, 0x73, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x47, 0x65, 0x6e, 0x69, 0x75, 0x73,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4c, 0x0a, 0x0a, 0x47, 0x65, 0x6e, 0x69, 0x75, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x41, 0x75, 0x72, 0x6f,
	0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x74, 0x47, 0x65, 0x6e, 0x69, 0x75, 0x73, 0x49, 0x6e, 0x66,
	0x6f, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x0a, 0x47, 0x65, 0x6e, 0x69, 0x75, 0x73, 0x4c,
	0x69, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x47, 0x55, 0x49, 0x44, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x04, 0x47, 0x55, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x47, 0x57, 0x54, 0x48, 0x52,
	0x61, 0x74, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x08, 0x47, 0x57, 0x54, 0x48, 0x52,
	0x61, 0x74, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x49, 0x73, 0x46, 0x69, 0x67, 0x68, 0x74, 0x50, 0x65,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x49, 0x73, 0x46, 0x69, 0x67, 0x68, 0x74,
	0x50, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x73, 0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x49, 0x73, 0x4c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x45, 0x58,
	0x50, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x45, 0x58,
	0x50, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x50, 0x65, 0x74, 0x49, 0x44, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x50, 0x65, 0x74, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x51,
	0x75, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x51, 0x75,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x55, 0x0a, 0x0d, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x42, 0x6f,
	0x6f, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x74, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x42,
	0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x0d, 0x53,
	0x6b, 0x69, 0x6c, 0x6c, 0x42, 0x6f, 0x6f, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09,
	0x53, 0x74, 0x61, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x53, 0x74, 0x61, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x58, 0x44,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x41, 0x47, 0x49, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x58, 0x44, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x41, 0x47, 0x49, 0x12, 0x1e, 0x0a, 0x0a, 0x58, 0x44,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x4f, 0x4e, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x58, 0x44, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x43, 0x4f, 0x4e, 0x12, 0x1e, 0x0a, 0x0a, 0x58, 0x44,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x4e, 0x54, 0x18, 0x15, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x58, 0x44, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x49, 0x4e, 0x54, 0x12, 0x1e, 0x0a, 0x0a, 0x58, 0x44,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x54, 0x52, 0x18, 0x16, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x58, 0x44, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x53, 0x54, 0x52, 0x12, 0x18, 0x0a, 0x07, 0x58, 0x69,
	0x61, 0x6e, 0x44, 0x61, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x58, 0x69, 0x61,
	0x6e, 0x44, 0x61, 0x6e, 0x1a, 0x40, 0x0a, 0x12, 0x42, 0x61, 0x73, 0x69, 0x63, 0x41, 0x74, 0x74,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x65, 0x0a, 0x0a, 0x46, 0x6c, 0x61, 0x69, 0x72, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x41, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x50, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x69, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x5f, 0x50, 0x49, 0x53,
	0x63, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x64, 0x0a,
	0x16, 0x50, 0x65, 0x74, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x20, 0x0a,
	0x0b, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0b, 0x53, 0x6b, 0x69, 0x6c, 0x6c, 0x42, 0x6f, 0x6f, 0x6b, 0x49, 0x44, 0x12,
	0x12, 0x0a, 0x04, 0x53, 0x6c, 0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x53,
	0x6c, 0x6f, 0x74, 0x22, 0x5a, 0x0a, 0x12, 0x50, 0x65, 0x74, 0x46, 0x69, 0x67, 0x68, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x44, 0x0a, 0x07, 0x50, 0x65, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x41, 0x75, 0x72,
	0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x74, 0x74, 0x72,
	0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x07, 0x50, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x54, 0x0a, 0x12, 0x50, 0x65, 0x74, 0x46, 0x6c, 0x61, 0x69, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x5f,
	0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x6c, 0x6c, 0x41, 0x64, 0x64, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x41, 0x6c, 0x6c, 0x41,
	0x64, 0x64, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x42, 0x61, 0x73, 0x65, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x42, 0x61, 0x73, 0x65,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x3b, 0x0a, 0x13, 0x50, 0x65, 0x74, 0x47, 0x65, 0x6e, 0x69,
	0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x41, 0x6d, 0x65, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x41, 0x6d, 0x65,
	0x6e, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02,
	0x49, 0x44, 0x22, 0xc4, 0x01, 0x0a, 0x12, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x6b, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x4c, 0x0a, 0x05, 0x46, 0x6d, 0x44,
	0x69, 0x63, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x4d, 0x61, 0x6b, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x5f,
	0x50, 0x49, 0x53, 0x63, 0x74, 0x2e, 0x46, 0x6d, 0x44, 0x69, 0x63, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x05, 0x46, 0x6d, 0x44, 0x69, 0x63, 0x1a, 0x60, 0x0a, 0x0a, 0x46, 0x6d, 0x44, 0x69, 0x63,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3c, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x44, 0x79, 0x65, 0x44, 0x61, 0x74, 0x61, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xa6, 0x02, 0x0a, 0x0a, 0x54, 0x65,
	0x61, 0x6d, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x53, 0x65, 0x6c, 0x66,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0f, 0x53, 0x65, 0x6c, 0x66, 0x54, 0x65, 0x61, 0x6d, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x49, 0x64, 0x12, 0x28, 0x0a, 0x0f, 0x54, 0x65, 0x61, 0x6d, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x54, 0x65, 0x61,
	0x6d, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x54, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x54, 0x65,
	0x61, 0x6d, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x54, 0x65, 0x61, 0x6d, 0x4a, 0x6f, 0x62, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x54, 0x65, 0x61, 0x6d,
	0x4a, 0x6f, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x54, 0x65, 0x61, 0x6d,
	0x4d, 0x65, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x54, 0x65, 0x61, 0x6d, 0x4d, 0x65, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x0f,
	0x54, 0x65, 0x61, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x54, 0x65, 0x61, 0x6d, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x54, 0x65, 0x61, 0x6d,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x54, 0x65, 0x61, 0x6d, 0x54, 0x79,
	0x70, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x13, 0x43, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x47, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x47,
	0x75, 0x69, 0x6c, 0x64, 0x47, 0x75, 0x69, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x47, 0x75,
	0x69, 0x6c, 0x64, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x47, 0x75, 0x69, 0x6c,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x50,
	0x6f, 0x73, 0x74, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x47, 0x75, 0x69,
	0x6c, 0x64, 0x50, 0x6f, 0x73, 0x74, 0x49, 0x64, 0x22, 0x4d, 0x0a, 0x11, 0x42, 0x6c, 0x6f, 0x6f,
	0x64, 0x56, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x42, 0x69, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x42, 0x69, 0x67, 0x4e, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x46, 0x69, 0x67, 0x68, 0x74,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0a, 0x46, 0x69, 0x67,
	0x68, 0x74, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0x9e, 0x01, 0x0a, 0x0d, 0x44, 0x79, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x12, 0x50, 0x0a, 0x08, 0x46, 0x61, 0x63,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x41, 0x75,
	0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x79, 0x65, 0x44, 0x61, 0x74, 0x61, 0x5f, 0x50, 0x49,
	0x53, 0x63, 0x74, 0x2e, 0x46, 0x61, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x08, 0x46, 0x61, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x3b, 0x0a, 0x0d, 0x46,
	0x61, 0x63, 0x65, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xee, 0x04, 0x0a, 0x0a, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x54, 0x0a, 0x11, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x5f, 0x50, 0x49, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x10, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x49, 0x12, 0x48, 0x0a,
	0x0b, 0x42, 0x61, 0x73, 0x65, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x73,
	0x65, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x0a, 0x42, 0x61, 0x73,
	0x65, 0x41, 0x74, 0x74, 0x72, 0x50, 0x49, 0x12, 0x45, 0x0a, 0x0a, 0x42, 0x61, 0x67, 0x41, 0x74,
	0x74, 0x72, 0x5f, 0x50, 0x49, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x41, 0x75,
	0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x61, 0x67, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50, 0x49,
	0x53, 0x63, 0x74, 0x52, 0x09, 0x42, 0x61, 0x67, 0x41, 0x74, 0x74, 0x72, 0x50, 0x49, 0x12, 0x54,
	0x0a, 0x0f, 0x43, 0x68, 0x61, 0x72, 0x42, 0x61, 0x73, 0x65, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50,
	0x49, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61,
	0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x42, 0x61, 0x73, 0x65, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50,
	0x49, 0x53, 0x63, 0x74, 0x52, 0x0e, 0x43, 0x68, 0x61, 0x72, 0x42, 0x61, 0x73, 0x65, 0x41, 0x74,
	0x74, 0x72, 0x50, 0x49, 0x12, 0x39, 0x0a, 0x06, 0x50, 0x65, 0x74, 0x5f, 0x50, 0x49, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50,
	0x65, 0x74, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x05, 0x50, 0x65, 0x74, 0x50, 0x49, 0x12,
	0x3c, 0x0a, 0x07, 0x54, 0x65, 0x61, 0x6d, 0x5f, 0x50, 0x49, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x54, 0x65, 0x61, 0x6d, 0x5f,
	0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x06, 0x54, 0x65, 0x61, 0x6d, 0x50, 0x49, 0x12, 0x57, 0x0a,
	0x10, 0x43, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x41, 0x74, 0x74, 0x72, 0x5f, 0x50,
	0x49, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61,
	0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x43, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x6c, 0x64, 0x41, 0x74, 0x74, 0x72, 0x5f,
	0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x0f, 0x43, 0x68, 0x61, 0x72, 0x47, 0x75, 0x69, 0x6c, 0x64,
	0x41, 0x74, 0x74, 0x72, 0x50, 0x49, 0x12, 0x51, 0x0a, 0x0e, 0x42, 0x6c, 0x6f, 0x6f, 0x64, 0x56,
	0x65, 0x73, 0x73, 0x65, 0x6c, 0x5f, 0x50, 0x49, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x42, 0x6c, 0x6f, 0x6f, 0x64, 0x56, 0x65,
	0x73, 0x73, 0x65, 0x6c, 0x5f, 0x50, 0x49, 0x53, 0x63, 0x74, 0x52, 0x0d, 0x42, 0x6c, 0x6f, 0x6f,
	0x64, 0x56, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x50, 0x49, 0x22, 0xcc, 0x01, 0x0a, 0x15, 0x53, 0x79,
	0x6e, 0x63, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x4c, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x75, 0x69, 0x64, 0x5f, 0x50,
	0x49, 0x53, 0x63, 0x74, 0x52, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x75, 0x69,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x43, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61,
	0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x2d, 0x0a, 0x13, 0x53, 0x79, 0x6e, 0x63,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x86, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x4c, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x75, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x75, 0x69, 0x64, 0x5f, 0x50, 0x49, 0x53, 0x63,
	0x74, 0x52, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x75, 0x69, 0x64, 0x12, 0x20,
	0x0a, 0x0b, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x0b, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x7b, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x43, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x41, 0x75, 0x72,
	0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x65,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x0b, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x83, 0x01,
	0x0a, 0x11, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x73, 0x6b, 0x55,
	0x6e, 0x69, 0x74, 0x12, 0x4c, 0x0a, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x75,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x75, 0x69, 0x64, 0x5f, 0x50,
	0x49, 0x53, 0x63, 0x74, 0x52, 0x0b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x47, 0x75, 0x69,
	0x64, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x7c, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x60, 0x0a, 0x15, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x73, 0x6b,
	0x55, 0x6e, 0x69, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x41, 0x73, 0x6b, 0x55, 0x6e, 0x69, 0x74, 0x52, 0x15, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x41, 0x73, 0x6b, 0x55, 0x6e, 0x69, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0x8f, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x4b, 0x0a, 0x0e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x0f, 0x65, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x0f, 0x65, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c,
	0x69, 0x73, 0x74, 0x2a, 0xac, 0x01, 0x0a, 0x0f, 0x45, 0x4e, 0x55, 0x4d, 0x5f, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x6f, 0x6e, 0x65, 0x10,
	0x00, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x42, 0x61, 0x73, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x50, 0x69, 0x73, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x42, 0x61, 0x73, 0x65,
	0x41, 0x74, 0x74, 0x72, 0x50, 0x69, 0x73, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x42, 0x61, 0x67,
	0x41, 0x74, 0x74, 0x72, 0x50, 0x69, 0x73, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x68, 0x61,
	0x72, 0x42, 0x61, 0x73, 0x65, 0x41, 0x74, 0x74, 0x72, 0x50, 0x69, 0x73, 0x10, 0x08, 0x12, 0x0a,
	0x0a, 0x06, 0x50, 0x65, 0x74, 0x50, 0x69, 0x73, 0x10, 0x10, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x65,
	0x61, 0x6d, 0x50, 0x69, 0x73, 0x10, 0x20, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x68, 0x61, 0x72, 0x47,
	0x75, 0x69, 0x6c, 0x64, 0x41, 0x74, 0x74, 0x72, 0x50, 0x69, 0x73, 0x10, 0x40, 0x12, 0x13, 0x0a,
	0x0e, 0x42, 0x6c, 0x6f, 0x6f, 0x64, 0x56, 0x65, 0x73, 0x73, 0x65, 0x6c, 0x50, 0x69, 0x73, 0x10,
	0x80, 0x01, 0x32, 0xa7, 0x03, 0x0a, 0x11, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x70, 0x0a, 0x0e, 0x53, 0x79, 0x6e, 0x63,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x2e, 0x41, 0x75, 0x72,
	0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x41, 0x75, 0x72,
	0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x2d, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x41, 0x75,
	0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x13,
	0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x69,
	0x6e, 0x66, 0x6f, 0x12, 0x96, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x31, 0x2e, 0x41, 0x75, 0x72, 0x6f,
	0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x1d, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x17, 0x3a, 0x01, 0x2a, 0x22, 0x12, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x2e, 0x5a, 0x2c,
	0x6c, 0x69, 0x74, 0x65, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x69,
	0x63, 0x72, 0x6f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x70, 0x6c, 0x61, 0x79,
	0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescOnce sync.Once
	file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescData = file_microservices_playerinfo_v1_playerinfostruct_proto_rawDesc
)

func file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescGZIP() []byte {
	file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescOnce.Do(func() {
		file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescData = protoimpl.X.CompressGZIP(file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescData)
	})
	return file_microservices_playerinfo_v1_playerinfostruct_proto_rawDescData
}

var file_microservices_playerinfo_v1_playerinfostruct_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes = make([]protoimpl.MessageInfo, 30)
var file_microservices_playerinfo_v1_playerinfostruct_proto_goTypes = []any{
	(ENUM_PlayerInfo)(0),             // 0: Aurora.PlayerInfoServer.ENUM_PlayerInfo
	(*PlayerBaseInfo)(nil),           // 1: Aurora.PlayerInfoServer.PlayerBaseInfo
	(*ItemAttr_PISct)(nil),           // 2: Aurora.PlayerInfoServer.ItemAttr_PISct
	(*BaseAttr_PISct)(nil),           // 3: Aurora.PlayerInfoServer.BaseAttr_PISct
	(*BagAttr_PISct)(nil),            // 4: Aurora.PlayerInfoServer.BagAttr_PISct
	(*AccountGuid_PISct)(nil),        // 5: Aurora.PlayerInfoServer.AccountGuid_PISct
	(*CharBaseAttr_PISct)(nil),       // 6: Aurora.PlayerInfoServer.CharBaseAttr_PISct
	(*Pet_PISct)(nil),                // 7: Aurora.PlayerInfoServer.Pet_PISct
	(*PetInfoAttr_PISct)(nil),        // 8: Aurora.PlayerInfoServer.PetInfoAttr_PISct
	(*PetSkillBookInfo_PISct)(nil),   // 9: Aurora.PlayerInfoServer.PetSkillBookInfo_PISct
	(*PetFightInfo_PISct)(nil),       // 10: Aurora.PlayerInfoServer.PetFightInfo_PISct
	(*PetFlairInfo_PISct)(nil),       // 11: Aurora.PlayerInfoServer.PetFlairInfo_PISct
	(*PetGeniusInfo_PISct)(nil),      // 12: Aurora.PlayerInfoServer.PetGeniusInfo_PISct
	(*FaceMakeInfo_PISct)(nil),       // 13: Aurora.PlayerInfoServer.FaceMakeInfo_PISct
	(*Team_PISct)(nil),               // 14: Aurora.PlayerInfoServer.Team_PISct
	(*CharGuildAttr_PISct)(nil),      // 15: Aurora.PlayerInfoServer.CharGuildAttr_PISct
	(*BloodVessel_PISct)(nil),        // 16: Aurora.PlayerInfoServer.BloodVessel_PISct
	(*DyeData_PISct)(nil),            // 17: Aurora.PlayerInfoServer.DyeData_PISct
	(*PlayerInfo)(nil),               // 18: Aurora.PlayerInfoServer.PlayerInfo
	(*SyncPlayerInfoRequest)(nil),    // 19: Aurora.PlayerInfoServer.SyncPlayerInfoRequest
	(*SyncPlayerInfoReply)(nil),      // 20: Aurora.PlayerInfoServer.SyncPlayerInfoReply
	(*GetPlayerInfoRequest)(nil),     // 21: Aurora.PlayerInfoServer.GetPlayerInfoRequest
	(*GetPlayerInfoReply)(nil),       // 22: Aurora.PlayerInfoServer.GetPlayerInfoReply
	(*PlayerInfoAskUnit)(nil),        // 23: Aurora.PlayerInfoServer.PlayerInfoAskUnit
	(*GetPlayerInfoListRequest)(nil), // 24: Aurora.PlayerInfoServer.GetPlayerInfoListRequest
	(*GetPlayerInfoListReply)(nil),   // 25: Aurora.PlayerInfoServer.GetPlayerInfoListReply
	nil,                              // 26: Aurora.PlayerInfoServer.BagAttr_PISct.EquipItemAttrsEntry
	nil,                              // 27: Aurora.PlayerInfoServer.PetInfoAttr_PISct.BasicAttrListEntry
	nil,                              // 28: Aurora.PlayerInfoServer.PetInfoAttr_PISct.FlairEntry
	nil,                              // 29: Aurora.PlayerInfoServer.FaceMakeInfo_PISct.FmDicEntry
	nil,                              // 30: Aurora.PlayerInfoServer.DyeData_PISct.FaceDataEntry
}
var file_microservices_playerinfo_v1_playerinfostruct_proto_depIdxs = []int32{
	8,  // 0: Aurora.PlayerInfoServer.PlayerBaseInfo.HenshinPet:type_name -> Aurora.PlayerInfoServer.PetInfoAttr_PISct
	26, // 1: Aurora.PlayerInfoServer.BagAttr_PISct.EquipItemAttrs:type_name -> Aurora.PlayerInfoServer.BagAttr_PISct.EquipItemAttrsEntry
	13, // 2: Aurora.PlayerInfoServer.CharBaseAttr_PISct.CharFaceMake:type_name -> Aurora.PlayerInfoServer.FaceMakeInfo_PISct
	10, // 3: Aurora.PlayerInfoServer.Pet_PISct.CombatPet:type_name -> Aurora.PlayerInfoServer.PetFightInfo_PISct
	8,  // 4: Aurora.PlayerInfoServer.Pet_PISct.HenshinPet:type_name -> Aurora.PlayerInfoServer.PetInfoAttr_PISct
	27, // 5: Aurora.PlayerInfoServer.PetInfoAttr_PISct.BasicAttrList:type_name -> Aurora.PlayerInfoServer.PetInfoAttr_PISct.BasicAttrListEntry
	28, // 6: Aurora.PlayerInfoServer.PetInfoAttr_PISct.Flair:type_name -> Aurora.PlayerInfoServer.PetInfoAttr_PISct.FlairEntry
	12, // 7: Aurora.PlayerInfoServer.PetInfoAttr_PISct.GeniusList:type_name -> Aurora.PlayerInfoServer.PetGeniusInfo_PISct
	9,  // 8: Aurora.PlayerInfoServer.PetInfoAttr_PISct.SkillBookList:type_name -> Aurora.PlayerInfoServer.PetSkillBookInfo_PISct
	8,  // 9: Aurora.PlayerInfoServer.PetFightInfo_PISct.PetInfo:type_name -> Aurora.PlayerInfoServer.PetInfoAttr_PISct
	29, // 10: Aurora.PlayerInfoServer.FaceMakeInfo_PISct.FmDic:type_name -> Aurora.PlayerInfoServer.FaceMakeInfo_PISct.FmDicEntry
	30, // 11: Aurora.PlayerInfoServer.DyeData_PISct.FaceData:type_name -> Aurora.PlayerInfoServer.DyeData_PISct.FaceDataEntry
	1,  // 12: Aurora.PlayerInfoServer.PlayerInfo.PlayerBaseInfo_PI:type_name -> Aurora.PlayerInfoServer.PlayerBaseInfo
	3,  // 13: Aurora.PlayerInfoServer.PlayerInfo.BaseAttr_PI:type_name -> Aurora.PlayerInfoServer.BaseAttr_PISct
	4,  // 14: Aurora.PlayerInfoServer.PlayerInfo.BagAttr_PI:type_name -> Aurora.PlayerInfoServer.BagAttr_PISct
	6,  // 15: Aurora.PlayerInfoServer.PlayerInfo.CharBaseAttr_PI:type_name -> Aurora.PlayerInfoServer.CharBaseAttr_PISct
	7,  // 16: Aurora.PlayerInfoServer.PlayerInfo.Pet_PI:type_name -> Aurora.PlayerInfoServer.Pet_PISct
	14, // 17: Aurora.PlayerInfoServer.PlayerInfo.Team_PI:type_name -> Aurora.PlayerInfoServer.Team_PISct
	15, // 18: Aurora.PlayerInfoServer.PlayerInfo.CharGuildAttr_PI:type_name -> Aurora.PlayerInfoServer.CharGuildAttr_PISct
	16, // 19: Aurora.PlayerInfoServer.PlayerInfo.BloodVessel_PI:type_name -> Aurora.PlayerInfoServer.BloodVessel_PISct
	5,  // 20: Aurora.PlayerInfoServer.SyncPlayerInfoRequest.AccountGuid:type_name -> Aurora.PlayerInfoServer.AccountGuid_PISct
	18, // 21: Aurora.PlayerInfoServer.SyncPlayerInfoRequest.PlayerInfo:type_name -> Aurora.PlayerInfoServer.PlayerInfo
	5,  // 22: Aurora.PlayerInfoServer.GetPlayerInfoRequest.AccountGuid:type_name -> Aurora.PlayerInfoServer.AccountGuid_PISct
	18, // 23: Aurora.PlayerInfoServer.GetPlayerInfoReply.PlayerInfo:type_name -> Aurora.PlayerInfoServer.PlayerInfo
	5,  // 24: Aurora.PlayerInfoServer.PlayerInfoAskUnit.AccountGuid:type_name -> Aurora.PlayerInfoServer.AccountGuid_PISct
	23, // 25: Aurora.PlayerInfoServer.GetPlayerInfoListRequest.playerInfoAskUnitList:type_name -> Aurora.PlayerInfoServer.PlayerInfoAskUnit
	18, // 26: Aurora.PlayerInfoServer.GetPlayerInfoListReply.PlayerInfoList:type_name -> Aurora.PlayerInfoServer.PlayerInfo
	2,  // 27: Aurora.PlayerInfoServer.BagAttr_PISct.EquipItemAttrsEntry.value:type_name -> Aurora.PlayerInfoServer.ItemAttr_PISct
	11, // 28: Aurora.PlayerInfoServer.PetInfoAttr_PISct.FlairEntry.value:type_name -> Aurora.PlayerInfoServer.PetFlairInfo_PISct
	17, // 29: Aurora.PlayerInfoServer.FaceMakeInfo_PISct.FmDicEntry.value:type_name -> Aurora.PlayerInfoServer.DyeData_PISct
	19, // 30: Aurora.PlayerInfoServer.PlayerInfoService.SyncPlayerInfo:input_type -> Aurora.PlayerInfoServer.SyncPlayerInfoRequest
	21, // 31: Aurora.PlayerInfoServer.PlayerInfoService.GetPlayerInfo:input_type -> Aurora.PlayerInfoServer.GetPlayerInfoRequest
	24, // 32: Aurora.PlayerInfoServer.PlayerInfoService.GetPlayerInfoList:input_type -> Aurora.PlayerInfoServer.GetPlayerInfoListRequest
	20, // 33: Aurora.PlayerInfoServer.PlayerInfoService.SyncPlayerInfo:output_type -> Aurora.PlayerInfoServer.SyncPlayerInfoReply
	22, // 34: Aurora.PlayerInfoServer.PlayerInfoService.GetPlayerInfo:output_type -> Aurora.PlayerInfoServer.GetPlayerInfoReply
	25, // 35: Aurora.PlayerInfoServer.PlayerInfoService.GetPlayerInfoList:output_type -> Aurora.PlayerInfoServer.GetPlayerInfoListReply
	33, // [33:36] is the sub-list for method output_type
	30, // [30:33] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_microservices_playerinfo_v1_playerinfostruct_proto_init() }
func file_microservices_playerinfo_v1_playerinfostruct_proto_init() {
	if File_microservices_playerinfo_v1_playerinfostruct_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_microservices_playerinfo_v1_playerinfostruct_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   30,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_microservices_playerinfo_v1_playerinfostruct_proto_goTypes,
		DependencyIndexes: file_microservices_playerinfo_v1_playerinfostruct_proto_depIdxs,
		EnumInfos:         file_microservices_playerinfo_v1_playerinfostruct_proto_enumTypes,
		MessageInfos:      file_microservices_playerinfo_v1_playerinfostruct_proto_msgTypes,
	}.Build()
	File_microservices_playerinfo_v1_playerinfostruct_proto = out.File
	file_microservices_playerinfo_v1_playerinfostruct_proto_rawDesc = nil
	file_microservices_playerinfo_v1_playerinfostruct_proto_goTypes = nil
	file_microservices_playerinfo_v1_playerinfostruct_proto_depIdxs = nil
}
