//GrpcAddressType:RedeemCode
//GrpcServerType:server

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.1
// source: microservices/redeemcode/v1/RedeemCode.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RedeemCode_AddBatchRedeemCode_FullMethodName    = "/Aurora.PlayerInfoServer.RedeemCode/AddBatchRedeemCode"
	RedeemCode_GetBatchRedeemCode_FullMethodName    = "/Aurora.PlayerInfoServer.RedeemCode/GetBatchRedeemCode"
	RedeemCode_DeleteBatchRedeemCode_FullMethodName = "/Aurora.PlayerInfoServer.RedeemCode/DeleteBatchRedeemCode"
	RedeemCode_GetRedeemCodeItems_FullMethodName    = "/Aurora.PlayerInfoServer.RedeemCode/GetRedeemCodeItems"
	RedeemCode_GetRedeemCodeReward_FullMethodName   = "/Aurora.PlayerInfoServer.RedeemCode/GetRedeemCodeReward"
)

// RedeemCodeClient is the client API for RedeemCode service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ServiceStart
type RedeemCodeClient interface {
	// 通过GMT平台添加一批新礼包码
	AddBatchRedeemCode(ctx context.Context, in *RedeemCodeBatchInfo, opts ...grpc.CallOption) (*AddBatchRedeemCodeRes, error)
	// 根据批次ID查询礼包码Info
	GetBatchRedeemCode(ctx context.Context, in *CodeBatchReq, opts ...grpc.CallOption) (*RedeemCodeBatchInfo, error)
	// 根据批次ID删除礼包码
	DeleteBatchRedeemCode(ctx context.Context, in *CodeBatchReq, opts ...grpc.CallOption) (*DeleteCodeBatchRes, error)
	// 接收游戏服的请求 校验礼包码 返回道具map
	GetRedeemCodeItems(ctx context.Context, in *GetRedeemCodeRewardReq, opts ...grpc.CallOption) (*GetRedeemCodeItemsRep, error)
	// 旧逻辑 校验礼包码 返回掉落包ID
	GetRedeemCodeReward(ctx context.Context, in *GetRedeemCodeRewardReq, opts ...grpc.CallOption) (*GetRedeemCodeRewardReply, error)
}

type redeemCodeClient struct {
	cc grpc.ClientConnInterface
}

func NewRedeemCodeClient(cc grpc.ClientConnInterface) RedeemCodeClient {
	return &redeemCodeClient{cc}
}

func (c *redeemCodeClient) AddBatchRedeemCode(ctx context.Context, in *RedeemCodeBatchInfo, opts ...grpc.CallOption) (*AddBatchRedeemCodeRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBatchRedeemCodeRes)
	err := c.cc.Invoke(ctx, RedeemCode_AddBatchRedeemCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *redeemCodeClient) GetBatchRedeemCode(ctx context.Context, in *CodeBatchReq, opts ...grpc.CallOption) (*RedeemCodeBatchInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RedeemCodeBatchInfo)
	err := c.cc.Invoke(ctx, RedeemCode_GetBatchRedeemCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *redeemCodeClient) DeleteBatchRedeemCode(ctx context.Context, in *CodeBatchReq, opts ...grpc.CallOption) (*DeleteCodeBatchRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteCodeBatchRes)
	err := c.cc.Invoke(ctx, RedeemCode_DeleteBatchRedeemCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *redeemCodeClient) GetRedeemCodeItems(ctx context.Context, in *GetRedeemCodeRewardReq, opts ...grpc.CallOption) (*GetRedeemCodeItemsRep, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRedeemCodeItemsRep)
	err := c.cc.Invoke(ctx, RedeemCode_GetRedeemCodeItems_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *redeemCodeClient) GetRedeemCodeReward(ctx context.Context, in *GetRedeemCodeRewardReq, opts ...grpc.CallOption) (*GetRedeemCodeRewardReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRedeemCodeRewardReply)
	err := c.cc.Invoke(ctx, RedeemCode_GetRedeemCodeReward_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RedeemCodeServer is the server API for RedeemCode service.
// All implementations must embed UnimplementedRedeemCodeServer
// for forward compatibility.
//
// ServiceStart
type RedeemCodeServer interface {
	// 通过GMT平台添加一批新礼包码
	AddBatchRedeemCode(context.Context, *RedeemCodeBatchInfo) (*AddBatchRedeemCodeRes, error)
	// 根据批次ID查询礼包码Info
	GetBatchRedeemCode(context.Context, *CodeBatchReq) (*RedeemCodeBatchInfo, error)
	// 根据批次ID删除礼包码
	DeleteBatchRedeemCode(context.Context, *CodeBatchReq) (*DeleteCodeBatchRes, error)
	// 接收游戏服的请求 校验礼包码 返回道具map
	GetRedeemCodeItems(context.Context, *GetRedeemCodeRewardReq) (*GetRedeemCodeItemsRep, error)
	// 旧逻辑 校验礼包码 返回掉落包ID
	GetRedeemCodeReward(context.Context, *GetRedeemCodeRewardReq) (*GetRedeemCodeRewardReply, error)
	mustEmbedUnimplementedRedeemCodeServer()
}

// UnimplementedRedeemCodeServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRedeemCodeServer struct{}

func (UnimplementedRedeemCodeServer) AddBatchRedeemCode(context.Context, *RedeemCodeBatchInfo) (*AddBatchRedeemCodeRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBatchRedeemCode not implemented")
}
func (UnimplementedRedeemCodeServer) GetBatchRedeemCode(context.Context, *CodeBatchReq) (*RedeemCodeBatchInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBatchRedeemCode not implemented")
}
func (UnimplementedRedeemCodeServer) DeleteBatchRedeemCode(context.Context, *CodeBatchReq) (*DeleteCodeBatchRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBatchRedeemCode not implemented")
}
func (UnimplementedRedeemCodeServer) GetRedeemCodeItems(context.Context, *GetRedeemCodeRewardReq) (*GetRedeemCodeItemsRep, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRedeemCodeItems not implemented")
}
func (UnimplementedRedeemCodeServer) GetRedeemCodeReward(context.Context, *GetRedeemCodeRewardReq) (*GetRedeemCodeRewardReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRedeemCodeReward not implemented")
}
func (UnimplementedRedeemCodeServer) mustEmbedUnimplementedRedeemCodeServer() {}
func (UnimplementedRedeemCodeServer) testEmbeddedByValue()                    {}

// UnsafeRedeemCodeServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RedeemCodeServer will
// result in compilation errors.
type UnsafeRedeemCodeServer interface {
	mustEmbedUnimplementedRedeemCodeServer()
}

func RegisterRedeemCodeServer(s grpc.ServiceRegistrar, srv RedeemCodeServer) {
	// If the following call pancis, it indicates UnimplementedRedeemCodeServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RedeemCode_ServiceDesc, srv)
}

func _RedeemCode_AddBatchRedeemCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RedeemCodeBatchInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RedeemCodeServer).AddBatchRedeemCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RedeemCode_AddBatchRedeemCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RedeemCodeServer).AddBatchRedeemCode(ctx, req.(*RedeemCodeBatchInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _RedeemCode_GetBatchRedeemCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CodeBatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RedeemCodeServer).GetBatchRedeemCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RedeemCode_GetBatchRedeemCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RedeemCodeServer).GetBatchRedeemCode(ctx, req.(*CodeBatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RedeemCode_DeleteBatchRedeemCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CodeBatchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RedeemCodeServer).DeleteBatchRedeemCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RedeemCode_DeleteBatchRedeemCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RedeemCodeServer).DeleteBatchRedeemCode(ctx, req.(*CodeBatchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RedeemCode_GetRedeemCodeItems_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRedeemCodeRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RedeemCodeServer).GetRedeemCodeItems(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RedeemCode_GetRedeemCodeItems_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RedeemCodeServer).GetRedeemCodeItems(ctx, req.(*GetRedeemCodeRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _RedeemCode_GetRedeemCodeReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRedeemCodeRewardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RedeemCodeServer).GetRedeemCodeReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RedeemCode_GetRedeemCodeReward_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RedeemCodeServer).GetRedeemCodeReward(ctx, req.(*GetRedeemCodeRewardReq))
	}
	return interceptor(ctx, in, info, handler)
}

// RedeemCode_ServiceDesc is the grpc.ServiceDesc for RedeemCode service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RedeemCode_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Aurora.PlayerInfoServer.RedeemCode",
	HandlerType: (*RedeemCodeServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddBatchRedeemCode",
			Handler:    _RedeemCode_AddBatchRedeemCode_Handler,
		},
		{
			MethodName: "GetBatchRedeemCode",
			Handler:    _RedeemCode_GetBatchRedeemCode_Handler,
		},
		{
			MethodName: "DeleteBatchRedeemCode",
			Handler:    _RedeemCode_DeleteBatchRedeemCode_Handler,
		},
		{
			MethodName: "GetRedeemCodeItems",
			Handler:    _RedeemCode_GetRedeemCodeItems_Handler,
		},
		{
			MethodName: "GetRedeemCodeReward",
			Handler:    _RedeemCode_GetRedeemCodeReward_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "microservices/redeemcode/v1/RedeemCode.proto",
}
