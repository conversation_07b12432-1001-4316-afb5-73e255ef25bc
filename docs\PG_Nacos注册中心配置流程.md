[Go微服务十二 Go使用nacos 注册服务，服务发现 - 知乎 (zhihu.com)](https://zhuanlan.zhihu.com/p/480990139)

[go微服务框架Kratos笔记（五）使用nacos作为服务注册和服务发现中心 - 悠悠听风 - 博客园 (cnblogs.com)](https://www.cnblogs.com/zly-go/p/15505164.html#!comments)

[Go微服务: Nacos的搭建和基础API的使用_nacos api-CSDN博客](https://blog.csdn.net/Tyro_java/article/details/139213323?utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-139213323-blog-136676681.235^v43^pc_blog_bottom_relevance_base7&spm=1001.2101.3001.4242.2&utm_relevant_index=2)

参考 链接

## Nacos 部署和配置相关

1.nacos-server-2.3.2 版本，自测用单列模式吧，等有腾讯云相关了 多例模式

2.环境依赖 JDK1.8以上

3.. 解压

4.修改启服脚本  \nacos\nacos\bin\startup.cmd  单列模式 ，liuxn 修改 startup.sh

如果是 Linux/Unix/Mac
启动命令(standalone代表着单机模式运行，非集群模式):

sh startup.sh -m standalone

如果您使用的是ubuntu系统，或者运行脚本报错提示[[符号找不到，可尝试如下运行：

bash startup.sh -m standalone

我是ubuntu系统，用了bash 起就对了



```
rem set MODE="cluster"
set MODE="standalone"
```

5.运行\nacos\nacos\bin\startup

6.输入网址 [Nacos](http://************:8848/nacos/index.html#/) 进入 webui 管理界面

7.停止 \nacos\nacos\bin\shutdown

8.配置文件在\nacos\nacos\conf\application.properties  修改是否需要账号密码，默认端口8848 等等

9.目前 http://10.1.9.102/ 已经部署

请求地址 http://10.1.9.102:8848/nacos   账号 nacos 密码 nacos

10.不要使用默认的public命名空间

## 配置中心

// 例子 按\gameserver\app\friendservice 为例，可以去里面copy

// 每次启动会从云服拉起 对应的配置文件缓存到本地路径，加载配置表，替换掉之前的配置结构

// 云服配置发生更变时 会有回调，然后重新读数据。修改配置缓存。这就需要功能里需要存最外成的配置缓存的指针，否则云服配置更新时无法动态修改程序里的运行时配置 如下的&bc

```go
app, cleanup, err := wireApp(bc.Server, &bc, nacosConfig.Nacosconfig, logger)
```

// 使用时和正常一样使用就行



1.拷贝函数 放到main.go   NacosConfigLoad(bc *conf.Bootstrap, nacosconfig *conf.Nacosconfig) config.Config {

```go
import (
	knacos "github.com/go-kratos/kratos/contrib/config/nacos/v2"
)

// NacosSubscribe 配置读取
func NacosConfigLoad(bc *conf.Bootstrap, nacosconfig *conf.Nacosconfig , serverType int) config.Config {
    ///var serverType = 5 //# 1 服务器列表服，2 公告服， 3 玩家信息服， 4 目录服，5 问卷回传服

    if nacosconfig != nil && nacosconfig.IsOpen > 0 {
       // 2 如果是需要读数据中心的就在覆盖

       sc := []constant.ServerConfig{
          *constant.NewServerConfig(nacosconfig.IpAddr, nacosconfig.Port),
       }

       cc := &constant.ClientConfig{
          NamespaceId:         nacosconfig.NamespaceId,
          TimeoutMs:           5000,
          NotLoadCacheAtStart: true,
          LogDir:              "./config/log",
          CacheDir:            "./config/cache",
          LogLevel:            "debug",
          Username:            nacosconfig.Username,
          Password:            nacosconfig.Password,
       }

       client, err := clients.NewConfigClient(vo.NacosClientParam{
          ClientConfig:  cc,
          ServerConfigs: sc,
       })
       if err != nil {
          log.Debug(err)
       }

       var configSources []config.Source

       for _, groupi := range nacosconfig.Groupitem {
          if groupi.ServerType == int32(serverType) {
             configSources = append(configSources,
                knacos.NewConfigSource(
                   client,
                   knacos.WithGroup(groupi.GroupName),
                   knacos.WithDataID(groupi.DataIDName),
                ))
          }
       }

       if len(configSources) == 0 {
          return nil
       }
       nacosConfigNew := config.New(
          config.WithSource(
             configSources...,
          ),
       )

       //defer nacosConfigNew.Close()

       if err1 := nacosConfigNew.Load(); err1 != nil {
          panic(err1)
       }

       for _, groupi := range nacosconfig.Groupitem {
          // watch key
          if groupi.ServerType == int32(serverType) && groupi.WatchName != "" {
             if err2 := nacosConfigNew.Watch(groupi.WatchName, func(key string, value config.Value) {
                if err3 := nacosConfigNew.Scan(bc); err3 != nil {
                   log.Debug(err3)
                }
             }); err2 != nil {
                log.Debug(err2)
             }
          }
       }
       if err4 := nacosConfigNew.Scan(bc); err4 != nil {
          panic(err4)
       }
       return nacosConfigNew

    }
    return nil
}
```

2.拷贝配置中心配置表nacosconfig.yaml，记得拷贝conf.proto 中的读表结构

```protobuf
message Nacosconfig {
  int32 isOpen = 1;
  string namespaceId = 2;
  string ipAddr = 3;
  uint64 port = 4;
  string username = 5;
  string password = 6;
  message Groupitem {
    string groupName = 1;
    string dataIDName = 2;
    string watchName = 3;
    int32 serverType = 4;
  }
  repeated Groupitem groupitem = 7;
}

message NacosconfigBase {
  Nacosconfig nacosconfig = 1;
}
```

3.配置nacosconfig.yaml  新的服务就加一个serverType 类型

```yaml
nacosconfig:
  isOpen: 1 # 是否取配置中心
  namespaceId: "public" # 配置中心命名空间
  ipAddr: "************" # 配置中心命地址
  port: 8848 # 配置中心端口
  username: nacos # 账号
  password: ppx123456 # 密码
  groupitem:
    - groupName: "KRATOS_PPX" # 组名
      dataIDName: "ppx-serverlist.yaml" # 文件名
      watchName: "serverlistgroup.severitem" # 监听改名名
      serverType: 1 # 1 服务器列表服，2 公告服， 3 玩家信息服， 4 目录服，5 问卷回传服 6 动态字典表

    - groupName: "KRATOS_PPX" # 组名
      dataIDName: "ppx-whitelistconfig.yaml" # 文件名
      watchName: "whitelist.whiteitem" # 监听改名名
      serverType: 1 # 1 服务器列表服，2 公告服， 3 玩家信息服， 4 目录服，5 问卷回传服  6 动态字典表

    - groupName: "KRATOS_PPX" # 组名
      dataIDName: "ppx-announcement.yaml" # 文件名
      watchName: "noticelistgroup.noticeitem" # 监听改名名
      serverType: 2 # 1 服务器列表服，2 公告服， 3 玩家信息服， 4 目录服，5 问卷回传服  6 动态字典表

    - groupName: "KRATOS_PPX" # 组名
      dataIDName: "ppx-myserverlisyt.yaml" # 文件名
      watchName: "" # 监听改名名
      serverType: 3 # 1 服务器列表服，2 公告服， 3 玩家信息服， 4 目录服，5 问卷回传服  6 动态字典表

    - groupName: "KRATOS_PPX" # 组名
      dataIDName: "ppx-director.yaml" # 文件名
      watchName: "directorgroup.severitem" # 监听改名名
      serverType: 4 # 1 服务器列表服，2 公告服， 3 玩家信息服， 4 目录服，5 问卷回传服  6 动态字典表

    - groupName: "KRATOS_PPX" # 组名
      dataIDName: "ppx-questionnaire.yaml" # 文件名
      watchName: "Questionnairegroup.severitem" # 监听改名名
      serverType: 5 # 1 服务器列表服，2 公告服， 3 玩家信息服， 4 目录服，5 问卷回传服  6 动态字典表

    - groupName: "KRATOS_PPX" # 组名
      dataIDName: "ppx-dynamicdictionary.yaml" # 文件名
      watchName: "Dynamicdictionarygroup.dicitem" # 监听改名名
      serverType: 6 # 1 服务器列表服，2 公告服， 3 玩家信息服， 4 目录服，5 问卷回传服  6 动态字典表
```

4.z执行以下 make config

2.main.go调用函数

```go
var nacosConfig conf.NacosconfigBase
	if err := c.Scan(&nacosConfig); err != nil {
		panic(err)
	}

	// 配置中心读取
	nConfig := NacosConfigLoad(&bc, nacosConfig.Nacosconfig)
	if nConfig != nil {
		defer nConfig.Close()
	}
```

## 服务注册

// 例子 按\gameserver\app\friendservice 为例，可以去里面copy

//建议每个服务都加上，这样nacos 配置中心会有对应服务的实例，可以查看具体服务的运行情况

目前我放到service上注册，放哪里都差不多

1.nacos 配置

```go
// ProviderSet is service providers.
var ProviderSet = wire.NewSet(NewQuestionnaireService, NewRegistrarNacos, NewRegistrar)

// NewRegistrar 引入 consul
func NewRegistrarNacos(conf *conf.Nacosconfig) vo.NacosClientParam {

	if conf.IsOpen < 1 {
		return vo.NacosClientParam{}
	}

	sc := []constant.ServerConfig{
		*constant.NewServerConfig(conf.IpAddr, conf.Port),
	}

	cc := &constant.ClientConfig{
		NamespaceId:         conf.NamespaceId,
		TimeoutMs:           5000,
		NotLoadCacheAtStart: true,
		LogDir:              "./config/log",
		CacheDir:            "./config/cache",
		LogLevel:            "debug",
		Username:            conf.Username,
		Password:            conf.Password,
	}
	return vo.NacosClientParam{
		ClientConfig:  cc,
		ServerConfigs: sc,
	}
}
```



2.注册  这个是用 kratos 框架带的 需要在启动是填服务名

```go
	
import (
	"github.com/go-kratos/kratos/contrib/registry/nacos/v2"
)


// NewRegistrar 服务注册业务注入
func NewRegistrar(param vo.NacosClientParam, conf *conf.Nacosconfig) registry.Registrar {
	if conf.IsOpen < 1 {
		return nil
	}

	client, err := clients.NewNamingClient(param)
	if err != nil {
		panic(err)
	}

	return nacos.New(client)
}
```

```go
// go build -ldflags "-X main.Version=x.y.z"
var (
    // Name is the name of the compiled software.
    Name = "questionnaire"  //// ----------这里-----------------------
    // Version is the version of the compiled software.
    Version = "v1.0"
    // flagconf is the config flag.
    flagconf string

    id, _ = os.Hostname()
)
```







3.main.go  newapp

```go
func newApp(logger log.Logger, gr *grpc.Server, hs *http.Server, rr registry.Registrar) *kratos.App {

	if rr == nil {
		return kratos.New(
			kratos.ID(id),
			kratos.Name(Name),
			kratos.Version(Version),
			kratos.Metadata(map[string]string{}),
			kratos.Logger(logger),
			kratos.Server(
				hs,
				gr,
			),
		)
	} else {
		return kratos.New(
			kratos.ID(id),
			kratos.Name(Name),
			kratos.Version(Version),
			kratos.Metadata(map[string]string{}),
			kratos.Logger(logger),
			kratos.Server(
				hs,
				gr,
			),
			kratos.Registrar(rr),
		)
	}
}
```

```
// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name = "friendservice"  ///// 修改以下 名字 服务名
	// Version is the version of the compiled software.
	Version string
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()
)
```

4.修改 wire.go 中的 函数 需要添加 *conf.Nacosconfig

```go
// wireApp init serverlist application.
func wireApp(*conf.Server, *conf.Data, *conf.Bootstrap, *conf.Nacosconfig, log.Logger) (*kratos.App, func(), error) {
    panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, newApp))
}
```



3.wire 生注入

```go
// ProviderSet is service providers.
var ProviderSet = wire.NewSet(NewQuestionnaireService, NewRegistrarNacos, NewRegistrar)
```



4.运行程序 注册成功后nacos 工作台 会显示对应的服务



## 服务器发现

kratos 自带的 服务发现

1.server.go 加入服务器发现

```go
// ProviderSet is service providers.
var ProviderSet = wire.NewSet(NewFriendserviceServiceService, NewRegistrarNacos, NewRegistrar, NewDiscovery)


// NewDiscovery nacos服务发现注入
func NewDiscovery(param vo.NacosClientParam, conf *conf.Nacosconfig) registry.Discovery {
	if conf.IsOpen < 1 {
		return nil
	}
	client, err := clients.NewNamingClient(param)
	if err != nil {
		panic(err)
	}
	return nacos.New(client)
}

```

2.data.go 引用对应的向发现的服务

```go
// ProviderSet is data providers.
var ProviderSet = wire.NewSet(NewData, NewRedisCmd, NewUserServiceClient, NewFriendserviceRepo)

func NewUserServiceClient(r registry.Discovery) td.TextDetectClient {
	if r == nil {
		return nil
	}
	conn, err := grpc.DialInsecure(
		context.Background(),
		grpc.WithEndpoint("discovery:///textdetect.grpc"),
		grpc.WithDiscovery(r),
		grpc.WithMiddleware(
			recovery.Recovery(),
		),
	)
	if err != nil {
		panic(err)
	}
	return td.NewTextDetectClient(conn)
}
```

3.返回的连接建议是缓存一下 如好友相关

```go
type FriendserviceRepo struct {
	data  *Data
	log   *log.Helper
	kconf vo.NacosClientParam
	td  td.TextDetectClient  //屏蔽服务
}

// 注入的代码
// NewRelationRepo .
func NewFriendserviceRepo(data *Data, logger log.Logger, kconf vo.NacosClientParam, td td.TextDetectClient) biz.FriendserviceRepo {
	return &FriendserviceRepo{
		data:  data,
		log:   log.NewHelper(logger),
		kconf: kconf,
		td:    td,
	}
}
```

4.具体是使用 就直接 用td就行

5.记得 用wire 名字 执行以下注入

## 例子 屏蔽字 grpc调用

好友服务器 已经接入 可以抄
