﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <BaseOutputPath>..\..\..\..\bin</BaseOutputPath>
        <OutputPath>..\..\..\..\bin</OutputPath>
        <PackageOutputPath>bin</PackageOutputPath>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
      <DefineConstants>$(DefineConstants);LOG_ENABLE;CONSOLE_LOG</DefineConstants>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
      <DefineConstants>$(DefineConstants);LOG_ENABLE</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Framework\Framework.csproj" />
      <ProjectReference Include="..\Common\Common.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="CommandLineParser" Version="2.9.1" />
      <PackageReference Include="Google.Protobuf" Version="3.29.1" />
      <PackageReference Include="MemoryPack" Version="1.21.4" />
      <PackageReference Include="MemoryPack.Core" Version="1.21.4" />
      <PackageReference Include="MemoryPack.Generator" Version="1.21.4">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Microsoft.Diagnostics.Runtime" Version="3.1.512801" />
      <PackageReference Include="Microsoft.Extensions.Diagnostics.Abstractions" Version="9.0.6" />
      <PackageReference Include="NATS.Net" Version="2.5.5" />
      <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
      <PackageReference Include="NLog" Version="6.0.0-rc4" />
      <PackageReference Include="Serilog" Version="4.2.0" />
      <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
      <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
      <PackageReference Include="YamlDotNet" Version="16.2.1" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="src\Common\Define\" />
      <Folder Include="src\Common\Config\" />
      <Folder Include="src\dao\" />
      <Folder Include="src\table\" />
    </ItemGroup>

</Project>
