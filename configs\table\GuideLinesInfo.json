[{"ID": 100101, "NextId": 100102, "actionType1": 3, "actionParam1": "", "ActionParamInt1": [[]], "ActionParamString1": [], "ButtonPath": "UI_Main/Top/buttonList_LT/EconomyBtn", "GoBackId": 100101, "IsHighlight": 1, "ArrowType": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogType": 1, "DialogHeadIcon": "UD_Head_costume2", "DialogID": 1002877, "DialogPosType": 1, "DialogOffsetY": 100, "AreaId": -1, "ButtonPathType": 3, "UIOpen": [], "CheckFunId": -1, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsBattleStop": 1}, {"ID": 100102, "NextId": 100103, "actionType1": 3, "actionParam1": "", "ActionParamInt1": [[]], "ActionParamString1": [], "ButtonPath": "UI_Main/FunTypeGroup/EconomyGroup/EconomyGroupCloseBtn", "GoBackId": 100101, "IsHighlight": -1, "ArrowType": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogType": 1, "DialogHeadIcon": "UD_Head_costume2", "DialogID": 1002878, "DialogPosType": 0, "DialogOffsetY": -100, "AreaId": -1, "ButtonPathType": 3, "UIOpen": [], "CheckFunId": -1, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsBattleStop": 1}, {"ID": 100103, "NextId": 100104, "actionType1": 3, "actionParam1": "", "ActionParamInt1": [[]], "ActionParamString1": [], "ButtonPath": "UI_Main/Top/buttonList_LT/RetainBtn", "GoBackId": 100101, "IsHighlight": 1, "ArrowType": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogType": 1, "DialogHeadIcon": "UD_Head_costume2", "DialogID": 1002881, "DialogPosType": 1, "DialogOffsetY": -100, "AreaId": -1, "ButtonPathType": 3, "UIOpen": [], "CheckFunId": -1, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsBattleStop": 1}, {"ID": 100104, "NextId": 100105, "actionType1": 3, "actionParam1": "", "ActionParamInt1": [[]], "ActionParamString1": [], "ButtonPath": "UI_Main/FunTypeGroup/RetainGroup/RetainGroupCloseBtn", "GoBackId": 100101, "IsHighlight": -1, "ArrowType": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogType": 1, "DialogHeadIcon": "UD_Head_costume2", "DialogID": 1002882, "DialogPosType": 0, "DialogOffsetY": -100, "AreaId": -1, "ButtonPathType": 3, "UIOpen": [], "CheckFunId": -1, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsBattleStop": 1}, {"ID": 100105, "NextId": 100106, "actionType1": 3, "actionParam1": "", "ActionParamInt1": [[]], "ActionParamString1": [], "ButtonPath": "UI_Main/Top/buttonList_RT/BaseFuncBtn", "GoBackId": 100101, "IsHighlight": 1, "ArrowType": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogType": 1, "DialogHeadIcon": "UD_Head_costume2", "DialogID": 1002883, "DialogPosType": 1, "DialogOffsetY": 100, "AreaId": -1, "ButtonPathType": 3, "UIOpen": [], "CheckFunId": -1, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsBattleStop": 1}, {"ID": 100106, "NextId": -1, "actionType1": 3, "actionParam1": "", "ActionParamInt1": [[]], "ActionParamString1": [], "ButtonPath": "UI_Main/FunTypeGroup/BaseFuncGroup/BaseFuncGroupCloseBtn", "GoBackId": 100101, "IsHighlight": -1, "ArrowType": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogType": 1, "DialogHeadIcon": "UD_Head_costume2", "DialogID": 1002884, "DialogPosType": 0, "DialogOffsetY": -100, "AreaId": -1, "ButtonPathType": 3, "UIOpen": [], "CheckFunId": -1, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsBattleStop": 1}, {"ID": 100201, "NextId": -1, "actionType1": 6, "actionParam1": "", "ActionParamInt1": [[]], "ActionParamString1": [], "ButtonPath": "", "GoBackId": 100201, "IsHighlight": -4, "ArrowType": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogType": 1, "DialogHeadIcon": "UD_Head_costume2", "DialogID": 1002891, "DialogPosType": 0, "DialogOffsetY": -100, "AreaId": -1, "ButtonPathType": 3, "UIOpen": [], "CheckFunId": -1, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsBattleStop": 1}]