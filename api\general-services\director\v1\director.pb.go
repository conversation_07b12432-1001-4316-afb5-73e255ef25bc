//
// @Author:zhouchen
// @Description: 消息包文件定义
// @Data: Created in 20:01 2023/6/6

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.23.2
// source: general-services/director/v1/director.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetDirectorReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Platform      string                 `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	Channel       string                 `protobuf:"bytes,2,opt,name=channel,proto3" json:"channel,omitempty"`
	Version       string                 `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDirectorReq) Reset() {
	*x = GetDirectorReq{}
	mi := &file_general_services_director_v1_director_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDirectorReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDirectorReq) ProtoMessage() {}

func (x *GetDirectorReq) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_director_v1_director_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDirectorReq.ProtoReflect.Descriptor instead.
func (*GetDirectorReq) Descriptor() ([]byte, []int) {
	return file_general_services_director_v1_director_proto_rawDescGZIP(), []int{0}
}

func (x *GetDirectorReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *GetDirectorReq) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *GetDirectorReq) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type DirectorData struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ServerListUrl   string                 `protobuf:"bytes,1,opt,name=serverListUrl,proto3" json:"serverListUrl,omitempty"`
	AnnounceInfoUrl string                 `protobuf:"bytes,3,opt,name=announceInfoUrl,proto3" json:"announceInfoUrl,omitempty"`
	ApUrl           string                 `protobuf:"bytes,4,opt,name=apUrl,proto3" json:"apUrl,omitempty"`
	ResUrl          string                 `protobuf:"bytes,5,opt,name=resUrl,proto3" json:"resUrl,omitempty"`
	ResVersionMd5   string                 `protobuf:"bytes,6,opt,name=resVersionMd5,proto3" json:"resVersionMd5,omitempty"`
	ServerVersion   string                 `protobuf:"bytes,7,opt,name=serverVersion,proto3" json:"serverVersion,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *DirectorData) Reset() {
	*x = DirectorData{}
	mi := &file_general_services_director_v1_director_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DirectorData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DirectorData) ProtoMessage() {}

func (x *DirectorData) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_director_v1_director_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DirectorData.ProtoReflect.Descriptor instead.
func (*DirectorData) Descriptor() ([]byte, []int) {
	return file_general_services_director_v1_director_proto_rawDescGZIP(), []int{1}
}

func (x *DirectorData) GetServerListUrl() string {
	if x != nil {
		return x.ServerListUrl
	}
	return ""
}

func (x *DirectorData) GetAnnounceInfoUrl() string {
	if x != nil {
		return x.AnnounceInfoUrl
	}
	return ""
}

func (x *DirectorData) GetApUrl() string {
	if x != nil {
		return x.ApUrl
	}
	return ""
}

func (x *DirectorData) GetResUrl() string {
	if x != nil {
		return x.ResUrl
	}
	return ""
}

func (x *DirectorData) GetResVersionMd5() string {
	if x != nil {
		return x.ResVersionMd5
	}
	return ""
}

func (x *DirectorData) GetServerVersion() string {
	if x != nil {
		return x.ServerVersion
	}
	return ""
}

type GetDirectorReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	Data          *DirectorData          `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDirectorReply) Reset() {
	*x = GetDirectorReply{}
	mi := &file_general_services_director_v1_director_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDirectorReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDirectorReply) ProtoMessage() {}

func (x *GetDirectorReply) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_director_v1_director_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDirectorReply.ProtoReflect.Descriptor instead.
func (*GetDirectorReply) Descriptor() ([]byte, []int) {
	return file_general_services_director_v1_director_proto_rawDescGZIP(), []int{2}
}

func (x *GetDirectorReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GetDirectorReply) GetData() *DirectorData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_general_services_director_v1_director_proto protoreflect.FileDescriptor

const file_general_services_director_v1_director_proto_rawDesc = "" +
	"\n" +
	"+general-services/director/v1/director.proto\x12\vdirector.v1\x1a\x1cgoogle/api/annotations.proto\"`\n" +
	"\x0eGetDirectorReq\x12\x1a\n" +
	"\bplatform\x18\x01 \x01(\tR\bplatform\x12\x18\n" +
	"\achannel\x18\x02 \x01(\tR\achannel\x12\x18\n" +
	"\aversion\x18\x03 \x01(\tR\aversion\"\xd8\x01\n" +
	"\fDirectorData\x12$\n" +
	"\rserverListUrl\x18\x01 \x01(\tR\rserverListUrl\x12(\n" +
	"\x0fannounceInfoUrl\x18\x03 \x01(\tR\x0fannounceInfoUrl\x12\x14\n" +
	"\x05apUrl\x18\x04 \x01(\tR\x05apUrl\x12\x16\n" +
	"\x06resUrl\x18\x05 \x01(\tR\x06resUrl\x12$\n" +
	"\rresVersionMd5\x18\x06 \x01(\tR\rresVersionMd5\x12$\n" +
	"\rserverVersion\x18\a \x01(\tR\rserverVersion\"Y\n" +
	"\x10GetDirectorReply\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12-\n" +
	"\x04data\x18\x02 \x01(\v2\x19.director.v1.DirectorDataR\x04data2\x9c\x01\n" +
	"\bDirector\x12\x8f\x01\n" +
	"\x0fGetDirectorData\x12\x1b.director.v1.GetDirectorReq\x1a\x1d.director.v1.GetDirectorReply\"@\x82\xd3\xe4\x93\x02:\x128/api/director/getdirector/{platform}/{channel}/{version}B\x1dZ\x1bdirector/api/director/v1;v1b\x06proto3"

var (
	file_general_services_director_v1_director_proto_rawDescOnce sync.Once
	file_general_services_director_v1_director_proto_rawDescData []byte
)

func file_general_services_director_v1_director_proto_rawDescGZIP() []byte {
	file_general_services_director_v1_director_proto_rawDescOnce.Do(func() {
		file_general_services_director_v1_director_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_general_services_director_v1_director_proto_rawDesc), len(file_general_services_director_v1_director_proto_rawDesc)))
	})
	return file_general_services_director_v1_director_proto_rawDescData
}

var file_general_services_director_v1_director_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_general_services_director_v1_director_proto_goTypes = []any{
	(*GetDirectorReq)(nil),   // 0: director.v1.GetDirectorReq
	(*DirectorData)(nil),     // 1: director.v1.DirectorData
	(*GetDirectorReply)(nil), // 2: director.v1.GetDirectorReply
}
var file_general_services_director_v1_director_proto_depIdxs = []int32{
	1, // 0: director.v1.GetDirectorReply.data:type_name -> director.v1.DirectorData
	0, // 1: director.v1.Director.GetDirectorData:input_type -> director.v1.GetDirectorReq
	2, // 2: director.v1.Director.GetDirectorData:output_type -> director.v1.GetDirectorReply
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_general_services_director_v1_director_proto_init() }
func file_general_services_director_v1_director_proto_init() {
	if File_general_services_director_v1_director_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_general_services_director_v1_director_proto_rawDesc), len(file_general_services_director_v1_director_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_general_services_director_v1_director_proto_goTypes,
		DependencyIndexes: file_general_services_director_v1_director_proto_depIdxs,
		MessageInfos:      file_general_services_director_v1_director_proto_msgTypes,
	}.Build()
	File_general_services_director_v1_director_proto = out.File
	file_general_services_director_v1_director_proto_goTypes = nil
	file_general_services_director_v1_director_proto_depIdxs = nil
}
