//GrpcAddressType:PlayerInfoServer
//GrpcServerType:all

syntax = "proto3";

package Aurora.PlayerInfoServer;

import "google/api/annotations.proto";

option go_package = "liteframe/api/microservices/playerinfo/v1;v1";

//ServiceStart
service PlayerInfoService {
// 同步玩家信息
rpc SyncPlayerInfo(SyncPlayerInfoRequest) returns (SyncPlayerInfoReply) {
}

// 查询玩家信息
rpc GetPlayerInfo(GetPlayerInfoRequest) returns (GetPlayerInfoReply) {
option (google.api.http) = {
post: "/v1/playerinfo"
body: "*"
};
}

// 查询玩家信息
rpc GetPlayerInfoList(GetPlayerInfoListRequest) returns (GetPlayerInfoListReply) {
option (google.api.http) = {
post: "/v1/playerinfolist"
body: "*"
};
}
}
//ServiceEnd


//StructStart
//Type:Http
//Type:Inner
enum ENUM_PlayerInfo
{
None=0;
PlayerBaseInfoPis=1;
BaseAttrPis=2;
BagAttrPis=4;
CharBaseAttrPis=8;
PetPis=16;
TeamPis=32;
CharGuildAttrPis=64;
BloodVesselPis=128;
}

//Type:Http
//Type:Inner
message PlayerBaseInfo
{
int64 FightPoint = 1;
int32 HenshinPetId = 2;
int32 Level = 3;
string Name = 4;
int32 SceneId = 5;
int32 Sex = 6;
int32 UnitType = 7;
uint64 ForbiddenLoginEndTime = 8;
uint64 Guid = 9;
uint32 ServerNum = 10;
uint32 ZoneWorldID = 11;
PetInfoAttr_PISct HenshinPet = 12;
int32 PetID = 13;
int32 SelfTeamIndexId = 14;
uint64 TeamId = 15;
int32 TeamJobTitle = 16;
int32 TeamMemCount = 17;
int32 TeamPlayerCount = 18;
int32 TeamType = 19;
uint64 GuildGuid = 20;
string GuildName = 21;
int32  HeadIcon  = 22;
int32  HeadFrame = 23;
bool IsOnline = 24;
int64 LastLoginTime = 25;
}

//Type:Http
//Type:Inner
message ItemAttr_PISct
{
uint64 GUID = 1;
}

//Type:Http
//Type:Inner
message BaseAttr_PISct
{
int32 CharModelID = 1;
int32 Level = 2;
string Name = 3;
int32 SceneId = 4;
int32 Sex = 5;
}

//Type:Http
//Type:Inner
message BagAttr_PISct
{
repeated bool BankLockState = 1;
map<int64,ItemAttr_PISct> EquipItemAttrs = 2;
}

//Type:Http
//Type:Inner
message AccountGuid_PISct
{
uint64 Guid = 1;
int32 ZoneWorldId = 2;
}

//Type:Http
//Type:Inner
message CharBaseAttr_PISct
{
FaceMakeInfo_PISct CharFaceMake = 1;
uint64 CreateTime = 2;
uint64 ForbiddenLoginEndTime = 3;
uint64 Guid = 4;
uint32 ServerNum = 5;
uint32 ZoneWorldID = 6;
}

//Type:Http
//Type:Inner
message Pet_PISct
{
PetFightInfo_PISct CombatPet = 1;
PetInfoAttr_PISct HenshinPet = 2;
}

//Type:Http
//Type:Inner
message PetInfoAttr_PISct
{
map<int64,int32> BasicAttrList = 1;
int32 CombatModelID = 2;
int32 EvolveLevel = 3;
int32 FightPoint = 4;
map<int64,PetFlairInfo_PISct> Flair = 5;
int32 GeniusCount = 6;
repeated PetGeniusInfo_PISct GeniusList = 7;
uint64 GUID = 8;
float GWTHRate = 9;
int32 IsFightPet = 10;
bool IsLocked = 11;
int32 Level = 12;
int64 LevelEXP = 13;
string Name = 14;
int32 PetID = 15;
int32 Quality = 16;
repeated PetSkillBookInfo_PISct SkillBookList = 17;
int32 StarLevel = 18;
int32 XDValueAGI = 19;
int32 XDValueCON = 20;
int32 XDValueINT = 21;
int32 XDValueSTR = 22;
int32 XianDan = 23;
}

//Type:Http
//Type:Inner
message PetSkillBookInfo_PISct
{
int32 Level = 1;
int32 SkillBookID = 2;
int32 Slot = 3;
}

//Type:Http
//Type:Inner
message PetFightInfo_PISct
{
PetInfoAttr_PISct PetInfo = 1;
}

//Type:Http
//Type:Inner
message PetFlairInfo_PISct
{
int32 AllAddValue = 1;
int32 BaseValue = 2;
}

//Type:Http
//Type:Inner
message PetGeniusInfo_PISct
{
float Amend = 1;
int32 ID = 2;
}

//Type:Http
//Type:Inner
message FaceMakeInfo_PISct
{
map<int64,DyeData_PISct> FmDic = 1;
}

//Type:Http
//Type:Inner
message Team_PISct
{
int32 SelfTeamIndexId = 1;
int32 TeamFollowState = 2;
uint64 TeamId = 3;
int32 TeamJobTitle = 4;
int32 TeamMemCount = 5;
int32 TeamPlayerCount = 6;
int32 TeamTarget = 7;
int32 TeamType = 8;
}

//Type:Http
//Type:Inner
message CharGuildAttr_PISct
{
uint64 GuildGuid = 1;
uint32 GuildLevel = 2;
string GuildName = 3;
int32 GuildPostId = 4;
}

//Type:Http
//Type:Inner
message BloodVessel_PISct
{
int32 BigNode = 1;
repeated double FightPoint = 2;
}

//Type:Http
//Type:Inner
message DyeData_PISct
{
map<int64,int32> FaceData = 1;
}

//Type:Http
//Type:Inner
message PlayerInfo
{
PlayerBaseInfo PlayerBaseInfo_PI = 1;
BaseAttr_PISct BaseAttr_PI = 2;
BagAttr_PISct BagAttr_PI = 3;
CharBaseAttr_PISct CharBaseAttr_PI = 4;
Pet_PISct Pet_PI = 5;
Team_PISct Team_PI = 6;
CharGuildAttr_PISct CharGuildAttr_PI = 7;
BloodVessel_PISct BloodVessel_PI = 8;
}

//StructEnd

// 请求消息定义
//Type:Inner
//Target:S2W
message SyncPlayerInfoRequest  //IMessage
{
AccountGuid_PISct AccountGuid = 1;
uint64 ePlayerInfo = 2;             // 类型实际是 ENUM_PlayerInfo
PlayerInfo PlayerInfo = 3;
}

//Type:Inner
//Target:W2S
message SyncPlayerInfoReply  //IMessage
{
int32 result = 1;
}

//Type:Http
//Type:Inner
//Target:S2W
//Response W2S_GetPlayerInfoReply
message GetPlayerInfoRequest  // IRequest
{
AccountGuid_PISct AccountGuid = 1;
uint64 ePlayerInfo = 2;            // 类型实际是 ENUM_PlayerInfo
}

//Type:Http
//Type:Inner
//Target:W2S
message GetPlayerInfoReply  // IResponse
{
PlayerInfo PlayerInfo = 1;
uint64 ePlayerInfo = 2;
}

//Type:Http
//Type:Inner
message PlayerInfoAskUnit
{
AccountGuid_PISct AccountGuid = 1;
uint64 ePlayerInfo = 2;            // 类型实际是 ENUM_PlayerInfo
}

//Type:Http
//Type:Inner
//Target:S2W
//Response W2S_GetPlayerInfoListReply
message GetPlayerInfoListRequest  // IRequest
{
repeated PlayerInfoAskUnit playerInfoAskUnitList = 1;
}

//Type:Http
//Type:Inner
//Target:W2S
message GetPlayerInfoListReply  // IResponse
{
repeated PlayerInfo PlayerInfoList = 1;
repeated uint64 ePlayerInfoList = 2;
}
