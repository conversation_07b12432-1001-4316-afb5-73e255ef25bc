using System;
using System.Collections.Generic;
using System.Linq;
using BattleServer.Game.Core;
using LiteFrame.Framework;

namespace BattleServer.Game.Player
{
    /// <summary>
    /// 对手配对管理器 - 负责4人自走棋的对手配对逻辑
    /// </summary>
    public class OpponentPairManager : BattleComponentBase
    {
        private List<long> _playerRanking; // 玩家排名（按血量排序）
        private Dictionary<long, long> _lastOpponents; // 记录上一回合的对手
        private string _logName;

        public OpponentPairManager()
        {
            _playerRanking = new List<long>();
            _lastOpponents = new Dictionary<long, long>();
            _logName = "OpponentPairManager";
        }

        protected override void OnInitialize()
        {
            _playerRanking.Clear();
            _lastOpponents.Clear();
            Log.Info($"[{_logName}] Initialized for battle {BattleId}");
        }

        protected override void OnClear()
        {
            _playerRanking.Clear();
            _lastOpponents.Clear();
        }

        /// <summary>
        /// 简化的配对方法 - 供AutoChessScene使用
        /// </summary>
        /// <param name="activePlayerIds">活跃玩家ID列表</param>
        /// <param name="playerManager">玩家管理器</param>
        /// <returns>对手配对字典</returns>
        public Dictionary<long, long> PairOpponents(List<long> activePlayerIds, PlayerManager playerManager)
        {
            // 获取玩家血量信息
            var playerHealths = new Dictionary<long, int>();
            foreach (var playerId in activePlayerIds)
            {
                playerHealths[playerId] = playerManager.GetPlayerHealth(playerId);
            }

            // 使用现有的配对逻辑
            return GenerateOpponentPairs(playerHealths, 1); // 简化实现，暂时使用回合1的逻辑
        }

        /// <summary>
        /// 生成对手配对 - 根据战斗文档的规则
        /// </summary>
        /// <param name="playerHealths">玩家血量字典</param>
        /// <param name="roundCount">当前回合数</param>
        /// <returns>对手配对字典</returns>
        public Dictionary<long, long> GenerateOpponentPairs(Dictionary<long, int> playerHealths, int roundCount)
        {
            var activePlayers = playerHealths.Where(p => p.Value > 0).Select(p => p.Key).ToList();
            var eliminatedPlayers = playerHealths.Where(p => p.Value <= 0).Select(p => p.Key).ToList();
            var opponentPairs = new Dictionary<long, long>();

            Log.Info($"[{_logName}] Generating opponent pairs for round {roundCount}, active players: {activePlayers.Count}, eliminated: {eliminatedPlayers.Count}");

            // 根据策划文档的淘汰处理规则
            if (eliminatedPlayers.Count >= 3)
            {
                // 3人淘汰：游戏结束，不需要配对
                Log.Info($"[{_logName}] 3+ players eliminated, game should end");
                return opponentPairs;
            }
            else if (eliminatedPlayers.Count == 2)
            {
                // 2人淘汰：移出匹配队列，剩余2人对战
                opponentPairs = GenerateTwoPlayerPairs(activePlayers);
            }
            else if (eliminatedPlayers.Count == 1)
            {
                // 1人淘汰：保持4人匹配队列，被淘汰者填充匹配位置
                opponentPairs = GenerateThreePlayerPairs(activePlayers, eliminatedPlayers, playerHealths, roundCount);
            }
            else
            {
                // 无人淘汰：正常4人配对
                if (roundCount == 1)
                {
                    opponentPairs = GenerateRandomPairs(activePlayers);
                }
                else
                {
                    opponentPairs = GenerateRankedPairs(activePlayers, playerHealths);
                }
            }

            // 记录本回合的对手关系
            UpdateLastOpponents(opponentPairs);

            Log.Info($"[{_logName}] Generated {opponentPairs.Count / 2} opponent pairs for round {roundCount}");
            return opponentPairs;
        }

        /// <summary>
        /// 第一回合随机配对
        /// </summary>
        private Dictionary<long, long> GenerateRandomPairs(List<long> activePlayers)
        {
            var pairs = new Dictionary<long, long>();
            var shuffledPlayers = activePlayers.OrderBy(x => Random.Shared.Next()).ToList();

            for (int i = 0; i < shuffledPlayers.Count - 1; i += 2)
            {
                var player1 = shuffledPlayers[i];
                var player2 = shuffledPlayers[i + 1];
                
                pairs[player1] = player2;
                pairs[player2] = player1;
                
                Log.Info($"[{_logName}] Random pair: Player {player1} vs Player {player2}");
            }

            return pairs;
        }

        /// <summary>
        /// 按血量排序配对
        /// </summary>
        private Dictionary<long, long> GenerateRankedPairs(List<long> activePlayers, Dictionary<long, int> playerHealths)
        {
            var pairs = new Dictionary<long, long>();
            
            // 按血量排序（血量高的排在前面）
            var rankedPlayers = activePlayers
                .OrderByDescending(p => playerHealths[p])
                .ThenBy(p => p) // 血量相同时按ID排序保持稳定性
                .ToList();

            // 更新排名
            _playerRanking = rankedPlayers;

            // 配对逻辑：No.1 vs No.2, No.3 vs No.4
            for (int i = 0; i < rankedPlayers.Count - 1; i += 2)
            {
                var player1 = rankedPlayers[i];
                var player2 = rankedPlayers[i + 1];
                
                // 检查是否与上回合对手重复
                if (ShouldAvoidPairing(player1, player2))
                {
                    // 尝试调整配对（简化实现：如果有第3个玩家，与第3个配对）
                    if (i + 2 < rankedPlayers.Count)
                    {
                        var player3 = rankedPlayers[i + 2];
                        pairs[player1] = player3;
                        pairs[player3] = player1;
                        pairs[player2] = player2; // 暂时自己对自己，后续处理
                        
                        Log.Info($"[{_logName}] Adjusted pair to avoid repeat: Player {player1} vs Player {player3}");
                        i++; // 跳过player3
                        continue;
                    }
                }
                
                pairs[player1] = player2;
                pairs[player2] = player1;
                
                Log.Info($"[{_logName}] Ranked pair: Player {player1}(HP:{playerHealths[player1]}) vs Player {player2}(HP:{playerHealths[player2]})");
            }

            // 处理奇数个玩家的情况（3人时）
            if (rankedPlayers.Count == 3)
            {
                HandleThreePlayerPairing(pairs, rankedPlayers, playerHealths);
            }

            return pairs;
        }

        /// <summary>
        /// 生成2人对战配对（2人淘汰情况）
        /// </summary>
        private Dictionary<long, long> GenerateTwoPlayerPairs(List<long> activePlayers)
        {
            var pairs = new Dictionary<long, long>();

            if (activePlayers.Count == 2)
            {
                var player1 = activePlayers[0];
                var player2 = activePlayers[1];

                pairs[player1] = player2;
                pairs[player2] = player1;

                Log.Info($"[{_logName}] Two player final: Player {player1} vs Player {player2}");
            }
            else
            {
                Log.Warning($"[{_logName}] Expected 2 active players but found {activePlayers.Count}");
            }

            return pairs;
        }

        /// <summary>
        /// 生成3人配对（1人淘汰情况）- 保持4人队列，被淘汰者填充位置
        /// </summary>
        private Dictionary<long, long> GenerateThreePlayerPairs(List<long> activePlayers, List<long> eliminatedPlayers, Dictionary<long, int> playerHealths, int roundCount)
        {
            var pairs = new Dictionary<long, long>();

            if (activePlayers.Count != 3)
            {
                Log.Warning($"[{_logName}] Expected 3 active players but found {activePlayers.Count}");
                return pairs;
            }

            // 使用被淘汰玩家填充第4个位置，保持4人配对队列
            var eliminatedPlayer = eliminatedPlayers.FirstOrDefault();
            var allPlayersForPairing = new List<long>(activePlayers);
            if (eliminatedPlayer != 0)
            {
                allPlayersForPairing.Add(eliminatedPlayer);
            }

            // 按血量排序（被淘汰玩家血量为0，排在最后）
            var rankedPlayers = allPlayersForPairing
                .OrderByDescending(p => playerHealths.GetValueOrDefault(p, 0))
                .ThenBy(p => p)
                .ToList();

            Log.Info($"[{_logName}] Three player pairing with eliminated player {eliminatedPlayer} as filler");

            // 正常配对：No.1 vs No.2, No.3 vs 被淘汰玩家
            for (int i = 0; i < rankedPlayers.Count - 1; i += 2)
            {
                var player1 = rankedPlayers[i];
                var player2 = rankedPlayers[i + 1];

                pairs[player1] = player2;
                pairs[player2] = player1;

                if (eliminatedPlayers.Contains(player1) || eliminatedPlayers.Contains(player2))
                {
                    Log.Info($"[{_logName}] Pair with eliminated player: Player {player1} vs Player {player2} (one eliminated)");
                }
                else
                {
                    Log.Info($"[{_logName}] Active player pair: Player {player1} vs Player {player2}");
                }
            }

            return pairs;
        }

        /// <summary>
        /// 处理3人配对情况（旧方法，保留兼容性）
        /// </summary>
        private void HandleThreePlayerPairing(Dictionary<long, long> pairs, List<long> rankedPlayers, Dictionary<long, int> playerHealths)
        {
            // 找到没有配对的玩家
            var unpairedPlayer = rankedPlayers.FirstOrDefault(p => !pairs.ContainsKey(p) || pairs[p] == p);

            if (unpairedPlayer != 0)
            {
                // 与已淘汰玩家配对（简化实现：设置为0表示与AI或已淘汰玩家对战）
                pairs[unpairedPlayer] = 0;
                Log.Info($"[{_logName}] Player {unpairedPlayer} paired with eliminated player/AI");
            }
        }

        /// <summary>
        /// 检查是否应该避免配对（与上回合对手重复）
        /// </summary>
        private bool ShouldAvoidPairing(long player1, long player2)
        {
            return _lastOpponents.TryGetValue(player1, out var lastOpponent) && lastOpponent == player2;
        }

        /// <summary>
        /// 更新上回合对手记录
        /// </summary>
        private void UpdateLastOpponents(Dictionary<long, long> currentPairs)
        {
            _lastOpponents.Clear();
            foreach (var pair in currentPairs)
            {
                _lastOpponents[pair.Key] = pair.Value;
            }
        }

        /// <summary>
        /// 获取当前玩家排名
        /// </summary>
        public List<long> GetPlayerRanking()
        {
            return new List<long>(_playerRanking);
        }

        /// <summary>
        /// 获取玩家的上一回合对手
        /// </summary>
        public long GetLastOpponent(long playerId)
        {
            return _lastOpponents.GetValueOrDefault(playerId, 0);
        }
    }
}
