//*********************************************************
// Framework 
// Author:  zhouchen
// Desc  :  全局的enum
// Date  :  2022-1-12
//*********************************************************

namespace BattleServer.Game
{
    /// <summary>
    /// 品质类型
    /// </summary>
    public enum QualityEnum
    {
        None = -1,
        White = 0,          // 白
        Green = 1,          // 绿
        Blue = 2,           // 蓝
        Purple = 3,         // 紫
        Orange = 4,         // 橙
        Red = 5,            // 红
        Colourful = 6,      // 彩
        Max
    }
    public enum SHOW_ICON_TYPE
    {
        None = 0,
        ITEM = 1,   //道具
        CURRENCY,   //货币
        EXP,        //经验
        MilitaryRankExp, //军阶经验
        Max
    }

    /// <summary>
    /// 赛季类型 对应表Season.txt的中列Type
    /// >>>!!!客户端(注意脚本文件在PublicDefine.cs)也有同名的枚举 注意两边同步添加!!!<<<
    /// </summary>
    public enum ESeasonType
    {
        ST_SECRETTERRITORY = 1, // 大秘境

        ST_OTHER = 2,
    }

    /// <summary>
    /// 赛季挑战类型
    /// </summary>
    public enum ESeaonChallenge
    {
        SC_COPYSCENE_CHALLENGE = 0, // 副本挑战
    }

    /// <summary>
    /// 赛季主题类型
    /// </summary>
    public enum ESeasonTheme
    {
        ST_AFFIX_THEME = 0, // 词缀主题
    }

    /// <summary>
    /// 赛季词缀类型
    /// >>>!!!客户端(注意脚本文件在PublicDefine.cs)也有同名的枚举 注意两边同步添加!!!<<<
    /// </summary>
    public enum ESeasonAffixType
    {
        SAT_TO_ALL_MONSTER = 1,         // 应用所有怪物
        SAT_TO_ALL_ELITE_MONSTER = 2,   // 应用精英怪物
        SAT_TO_SCENE = 3,               // 应用场景
    }

    /// <summary>
    /// 赛季状态
    /// >>>!!!客户端(注意脚本文件在PublicDefine.cs)也有同名的枚举 注意两边同步添加!!!<<<
    /// </summary>
    public enum ESeasonStatus
    {
        SS_PREPARE = 0,  // 准备
        SS_RUNNING = 1,  // 进行时
        SS_OVERTIME = 2, // 过期
    }

    public enum EMonsterBloodType
    {
        MBT_NONE = 0,
        MBT_NORMAL = 1,             // 小怪
        MBT_ELITE = 2,              // 精英
        MBT_BOSS_ONLY_FIGHT = 3,    // BOSS且血条战斗时显示
        MBT_BOSS_ALWAYS = 4,        // BOSS且血条强制显示

        MBT_MAX = 5,
    }

    public enum MQServerState
    {
        Connecting,
        Connected,
        Busy,
        Disconnected,
    }

    public enum ChangeSceneResult
    {
        OK,
        CommonErr,
        CommonErr_ParamNull,
        CommonErr_SelfNull,
        CommonErr_UnitNull,
        CommonErr_UnitPlayerComNull,
        CommonErr_PlayerId_0,
        CommonErr_PvPComNull,
        CommonErr_PvPPunishment,
        CommonErr_DstSceneNull,
        CommonErr_BaseAttrComNull,
        CommonErr_InCombat,
        CommonErr_ToGuild_SelfNull,
        CommonErr_ToGuild_ParamNull,
        CommonErr_ToGuild_DstCfgId_0,
        CommonErr_ToGuild_UnitNull,
        CommonErr_ToGuild_GuildAttrComNull,
        CommonErr_ToGuild_Guild_0,
        CommonErr_ToGuild_CombatAttrComNull,
        CommonErr_ToGuild_AliveState_False,
        CommonErr_ToGuild_InCombat,
        SelfServer_UnitPlayerNull,
        SelfServer_SessionComNull,
        SelfServer_SessionNull,
        SelfServer_SceneDefineNull,
        SelfServer_CopySceneId_0,
        SelfServer_DstSceneNull,
        SelfServer_DstSceneIdErr,
        SelfServer_CopySceneCanEnterErr,
        SelfServer_CurSceneSameDstScene,
        SelfServer_TwinTree_SelfNull,
        SelfServer_TwinTree_ComNull,
        SelfServer_TwinTree_NotUnlock,
        DiffServer_SceneDefineNull,
        DiffServer_DstSceneIsCopyScene,
        DiffServer_RemoveCheck_SelfoParamNull,
        DiffServer_RemoveCheck_UnitNull,
        DiffServer_RemoveCheck_SessionComNull,
        DiffServer_RemoveCheck_CharBaseAttrComNull,
        DiffServer_RemoveCheck_SessionNull,
        DiffServer_RemoveCheck_WorldMgrNull,
        DiffServer_RemoveCheck_WorldDisConnectErr,
        DiffServer_RemoveCheck_DstParamNull,
        DiffServer_RemoveCheck_CheckMsgNull,
        DestSceneNullErr,
        LeaveServerErr,
        TServerDisConnectErr,
        DirectCopySceneErr,
        ChangeSceneTypeErr,
    }


    public enum ChangeSceneType
    {
        SameServer,
        DiffServer,
        Invalid
    }

    public enum KickPlayerReason
    {
        OtherServerOnline,
        GMTKick,
    }

    public enum KickPlayerResult
    {
        OK,
        CommonErr,
    }

    public enum VerifyTokenRetType
    {
        Common,
        AfterDiffServerKick,        //这种是不同服的顶号行为，比如你在T服，结果源服上来号要顶你
    }

    /// <summary>
    /// NPC交互枚举
    /// </summary>
    public enum InteractiveEnum
    {
        None = 0,
        Npc_Generate = 1,               // 通用(氛围)NPC
        Npc_Function = 2,               // 功能NPC
        Npc_Mission = 3,                // 任务NPC
        Collect_Mission = 4,            // 任务采集(进度条)
        Entity_Scene = 5,               // 场景交互物
        Area_Mission = 6,               // 任务交互区域
        Area_Scene = 7,                 // 场景交互区域
        CopyScene_Interactive = 8,      // 副本交互类型
        Capture = 9,                    // 捕捉
        Trial_Pet_Tame = 10,            // 爬塔-宠物驯服
        Cut_Down_Tree = 11,             // 砍树
        Cook = 12,                      // 烹饪
        PullOut_Umbrella = 13,          // 拔伞
        Rescue_Npc = 14,          // 解救
        Telescope = 15,                 // 望远镜
        Arena4v4GetPetEgg = 16,                 // 竞技场-幻灵争夺战获取幻灵蛋
        TreasureBox = 17,               // 宝箱
        NpcScriptLogic = 18,            // 就纯纯的发起交互请求
        NoviceBox = 19,                 // 新手宝箱
        Fishing = 20,                 // 捕捞
        SecretTerritoryRepeatBonusBox = 21,//大秘境复刷奖励关开宝箱
        SecretTerritoryAltar = 22,//大秘境祭坛
        TinyWorldClickBubble = 23, // 微缩世界点击泡泡
    }

    public enum CustomMessageType : ushort
    {
        None = 0,
        SpaceUpdateData,
        NearbyUpdateData,
        AssignCopySceneConstruction,
        DeliverCopySceneConstruction,
        AssignCopySceneDestruction,
        DeliverCopySceneDestruction,
        SceneHasShutDown,
        S2S_ReqWaitCopySceneInitFinished,
        S2S_ResWaitCopySceneInitFinished,
        S2S_BroadCastToServerAllPlayer,
        S2S_SceneManagerBroadCastToServerAllPlayer,
        S2S_TwinTreeWildernessLeaderDead,          //提交双生树荒野首领死亡
        S2S_TwinTreeDimensionalCrystalFinish,      //提交双生树次元水晶进度完成
        S2S_ReqLoadPlayerDB,                       //异步加载玩家数据
        S2S_RetLoadPlayerDB,                       //异步加载玩家数据返回
        S2S_SavePlayerDB,                          //异步存储玩家数据
        S2S_ReqSavePlayerDBForQuit,                //异步存储玩家数据并删除请求
        S2S_RetSavePlayerDBForQuit,                //异步存储玩家数据并删除返回
        S2S_ReqOtherTwinTreeLinePlayProcess,
        S2S_NotifyTwinTreeAllLineProcess,
        S2S_RetPlayProcessToGamePlayer,
        S2S_SyncServerOpenDayToTwinTreeSeasonPlay, //同步开服时间给双生树玩法管理器
        S2S_SyncIntegralToTwinTreeSeasonPlay,      //同步双生树积分
        S2S_ReqTwinTreeServerChasingScore,         //向场景管理器同步服务器追猎积分数据
        S2S_RetTwinTreeServerChasingScore,         //返回追猎积分数据
        S2S_ReqTwinTreeServerRankAdditionCoefficient, //请求双生树服务器积分排名经验加成
        S2S_RetTwinTreeServerRankAdditionCoefficient,
        S2S_SyncTwinTreeSeasonPlayClose,                //向双生树场景通知双生树赛季玩法关闭
        S2S_ReqCopySceneCanEnter,                  //进入副本时候判断副本是否还可以再进入新玩家
        S2S_RetCopySceneCanEnter,
        S2S_CloseAllThinClient,
        S2S_RetCloseAllThinClient,
        S2S_CloseServer,
        S2S_RetCloseServer,
        S2S_CloseServerDB,
        S2S_RetCloseServerDB,
        S2S_CloseServerKickPlayer,
        S2S_RetCloseServerKickPlayer,
        GrpcServiceRequest,
        GrpcServiceResponse,
        S2S_GMTReqLoadPlayerDB,
        S2S_GMTRetLoadPlayerDB,
        S2S_GMTSavePlayerDB,
        S2S_TwinTreeInitScene,
        S2S_TwinTreeRefreshAllPlayEffect,
        S2S_TwinTreeEventOpenStageChange,
        S2S_TwinTreeLetSceneCreatePlay,
        S2S_TeamCreateCopyScene,
        S2S_RetTeamCreateCopyScene,
        S2S_DestoryCopySceneForTeam,
        S2S_TimingSyncPlayProcessToSceneManager,
        S2S_ReqAllScenePlayProcess,
        S2S_RetAllScenePlayProcess,
        S2S_ReqAssignScenePlayProcess,
        S2S_RetAssignScenePlayProcess,
        S2S_ReqAssignSceneLinePlayProcess,
        S2S_RetAssignSceneLinePlayProcess,
        S2S_ReqAssignWildernessLeaderDamageInfo,
        S2S_RetAssignWildernessLeaderDamageInfo,
        S2S_RetCloseServer_KServer,
        S2S_CloseServer_KServer,
        S2S_ReqCopySceneAdmittanceCheck,
        S2S_RetCopySceneAdmittanceCheck,
        S2S_SyncDayChangeToSceneManager,    // 向场景管理器线程同步已经跨天了
        S2M_ReqLoadPlayerTradeItemDB,       //异步加载交易行Item数据
        S2M_RetLoadPlayerTradeItemDB,       //异步加载交易行Item数据返回
        S2M_UpdateLoadPlayerTradeItemDB,    //更新交易行Item数据，删除or更新
        S2S_ReqMonsterInvasionInfo,
        S2S_RetMonsterInvasionInfo,
    }

    public enum NearbyRangeLevel : byte
    {
        SightRange = 0,
        MediumRange,
        CloseRange,
        LevelCount
    }

    public enum CURRENCY_TYPE
    {
        CU_Crusade = -2, //讨伐分裂者活动参与次数(实际上不是货币 只是走货币的显示逻辑 仅客户端显示用)

        CU_FREE = -1, // 免费
        CU_MORE = 0,    //更多
        CU_GOLD = 1,  // 金币
        CU_SILVER = 2,  // 银币
        CU_DIAMOND = 3,  // 钻石
        CU_BIND_DIAMOND = 4,  // 绑钻
        CU_PetSkillBookLevelUp = 5,//幻灵技能书升级用的
        CU_GuildContribute = 6, //公会个人贡献度
        CU_WeaponSkillDebris = 7, //武器技能碎片
        CU_BloodVessel = 8,//血脉激活道具
        CU_TwinTreeEquipLuck = 9,//福印（双生树幸运）
        CU_SevenDayScore = 11,//满月铃（7日签到）
        CU_Arena4v4 = 12,//4v4竞技场
        CU_Rainbow_SPAR = 13,//虹晶(充值)(只有客户用，服务器找cgy)
        CU_Dreamland = 15,//游园币
        CU_WarOrderExp = 17,//战令经验
        CU_WeaponSlotUpgrade = 18,//武器格位升级材料
        CU_MAX,
    };

    // 神兽聚星任务类型
    public enum RouletteMissionType
    {
        Invalid = 0,
        DrawLottery = 1,    // 抽卡次数
        PetCodexLevel = 2,
        PetBagNum = 3,
        PetQualityStar = 4,
        Max
    }
    public enum RouletteMissionState
    {
        INVALID = 0,           // 无效的
        UNFINISH = 1,          // 未完成
        FINISH_CAN_AWARD = 2,   // 完成未领奖
        FINISH_AWARD_DONE = 3, // 完成已领奖
    }

    // 对应marqueeLottery表ID
    public enum ERouletteType
    {
        None = 0,
        MarqueeLottery, // 神兽聚星
        Activity2,
        Activity3,
    }

    public enum GMT_Opcode
    {
        Invalid = -1,
        QueryRoleInfo = 0,
        ForbiddenChat = 1,
        ForbiddenLogin = 2,
        SendRoleMail = 3,
        SendGlobalMail = 4,
        Item = 5,
        QueryPlayerData = 6,
        ModifyPlayerData = 7,
        ModifyPlayerAttrData = 8,
        DeletMail = 9,
        QueryRoleMail = 10,
        QueryOnlineNumber = 11,
        MarQuee = 12,
        SendGlobalMailCheck = 13,
        GlobalKickPlayer = 14,
        QueryGlobalMail = 15,
        MarQueeDelete = 16,
        QueryWorldData = 17,
        MAX,
    }

    //这个必须从1000开启，1000之前的操作是GMT操作
    public enum OffLinePlayerOperate
    {
        OffLinePlayerOperate_Min = 1000,
        OffLinePlayerOperate_DeleteMail = 1001,
        OffLinePlayerOperate_DailyActivityGiveLike = 1002,
        OffLinePlayerOperate_PlayerTradeRecord = 1003,    // 交易行交易记录
    }

    public enum eSendChatMessageToKratosResult
    {
        SendResult_Success = 0,
        SendResult_ParamErr = 1,
        SendResult_ResponseDataNull = 2,
        SendResult_ReplyNull = 3,
        SendResult_ResultError = 4,
        SendResult_GrpcMgrNull = 5,
        SendResult_NetServiceNull = 6,
        SendResult_KratosExcept = 7,
        SendResult_WebSocketProxyNull = 8,
        SendResult_WebSocketNull = 9,
        SendResult_WebSocketNotOpen = 10
    }

    public enum FittingRoomType
    {
        Model = 0,
        Player = 1,
        Mount = 2,
    }
    public enum FittingRoomBubbleModelType
    {
        None = 0,
        GetByModelID = 1,
        PlayerSelf = 2,
        GetByServerID = 3
    }
    public enum MaskSkillSlotType
    {
        MaskUI = 1,//设置图片透明度
        HideUI_MoXiang = 2,//魔像隐藏UI
    }

    public enum EChangeShapeType
    {
        Revert = -1,             // 变回去
        Invalid = 0,
        ActiveChange = 1,       // 主动变身
        PassiveChange = 2,      // 被传染的变身
    }
}
