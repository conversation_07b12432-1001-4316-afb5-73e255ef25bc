// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.28.1
// source: general-services/serverlist/v1/serverlist.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationServerlistGetIsWhitelist = "/serverlist.v1.Serverlist/GetIsWhitelist"
const OperationServerlistGetServerListData = "/serverlist.v1.Serverlist/GetServerListData"
const OperationServerlistSetServerInfo = "/serverlist.v1.Serverlist/SetServerInfo"

type ServerlistHTTPServer interface {
	GetIsWhitelist(context.Context, *GetIsWhitelistReq) (*GetIsWhitelistReply, error)
	GetServerListData(context.Context, *GetServerListReq) (*GetServerListReply, error)
	SetServerInfo(context.Context, *KratosServerInfo) (*KratosServerInfo, error)
}

func RegisterServerlistHTTPServer(s *http.Server, srv ServerlistHTTPServer) {
	r := s.Route("/")
	r.GET("/serverlist/api/serverlist/getserverlist/{targetPlatformId}", _Serverlist_GetServerListData0_HTTP_Handler(srv))
	r.GET("/serverlist/api/serverlist/getiswhitelist/{addrip}/{serverid}", _Serverlist_GetIsWhitelist0_HTTP_Handler(srv))
	r.POST("/serverlist/api/serverlist/setserverinfo", _Serverlist_SetServerInfo0_HTTP_Handler(srv))
}

func _Serverlist_GetServerListData0_HTTP_Handler(srv ServerlistHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetServerListReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServerlistGetServerListData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetServerListData(ctx, req.(*GetServerListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetServerListReply)
		return ctx.Result(200, reply)
	}
}

func _Serverlist_GetIsWhitelist0_HTTP_Handler(srv ServerlistHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetIsWhitelistReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServerlistGetIsWhitelist)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetIsWhitelist(ctx, req.(*GetIsWhitelistReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetIsWhitelistReply)
		return ctx.Result(200, reply)
	}
}

func _Serverlist_SetServerInfo0_HTTP_Handler(srv ServerlistHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in KratosServerInfo
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationServerlistSetServerInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.SetServerInfo(ctx, req.(*KratosServerInfo))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*KratosServerInfo)
		return ctx.Result(200, reply)
	}
}

type ServerlistHTTPClient interface {
	GetIsWhitelist(ctx context.Context, req *GetIsWhitelistReq, opts ...http.CallOption) (rsp *GetIsWhitelistReply, err error)
	GetServerListData(ctx context.Context, req *GetServerListReq, opts ...http.CallOption) (rsp *GetServerListReply, err error)
	SetServerInfo(ctx context.Context, req *KratosServerInfo, opts ...http.CallOption) (rsp *KratosServerInfo, err error)
}

type ServerlistHTTPClientImpl struct {
	cc *http.Client
}

func NewServerlistHTTPClient(client *http.Client) ServerlistHTTPClient {
	return &ServerlistHTTPClientImpl{client}
}

func (c *ServerlistHTTPClientImpl) GetIsWhitelist(ctx context.Context, in *GetIsWhitelistReq, opts ...http.CallOption) (*GetIsWhitelistReply, error) {
	var out GetIsWhitelistReply
	pattern := "/serverlist/api/serverlist/getiswhitelist/{addrip}/{serverid}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServerlistGetIsWhitelist))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServerlistHTTPClientImpl) GetServerListData(ctx context.Context, in *GetServerListReq, opts ...http.CallOption) (*GetServerListReply, error) {
	var out GetServerListReply
	pattern := "/serverlist/api/serverlist/getserverlist/{targetPlatformId}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationServerlistGetServerListData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *ServerlistHTTPClientImpl) SetServerInfo(ctx context.Context, in *KratosServerInfo, opts ...http.CallOption) (*KratosServerInfo, error) {
	var out KratosServerInfo
	pattern := "/serverlist/api/serverlist/setserverinfo"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationServerlistSetServerInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
