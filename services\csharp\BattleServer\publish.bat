@echo off
echo ========================================
echo BattleServer 发布脚本
echo ========================================

set PROJECT_PATH=BattleServer\BattleServer.csproj
set OUTPUT_DIR=.\publish

echo 清理旧的发布文件...
if exist "%OUTPUT_DIR%" rmdir /s /q "%OUTPUT_DIR%"

echo.
echo 开始发布 BattleServer...
echo.

echo [1/3] 发布 Windows x64 自包含版本...
dotnet publish "%PROJECT_PATH%" -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o "%OUTPUT_DIR%\win-x64"
if %errorlevel% neq 0 (
    echo 发布失败！
    pause
    exit /b 1
)

echo [2/3] 发布 Windows x64 框架依赖版本...
dotnet publish "%PROJECT_PATH%" -c Release -r win-x64 --self-contained false -o "%OUTPUT_DIR%\win-x64-framework"
if %errorlevel% neq 0 (
    echo 发布失败！
    pause
    exit /b 1
)

echo [3/3] 发布 Linux x64 版本...
dotnet publish "%PROJECT_PATH%" -c Release -r linux-x64 --self-contained true -p:PublishSingleFile=true -o "%OUTPUT_DIR%\linux-x64"
if %errorlevel% neq 0 (
    echo 发布失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 发布完成！
echo ========================================
echo.
echo 输出目录：
echo - Windows x64 (自包含): %OUTPUT_DIR%\win-x64\BattleServer.exe
echo - Windows x64 (框架依赖): %OUTPUT_DIR%\win-x64-framework\BattleServer.exe
echo - Linux x64: %OUTPUT_DIR%\linux-x64\BattleServer
echo.
echo 自包含版本可以直接运行，无需安装.NET运行时
echo 框架依赖版本需要目标机器安装.NET 9.0运行时
echo.

pause
