// 协议ID类型为short，-32767 到 32767
//StartMessageID = 0; // 必须以;分号结束
//MaxMessageID = 199; // 必须以;分号结束
syntax = "proto3";
option go_package = "liteframe/internal/common/protos/cs";

package GamePackage;

import "PublicMessage.proto";
import "PublicEnum.proto";

//Switch与其他服务的心跳检测
message SS_HEARTBEAT_REQUEST
{
	int64 timestamp = 1;
}

//其他服务返回给Switch的心跳检测
message SS_HEARTBEAT_RESPONSE
{
	int64 timestamp = 1;
}

//======================公会开始===============================
//-------------------- Player -> GuildSystem (请求) -------------------------
// 玩家请求创建公会
// 对应 CL: CLGuildCreate
message P2GCreateGuild {
    string name = 1;        // 公会名称
    string notice = 2;      // 公会宣言
    int32 iconId = 3;       // 徽章ID
    bool freeJoin = 4;      // 是否允许自由加入
    int32 reqStage = 5;     // 加入申请的关卡条件
    string announcement = 6;// 公会公告
    uint64 uid = 7;         // 创建者玩家的UID
}

// 玩家请求申请加入公会
// 对应 CL: CLGuildApply
message P2GApplyJoinGuild {
    int64 guildId = 1;      // 目标公会ID
    uint64 uid = 2;         // 申请者玩家的UID GuildSystem将通过此UID从UserSnapSystem获取详情
}

message P2GJoinGuild {
    int64 guild_id = 1;     // 目标公会ID
}

// 玩家请求快速加入公会
// 对应 CL: CLGuildFastJoin
message P2GFastJoinGuild {
    uint64 uid = 1;         // 请求快速加入的玩家UID GuildSystem将通过此UID从UserSnapSystem获取信息以匹配联盟
	uint32 level = 2;		// 玩家等级
}

// 玩家请求离开公会
// 对应 CL: CLGuildQuit
message P2GLeaveGuild {
    int64 guildId = 1;      // 当前公会ID
    uint64 uid = 2;         // 执行离开操作的玩家UID
}

message P2GDismissGuildReq {
    int64 guild_id = 1;
}

// 玩家请求解散公会 (会长操作)
// 对应 CL: CLGuildDismiss
message P2GDismissGuild {
    int64 guildId = 1;      // 要解散的公会ID
    uint64 uid = 2;         // 执行解散操作的会长玩家UID
}


message P2GUpdatePosition {
    int64 guild_id = 1;     // 公会ID
    uint64 target_id = 2;   // 目标玩家ID
    int32 position = 3;     // 新职位
}

message P2GUpdateContribution {
    int64 guild_id = 1;     // 公会ID
    int32 value = 2;        // 贡献值变化量
}

// 玩家请求获取公会推荐列表 (当玩家未加入公会时)
// 对应 CL: CLGuildHall (当传入 guildId 为 0 时，Player Actor 内部转换为此请求)
message P2GGetRecommendList {
    uint64 uid = 1;         // 请求推荐列表的玩家UID
}

// 玩家请求获取其已加入公会的详细信息
// 对应 CL: CLGuildHall (当传入 guildId 不为 0 时，Player Actor 内部转换为此请求)
message P2GGetGuildDetail {
    int64 guildId = 1;      // 玩家当前所在公会ID
    uint64 uid = 2;         // 请求公会详情的玩家UID (用于GuildSystem确定该玩家在公会中的职位、贡献等)
}

// 官员请求获取公会申请列表
// 对应 CL: CLGuildApplyMgrList
message P2GGetApplyList {
    int64 guildId = 1;      // 官员所在公会ID
    uint64 uid = 2;         // 执行操作的官员玩家UID (用于权限校验，虽然权限主要在Player Actor做，但GuildSystem可做二次校验)
}

// 官员处理公会申请 (同意/拒绝)
// 对应 CL: CLGuildApplyMgr
message P2GProcessApplication {
    int64 guildId = 1;         // 官员所在公会ID
    uint64 operatorUid = 2;    // 执行操作的官员玩家UID
    uint64 targetUid = 3;      // 被处理申请的玩家UID
    GuildOpt action = 4;       // 操作类型: GuildOpt_ApplyMgrAgree 或 GuildOpt_ApplyMgrRefuse
}

// 官员执行成员管理操作 (任命职位/踢人)
// 对应 CL: CLGuildMemberMgr
message P2GMemberAction {
    int64 guildId = 1;         // 官员所在公会ID
    uint64 operatorUid = 2;    // [注释: 执行操作的官员玩家UID]
    uint64 targetUid = 3;      // [注释: 被操作的目标成员玩家UID]
    GuildOpt action = 4;       // 操作类型: 任命职位 (GuildOpt_MemberMgrXXX等) 或踢人 (GuildOpt_MemberMgrKick)
    GuildPosition targetPosition = 5; // 如果是任命操作，这里是目标职位
}

// 官员修改公会信息
// 对应 CL: CLGuildEdit
message P2GEditGuildInfo {
    int64 guildId = 1;         // 官员所在公会ID
    uint64 operatorUid = 2;    // 执行操作的官员玩家UID
    GuildOpt opt = 3;          // 具体修改哪个信息 (GuildOpt_EditIcon, GuildOpt_EditName 等)
    string name = 4;           // 新名称 (如果 opt 是修改名称)
    string notice = 5;         // 新宣言
    int32 iconId = 6;          // 新徽章ID
    bool freeJoin = 7;         // 新的自由加入设置
    int32 reqStage = 8;        // 新的申请关卡条件
    string announcement = 9;   // 新的公告
}

// Player Actor (GM) 请求 GuildSystem 修改公会属性 (等级/经验)
message P2GGMUpdateGuildAttrs {
    uint64 playerId = 1;       // 执行GM操作的玩家UID
    int64 guildId = 2;  	   // 目标公会的数据库ID
    int32 target_level = 3;    // 如果要修改等级，则设置 target_level；否则客户端可以不设置或发送一个特殊值 (如 -1)
    int64 exp_to_add = 4;      // 如果要增加经验，则设置 exp_to_add；否则客户端可以发送一个特殊值 (如 -1)
}

// 玩家请求执行联盟捐献
message P2GGuildDonate {
    uint64 uid = 1;         // [注释: 执行捐献的玩家UID]
    int64 guildId = 2;      // 玩家所在公会ID
    int32 donateOpt = 3;    // 捐献操作选项 (0:免费, 1:金币, 2:钻石)
    int64 guildExpGained = 4;         // [注释: 本次捐献为公会增加的经验值]
    int64 guildContributionGained = 5; // [注释: 本次捐献为公会增加的总贡献值/资金]
}
//-------------------- GuildSystem -> Player (响应/通知) --------------------
message G2PGuildCreated {
    bool success = 1;       // 创建结果
    int64 guild_id = 2;     // 创建的公会ID
    string name = 3;        // 公会名称
    int32 position = 4;     // 玩家职位(会长)
}

// 创建公会响应
// 对应 P2GCreateGuild 的响应
message G2PCreateGuildRsp {
    int32 errorcode = 1;
    int64 guildId = 3;
    string name = 4;
    GuildPosition position = 5; // 玩家职位(会长)
}

message G2PJoinGuild {
    bool success = 1;       // 加入结果
    int64 guild_id = 2;     // 公会ID
    string name = 3;        // 公会名称
    int32 position = 4;     // 玩家职位
}

// 申请/快速加入公会响应
// 对应 P2GApplyJoinGuild / P2GFastJoinGuild 的响应
message G2PJoinGuildRsp {
    int32 errorcode = 1;
    int64 guildId = 3;         // 目标公会ID
    string name = 4;           // 公会名称 (仅快速加入成功时有意义)
    GuildPosition position = 5; // 玩家职位 (仅快速加入成功时有意义)
}

message G2PLeaveGuild {
    bool success = 1;       // 退出结果
}

// 离开公会响应
// 对应 P2GLeaveGuild 的响应
message G2PLeaveGuildRsp {
    int32 errorcode = 1;
}

message G2PDismissGuildRsp {
    int32 errorcode = 1;
    int64 guildId = 2; // 被解散的公会ID
}

message G2PPositionChanged {
    bool success = 1;       // 更新结果
    int32 position = 2;     // 新职位
}

message G2PContributionChanged {
    bool success = 1;       // 更新结果
    int32 new_value = 2;    // 新的贡献值
}

// 获取公会推荐列表响应
// 对应 P2GGetRecommendList 的响应
message G2PGetRecommendListRsp {
    int32 errorcode = 1;
    repeated PBGuildRecommend guildList = 2;
}

// 获取公会详细信息响应
// 对应 P2GGetGuildDetail 的响应
message G2PGetGuildDetailRsp {
    int32 errorcode = 1;
    PBGuildDetailInfo guild_info = 2;                  // 联盟的核心详细信息
    repeated PBGuildMember member_list = 3;            // 成员列表
    GuildPosition player_guild_position = 4;           // 请求者在该公会的职位
    int64 player_contribution = 5;                     // 请求者在该公会的贡献度
}

// 获取公会申请列表响应
// 对应 P2GGetApplyList 的响应
message G2PGetApplyListRsp {
    int32 errorcode = 1;
    repeated PBGuildApply applyList = 2;
	int32 today_joined_count = 3;     		  // 今日已通过申请/快速加入的人数
    int32 daily_max_join_limit = 4;   		  // 服务器写死的每日最大入盟人数上限 (方便客户端显示 X/Y)
}

// 处理公会申请响应
// 对应 P2GProcessApplication 的响应
message G2PProcessApplicationRsp {
    int32 errorcode = 1;
    GuildOpt opt = 2;           // 实际执行的操作 (同意/拒绝)
    uint64 targetUid = 3;       // 被处理申请的玩家UID
	PBGuildMember member = 4;	// 被处理申请的玩家的公会信息
}

// 成员管理操作响应 (任命/踢人)
// 对应 P2GMemberAction 的响应
message G2PMemberActionRsp {
    int32 errorcode = 1;
    GuildOpt action = 2;        // 实际执行的操作
    uint64 targetUid = 3;       // 被操作的目标成员玩家UID
	PBGuildMember member = 4;	// 被操作的目标成员玩家的公会信息
}

// 修改公会信息响应
// 对应 P2GEditGuildInfo 的响应
message G2PEditGuildInfoRsp {
    int32 errorcode = 1;
    GuildOpt opt = 2; 			// 实际执行的修改操作
	string name 	= 3;		//名称
	string notice 	= 4;		//宣言
	int32 iconId 	= 5;		//图标
	bool freeJoin 	= 6;		//true 自由加入 false 需要审批，
	int32 reqStage	= 7;		//审批时需具备的关卡条件：0.无限制 1.困难 2.疯狂 3.地狱
	string announcement = 8; 	// 修改后的公告
}

// 公会状态通用更新通知 (GuildSystem 主动推送给 Player)
// 用于处理: 玩家成功加入、被踢、职位变更、联盟升级、申请被处理或过期等需要更新 Player 内部状态的场景
// LC 对应: 多种LC场景都可能由这个SS消息触发 Player 状态更新，进而影响后续LC消息的准确性 (如LC_Guild_Sync, LCGuildHall)
message G2PGuildGeneralUpdateNtf {
    GuildUpdateType updateType = 1;   // 更新类型枚举
    int64 guildId = 2;                // 相关公会ID
    string guildName = 3;             // 相关公会名称
    GuildPosition newPosition = 4;    // 新的职位
    int32 newGuildLevel = 5;          // 新的联盟等级
    bool pendingApplyRemoved = 6;     // 如果是申请过期或被处理，标记此申请是否从玩家待处理列表移除
    int64 relatedGuildIdForApply = 7; // 如果是申请相关的通知，这里是被申请的公会ID
    uint64 targetUid = 8;             // 玩家uid
}

// GuildSystem 对 Player Actor (GM) 发起的GM操作的响应
message G2PCommonGMRsp {
    int32 errorcode = 1;
}

// 联盟捐献响应
message G2PGuildDonateRsp {
    int32 errorcode = 1;
    int32 newGuildLevel = 2;				// 捐献后的公会等级
    int64 newGuildExp = 3;					// 捐献后的公会经验
    int64 newGuildTotalContribution = 4;	// 捐献后的公会总贡献
	int32 opt = 5;							//捐献操作：0.免费 1.金币 2.钻石
}
//-------------------- GuildSystem <-> PlayerSystem -------------------------
message G2PSUpdateGuildMembersReq {
    int64 guild_id = 1;        // 解散的公会id
    repeated uint64 uids = 2;   // 需要更新的玩家uid列表
	GuildOpt opt = 3;
}

message PS2GUpdateGuildMembersRsp {
    uint32 code = 1;       
    int64 guild_id = 2;
    repeated uint64 failed_uids = 3;  // 更新失败的uid列表
}

// GuildSystem -> PlayerSystem: 请求批量更新玩家的公会相关状态
// 用于: 公会解散时通知所有成员，联盟升级时通知所有成员
message G2PSBatchUpdatePlayerGuildStatusReq {
    int64 guildId = 1;
    repeated uint64 uids = 2;   // 需要更新状态的玩家UID列表
    GuildSystemInternalActionType actionType = 3;
    int32 newGuildLevel = 4;    // 如果 actionType 是 LEVEL_UP
    int32 newMaxMembers = 5;    // 如果 actionType 是 LEVEL_UP
}

// PlayerSystem -> GuildSystem: 批量更新玩家公会状态的响应
// message PS2GBatchUpdatePlayerGuildStatusRsp {
//     int32 errorcode = 1;
//     int64 guildId = 2;
//     repeated uint64 failedUids = 3; // 更新失败的玩家UID列表
// }

// GuildSystem -> PlayerSystem: 通知单个玩家成功加入公会 (申请被同意或快速加入成功)
message G2PSNotifyPlayerJoinedGuild {
    uint64 uid = 1;           	// 加入公会的玩家UID
    int64 guildId = 2;
    string guildName = 3;
    GuildPosition position = 4;
    int32 guildLevel = 5;
}

// GuildSystem -> PlayerSystem: 通知单个玩家离开或被踢出公会
message G2PSNotifyPlayerLeftGuild {
    uint64 uid = 1;           	// 离开/被踢的玩家UID
    int64 guildId = 2;        	// 涉及的公会ID
}

// GuildSystem -> PlayerSystem: 通知单个玩家职位发生变更
message G2PSNotifyPlayerPositionChanged {
    uint64 uid = 1;           	// 职位变更的玩家UID
    int64 guildId = 2;
    GuildPosition newPosition = 3;
	GuildOpt opt = 4;
}

// GuildSystem -> PlayerSystem: 通知单个玩家其对某个公会的待处理申请应被移除 (过期/已被处理(批准/拒绝))
message G2PSNotifyRemovePendingApply {
    uint64 uid = 1;           	// 申请被移除的玩家UID
    int64 guildId = 2;       	// 对应的申请公会ID
	GuildOpt opt = 3;
}

//-------------------- PlayerSystem -> Player -------------------------------
message PS2PUpdateGuildStatusReq {
    int64 guild_id = 1;    // 被解散的公会id
	GuildOpt opt = 2;
}

message P2PSUpdateGuildStatusRsp {
    uint32 code = 1;       
    int64 guild_id = 2;    
    uint64 uid = 3;        // 当前player actor的uid
}

// PlayerSystem -> Player: 要求玩家 Actor 更新其内部的公会相关数据
// 触发场景: 收到 G2PSxxx 通知后，PlayerSystem 转发给对应的 Player Actor
message PS2PUpdateGuildAffiliationReq {
    int64 guildId = 1;        	// 新的 guild_id (0 表示离开/解散)
    string guildName = 2;     	// 新的公会名称
    GuildPosition position = 3; // 新的职位
    int32 guildLevel = 4;     	// 新的公会等级
	GuildOpt opt = 5;
}

// PlayerSystem -> Player: 要求玩家 Actor 移除其本地维护的某个待处理申请ID
message PS2PRemovePendingApplyReq {
    int64 guildId = 1;       	// 需要从玩家的待处理申请列表中移除的公会ID
	GuildOpt opt = 2;
}

// Player -> PlayerSystem: 玩家 Actor 已更新公会状态后的响应
message P2PSUpdateGuildAffiliationRsp {
    int32 errorcode = 1;
    int64 guildId = 2;
    uint64 uid = 3;         	// 当前玩家的UID
}
//======================公会结束===============================


// 付款预请求创建订单 玩家->支付系统
message P2PMCreateOrderReq {
    string orderId = 1; 			// 订单 ID，主键
    string goodsRegisterId = 2; 	// 商品注册ID
	int32 moduleType = 3;	   		// 支付对应模块
	int32 goodsPrice = 4;			// 产品实际支付价格（RMB级别为元，此参数不带引号，请用数字类型处理）
	string channelId = 5;			// 渠道标识
	string gameGoodsId = 6;			// 游戏商品ID
	uint64 playerId = 7;      		// 玩家 ID
}

message PM2PCreateOrderRsp {
    uint32 code = 1;       			// 错误码
    string pushInfo = 2;			// 透传字段
}

// 发货	支付系统->玩家
message PM2PSDeliverReq {
	PBPayGoodsInfo goods = 1;
	PBQuestDataInfo quest = 2;
}

message PS2PMDeliverRsp {
	uint32 code = 1;       			// 错误码
	PBPayGoodsInfo goods = 2;
	PBQuestDataInfo quest = 3;
}

message PS2PDeliverReq {
	PBPayGoodsInfo goods = 1;
}

message PS2PQuestReq {
	PBQuestDataInfo quest = 1;
}

message P2PMDeliverRsp {
	uint32 code = 1;       			// 错误码
	PBPayGoodsInfo goods = 2;
}

// 玩家->监控系统
message P2USUserUpdate {
  UserSnapUserInfo user_data = 1;
}

// 所需服务器->监控系统
message S2USGetUserData {
  uint64 uid = 1;
}

message US2SGetUserDataRsp {
  uint32 code = 1;      			// 0成功，1失败
  UserSnapUserInfo user_data = 2;
}

message S2USSetNameIfAbsent{
  uint64 uid = 1;
  string name = 2;                                                          //新名字
}

message US2SSetNameIfAbsentRsp {
  uint32 code = 1;      			// 0成功，1失败
}

//====================竞技场开始=======================
//-------------------- ArenaSystem <-> Player -------------------------------
//竞技场获取数据
message P2ASArenaGetData
{
	uint64 uid = 1;		// ID
	string name = 2;	// 名字
	uint32 level = 3; 	// 等级
	int32 headIcon = 4; 	// 头像
	int32 headFrame = 5; 	// 头像框
}
message AS2PArenaGetData
{
	uint32 score = 1; // 竞技场积分
	repeated PBArenaRivalInfo rivalList = 2; // 对手列表
}

//竞技场请求挑战对手
message P2ASArenaReqChallenge
{
	int32 rivalIdx = 1; // 对手索引 1~3
}
message AS2PArenaChallengeResult
{
	int32 rivalIdx = 1; // 对手索引 1~3
	int32 result = 2; // 挑战结果 0 成功   1 失败
}

//===================竞技场结束========================

//=================== 举报开始 ========================
// 举报信息
message P2TSTipOff
{
  uint64 playerId = 1;  // 被举报者的玩家ID
  int32 TipOffType = 2;  // 举报类型
  string TipOffContent = 3;  // 举报说明
  uint64 senderPlayerId = 4;  // 举报者的玩家ID
}
// 举报信息返回
message TS2PTipOff
{
	int32 result = 2; // 0 成功   1 失败
}
//=================== 举报结束 ========================

//====================赛季BUFF开始=======================
//-------------------- SeasonBuffSystem <-> Player -------------------------------
//请求赛季BUFF信息
message P2SBSeasonBuffReq
{
}
//赛季BUFF信息返回
message SB2PSeasonBuffRsp
{
    int32 errorCode = 1;
    PBSeasonBuff info = 2;
}
//-------------------- SeasonBuffSystem <-> PlayerSystem -------------------------------
//赛季buff同步玩家系统
message SB2PSSeasonBuffSync
{
	PBSeasonBuff info = 1;
}
//-------------------- PlayerSystem <-> Player -------------------------------
message PS2PSeasonBuffSync
{
	PBSeasonBuff info = 1;
}
//===================赛季BUFF结束========================

//====================赛季开始=======================
message SeasonResetReq {
    int32 oldId = 1;
	int32 newId = 2;
}
//====================赛季结束=======================