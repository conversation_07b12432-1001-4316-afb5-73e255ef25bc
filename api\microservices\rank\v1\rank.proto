//GrpcAddressType:Rank
//GrpcServerType:server,world

syntax = "proto3";

package Aurora.PlayerInfoServer;

import "google/api/annotations.proto";
import "playerinfo/v1/playerinfostruct.proto";

option go_package = "gameserver/api/rankservice/v1;v1";

//ServiceStart
service RankService {
  rpc SyncRoleInfo(SyncRoleInfoRequest) returns (SyncRoleInfoReply) {
  }

  rpc UpdateRoleRankInfo(UpdateRoleRankRequest) returns (UpdateRoleRankReply) {
  }

  rpc RemoveRole(RemoveRoleRequest) returns (RemoveRoleReply) {
  }
}
//ServiceEnd

service RankListHttp {
  rpc GetRankListData (GetRankListReq) returns (GetRankListReply) {
    option (google.api.http) = {
      post : "/api/rankservice/ranklist",
      body : "*",
    };
  }
}

enum RankGroupType
{
  RoleRank = 0;
}

//Type:Http
enum RankType
{
  RankTypeNone = 0;
  //总评分
  FightPoint = 101;
}

message RankRoleInfo
{
  uint64  uid = 1;
  int32   kserver = 3;
  string  name = 4;
  int32   level = 6;
  int64   fightPoint = 7;
}

message SyncRoleInfoRequest
{
  RankRoleInfo info = 1;
}

// The response message containing the message
message SyncRoleInfoReply
{
  int32 result = 1;
}

message RankValueInfo
{
  int32  rankType = 1;
  int64  rankValue = 2;
}

message UpdateRoleRankRequest
{
  uint64  uid = 1;
  int32   kserver = 3;
  repeated RankValueInfo info = 4;
}

message UpdateRoleRankReply
{
  int32 result = 1;
}

//Type:Inner
message RemoveInfo
{
  uint64    uid = 1;
  int32     kserver = 3;
}

message RemoveRoleRequest
{
  RemoveInfo role = 1;
}

message  RemoveRoleReply
{
  int32 result = 1;
}

//Type:Http
message RoleRankInfo
{
  uint64  uid = 1;
  string  name = 3;
  int32   level = 5;
  int64   fightPoint = 6;
  int32   rank = 7;
  int32   tilte = 8;
}


//Type:Http
message RankDataInfo
{
  RoleRankInfo  role = 1;
  int64  rankValue = 10;
  int64  rankValue2 = 11;
}

//Type:Http
message GetRankListReq
{
  uint64 uid = 1;
  //排行榜大类 0：角色 1：公会 2：队伍 3:单个幻灵
  int32 rankGroup = 3;
  //排行榜类型
  int32 rankType = 4;
  //三级页签 0：总榜 1：好友
  int32 rankFlag = 5;
  //分页 0：全部  （总共100行&每页50行）
  int32 rankPage = 6;
}

//Type:Http
message GetRankListReply
{
  uint64 uid = 1;
  int32 rankGroup = 2;
  int32 rankType = 3;
  int32 rankFlag = 4;
  int32 rankPage = 5;
  repeated RankDataInfo data = 10;
  RankDataInfo  selfInfo = 20;
}




