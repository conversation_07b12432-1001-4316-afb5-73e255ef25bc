[{"ID": 1, "NameID": 2000001, "Desc": 2000002, "Icon": "UD_Icon_<PERSON>_jinbi", "Type": -1, "SubType": -1, "Qulity": 0, "SortOrder": 0, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": -1, "Source": 0}, {"ID": 2, "NameID": 2000003, "Desc": 2000004, "Icon": "UD_Icon_<PERSON>_zu<PERSON>hi", "Type": -1, "SubType": 10, "Qulity": 0, "SortOrder": 1, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [0], "StrParams": ["0"], "IsUse": 0, "Stacking": -1, "Source": 0}, {"ID": 3, "NameID": 1000004, "Desc": 1000028, "Icon": "UD_Icon_item_dianchi", "Type": -1, "SubType": 11, "Qulity": 3, "SortOrder": 2, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [0], "StrParams": ["0"], "IsUse": 0, "Stacking": -1, "Source": 1}, {"ID": 4, "NameID": 2000005, "Desc": 2000006, "Icon": "UD_Icon_F_jingyan", "Type": -1, "SubType": 11, "Qulity": 3, "SortOrder": 2, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [0], "StrParams": ["0"], "IsUse": 0, "Stacking": -1, "Source": 1}, {"ID": 100, "NameID": 4000003, "Desc": 4010003, "Icon": "UD_Icon_item_gold", "Type": -1, "SubType": -1, "Qulity": -1, "SortOrder": 4, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [400], "StrParams": ["10"], "IsUse": 0, "Stacking": -1, "Source": 2}, {"ID": 101, "NameID": 4000003, "Desc": 4010003, "Icon": "UD_Icon_item_gold", "Type": -1, "SubType": -1, "Qulity": -1, "SortOrder": 5, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [401], "StrParams": ["10"], "IsUse": 0, "Stacking": -1, "Source": 1}, {"ID": 102, "NameID": 4000003, "Desc": 4010003, "Icon": "UD_Icon_item_gold", "Type": -1, "SubType": -1, "Qulity": -1, "SortOrder": 6, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [402], "StrParams": ["20"], "IsUse": 0, "Stacking": -1, "Source": 0}, {"ID": 100001, "NameID": 4000010, "Desc": 4010010, "Icon": "UD_Icon_item_egg01", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 11, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 0, "Stacking": 9999, "Source": 1}, {"ID": 100002, "NameID": 1001935, "Desc": 1001940, "Icon": "UD_Icon_item_longjing1", "Type": 0, "SubType": 1, "Qulity": 4, "SortOrder": 12, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 3}, {"ID": 100003, "NameID": 1001936, "Desc": 1001941, "Icon": "UD_Icon_item_longjing2", "Type": 0, "SubType": 1, "Qulity": 4, "SortOrder": 13, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 2}, {"ID": 100004, "NameID": 1001937, "Desc": 1001942, "Icon": "UD_Icon_item_longjing3", "Type": 0, "SubType": 1, "Qulity": 4, "SortOrder": 14, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 3}, {"ID": 100005, "NameID": 2000013, "Desc": 2000014, "Icon": "UD_Icon_F_xingxing", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 15, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 2}, {"ID": 100006, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_longjing5", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 1}, {"ID": 100007, "NameID": 1000045, "Desc": 1000046, "Icon": "UD_Icon_item_longjing6", "Type": 0, "SubType": 1, "Qulity": 2, "SortOrder": 3, "Price": [], "ShowPacket": 1, "LevelLimit": 1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [], "StrParams": ["1"], "IsUse": 0, "Stacking": -1, "Source": 0}, {"ID": 200001, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": -1, "Source": 0}, {"ID": 200002, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": -1, "Source": 3}, {"ID": 200003, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": -1, "Source": 2}, {"ID": 200004, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": -1, "Source": 1}, {"ID": 200005, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": -1, "Source": 0}, {"ID": 200006, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": -1, "Source": 1}, {"ID": 200101, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [2000], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 2}, {"ID": 200102, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [3000], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 3}, {"ID": 200103, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [4000], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 3}, {"ID": 200104, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [5000], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 2}, {"ID": 200105, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [6000], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 1}, {"ID": 200106, "NameID": 1001939, "Desc": 1001944, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": 1, "Qulity": -1, "SortOrder": 16, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [100001, 100002, 100003], "NumParams": [7000], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 0}, {"ID": 200201, "NameID": 1003507, "Desc": 1003514, "Icon": "UD_Icon_item_gold", "Type": 2, "SubType": -1, "Qulity": 3, "SortOrder": 20, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [5], "StrParams": [], "IsUse": 1, "Stacking": -1, "Source": 3}, {"ID": 200301, "NameID": 1003521, "Desc": 1003528, "Icon": "UD_Icon_genderfamale", "Type": 4, "SubType": -1, "Qulity": 3, "SortOrder": 21, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [1], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [5], "StrParams": [], "IsUse": 1, "Stacking": -1, "Source": 2}, {"ID": 200401, "NameID": 2000011, "Desc": 2000012, "Icon": "UD_Icon_F_caihong", "Type": 0, "SubType": 18, "Qulity": 4, "SortOrder": 23, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [100001], "StrParams": ["0"], "IsUse": 1, "Stacking": -1, "Source": 1}, {"ID": 300001, "NameID": 2000015, "Desc": 2000016, "Icon": "UD_Icon_F_hua", "Type": 0, "SubType": 18, "Qulity": 4, "SortOrder": 23, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [200001, 200002, 200003, 200004, 200005, 200006], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [100001], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 0}, {"ID": 300002, "NameID": 2000017, "Desc": 2000018, "Icon": "UD_Icon_F_zhusun", "Type": 0, "SubType": 18, "Qulity": 4, "SortOrder": 23, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [200101, 200102, 200103, 200104, 200105, 200106], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [100001], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 3}, {"ID": 300003, "NameID": 2000019, "Desc": 2000020, "Icon": "UD_Icon_F_hong<PERSON>shi", "Type": 0, "SubType": 18, "Qulity": 4, "SortOrder": 23, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [100001], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 2}, {"ID": 300004, "NameID": 1001470, "Desc": 2000020, "Icon": "UD_Icon_item_chanchu", "Type": 0, "SubType": 18, "Qulity": 4, "SortOrder": 23, "Price": [], "ShowPacket": -1, "LevelLimit": -1, "OptList": [200001, 200002, 200003, 200004, 200005, 200006], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [100001], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 1}, {"ID": 900001, "NameID": 1003501, "Desc": 1003504, "Icon": "UD_Icon_item_buliao", "Type": 1, "SubType": 2, "Qulity": 3, "SortOrder": 24, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [0, 1], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [50], "StrParams": [], "IsUse": 1, "Stacking": 9999, "Source": 0}, {"ID": 1000000, "NameID": 1095011, "Desc": 1095023, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": -1, "Qulity": 3, "SortOrder": 25, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 0}, {"ID": 1000001, "NameID": 1095012, "Desc": 1095024, "Icon": "UD_Icon_item_gold", "Type": 0, "SubType": -1, "Qulity": 4, "SortOrder": 26, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 0}, {"ID": 1000002, "NameID": 1095013, "Desc": 1095025, "Icon": "UD_Icon_item_baoxiang04", "Type": 1, "SubType": 1, "Qulity": 3, "SortOrder": 27, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [140005], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 1}, {"ID": 1000003, "NameID": 1095014, "Desc": 1095026, "Icon": "UD_Icon_item_baoxiang04", "Type": 1, "SubType": 1, "Qulity": 4, "SortOrder": 28, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [140006], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 1}, {"ID": 1000004, "NameID": 1095015, "Desc": 1095027, "Icon": "UD_Icon_item_baoxiang00", "Type": 1, "SubType": 0, "Qulity": 3, "SortOrder": 29, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [140007], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 2}, {"ID": 1000005, "NameID": 1095016, "Desc": 1095028, "Icon": "UD_Icon_item_baoxiang00", "Type": 1, "SubType": 0, "Qulity": 4, "SortOrder": 30, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [140008], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 2}, {"ID": 1000006, "NameID": 1095017, "Desc": 1095029, "Icon": "UD_Icon_item_jianzao", "Type": 2, "SubType": -1, "Qulity": 3, "SortOrder": 31, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [60, 1, 0], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 3}, {"ID": 1000007, "NameID": 1095018, "Desc": 1095030, "Icon": "UD_Icon_item_jinghua", "Type": 2, "SubType": -1, "Qulity": 4, "SortOrder": 32, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [60, 1, 1], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 3}, {"ID": 1000008, "NameID": 1095019, "Desc": 1095031, "Icon": "UD_Icon_item_jinkuai", "Type": 3, "SubType": 0, "Qulity": 3, "SortOrder": 33, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [1130001, 1130002], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 1}, {"ID": 1000009, "NameID": 1095020, "Desc": 1095032, "Icon": "UD_Icon_item_kejidian", "Type": 3, "SubType": 1, "Qulity": 4, "SortOrder": 34, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [1130001, 1130002], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 1}, {"ID": 1000010, "NameID": 1095021, "Desc": 1095033, "Icon": "UD_Icon_F_suipian1", "Type": 4, "SubType": 1, "Qulity": 3, "SortOrder": 35, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 3}, {"ID": 1000011, "NameID": 1095022, "Desc": 1095034, "Icon": "UD_Icon_F_suipian2", "Type": 4, "SubType": 2, "Qulity": 4, "SortOrder": 36, "Price": [], "ShowPacket": 1, "LevelLimit": -1, "OptList": [], "BatchUseMaxCount": -1, "FromFunctionList": [], "NumParams": [0], "StrParams": ["0"], "IsUse": 1, "Stacking": 9999, "Source": 3}]