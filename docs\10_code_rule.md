## 代码规范

### 原则

1. 统一的编码风格：使用 [goimports](https://pkg.go.dev/golang.org/x/tools/cmd/goimports) 格式化代码
2. 逻辑一定要简单清晰（KISS原则）
3. 没有重复性的代码，复制黏贴是万恶之源
4. 导出方法和接口，必须添加清晰注释
5. 错误和异常一定要记录日志
6. 函数、变量等采用国际化标准英文单词命名，尽量使用短命名
7. 不能使用Magic Number，定义合理变量并添加注释
8. 避免使用递归函数，尽量使用for来代替递归
9. 公共库代码必须保证充分的单元测试
10. 运行时一定不要调用Panic
11. 避免对Map进行不加锁的并发读写
12. 注意浮点数精度问题，慎用float32：超过千万的浮点数值用float64
13. 资源申请一定要释放，避免资源泄漏:

```
ticker := time.NewTicker(time.Second)
defer ticker.Stop()

resp, err := client.Do(req)
if resp != nil && resp.Body != nil {
    defer resp.Body.Close()
}
```

### 禁忌

1. <font color=#A52A2A size=4 >严禁本地代码编译不通过、测试不通过直接提交的行为</font>
2. <font color=#A52A2A size=4 >严禁提交内容自己不测试的行为</font>
3. <font color=#A52A2A size=4 >严禁图自己方便修改框架代码，破坏已有的稳定结构</font>
4. <font color=#A52A2A size=4 >严禁不按照工作流程提交工作内容的行为</font>
5. <font color=#A52A2A size=4 >严禁后端直接信任前端的协议数据不做严格判断</font>
6. <font color=#A52A2A size=4 >严禁图自己方便破坏基本原则和设计意图</font>
7. <font color=#A52A2A size=4 >严禁不经过上级同意的情况下擅自操作线上服务器</font>
8. <font color=#A52A2A size=4 >严禁不经过运营同意的情况下擅自让运维操作线上服务器</font>

### 项目相关

1. 绝不能信任客户端请求参数，必须进行严格的校验！！！
2. 道具货币等资源类操作遵循先扣再给！！！
3. 避免野生Goroutine，统一使用封装的util.Go开goroutine，强制添加recover！！！
4. player Actor里慎用异步请求，使用同步请求！！！
5. player的访问只能通过player system中转！！！
6. Actor Response的Data 一定注意返回的Data不能在当前的goroutine里继续访问！！！
7. 使用gamedef.SystemPID时不能用回调！！！
8. 读表时进行判空处理, 有问题时记录日志, 但是对于客户端传id在表中找不到的情况, 可以不打日志, 返回错误码即可！！！
9. 对配置表进行预处理, 基于性能考虑可以添加映射, 方便查找！！！
10. 对配置表进行检查, 以避免下标越界或者循环查找等等问题！！！
11. slice在使用时多做判断, 防止下标越界！！！
12. 当一个slice特别大时, 基于性能考虑, 可使用map来代替！！！
13. 操作slice时尽量使用公共的工具函数, 手动删除时注意从后往前！！！
14. 功能中做玩法条件校验时, 遵循fail fast, 顺序上先做敏感或者容易的校验 ！！！
15. 错误码应方便定位和说明, 尽量不使用通用的错误码！！！
16. 公共逻辑提取成独立代码, 提高代码复用性！！！
17. 对于某种分支情况才需要进行的逻辑, 不应该放在主分支中, 比如某些查询, 没必要提前查, 不走那个分支就浪费了 ！！！
18. 复杂的玩法逻辑应有注释说明！！！


### 特殊处理和约定

1. 策划配置日期格式为：`2006-1-2_15:4:5`
2. 不需要日期限制的字段，统一配置为`-1`（字段类型为String）
3. 日期相关判断统一使用`logic/gameutil/time.go`中`CheckTimeRange`等方法
4. 和前端约定的索引，下标统一从1开始(避免proto里默认值不传递前端要处理的问题)
5. 需要给前端返回默认值的字段，需要设置成optional否则前端无法判断是否有值（比如changeData返回里）: 比如可能会扣为0的数据，需要设置成optional