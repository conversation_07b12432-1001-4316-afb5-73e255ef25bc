## 日志提交规范

### 日志格式：

* [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/)

* 提交信息的结构应该如下所示：

```text
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### 日志类型：

* feat：新功能
* fix：修改bug
* docs：文档
* style：格式（不影响代码运行的变动）
* refactor：重构或修改（即不是新增功能，也不是修改bug的代码变动）
* test：增加测试
* chore：构建过程或辅助工具的变动

### 例子：

```
feat(hero): 添加英雄养成模块
fix(proto): 修改proto版本导致的报错
build(Makefile): 修改编译脚本
```
