//
// @Author:zhouchen
// @Description: 消息包文件定义
// @Data: Created in 20:01 2023/6/6

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.1
// source: general-services/announcement/v1/announcement.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Announcement_GetAnnouncementListData_FullMethodName = "/message.v1.Announcement/GetAnnouncementListData"
	Announcement_AddAnnouncementItemData_FullMethodName = "/message.v1.Announcement/AddAnnouncementItemData"
	Announcement_DelAnnouncementItemData_FullMethodName = "/message.v1.Announcement/DelAnnouncementItemData"
)

// AnnouncementClient is the client API for Announcement service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// The relation service definition.
type AnnouncementClient interface {
	GetAnnouncementListData(ctx context.Context, in *GetAnnouncementListReq, opts ...grpc.CallOption) (*GetAnnouncementListReply, error)
	AddAnnouncementItemData(ctx context.Context, in *NoticeItem, opts ...grpc.CallOption) (*GetAnnouncementListReply, error)
	DelAnnouncementItemData(ctx context.Context, in *GetAnnouncementListReq, opts ...grpc.CallOption) (*GetAnnouncementListReply, error)
}

type announcementClient struct {
	cc grpc.ClientConnInterface
}

func NewAnnouncementClient(cc grpc.ClientConnInterface) AnnouncementClient {
	return &announcementClient{cc}
}

func (c *announcementClient) GetAnnouncementListData(ctx context.Context, in *GetAnnouncementListReq, opts ...grpc.CallOption) (*GetAnnouncementListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAnnouncementListReply)
	err := c.cc.Invoke(ctx, Announcement_GetAnnouncementListData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *announcementClient) AddAnnouncementItemData(ctx context.Context, in *NoticeItem, opts ...grpc.CallOption) (*GetAnnouncementListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAnnouncementListReply)
	err := c.cc.Invoke(ctx, Announcement_AddAnnouncementItemData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *announcementClient) DelAnnouncementItemData(ctx context.Context, in *GetAnnouncementListReq, opts ...grpc.CallOption) (*GetAnnouncementListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAnnouncementListReply)
	err := c.cc.Invoke(ctx, Announcement_DelAnnouncementItemData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AnnouncementServer is the server API for Announcement service.
// All implementations must embed UnimplementedAnnouncementServer
// for forward compatibility.
//
// The relation service definition.
type AnnouncementServer interface {
	GetAnnouncementListData(context.Context, *GetAnnouncementListReq) (*GetAnnouncementListReply, error)
	AddAnnouncementItemData(context.Context, *NoticeItem) (*GetAnnouncementListReply, error)
	DelAnnouncementItemData(context.Context, *GetAnnouncementListReq) (*GetAnnouncementListReply, error)
	mustEmbedUnimplementedAnnouncementServer()
}

// UnimplementedAnnouncementServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAnnouncementServer struct{}

func (UnimplementedAnnouncementServer) GetAnnouncementListData(context.Context, *GetAnnouncementListReq) (*GetAnnouncementListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAnnouncementListData not implemented")
}
func (UnimplementedAnnouncementServer) AddAnnouncementItemData(context.Context, *NoticeItem) (*GetAnnouncementListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAnnouncementItemData not implemented")
}
func (UnimplementedAnnouncementServer) DelAnnouncementItemData(context.Context, *GetAnnouncementListReq) (*GetAnnouncementListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelAnnouncementItemData not implemented")
}
func (UnimplementedAnnouncementServer) mustEmbedUnimplementedAnnouncementServer() {}
func (UnimplementedAnnouncementServer) testEmbeddedByValue()                      {}

// UnsafeAnnouncementServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AnnouncementServer will
// result in compilation errors.
type UnsafeAnnouncementServer interface {
	mustEmbedUnimplementedAnnouncementServer()
}

func RegisterAnnouncementServer(s grpc.ServiceRegistrar, srv AnnouncementServer) {
	// If the following call pancis, it indicates UnimplementedAnnouncementServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Announcement_ServiceDesc, srv)
}

func _Announcement_GetAnnouncementListData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnnouncementListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnouncementServer).GetAnnouncementListData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Announcement_GetAnnouncementListData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnouncementServer).GetAnnouncementListData(ctx, req.(*GetAnnouncementListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Announcement_AddAnnouncementItemData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NoticeItem)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnouncementServer).AddAnnouncementItemData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Announcement_AddAnnouncementItemData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnouncementServer).AddAnnouncementItemData(ctx, req.(*NoticeItem))
	}
	return interceptor(ctx, in, info, handler)
}

func _Announcement_DelAnnouncementItemData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAnnouncementListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AnnouncementServer).DelAnnouncementItemData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Announcement_DelAnnouncementItemData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AnnouncementServer).DelAnnouncementItemData(ctx, req.(*GetAnnouncementListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Announcement_ServiceDesc is the grpc.ServiceDesc for Announcement service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Announcement_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "message.v1.Announcement",
	HandlerType: (*AnnouncementServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAnnouncementListData",
			Handler:    _Announcement_GetAnnouncementListData_Handler,
		},
		{
			MethodName: "AddAnnouncementItemData",
			Handler:    _Announcement_AddAnnouncementItemData_Handler,
		},
		{
			MethodName: "DelAnnouncementItemData",
			Handler:    _Announcement_DelAnnouncementItemData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "general-services/announcement/v1/announcement.proto",
}
