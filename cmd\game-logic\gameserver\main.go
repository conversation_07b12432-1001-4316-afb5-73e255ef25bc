package main

import (
	"fmt"
	"liteframe/internal/common/constant"
	"liteframe/internal/common/version"
	"os"

	"liteframe/internal/game-logic/gameserver/server"
	"liteframe/pkg/log"

	"github.com/urfave/cli/v2"
)

func main() {
	app := cli.App{
		Name:    constant.ServiceNameGameServer,
		Flags:   server.Flags(),
		Version: version.String(),
		Action:  mainLoop,
	}

	if err := app.Run(os.Args); err != nil {
		fmt.Fprintf(os.Stderr, "app Run error=%s", err.Error())
	}
}

func mainLoop(c *cli.Context) error {
	s := server.NewServer()
	if err := s.Init(); err != nil {
		log.Error("server Init failed", log.Err(err))
		return err
	}

	if err := s.Run(); err != nil {
		log.Error("server Run failed", log.Err(err))
		return err
	}

	if err := s.Exit(); err != nil {
		log.Error("server Exit failed", log.Err(err))
		return err
	}

	return nil
}
