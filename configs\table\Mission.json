[{"ID": 30001, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 2, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30002, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 3, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30003, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 4, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30004, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 5, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30005, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 6, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30006, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 7, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30007, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 8, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30008, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 9, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30009, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 10, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30010, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 11, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30011, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 12, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30012, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 13, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 30013, "Conditiondesc": 1002713, "TaskType": 1, "ConditionType": 2, "Param": "", "TotalCount": 14, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70001, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 1, "RewadId": 140007, "ExtraParam": [3], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70002, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 2, "RewadId": 140007, "ExtraParam": [4], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70003, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 3, "RewadId": 140007, "ExtraParam": [5], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70004, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 4, "RewadId": 140007, "ExtraParam": [6], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70005, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 5, "RewadId": 140007, "ExtraParam": [7], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70006, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 6, "RewadId": 140007, "ExtraParam": [8], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70007, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 7, "RewadId": 140007, "ExtraParam": [9], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70008, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 2, "RewadId": 140007, "ExtraParam": [10], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70009, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 3, "RewadId": 140007, "ExtraParam": [11], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70010, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 4, "RewadId": 140007, "ExtraParam": [12], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70011, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 5, "RewadId": 140007, "ExtraParam": [13], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70012, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 6, "RewadId": 140007, "ExtraParam": [14], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70013, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 7, "RewadId": 140007, "ExtraParam": [15], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70014, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 8, "RewadId": 140007, "ExtraParam": [16], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70015, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 9, "RewadId": 140007, "ExtraParam": [17], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70016, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 10, "RewadId": 140007, "ExtraParam": [18], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70017, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 11, "RewadId": 140007, "ExtraParam": [18], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70018, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 12, "RewadId": 140007, "ExtraParam": [18], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70019, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 13, "RewadId": 140007, "ExtraParam": [18], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70020, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 14, "RewadId": 140007, "ExtraParam": [18], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 70021, "Conditiondesc": 1002713, "TaskType": 2, "ConditionType": 2, "Param": "", "TotalCount": 15, "RewadId": 140007, "ExtraParam": [18], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80001, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 1, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80002, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 2, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80003, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 3, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80004, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 4, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80005, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 5, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80006, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 6, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80007, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 7, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80008, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 2, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80009, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 3, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80010, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 4, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80011, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 5, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80012, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 6, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80013, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 7, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80014, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 8, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80015, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 9, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80016, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 10, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80017, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 11, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80018, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 12, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80019, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 13, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80020, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 14, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 80021, "Conditiondesc": 1002713, "TaskType": 3, "ConditionType": 2, "Param": "", "TotalCount": 15, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90001, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 1, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90002, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 2, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90003, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 3, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90004, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 4, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90005, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 5, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90006, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 6, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90007, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 7, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90008, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 8, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90009, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 9, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90010, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 10, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90011, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 11, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90012, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 12, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90013, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 13, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90014, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 14, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90015, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 15, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90016, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 16, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90017, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 17, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90018, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 18, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90019, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 19, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}, {"ID": 90020, "Conditiondesc": 1002713, "TaskType": 4, "ConditionType": 2, "Param": "", "TotalCount": 20, "RewadId": 140007, "ExtraParam": [0], "JumpId": 0, "weakGuideClick": 0, "weakGuideStay": 0, "Next": -1}]