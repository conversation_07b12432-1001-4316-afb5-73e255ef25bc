



#### 日志系统架构与组件解析

1. 系统架构整体设计：

```
用户代码
   ↓
全局 API (global.go)
   ↓
ZapLogger (zap_logger.go)
   ↓
底层实现 (zap + lumberjack)
```

2. 各层次职责：

a) 全局 API 层（global.go）：
- 提供全局单例日志器
- 暴露统一的日志接口
- 处理日志初始化和默认行为
```go
var global log.Logger  // 全局单例

// 全局方法
func Info(msg string, fields ...Field)
func Error(msg string, fields ...Field)
```

b) 适配层（ZapLogger）：
- 实现 **Kratos** 日志接口
- 管理日志配置和输出
- 处理日志级别和编码


3. 核心组件详解：

a) **Zap Logger**：
- log：主要的日志记录器，处理结构化日志
- sugar：提供更简便的格式化API

b) AtomicLevel：
- 原子级别控制器
- 线程安全的日志级别管理
- 支持动态调整日志级别

c) Core 组件：
- 处理日志的实际写入
- 管理编码和输出
- 支持多目标输出（console/file）

d) Encoder：
- consoleEncoder：格式化控制台输出，支持颜色
- fileEncoder：JSON格式文件输出，便于解析

4. 数据流向：
```
Log调用 
  -> 级别检查(atomicLevel)
  -> 参数处理
  -> 编码(Encoder)
  -> 写入(Core)
  -> 输出目标
```

5. 关键接口：

a) Kratos Logger 接口：
```go
type Logger interface {
    Log(level Level, keyvals ...interface{}) error
}
```

b) Zap Core 接口：
```go
type Core interface {
    Enabled(Level) bool
    With([]Field) Core
    Check(Entry, *CheckedEntry) *CheckedEntry
    Write(Entry, []Field) error
    Sync() error
}
```

6. 配置系统：
```go
type Config struct {
	// 基础配置
	Console  bool   `yaml:"console"`  // 是否输出到控制台
	FilePath string `yaml:"filepath"` // 日志文件路径
	Level    string `yaml:"level"`    // 日志级别

	// 轮转配置
	Rotate      bool `yaml:"rotate"`       // 是否启用日志轮转
	RotateSize  int  `yaml:"rotate_size"`  // 单个日志文件大小限制(MB)
	MaxAge      int  `yaml:"max_age"`      // 日志保留天数
	MaxBackups  int  `yaml:"max_backups"`  // 保留的旧日志文件个数
	Compress    bool `yaml:"compress"`     // 是否压缩旧日志
	RotateDaily bool `yaml:"rotate_daily"` // 是否按天轮转
}
```

7. 设计：

a) `Kratos` 接口：
- 标准化：符合微服务框架规范
- 可替换性：**便于更换底层实现**
- 兼容性：与 Kratos 生态集成

b)  `Zap`：
- 性能：***高性能的结构化日志，通过 Zap 的异步机制提高吞吐***
- 灵活性：支持多种输出格式
- 可扩展：丰富的扩展接口

c) 封装 `ZapLogger`：
- 适配：桥接 Kratos 和 Zap
- 管理：统一配置和生命周期
- 扩展：添加额外功能

这个架构设计允许：
1. 灵活的日志配置
2. 高效的日志记录
3. 可靠的日志管理
4. **良好的扩展性（可替换zap成任意日志库）**



#### 日志轮转

1. 需求：
- 按时间分割：基于小时级别的日志文件
- 启动序号：同一时间段内多次启动需要序号区分
- 轮转序号：同一时段内的日志轮转也需要序号
- 命名格式：gameserver-{date}-{hour}+{seq}.log

2. Lumberjack 能力：
- 原生功能：
  - 基于大小的轮转
  - 保留备份数量控制
  - 压缩旧文件
  - 基于时间的清理
- 局限性：
  - 不支持自定义文件名格式
  - 不支持基于时间的轮转
  - 不支持启动序号

3. 解决方案：

方案 1: 包装 Lumberjack
```go
// RotateConfig 定义日志轮转配置
type RotateConfig struct {
    // 基础配置
    BaseName   string         `json:"base_name"`   // 日志文件基础名，如 "gameserver"
    Directory  string         `json:"directory"`   // 日志目录
    Strategy   RotateStrategy `json:"strategy"`    // 轮转策略
    
    // 大小轮转配置
    MaxSize    int  `json:"max_size"`    // 单个文件最大大小(MB)
    MaxBackups int  `json:"max_backups"` // 保留的最大文件数
    MaxAge     int  `json:"max_age"`     // 文件保留最大天数
    Compress   bool `json:"compress"`    // 是否压缩旧文件
}

type TimeRotator struct {
    config     *RotateConfig		// 轮转配置管理
    lumberjack *lumberjack.Logger 	// 底层文件管理
    mu         sync.Mutex
    lastRotate time.Time    		// 上次轮转时间
    rotateIdx  int         			// 当前小时内的轮转序号
}
```



#### Actor包日志独立日志系统

1. 设计：

- 关注点分离：Actor 系统是一个相对独立的框架，它应该能够独立运行而不依赖外部的日志系统
- 框架解耦：Actor 包作为基础框架，不应该直接依赖具体的业务层日志实现
- 可替换性：通过 Logger 接口，允许使用者注入自己的日志实现

2. logAdapter 的作用：
```go
// 在 system 包中实现的适配器
type logAdapter struct{}

func (logAdapter) Info(format string, v ...interface{}) {
    log.Info(fmt.Sprintf(format, v...))
}
```
- 这是一个适配器模式的应用
- 将业务层的日志系统（新实现的 zap 日志）适配到 Actor 框架需要的接口
- 实现了框架和具体日志实现的解耦

3. 完整的调用链：
```go
// 1. Actor 框架中使用自己的 logger 接口
logger.Error("dispatch failed: ...")  // 在 actor 包中

// 2. 业务层通过适配器注入具体实现
func initActorLogger() {
    actor.SetLogger(&logAdapter{})  // 在系统初始化时
}

// 3. 适配器将调用转发到实际的日志系统
func (logAdapter) Error(format string, v ...interface{}) {
    log.Error(fmt.Sprintf(format, v...))  // 转发到 zap 日志
}
```

4. 设计的优势：
- 解耦：Actor 框架不依赖具体的日志实现
- 可测试：可以在测试时注入 mock logger
- 灵活性：可以根据需要切换不同的日志实现
- 边界清晰：框架代码和业务代码职责分明

5. 总的来说，这种设计是合理的，因为：

- 遵循了依赖倒置原则
- 保持了框架的独立性
- 提供了灵活的扩展性
- 便于测试和维护

> 不直接使用全局日志接口的原因是为了保持 Actor 框架的独立性和可复用性。如果直接使用全局日志，会造成框架对具体实现的依赖，这在软件工程上是不推荐的做法。