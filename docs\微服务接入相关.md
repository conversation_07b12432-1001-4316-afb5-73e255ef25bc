### 游戏服微服务接入

#### 使用

​	1、-- 配置gameserver.yaml

​	grpc_config：微服务相关配置都在这里面(当前只有针对每个连接的配置，如果后续有针对整体的配置也加这)

​		--connect_config_group：连接数据数组，包含每个微服务的连接配置信息

​			--GrpcConnectConfig

​				-- id :该微服务的serviceId，与kratos_call_map_def中定义的服务号对应

​				-- service_name：该微服务名称，需要和其proto文件中的serviceName对应

​				-- open：标识该微服务是否开启

​				-- connectNum：标识该微服务提供的Client连接数量

​				-- url：该微服务服务器地址

​	2、-- 根据微服务服务器代码，生成游戏服Grpc调用相关代码，

​		执行目录Server/kratos/grpcGen下 GenGrpcPb.bat，生成微服务接入代码，

​		生成路径为：servers\gameserver\internal\logic\system\grpcclient\pbgen

​	

​	3、游戏服逻辑接入player.SendGrpcRequest

​		使用pbgen.GrpcMessage结构去包装请求的内容：

​				serviceId：请求的微服务id

​				functionName：rpc调用的函数名

​				data：rpc调用的函数数据



#### 框架逻辑

​	总共考虑了三种实现：

​		1、使用游戏服的actor通信模型，微服务部分作为一个独立模块运行在一个goroutine，通过actor通信实现微服务请求

​		优点：支持异步请求，不会卡住玩家逻辑，单goroutine进行逻辑，消耗小

​		缺点：针对一个连接只有单个goroutine去进行请求，效率低



​		2、玩家goroutine中直接使用grpcClient进行微服务请求

​		优点 ： 充分使用grpcClient效率，且玩家请求与回复能保证有序，不用开启额外goroutine去执行逻辑

​		缺点： 请求过程会阻塞玩家goroutine



​		3、 玩家创建新的goroutine(player_microservice)，专门用于自己的微服务请求

​		优点：充分使用grpcClient效率，且玩家请求与回复能保证有序，不阻塞玩家

​		缺点：会开启更多的goroutine，增加性能消耗



特殊情况处理，玩家掉线后，如何保证grpc请求相关逻辑完成？

​	针对三种实现方式，

​		1：需要在微服务goroutine中做额外逻辑，当玩家已经离线时，额外处理

​		2：由于请求过程是阻塞玩家goroutine的，所以不会存在问题

​		3：需要等玩家微服务goroutine中任务都完成后，再关闭玩家actor
