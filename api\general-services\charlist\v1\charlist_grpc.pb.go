//下面这行很重要，是表明你这个Service用于哪个URL的，见游戏服的GrpcAddressType
//GrpcAddressType:CharList

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.1
// source: general-services/charlist/v1/charlist.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CharListDotNet_SaveCharList_FullMethodName = "/charlist.v1.CharListDotNet/SaveCharList"
)

// CharListDotNetClient is the client API for CharListDotNet service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ServiceStart
type CharListDotNetClient interface {
	SaveCharList(ctx context.Context, in *SaveCharListRequest, opts ...grpc.CallOption) (*SaveCharListReply, error)
}

type charListDotNetClient struct {
	cc grpc.ClientConnInterface
}

func NewCharListDotNetClient(cc grpc.ClientConnInterface) CharListDotNetClient {
	return &charListDotNetClient{cc}
}

func (c *charListDotNetClient) SaveCharList(ctx context.Context, in *SaveCharListRequest, opts ...grpc.CallOption) (*SaveCharListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SaveCharListReply)
	err := c.cc.Invoke(ctx, CharListDotNet_SaveCharList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CharListDotNetServer is the server API for CharListDotNet service.
// All implementations must embed UnimplementedCharListDotNetServer
// for forward compatibility.
//
// ServiceStart
type CharListDotNetServer interface {
	SaveCharList(context.Context, *SaveCharListRequest) (*SaveCharListReply, error)
	mustEmbedUnimplementedCharListDotNetServer()
}

// UnimplementedCharListDotNetServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCharListDotNetServer struct{}

func (UnimplementedCharListDotNetServer) SaveCharList(context.Context, *SaveCharListRequest) (*SaveCharListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SaveCharList not implemented")
}
func (UnimplementedCharListDotNetServer) mustEmbedUnimplementedCharListDotNetServer() {}
func (UnimplementedCharListDotNetServer) testEmbeddedByValue()                        {}

// UnsafeCharListDotNetServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CharListDotNetServer will
// result in compilation errors.
type UnsafeCharListDotNetServer interface {
	mustEmbedUnimplementedCharListDotNetServer()
}

func RegisterCharListDotNetServer(s grpc.ServiceRegistrar, srv CharListDotNetServer) {
	// If the following call pancis, it indicates UnimplementedCharListDotNetServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CharListDotNet_ServiceDesc, srv)
}

func _CharListDotNet_SaveCharList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SaveCharListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CharListDotNetServer).SaveCharList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CharListDotNet_SaveCharList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CharListDotNetServer).SaveCharList(ctx, req.(*SaveCharListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CharListDotNet_ServiceDesc is the grpc.ServiceDesc for CharListDotNet service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CharListDotNet_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "charlist.v1.CharListDotNet",
	HandlerType: (*CharListDotNetServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SaveCharList",
			Handler:    _CharListDotNet_SaveCharList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "general-services/charlist/v1/charlist.proto",
}

const (
	CharListHttp_GetCharListData_FullMethodName = "/charlist.v1.CharListHttp/GetCharListData"
)

// CharListHttpClient is the client API for CharListHttp service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CharListHttpClient interface {
	GetCharListData(ctx context.Context, in *GetCharListReq, opts ...grpc.CallOption) (*GetCharListReply, error)
}

type charListHttpClient struct {
	cc grpc.ClientConnInterface
}

func NewCharListHttpClient(cc grpc.ClientConnInterface) CharListHttpClient {
	return &charListHttpClient{cc}
}

func (c *charListHttpClient) GetCharListData(ctx context.Context, in *GetCharListReq, opts ...grpc.CallOption) (*GetCharListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCharListReply)
	err := c.cc.Invoke(ctx, CharListHttp_GetCharListData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CharListHttpServer is the server API for CharListHttp service.
// All implementations must embed UnimplementedCharListHttpServer
// for forward compatibility.
type CharListHttpServer interface {
	GetCharListData(context.Context, *GetCharListReq) (*GetCharListReply, error)
	mustEmbedUnimplementedCharListHttpServer()
}

// UnimplementedCharListHttpServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCharListHttpServer struct{}

func (UnimplementedCharListHttpServer) GetCharListData(context.Context, *GetCharListReq) (*GetCharListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCharListData not implemented")
}
func (UnimplementedCharListHttpServer) mustEmbedUnimplementedCharListHttpServer() {}
func (UnimplementedCharListHttpServer) testEmbeddedByValue()                      {}

// UnsafeCharListHttpServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CharListHttpServer will
// result in compilation errors.
type UnsafeCharListHttpServer interface {
	mustEmbedUnimplementedCharListHttpServer()
}

func RegisterCharListHttpServer(s grpc.ServiceRegistrar, srv CharListHttpServer) {
	// If the following call pancis, it indicates UnimplementedCharListHttpServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CharListHttp_ServiceDesc, srv)
}

func _CharListHttp_GetCharListData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCharListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CharListHttpServer).GetCharListData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CharListHttp_GetCharListData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CharListHttpServer).GetCharListData(ctx, req.(*GetCharListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// CharListHttp_ServiceDesc is the grpc.ServiceDesc for CharListHttp service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CharListHttp_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "charlist.v1.CharListHttp",
	HandlerType: (*CharListHttpServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCharListData",
			Handler:    _CharListHttp_GetCharListData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "general-services/charlist/v1/charlist.proto",
}
