{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"BattleServer/1.0.0": {"dependencies": {"CommandLineParser": "2.9.1", "Common": "1.0.0", "Framework": "1.0.0", "Google.Protobuf": "3.29.1", "MemoryPack": "1.21.4", "MemoryPack.Core": "1.21.4", "MemoryPack.Generator": "1.21.4", "Microsoft.Diagnostics.Runtime": "3.1.512801", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "NATS.Net": "2.5.5", "NLog": "6.0.0-rc4", "Newtonsoft.Json": "13.0.3", "Serilog": "4.2.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0", "YamlDotNet": "16.2.1"}, "runtime": {"BattleServer.dll": {}}}, "CommandLineParser/2.9.1": {"runtime": {"lib/netstandard2.0/CommandLine.dll": {"assemblyVersion": "2.9.1.0", "fileVersion": "2.9.1.0"}}}, "Google.Protobuf/3.29.1": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.29.1.0", "fileVersion": "3.29.1.0"}}}, "MemoryPack/1.21.4": {"dependencies": {"MemoryPack.Core": "1.21.4", "MemoryPack.Generator": "1.21.4"}}, "MemoryPack.Core/1.21.4": {"runtime": {"lib/net8.0/MemoryPack.Core.dll": {"assemblyVersion": "1.21.4.0", "fileVersion": "1.21.4.0"}}}, "MemoryPack.Generator/1.21.4": {}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "Microsoft.Diagnostics.NETCore.Client/0.2.410101": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.0", "Microsoft.Extensions.Logging": "2.1.1"}, "runtime": {"lib/net6.0/Microsoft.Diagnostics.NETCore.Client.dll": {"assemblyVersion": "0.2.8.10101", "fileVersion": "0.2.8.10101"}}}, "Microsoft.Diagnostics.Runtime/3.1.512801": {"dependencies": {"Microsoft.Diagnostics.NETCore.Client": "0.2.410101"}, "runtime": {"lib/net6.0/Microsoft.Diagnostics.Runtime.dll": {"assemblyVersion": "3.1.10.12801", "fileVersion": "3.1.10.12801"}}}, "Microsoft.Extensions.Configuration/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.1.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/2.1.1": {"dependencies": {"Microsoft.Extensions.Configuration.Binder": "2.1.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "2.1.1.18157"}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "NATS.Client.Core/2.5.5": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.6", "System.IO.Pipelines": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net8.0/NATS.Client.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NATS.Client.Hosting/2.5.5": {"dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "NATS.Client.Core": "2.5.5"}, "runtime": {"lib/net8.0/NATS.Client.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NATS.Client.JetStream/2.5.5": {"dependencies": {"NATS.Client.Core": "2.5.5"}, "runtime": {"lib/net8.0/NATS.Client.JetStream.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NATS.Client.KeyValueStore/2.5.5": {"dependencies": {"NATS.Client.JetStream": "2.5.5"}, "runtime": {"lib/net8.0/NATS.Client.KeyValueStore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NATS.Client.ObjectStore/2.5.5": {"dependencies": {"NATS.Client.JetStream": "2.5.5"}, "runtime": {"lib/net8.0/NATS.Client.ObjectStore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NATS.Client.Serializers.Json/2.5.5": {"dependencies": {"NATS.Client.Core": "2.5.5"}, "runtime": {"lib/net8.0/NATS.Client.Serializers.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NATS.Client.Services/2.5.5": {"dependencies": {"NATS.Client.Core": "2.5.5"}, "runtime": {"lib/net8.0/NATS.Client.Services.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NATS.Client.Simplified/2.5.5": {"dependencies": {"NATS.Client.Core": "2.5.5", "NATS.Client.Serializers.Json": "2.5.5"}, "runtime": {"lib/net8.0/NATS.Client.Simplified.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NATS.Net/2.5.5": {"dependencies": {"NATS.Client.Core": "2.5.5", "NATS.Client.Hosting": "2.5.5", "NATS.Client.JetStream": "2.5.5", "NATS.Client.KeyValueStore": "2.5.5", "NATS.Client.ObjectStore": "2.5.5", "NATS.Client.Serializers.Json": "2.5.5", "NATS.Client.Services": "2.5.5", "NATS.Client.Simplified": "2.5.5"}, "runtime": {"lib/net8.0/NATS.Net.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "NLog/6.0.0-rc4": {"runtime": {"lib/netstandard2.1/NLog.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.0.4093"}}}, "Serilog/4.2.0": {"runtime": {"lib/net9.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.2.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.IO.Pipelines/8.0.0": {}, "System.Text.Json/8.0.5": {}, "YamlDotNet/16.2.1": {"runtime": {"lib/net8.0/YamlDotNet.dll": {"assemblyVersion": "1*******", "fileVersion": "********"}}}, "Common/1.0.0": {"dependencies": {"Google.Protobuf": "3.29.1", "MemoryPack": "1.21.4", "MemoryPack.Core": "1.21.4", "Newtonsoft.Json": "13.0.3"}, "runtime": {"Common.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Framework/1.0.0": {"dependencies": {"MemoryPack": "1.21.4", "MemoryPack.Core": "1.21.4", "Newtonsoft.Json": "13.0.3"}, "runtime": {"Framework.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"BattleServer/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "CommandLineParser/2.9.1": {"type": "package", "serviceable": true, "sha512": "sha512-OE0sl1/sQ37bjVsPKKtwQlWDgqaxWgtme3xZz7JssWUzg5JpMIyHgCTY9MVMxOg48fJ1AgGT3tgdH5m/kQ5xhA==", "path": "commandlineparser/2.9.1", "hashPath": "commandlineparser.2.9.1.nupkg.sha512"}, "Google.Protobuf/3.29.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDFLP72bPu4GwquVN7HtFnfqxhQs4CLbUEyOc/0yV48HB0Pxf7tDK7zIx6ifaQwGp+RSoLY1sz1CXqoAEAu+AQ==", "path": "google.protobuf/3.29.1", "hashPath": "google.protobuf.3.29.1.nupkg.sha512"}, "MemoryPack/1.21.4": {"type": "package", "serviceable": true, "sha512": "sha512-wy3JTBNBsO8LfQcBvvYsWr3lm2Oakolrfu0UQ3oSJSEiD+7ye0GUhYTaXuYYBowqsXBXWD9gf2218ae0JRiYVQ==", "path": "memorypack/1.21.4", "hashPath": "memorypack.1.21.4.nupkg.sha512"}, "MemoryPack.Core/1.21.4": {"type": "package", "serviceable": true, "sha512": "sha512-6RszGorZ0ejNmp37ZcboPBMvvPCuNW2jlrdQfcs/lMzE5b3pmPF6hsm/laDc34hRlbAST1ZxaX/DvYu2DF5sBQ==", "path": "memorypack.core/1.21.4", "hashPath": "memorypack.core.1.21.4.nupkg.sha512"}, "MemoryPack.Generator/1.21.4": {"type": "package", "serviceable": true, "sha512": "sha512-g14EsSS85yn0lHTi0J9ivqlZMf09A2iI51fmI+0KkzIzyCbWOBWPi5mdaY7YWmXprk12aYh9u/qfWHQUYthlwg==", "path": "memorypack.generator/1.21.4", "hashPath": "memorypack.generator.1.21.4.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Am6l4Vpn3/K32daEqZI+FFr96OlZkgwK2LcT3pZ2zWubR5zTPW3/FkO1Rat9kb7oQOa4rxgl9LJHc5tspCWfg==", "path": "microsoft.bcl.asyncinterfaces/1.1.0", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.0.nupkg.sha512"}, "Microsoft.Diagnostics.NETCore.Client/0.2.410101": {"type": "package", "serviceable": true, "sha512": "sha512-I4hMjlbPcM5R+M4ThD2Zt1z58M8uZnWkDbFLXHntOOAajajEucrw4XYNSaoi5rgoqksgxQ3g388Vof4QzUNwdQ==", "path": "microsoft.diagnostics.netcore.client/0.2.410101", "hashPath": "microsoft.diagnostics.netcore.client.0.2.410101.nupkg.sha512"}, "Microsoft.Diagnostics.Runtime/3.1.512801": {"type": "package", "serviceable": true, "sha512": "sha512-0lMUDr2oxNZa28D6NH5BuSQEe5T9tZziIkvkD44YkkCGQXPJqvFjLq5ZQq1hYLl3RjQJrY+hR0jFgap+EWPDTw==", "path": "microsoft.diagnostics.runtime/3.1.512801", "hashPath": "microsoft.diagnostics.runtime.3.1.512801.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-LjVKO6P2y52c5ZhTLX/w8zc5H4Y3J/LJsgqTBj49TtFq/hAtVNue/WA0F6/7GMY90xhD7K0MDZ4qpOeWXbLvzg==", "path": "microsoft.extensions.configuration/2.1.1", "hashPath": "microsoft.extensions.configuration.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-fcLCTS03poWE4v9tSNBr3pWn0QwGgAn1vzqHXlXgvqZeOc7LvQNzaWcKRQZTdEc3+YhQKwMsOtm3VKSA2aWQ8w==", "path": "microsoft.extensions.configuration.binder/2.1.1", "hashPath": "microsoft.extensions.configuration.binder.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-GIoXX7VDcTEsNM6yvffTBaOwnPQELGI5dzExR7L2O7AUkDsHBYIZawUbuwfq3cYzz8dIAAJotQYJMzH7qy27Ng==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.6", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-hh+mkOAQDTp6XH80xJt3+wwYVzkbwYQl9XZRCz4Um0JjP/o7N9vHM3rZ6wwwtr+BBe/L6iBO2sz0px6OWBzqZQ==", "path": "microsoft.extensions.logging/2.1.1", "hashPath": "microsoft.extensions.logging.2.1.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "NATS.Client.Core/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-vs9t6GOpxkV06TETOTarWrch+d7bwpg2iu6Wh+aQt2r3mm3OcoKUtYk+WoViKS57RzOYkTuhxsTDW8inzUROfg==", "path": "nats.client.core/2.5.5", "hashPath": "nats.client.core.2.5.5.nupkg.sha512"}, "NATS.Client.Hosting/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-6CLhpz60wxEOgWKEESUjO2ELFYmBb/0/8shtR6dyrJhO0X/Uu4neBaE8/XG6s7p6YkjEWfCaKlNVGIO5Nc4G6w==", "path": "nats.client.hosting/2.5.5", "hashPath": "nats.client.hosting.2.5.5.nupkg.sha512"}, "NATS.Client.JetStream/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-h+AV2xlc1qNZhD6GOvJ2QV9M8+VTDbkte9p5L+baWFFxVKkS8mQSA5VUQoaC/UKu5t2U4TXdZ00p3AX46PrIow==", "path": "nats.client.jetstream/2.5.5", "hashPath": "nats.client.jetstream.2.5.5.nupkg.sha512"}, "NATS.Client.KeyValueStore/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-0Z0u7BSWrA4PO8kUO8TiDe4VpTsI55V11x2rUsf0BBDv2iAzfZaBywIlindhWC3y5Ak6aLXskNKSw9o2ttco1g==", "path": "nats.client.keyvaluestore/2.5.5", "hashPath": "nats.client.keyvaluestore.2.5.5.nupkg.sha512"}, "NATS.Client.ObjectStore/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-ZrkU7HbPqlATHM4CJQpTa2a3l1Ss0M8gZ1iqaJ/ZxJYcQpnZEoTYb7exL+bowDVQTjfocLj7fINw/6qGLHAJxw==", "path": "nats.client.objectstore/2.5.5", "hashPath": "nats.client.objectstore.2.5.5.nupkg.sha512"}, "NATS.Client.Serializers.Json/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-hSdKO7thXybki+kzvvi4JgKIXOf5M455JHnW8M/5G0imyzQUYBvSEbJrOWQk4MjMYP0JXQ7OPZ9U5WKRXtqJkA==", "path": "nats.client.serializers.json/2.5.5", "hashPath": "nats.client.serializers.json.2.5.5.nupkg.sha512"}, "NATS.Client.Services/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-mheYgAw2xJKHQu1G8GZMBpj18eijPLS+X7yd398zbUAhJ78K5jhHnaPUwhB8bLWNGe6T2sENBN78fQecTV2dSg==", "path": "nats.client.services/2.5.5", "hashPath": "nats.client.services.2.5.5.nupkg.sha512"}, "NATS.Client.Simplified/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-IlhjjWLQdnwXqUf6JQe5m5P/QaID0wD8uV7u0aeWHfx0OJuLK6rM/+fd/LkGM8XNI8cSIqFcWXgtKGlH1rxrCA==", "path": "nats.client.simplified/2.5.5", "hashPath": "nats.client.simplified.2.5.5.nupkg.sha512"}, "NATS.Net/2.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-xCXm8Cjcbe/3pqvRtDREeGw0o58t3ibMjYSX/bX66rXjqnXzYWmwKKnZgEHjy/J2HyePxiTWMT3yA3l9fgXrTw==", "path": "nats.net/2.5.5", "hashPath": "nats.net.2.5.5.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NLog/6.0.0-rc4": {"type": "package", "serviceable": true, "sha512": "sha512-7eu5glBYoO0jXK3f1t79m/tvuRQODSdhc0PB4EEoVEyLP/rq0xKwMkXM71/fqQNLtrSRF0aQjdVc/F2MDo+asg==", "path": "nlog/6.0.0-rc4", "hashPath": "nlog.6.0.0-rc4.nupkg.sha512"}, "Serilog/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-gmoWVOvKgbME8TYR+gwMf7osROiWAURterc6Rt2dQyX7wtjZYpqFiA/pY6ztjGQKKV62GGCyOcmtP1UKMHgSmA==", "path": "serilog/4.2.0", "hashPath": "serilog.4.2.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "System.IO.Pipelines/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "path": "system.io.pipelines/8.0.0", "hashPath": "system.io.pipelines.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "YamlDotNet/16.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-im6zTVgesjcfTRfuMpnx51Rg2svWenp/3q5XBfcIzgj8PNIkkSD2xEl9HWcVi2SaJPP9XcXUdzed9gSDEuf1TA==", "path": "yamldotnet/16.2.1", "hashPath": "yamldotnet.16.2.1.nupkg.sha512"}, "Common/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Framework/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}