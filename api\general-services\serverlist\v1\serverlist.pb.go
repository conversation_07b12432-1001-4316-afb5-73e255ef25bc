// @Author:zhouchen
// @Description: 消息包文件定义
// @Data: Created in 20:01 2023/6/6

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.1
// source: general-services/serverlist/v1/serverlist.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetServerListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TargetPlatformId string `protobuf:"bytes,1,opt,name=targetPlatformId,proto3" json:"targetPlatformId,omitempty"`
}

func (x *GetServerListReq) Reset() {
	*x = GetServerListReq{}
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServerListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServerListReq) ProtoMessage() {}

func (x *GetServerListReq) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServerListReq.ProtoReflect.Descriptor instead.
func (*GetServerListReq) Descriptor() ([]byte, []int) {
	return file_general_services_serverlist_v1_serverlist_proto_rawDescGZIP(), []int{0}
}

func (x *GetServerListReq) GetTargetPlatformId() string {
	if x != nil {
		return x.TargetPlatformId
	}
	return ""
}

type KratosServerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ServerPlayerCount string `protobuf:"bytes,1,opt,name=ServerPlayerCount,proto3" json:"ServerPlayerCount,omitempty"`
	ServerStatus      string `protobuf:"bytes,2,opt,name=ServerStatus,proto3" json:"ServerStatus,omitempty"`
	ServerId          int32  `protobuf:"varint,3,opt,name=ServerId,proto3" json:"ServerId,omitempty"`
}

func (x *KratosServerInfo) Reset() {
	*x = KratosServerInfo{}
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KratosServerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KratosServerInfo) ProtoMessage() {}

func (x *KratosServerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KratosServerInfo.ProtoReflect.Descriptor instead.
func (*KratosServerInfo) Descriptor() ([]byte, []int) {
	return file_general_services_serverlist_v1_serverlist_proto_rawDescGZIP(), []int{1}
}

func (x *KratosServerInfo) GetServerPlayerCount() string {
	if x != nil {
		return x.ServerPlayerCount
	}
	return ""
}

func (x *KratosServerInfo) GetServerStatus() string {
	if x != nil {
		return x.ServerStatus
	}
	return ""
}

func (x *KratosServerInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

type ServerListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// string BoNo = 1;
	// string Expand = 2;
	Ip         string `protobuf:"bytes,3,opt,name=Ip,proto3" json:"Ip,omitempty"`      // 登录服IP地址（兼容旧字段，实际对应loginIp）
	Port       int32  `protobuf:"varint,4,opt,name=Port,proto3" json:"Port,omitempty"` // 登录服端口（兼容旧字段，实际对应loginPort）
	Recommend  int32  `protobuf:"varint,5,opt,name=Recommend,proto3" json:"Recommend,omitempty"`
	ServerId   int32  `protobuf:"varint,6,opt,name=ServerId,proto3" json:"ServerId,omitempty"`
	ServerName string `protobuf:"bytes,7,opt,name=ServerName,proto3" json:"ServerName,omitempty"`
	// string ServerSep = 8;
	Status      int32 `protobuf:"varint,9,opt,name=Status,proto3" json:"Status,omitempty"` // 服务器状态：1=流畅，2=拥挤，3=爆满，4=未上线，5=维护
	ServerBigId int32 `protobuf:"varint,10,opt,name=ServerBigId,proto3" json:"ServerBigId,omitempty"`
	// string TargetPlatformId = 11;
	// string MaxPlayerCount = 12;
	// string LoginNewPlayer = 13;
	ServerInfo *KratosServerInfo `protobuf:"bytes,14,opt,name=ServerInfo,proto3" json:"ServerInfo,omitempty"`
	// string GameServerIp = 15;     // 游戏服IP地址
	// string GameServerPort = 16;   // 游戏服端口
	ServerOpenTime int64 `protobuf:"varint,17,opt,name=ServerOpenTime,proto3" json:"ServerOpenTime,omitempty"` // 开服时间（Unix时间戳）
	IsNew          int32 `protobuf:"varint,18,opt,name=IsNew,proto3" json:"IsNew,omitempty"`                   // 是否新服
}

func (x *ServerListData) Reset() {
	*x = ServerListData{}
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerListData) ProtoMessage() {}

func (x *ServerListData) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerListData.ProtoReflect.Descriptor instead.
func (*ServerListData) Descriptor() ([]byte, []int) {
	return file_general_services_serverlist_v1_serverlist_proto_rawDescGZIP(), []int{2}
}

func (x *ServerListData) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ServerListData) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ServerListData) GetRecommend() int32 {
	if x != nil {
		return x.Recommend
	}
	return 0
}

func (x *ServerListData) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *ServerListData) GetServerName() string {
	if x != nil {
		return x.ServerName
	}
	return ""
}

func (x *ServerListData) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ServerListData) GetServerBigId() int32 {
	if x != nil {
		return x.ServerBigId
	}
	return 0
}

func (x *ServerListData) GetServerInfo() *KratosServerInfo {
	if x != nil {
		return x.ServerInfo
	}
	return nil
}

func (x *ServerListData) GetServerOpenTime() int64 {
	if x != nil {
		return x.ServerOpenTime
	}
	return 0
}

func (x *ServerListData) GetIsNew() int32 {
	if x != nil {
		return x.IsNew
	}
	return 0
}

type GetServerListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result         int32             `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	Serverlistdata []*ServerListData `protobuf:"bytes,2,rep,name=serverlistdata,proto3" json:"serverlistdata,omitempty"`
}

func (x *GetServerListReply) Reset() {
	*x = GetServerListReply{}
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServerListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServerListReply) ProtoMessage() {}

func (x *GetServerListReply) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServerListReply.ProtoReflect.Descriptor instead.
func (*GetServerListReply) Descriptor() ([]byte, []int) {
	return file_general_services_serverlist_v1_serverlist_proto_rawDescGZIP(), []int{3}
}

func (x *GetServerListReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GetServerListReply) GetServerlistdata() []*ServerListData {
	if x != nil {
		return x.Serverlistdata
	}
	return nil
}

type GetIsWhitelistReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Addrip   string `protobuf:"bytes,1,opt,name=addrip,proto3" json:"addrip,omitempty"`
	Serverid string `protobuf:"bytes,2,opt,name=serverid,proto3" json:"serverid,omitempty"`
}

func (x *GetIsWhitelistReq) Reset() {
	*x = GetIsWhitelistReq{}
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIsWhitelistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIsWhitelistReq) ProtoMessage() {}

func (x *GetIsWhitelistReq) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIsWhitelistReq.ProtoReflect.Descriptor instead.
func (*GetIsWhitelistReq) Descriptor() ([]byte, []int) {
	return file_general_services_serverlist_v1_serverlist_proto_rawDescGZIP(), []int{4}
}

func (x *GetIsWhitelistReq) GetAddrip() string {
	if x != nil {
		return x.Addrip
	}
	return ""
}

func (x *GetIsWhitelistReq) GetServerid() string {
	if x != nil {
		return x.Serverid
	}
	return ""
}

type GetIsWhitelistReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Whiteresult int32 `protobuf:"varint,1,opt,name=whiteresult,proto3" json:"whiteresult,omitempty"`
	Serverstate int32 `protobuf:"varint,2,opt,name=serverstate,proto3" json:"serverstate,omitempty"`
}

func (x *GetIsWhitelistReply) Reset() {
	*x = GetIsWhitelistReply{}
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIsWhitelistReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIsWhitelistReply) ProtoMessage() {}

func (x *GetIsWhitelistReply) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_serverlist_v1_serverlist_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIsWhitelistReply.ProtoReflect.Descriptor instead.
func (*GetIsWhitelistReply) Descriptor() ([]byte, []int) {
	return file_general_services_serverlist_v1_serverlist_proto_rawDescGZIP(), []int{5}
}

func (x *GetIsWhitelistReply) GetWhiteresult() int32 {
	if x != nil {
		return x.Whiteresult
	}
	return 0
}

func (x *GetIsWhitelistReply) GetServerstate() int32 {
	if x != nil {
		return x.Serverstate
	}
	return 0
}

var File_general_services_serverlist_v1_serverlist_proto protoreflect.FileDescriptor

var file_general_services_serverlist_v1_serverlist_proto_rawDesc = []byte{
	0x0a, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76, 0x31,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3e,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x2a, 0x0a, 0x10, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74,
	0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x22, 0x80,
	0x01, 0x0a, 0x10, 0x4b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x2c, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x22, 0x0a, 0x0c, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49,
	0x64, 0x22, 0xc7, 0x02, 0x0a, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x49, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x50, 0x6f, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x52, 0x65, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x52, 0x65, 0x63,
	0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x42, 0x69, 0x67, 0x49, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x42, 0x69, 0x67, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0a,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x4b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a,
	0x0e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4f, 0x70, 0x65, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4f, 0x70, 0x65,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x49, 0x73, 0x4e, 0x65, 0x77, 0x18, 0x12,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x49, 0x73, 0x4e, 0x65, 0x77, 0x22, 0x73, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x45, 0x0a, 0x0e, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61,
	0x22, 0x47, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x49, 0x73, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x64, 0x64, 0x72, 0x69, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x64, 0x64, 0x72, 0x69, 0x70, 0x12, 0x1a, 0x0a,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x69, 0x64, 0x22, 0x59, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x49, 0x73, 0x57, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x20, 0x0a, 0x0b, 0x77, 0x68, 0x69, 0x74, 0x65, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x77, 0x68, 0x69, 0x74, 0x65, 0x72, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x32, 0xd4, 0x03, 0x0a, 0x0a, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0x9c, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x43, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x3d, 0x12, 0x3b, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69,
	0x73, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73,
	0x74, 0x2f, 0x67, 0x65, 0x74, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f,
	0x7b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x49,
	0x64, 0x7d, 0x12, 0x9d, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x49, 0x73, 0x57, 0x68, 0x69, 0x74,
	0x65, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69,
	0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x73, 0x57, 0x68, 0x69, 0x74, 0x65,
	0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x73, 0x57, 0x68, 0x69,
	0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x45, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x3f, 0x12, 0x3d, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f,
	0x67, 0x65, 0x74, 0x69, 0x73, 0x77, 0x68, 0x69, 0x74, 0x65, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x7b,
	0x61, 0x64, 0x64, 0x72, 0x69, 0x70, 0x7d, 0x2f, 0x7b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x69,
	0x64, 0x7d, 0x12, 0x86, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69,
	0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4b, 0x72, 0x61, 0x74, 0x6f, 0x73, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x33, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2d, 0x3a, 0x01,
	0x2a, 0x22, 0x28, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x73, 0x65,
	0x74, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x21, 0x5a, 0x1f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_general_services_serverlist_v1_serverlist_proto_rawDescOnce sync.Once
	file_general_services_serverlist_v1_serverlist_proto_rawDescData = file_general_services_serverlist_v1_serverlist_proto_rawDesc
)

func file_general_services_serverlist_v1_serverlist_proto_rawDescGZIP() []byte {
	file_general_services_serverlist_v1_serverlist_proto_rawDescOnce.Do(func() {
		file_general_services_serverlist_v1_serverlist_proto_rawDescData = protoimpl.X.CompressGZIP(file_general_services_serverlist_v1_serverlist_proto_rawDescData)
	})
	return file_general_services_serverlist_v1_serverlist_proto_rawDescData
}

var file_general_services_serverlist_v1_serverlist_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_general_services_serverlist_v1_serverlist_proto_goTypes = []any{
	(*GetServerListReq)(nil),    // 0: serverlist.v1.GetServerListReq
	(*KratosServerInfo)(nil),    // 1: serverlist.v1.KratosServerInfo
	(*ServerListData)(nil),      // 2: serverlist.v1.ServerListData
	(*GetServerListReply)(nil),  // 3: serverlist.v1.GetServerListReply
	(*GetIsWhitelistReq)(nil),   // 4: serverlist.v1.GetIsWhitelistReq
	(*GetIsWhitelistReply)(nil), // 5: serverlist.v1.GetIsWhitelistReply
}
var file_general_services_serverlist_v1_serverlist_proto_depIdxs = []int32{
	1, // 0: serverlist.v1.ServerListData.ServerInfo:type_name -> serverlist.v1.KratosServerInfo
	2, // 1: serverlist.v1.GetServerListReply.serverlistdata:type_name -> serverlist.v1.ServerListData
	0, // 2: serverlist.v1.Serverlist.GetServerListData:input_type -> serverlist.v1.GetServerListReq
	4, // 3: serverlist.v1.Serverlist.GetIsWhitelist:input_type -> serverlist.v1.GetIsWhitelistReq
	1, // 4: serverlist.v1.Serverlist.SetServerInfo:input_type -> serverlist.v1.KratosServerInfo
	3, // 5: serverlist.v1.Serverlist.GetServerListData:output_type -> serverlist.v1.GetServerListReply
	5, // 6: serverlist.v1.Serverlist.GetIsWhitelist:output_type -> serverlist.v1.GetIsWhitelistReply
	1, // 7: serverlist.v1.Serverlist.SetServerInfo:output_type -> serverlist.v1.KratosServerInfo
	5, // [5:8] is the sub-list for method output_type
	2, // [2:5] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_general_services_serverlist_v1_serverlist_proto_init() }
func file_general_services_serverlist_v1_serverlist_proto_init() {
	if File_general_services_serverlist_v1_serverlist_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_general_services_serverlist_v1_serverlist_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_general_services_serverlist_v1_serverlist_proto_goTypes,
		DependencyIndexes: file_general_services_serverlist_v1_serverlist_proto_depIdxs,
		MessageInfos:      file_general_services_serverlist_v1_serverlist_proto_msgTypes,
	}.Build()
	File_general_services_serverlist_v1_serverlist_proto = out.File
	file_general_services_serverlist_v1_serverlist_proto_rawDesc = nil
	file_general_services_serverlist_v1_serverlist_proto_goTypes = nil
	file_general_services_serverlist_v1_serverlist_proto_depIdxs = nil
}
