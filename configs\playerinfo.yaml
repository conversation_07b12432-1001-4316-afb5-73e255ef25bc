server:
  http:
    addr: 10.12.17.119:8009
    timeout: 25
  grpc:
    addr: 10.12.17.119:9009
    timeout: 25

data:
  redis:
    addr: 10.1.8.64:6379
    passwd: ""
    read_timeout: 10
    write_timeout: 10
  log:
    level: -1       # 日志级别: -1=DEBUG, 0=INFO, 1=WARN, 2=ERROR, 3=DPanic, 4=Panic, 5=Fatal
    console: true   # 是否输出到控制台

registry:
  etcd:
    endpoints: ["10.1.8.64:2379"]
    username: ""
    password: ""
    timeout: 5
    prefix: "/weibotao"