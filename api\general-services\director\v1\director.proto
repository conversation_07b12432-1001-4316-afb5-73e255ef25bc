//
// @Author:zhouchen
// @Description: 消息包文件定义
// @Data: Created in 20:01 2023/6/6
syntax = "proto3";

package director.v1;

import "google/api/annotations.proto";

option go_package = "director/api/director/v1;v1";

// The relation service definition.
service Director {
  rpc GetDirectorData (GetDirectorReq) returns (GetDirectorReply) {
    option (google.api.http) = {
      get : "/api/director/getdirector/{platform}/{channel}/{version}",
    };
  }
}

message GetDirectorReq {
  string platform=1;
  string channel=2;
  string version=3;
}

message  DirectorData
{
  string serverListUrl = 1;
  string announceInfoUrl = 3;
  string apUrl = 4;
  string resUrl = 5;
  string resVersionMd5 = 6;
  string serverVersion = 7;
}

message GetDirectorReply {
  int32 result = 1;
  DirectorData data = 2;
}
