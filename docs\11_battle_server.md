## 战斗服

1. 战斗服目前有两种方案，一种是go，一种是纯.net，两种方案都是用nats和游戏服交互，核心目的是和客户端共用战斗逻辑代码，具体使用哪种方案可以具体分析。

2. 建议：如果客户端是csharp，使用纯csharp方案，将战斗和玩家数据解耦，把战斗数据通过rpc发给战斗服。如果战斗内容对玩家数据依赖过大，或战斗数据太大，可考虑使用go方案，直接由战斗服加载数据库。如果客户端战斗是lua，建议使用go+lua的方式。

   

#### go+dll方案

目录：Server/tools/battle_adapter

使用go的nats生成，通过nativeAot编译出的battle_adapter工程，实现go调用客户端工程编译出的Battle.dll。

从客户端打出Temp\Bin\Debug\Battle.dll包后，引用到battle_adapter工程，使用nativeAOT发布，将发布好的\native\BattleAdapter.dll放到./res/dll下，使用go调用

优点：共用服务器代码，可以省去数据库相关开发，可以接入lua

缺点：go调用nativeaot的dll性能未知



#### csharp方案

目录：Server/BattleServer

使用csharp的nats生成，共用客户端战斗代码，使用.net原生跨平台发布。

优点：无跨语言调用

缺点：需要二次开发，如读表，数据库等。