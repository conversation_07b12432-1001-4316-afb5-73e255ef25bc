//GrpcAddressType:Rank
//GrpcServerType:server,world

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.23.2
// source: microservices/rank/v1/rank.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RankService_SyncRoleInfo_FullMethodName       = "/Aurora.PlayerInfoServer.RankService/SyncRoleInfo"
	RankService_UpdateRoleRankInfo_FullMethodName = "/Aurora.PlayerInfoServer.RankService/UpdateRoleRankInfo"
	RankService_RemoveRole_FullMethodName         = "/Aurora.PlayerInfoServer.RankService/RemoveRole"
)

// RankServiceClient is the client API for RankService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ServiceStart
type RankServiceClient interface {
	SyncRoleInfo(ctx context.Context, in *SyncRoleInfoRequest, opts ...grpc.CallOption) (*SyncRoleInfoReply, error)
	UpdateRoleRankInfo(ctx context.Context, in *UpdateRoleRankRequest, opts ...grpc.CallOption) (*UpdateRoleRankReply, error)
	RemoveRole(ctx context.Context, in *RemoveRoleRequest, opts ...grpc.CallOption) (*RemoveRoleReply, error)
}

type rankServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewRankServiceClient(cc grpc.ClientConnInterface) RankServiceClient {
	return &rankServiceClient{cc}
}

func (c *rankServiceClient) SyncRoleInfo(ctx context.Context, in *SyncRoleInfoRequest, opts ...grpc.CallOption) (*SyncRoleInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncRoleInfoReply)
	err := c.cc.Invoke(ctx, RankService_SyncRoleInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) UpdateRoleRankInfo(ctx context.Context, in *UpdateRoleRankRequest, opts ...grpc.CallOption) (*UpdateRoleRankReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRoleRankReply)
	err := c.cc.Invoke(ctx, RankService_UpdateRoleRankInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *rankServiceClient) RemoveRole(ctx context.Context, in *RemoveRoleRequest, opts ...grpc.CallOption) (*RemoveRoleReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RemoveRoleReply)
	err := c.cc.Invoke(ctx, RankService_RemoveRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RankServiceServer is the server API for RankService service.
// All implementations must embed UnimplementedRankServiceServer
// for forward compatibility.
//
// ServiceStart
type RankServiceServer interface {
	SyncRoleInfo(context.Context, *SyncRoleInfoRequest) (*SyncRoleInfoReply, error)
	UpdateRoleRankInfo(context.Context, *UpdateRoleRankRequest) (*UpdateRoleRankReply, error)
	RemoveRole(context.Context, *RemoveRoleRequest) (*RemoveRoleReply, error)
	mustEmbedUnimplementedRankServiceServer()
}

// UnimplementedRankServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRankServiceServer struct{}

func (UnimplementedRankServiceServer) SyncRoleInfo(context.Context, *SyncRoleInfoRequest) (*SyncRoleInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncRoleInfo not implemented")
}
func (UnimplementedRankServiceServer) UpdateRoleRankInfo(context.Context, *UpdateRoleRankRequest) (*UpdateRoleRankReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRoleRankInfo not implemented")
}
func (UnimplementedRankServiceServer) RemoveRole(context.Context, *RemoveRoleRequest) (*RemoveRoleReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveRole not implemented")
}
func (UnimplementedRankServiceServer) mustEmbedUnimplementedRankServiceServer() {}
func (UnimplementedRankServiceServer) testEmbeddedByValue()                     {}

// UnsafeRankServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RankServiceServer will
// result in compilation errors.
type UnsafeRankServiceServer interface {
	mustEmbedUnimplementedRankServiceServer()
}

func RegisterRankServiceServer(s grpc.ServiceRegistrar, srv RankServiceServer) {
	// If the following call pancis, it indicates UnimplementedRankServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RankService_ServiceDesc, srv)
}

func _RankService_SyncRoleInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncRoleInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).SyncRoleInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_SyncRoleInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).SyncRoleInfo(ctx, req.(*SyncRoleInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_UpdateRoleRankInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRoleRankRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).UpdateRoleRankInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_UpdateRoleRankInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).UpdateRoleRankInfo(ctx, req.(*UpdateRoleRankRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RankService_RemoveRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RemoveRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankServiceServer).RemoveRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankService_RemoveRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankServiceServer).RemoveRole(ctx, req.(*RemoveRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RankService_ServiceDesc is the grpc.ServiceDesc for RankService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RankService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Aurora.PlayerInfoServer.RankService",
	HandlerType: (*RankServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncRoleInfo",
			Handler:    _RankService_SyncRoleInfo_Handler,
		},
		{
			MethodName: "UpdateRoleRankInfo",
			Handler:    _RankService_UpdateRoleRankInfo_Handler,
		},
		{
			MethodName: "RemoveRole",
			Handler:    _RankService_RemoveRole_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "microservices/rank/v1/rank.proto",
}

const (
	RankListHttp_GetRankListData_FullMethodName = "/Aurora.PlayerInfoServer.RankListHttp/GetRankListData"
)

// RankListHttpClient is the client API for RankListHttp service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RankListHttpClient interface {
	GetRankListData(ctx context.Context, in *GetRankListReq, opts ...grpc.CallOption) (*GetRankListReply, error)
}

type rankListHttpClient struct {
	cc grpc.ClientConnInterface
}

func NewRankListHttpClient(cc grpc.ClientConnInterface) RankListHttpClient {
	return &rankListHttpClient{cc}
}

func (c *rankListHttpClient) GetRankListData(ctx context.Context, in *GetRankListReq, opts ...grpc.CallOption) (*GetRankListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRankListReply)
	err := c.cc.Invoke(ctx, RankListHttp_GetRankListData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RankListHttpServer is the server API for RankListHttp service.
// All implementations must embed UnimplementedRankListHttpServer
// for forward compatibility.
type RankListHttpServer interface {
	GetRankListData(context.Context, *GetRankListReq) (*GetRankListReply, error)
	mustEmbedUnimplementedRankListHttpServer()
}

// UnimplementedRankListHttpServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRankListHttpServer struct{}

func (UnimplementedRankListHttpServer) GetRankListData(context.Context, *GetRankListReq) (*GetRankListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRankListData not implemented")
}
func (UnimplementedRankListHttpServer) mustEmbedUnimplementedRankListHttpServer() {}
func (UnimplementedRankListHttpServer) testEmbeddedByValue()                      {}

// UnsafeRankListHttpServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RankListHttpServer will
// result in compilation errors.
type UnsafeRankListHttpServer interface {
	mustEmbedUnimplementedRankListHttpServer()
}

func RegisterRankListHttpServer(s grpc.ServiceRegistrar, srv RankListHttpServer) {
	// If the following call pancis, it indicates UnimplementedRankListHttpServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RankListHttp_ServiceDesc, srv)
}

func _RankListHttp_GetRankListData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRankListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RankListHttpServer).GetRankListData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RankListHttp_GetRankListData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RankListHttpServer).GetRankListData(ctx, req.(*GetRankListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// RankListHttp_ServiceDesc is the grpc.ServiceDesc for RankListHttp service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RankListHttp_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Aurora.PlayerInfoServer.RankListHttp",
	HandlerType: (*RankListHttpServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetRankListData",
			Handler:    _RankListHttp_GetRankListData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "microservices/rank/v1/rank.proto",
}
