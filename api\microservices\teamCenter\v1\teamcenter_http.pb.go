// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v4.23.2
// source: v1/teamcenter.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationTeamCenterServiceTCAskTeamInfoByPlayerGuid = "/Aurora.TeamCenter.TeamCenterService/TCAskTeamInfoByPlayerGuid"
const OperationTeamCenterServiceTCAskTeamInfoByTeamId = "/Aurora.TeamCenter.TeamCenterService/TCAskTeamInfoByTeamId"

type TeamCenterServiceHTTPServer interface {
	// TCAskTeamInfoByPlayerGuid 通过玩家guid，查询队伍信息
	TCAskTeamInfoByPlayerGuid(context.Context, *TCAskTeamInfoByPlayerGuidRequest) (*TCTeamInfoReply, error)
	// TCAskTeamInfoByTeamId 通过队伍ID，查询队伍信息
	TCAskTeamInfoByTeamId(context.Context, *TCAskTeamInfoByTeamIdRequest) (*TCTeamInfoReply, error)
}

func RegisterTeamCenterServiceHTTPServer(s *http.Server, srv TeamCenterServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/Teaminfo/ByTeamId", _TeamCenterService_TCAskTeamInfoByTeamId0_HTTP_Handler(srv))
	r.POST("/v1/Teaminfo/ByPlayerGuid", _TeamCenterService_TCAskTeamInfoByPlayerGuid0_HTTP_Handler(srv))
}

func _TeamCenterService_TCAskTeamInfoByTeamId0_HTTP_Handler(srv TeamCenterServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TCAskTeamInfoByTeamIdRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamCenterServiceTCAskTeamInfoByTeamId)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TCAskTeamInfoByTeamId(ctx, req.(*TCAskTeamInfoByTeamIdRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TCTeamInfoReply)
		return ctx.Result(200, reply)
	}
}

func _TeamCenterService_TCAskTeamInfoByPlayerGuid0_HTTP_Handler(srv TeamCenterServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in TCAskTeamInfoByPlayerGuidRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationTeamCenterServiceTCAskTeamInfoByPlayerGuid)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.TCAskTeamInfoByPlayerGuid(ctx, req.(*TCAskTeamInfoByPlayerGuidRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*TCTeamInfoReply)
		return ctx.Result(200, reply)
	}
}

type TeamCenterServiceHTTPClient interface {
	TCAskTeamInfoByPlayerGuid(ctx context.Context, req *TCAskTeamInfoByPlayerGuidRequest, opts ...http.CallOption) (rsp *TCTeamInfoReply, err error)
	TCAskTeamInfoByTeamId(ctx context.Context, req *TCAskTeamInfoByTeamIdRequest, opts ...http.CallOption) (rsp *TCTeamInfoReply, err error)
}

type TeamCenterServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewTeamCenterServiceHTTPClient(client *http.Client) TeamCenterServiceHTTPClient {
	return &TeamCenterServiceHTTPClientImpl{client}
}

func (c *TeamCenterServiceHTTPClientImpl) TCAskTeamInfoByPlayerGuid(ctx context.Context, in *TCAskTeamInfoByPlayerGuidRequest, opts ...http.CallOption) (*TCTeamInfoReply, error) {
	var out TCTeamInfoReply
	pattern := "/v1/Teaminfo/ByPlayerGuid"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTeamCenterServiceTCAskTeamInfoByPlayerGuid))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *TeamCenterServiceHTTPClientImpl) TCAskTeamInfoByTeamId(ctx context.Context, in *TCAskTeamInfoByTeamIdRequest, opts ...http.CallOption) (*TCTeamInfoReply, error) {
	var out TCTeamInfoReply
	pattern := "/v1/Teaminfo/ByTeamId"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationTeamCenterServiceTCAskTeamInfoByTeamId))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
