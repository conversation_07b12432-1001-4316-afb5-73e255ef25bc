// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"liteframe/internal/microservices/playerinfo/biz"
	"liteframe/internal/microservices/playerinfo/conf"
	"liteframe/internal/microservices/playerinfo/data"
	"liteframe/internal/microservices/playerinfo/registry"
	"liteframe/internal/microservices/playerinfo/server"
	"liteframe/internal/microservices/playerinfo/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init playerinfoserver application.
func wireApp(confServer *conf.Server, confData *conf.Data, bootstrap *conf.Bootstrap, logger log.Logger, arg map[string]string) (*kratos.App, func(), error) {
	cmdable := data.NewRedisCmd(confData, logger)
	dataData, err := data.NewData(cmdable, logger)
	if err != nil {
		return nil, nil, err
	}
	playerInfoServiceRepo := data.NewPlayerInfoServiceRepo(dataData, logger)
	playerInfoServiceUsecase := biz.NewPlayerInfoServiceUsecase(playerInfoServiceRepo, logger)
	playerInfoService := service.NewPlayerInfoService(playerInfoServiceUsecase)
	grpcServer := server.NewGRPCServer(confServer, playerInfoService, logger)
	httpServer := server.NewHTTPServer(confServer, playerInfoService, logger)
	confRegistry := registry.ProvideRegistry(bootstrap)
	etcdRegistry := registry.NewEtcdRegistry(confRegistry)
	app := newApp(logger, grpcServer, httpServer, etcdRegistry, arg)
	return app, func() {
	}, nil
}
