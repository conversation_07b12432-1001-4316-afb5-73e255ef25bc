package system

import (
	"context"
	"errors"
	"fmt"
	rankv1 "liteframe/api/microservices/rank/v1"
	"liteframe/internal/common/constant/def"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/internal/game-logic/gameserver/global"
	"math/rand"
	"time"

	playerInfov1 "liteframe/api/microservices/playerinfo/v1"
	"liteframe/internal/game-logic/gameserver/gameutil/metrics"
	"liteframe/internal/game-logic/gameserver/logic/system/dao/dbhelper"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"

	"google.golang.org/protobuf/proto"
)

const (
	DailyMaxJoinCountPerGuild = 50 // 公会每日最大招收成员数量 TODO: 从配置读取
)

type GuildSystem struct {
	dispatch *actor.Dispatcher
	guildDB  *dbstruct.GuildDB
}

func NewGuildSystem() *GuildSystem {
	gs := &GuildSystem{
		dispatch: actor.NewDispatcher(100, func(uid uint64, id uint32, ms int64) {
			metrics.SetRpcCost(fmt.Sprintf("guilds_%d", id), ms)
			if ms > 100 {
				log.Debug("guild system", log.Kv("id", id), log.Kv("cost", ms))
			}
		}),
		guildDB: &dbstruct.GuildDB{
			GuildDataMap: make(map[int64]*dbstruct.GuildData),
		},
	}
	gs.registerHandler()
	return gs
}

func (gs *GuildSystem) PID() actor.PID {
	return actor_def.GuildSystemPID
}

func (gs *GuildSystem) Process(msg *actor.Message) {
	if err := gs.dispatch.Dispatch(context.Background(), msg); err != nil {
		log.Error("guild system dispatch failed", log.Err(err), log.Kv("msgId", msg.Id))
	}
}

func (gs *GuildSystem) OnStop() {
	log.Info("guild system stop")
}

func (gs *GuildSystem) registerHandler() {
	gs.dispatch.Register(uint32(def.MsgId_Actor_Player2GuildSystem), gs.playerMessageHandler)
	gs.dispatch.Register(uint32(def.MsgId_Actor_PlayerSystem2GuildSystem), gs.playerSystemMessageHandler)
	gs.dispatch.Register(uint32(def.MsgId_Actor_Second), gs.serverTickHandler)
}

func (gs *GuildSystem) playerMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		return errors.New("msg nil")
	}
	if msg.Data == nil {
		return errors.New("data nil")
	}
	switch msg.Data.(type) {
	case *cs.P2GCreateGuild:
		gs.handleCreateGuild(msg)
	//case *cs.P2GJoinGuild:
	//	gs.handleJoinGuild(msg)
	case *cs.P2GLeaveGuild:
		gs.handleLeaveGuild(msg)
	case *cs.P2GDismissGuildReq:
		gs.handleDismissGuild(msg)
	case *cs.P2GGetGuildDetail:
		gs.handleGetGuildDetail(msg)
	case *cs.P2GGetRecommendList:
		gs.handleGetRecommendList(msg)
	case *cs.P2GFastJoinGuild:
		gs.handleFastJoinGuild(msg)
	case *cs.P2GApplyJoinGuild:
		gs.handleApplyJoinGuild(msg)
	case *cs.P2GEditGuildInfo:
		gs.handleEditGuildInfo(msg)
	case *cs.P2GGetApplyList:
		gs.handleGetApplyList(msg)
	case *cs.P2GProcessApplication:
		gs.handleProcessApplication(msg)
	case *cs.P2GMemberAction:
		gs.handleMemberAction(msg)
	case *cs.P2GGuildDonate:
		gs.handleGuildDonate(msg)
	case *cs.P2GGMUpdateGuildAttrs:
		gs.handleGMUpdateGuildAttrs(msg)
	default:
		log.Warn("Invalid guild system message type", log.Kv("type", fmt.Sprintf("%T", msg.Data)))
		return errors.New("invalid message type")
	}
	return nil
}

func (gs *GuildSystem) playerSystemMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		return errors.New("msg nil")
	}
	if msg.Data == nil {
		return errors.New("data nil")
	}
	switch msg.Data.(type) {
	case *cs.PS2GUpdateGuildMembersRsp:
		gs.handleUpdateGuildMembersRsp(msg.Data.(*cs.PS2GUpdateGuildMembersRsp))
	default:
		return fmt.Errorf("unknown message type: %T", msg.Data)
	}
	return nil
}

func (gs *GuildSystem) serverTickHandler(ctx context.Context, msg *actor.Message) error {
	// 每日凌晨5点重置公会入盟计数
	now := time.Now()
	currentTime := time.Date(now.Year(), now.Month(), now.Day(), 5, 0, 0, 0, now.Location())

	// 遍历所有公会，检查是否需要重置入盟计数
	for _, guild := range gs.guildDB.GuildDataMap {
		// 如果没有上次重置时间或者上次重置时间早于今天凌晨5点，则进行重置
		if guild.LastJoinCountResetTime == 0 || time.Unix(guild.LastJoinCountResetTime, 0).Before(currentTime) {
			// 重置公会今日入盟计数
			guild.TodayJoinedCount = 0
			guild.LastJoinCountResetTime = now.Unix()

			log.Debug("reset guild daily join count", log.Kv("guildId", guild.GuildId), log.Kv("guildName", guild.Name))

			// 保存公会数据
			dbhelper.SaveGuildData(gs.PID(), guild.GuildId, gs.guildDB, func(err error) {
				if err != nil {
					log.Error("save guild failed when reset daily join count", log.Kv("guild_id", guild.GuildId), log.Err(err))
				}
			})
		}
	}

	return nil
}

// LoadGuildsSync 从DB同步加载公会系统数据
func (gs *GuildSystem) LoadGuildsSync() error {
	data, err := dbhelper.LoadGuildDataSync(gs.PID())
	if err != nil {
		log.Error("load guilds failed", log.Err(err))
		return err
	}

	if data != nil && len(data) > 0 {
		if err := proto.Unmarshal(data, gs.guildDB); err != nil {
			log.Error("unmarshal guild data failed", log.Err(err))
			return err
		}
	}

	log.Info("load guilds success", log.Kv("count", len(gs.guildDB.GuildDataMap)))
	return nil
}

func (gs *GuildSystem) handleCreateGuild(msg *actor.Message) {
	req := msg.Data.(*cs.P2GCreateGuild)
	playerId := msg.Uid

	if gs.isPlayerInGuild(playerId) {
		if err := gs.sendCreateGuildResponse(msg.From(), false, 0, req.Name, 0); err != nil {
			log.Error("send create guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			return
		}
		log.Error("player already in guild", log.Kv("playerId", playerId))
		return
	}

	guildId := gs.generateGuildId()
	// 验证公会名称
	if len(req.Name) == 0 || len(req.Name) > 20 { // 假设公会名称长度限制为20
		if err := gs.sendCreateGuildResponse(msg.From(), false, 0, req.Name, 0); err != nil {
			log.Error("send create guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		log.Error("invalid guild name", log.Kv("playerId", playerId), log.Kv("name", req.Name))
		return
	}

	newGuild := &dbstruct.GuildData{
		GuildId:                guildId,
		Name:                   req.Name,
		LeaderId:               playerId,
		Level:                  1,
		MemberCount:            1,
		MaxMember:              50,
		CreateTime:             time.Now().Unix(),
		Members:                make(map[uint64]*dbstruct.GuildMember),
		Applications:           make(map[uint64]*dbstruct.GuildApplicationEntry),
		TodayJoinedCount:       1, // 创建者算作第一个加入的成员
		LastJoinCountResetTime: time.Now().Unix(),
		FreeJoin:               req.FreeJoin,
		ReqStage:               req.ReqStage,
		IconId:                 req.IconId,
		Announcement:           req.Announcement,
		Notice:                 req.Notice,
	}

	newGuild.Members[playerId] = &dbstruct.GuildMember{
		PlayerId:     playerId,
		Position:     public.GuildPosition_GuildPosition_President, // 使用枚举值
		JoinTime:     time.Now().Unix(),
		Contribution: 0,
	}

	gs.guildDB.GuildDataMap[guildId] = newGuild

	// 异步保存单个公会数据
	dbhelper.SaveGuildData(gs.PID(), guildId, gs.guildDB, func(err error) {
		if err != nil {
			log.Error("save guild failed", log.Kv("guild_id", guildId), log.Err(err))
		}
	})

	// 同步公会基本信息到排行榜服务
	gs.SyncGuildInfo(newGuild)
	// 初始化公会排行信息
	gs.UpdateAllGuildRankInfo(newGuild)

	if err := gs.sendCreateGuildResponse(msg.From(), true, guildId, req.Name, int32(public.GuildPosition_GuildPosition_President)); err != nil {
		log.Error("send create guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		return
	}
}

//func (gs *GuildSystem) handleJoinGuild(msg *actor.Message) {
//	req := msg.Data.(*cs.P2GJoinGuild)
//	playerId := msg.Uid
//
//	if gs.isPlayerInGuild(playerId) {
//		if err := gs.sendJoinGuildResponse(msg.From(), false, 0, "", 0); err != nil {
//			log.Error("send join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
//			return
//		}
//		log.Error("player already in guild", log.Kv("playerId", playerId))
//		return
//	}
//
//	guild, exists := gs.guildDB.GuildDataMap[req.GuildId]
//	if !exists {
//		if err := gs.sendJoinGuildResponse(msg.From(), false, 0, "", 0); err != nil {
//			log.Error("send join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
//			return
//		}
//		log.Error("guild not exist", log.Kv("guildId", req.GuildId))
//		return
//	}
//
//	if guild.MemberCount >= guild.MaxMember {
//		if err := gs.sendJoinGuildResponse(msg.From(), false, 0, "", 0); err != nil {
//			log.Error("send join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
//			return
//		}
//		log.Error("guild member count exceed", log.Kv("guildId", req.GuildId), log.Kv("memberCount", guild.MemberCount), log.Kv("maxMember", guild.MaxMember))
//		return
//	}
//
//	// 检查公会今日加入人数是否达到上限
//	if guild.TodayJoinedCount >= DailyMaxJoinCountPerGuild {
//		if err := gs.sendJoinGuildResponse(msg.From(), false, 0, "", 0); err != nil {
//			log.Error("send join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
//			return
//		}
//		log.Error("guild daily join count exceed", log.Kv("guildId", req.GuildId), log.Kv("todayJoinedCount", guild.TodayJoinedCount), log.Kv("maxJoinCount", DailyMaxJoinCountPerGuild))
//		return
//	}
//
//	guild.Members[playerId] = &dbstruct.GuildMember{
//		PlayerId:     playerId,
//		Position:     public.GuildPosition_GuildPosition_Normal, // 使用查举值
//		JoinTime:     time.Now().Unix(),
//		Contribution: 0,
//	}
//	guild.MemberCount++
//
//	// 增加今日公会加入人数
//	guild.TodayJoinedCount++
//	log.Debug("guild today join count increased", log.Kv("guildId", req.GuildId), log.Kv("guildName", guild.Name), log.Kv("todayJoinedCount", guild.TodayJoinedCount))
//
//	dbhelper.SaveGuildData(gs.PID(), guild.GuildId, gs.guildDB, func(err error) {
//		if err != nil {
//			log.Error("save guild failed", log.Kv("guild_id", guild.GuildId), log.Err(err))
//		}
//	})
//
//	if err := gs.sendJoinGuildResponse(msg.From(), true, guild.GuildId, guild.Name, int32(public.GuildPosition_GuildPosition_Normal)); err != nil {
//		log.Error("send join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
//		return
//	}
//}

func (gs *GuildSystem) handleLeaveGuild(msg *actor.Message) {
	req := msg.Data.(*cs.P2GLeaveGuild)
	playerId := msg.Uid

	guild, exists := gs.guildDB.GuildDataMap[req.GuildId]
	if !exists {
		if err := gs.sendLeaveGuildResponse(msg.From(), false); err != nil {
			log.Error("send leave guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			return
		}
		log.Error("guild not exist", log.Kv("guildId", req.GuildId), log.Kv("playerId", playerId))
		return
	}

	// 检查玩家是否在公会中
	member, inGuild := guild.Members[playerId]
	if !inGuild {
		if err := gs.sendLeaveGuildResponse(msg.From(), false); err != nil {
			log.Error("send leave guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			return
		}
		log.Error("player not in this guild", log.Kv("guildId", req.GuildId), log.Kv("playerId", playerId))
		return
	}

	// 检查是否为会长，会长不能退出公会
	if guild.LeaderId == playerId {
		if err := gs.sendLeaveGuildResponse(msg.From(), false); err != nil {
			log.Error("send leave guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			return
		}
		log.Error("guild leader cannot leave guild", log.Kv("guildId", req.GuildId), log.Kv("playerId", playerId), log.Kv("position", member.Position))
		return
	}

	delete(guild.Members, playerId)
	guild.MemberCount--

	dbhelper.SaveGuildData(gs.PID(), guild.GuildId, gs.guildDB, func(err error) {
		if err != nil {
			log.Error("save guild failed", log.Kv("guild_id", guild.GuildId), log.Err(err))
		}
	})

	if err := gs.sendLeaveGuildResponse(msg.From(), true); err != nil {
		log.Error("send leave guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		return
	}
}

func (gs *GuildSystem) handleDismissGuild(msg *actor.Message) {
	req := msg.Data.(*cs.P2GDismissGuildReq)
	playerId := msg.Uid

	// 1. 检查公会是否存在
	guildData, exists := gs.guildDB.GuildDataMap[req.GuildId]
	if !exists || guildData == nil {
		log.Error("guild not exist", log.Kv("guildId", req.GuildId), log.Kv("playerId", playerId))
		if err := gs.sendDismissGuildResponse(msg.From(), uint32(error_code.ERROR_NO_GUILD), req.GuildId); err != nil {
			log.Error("send dismiss guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 2. 检查权限
	if guildData.LeaderId != playerId {
		log.Error("no permission to dismiss guild", log.Kv("guildId", req.GuildId), log.Kv("playerId", playerId), log.Kv("leaderId", guildData.LeaderId))
		if err := gs.sendDismissGuildResponse(msg.From(), uint32(error_code.ERROR_GUILD_NO_PERMISSION), req.GuildId); err != nil {
			log.Error("send dismiss guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 3. 获取所有成员uid列表
	memberUid := make([]uint64, 0, len(guildData.Members))
	for uid := range guildData.Members {
		memberUid = append(memberUid, uid)
	}

	// 4. 通过player system更新所有成员状态
	updateReq := &cs.G2PSUpdateGuildMembersReq{
		GuildId: req.GuildId,
		Uids:    memberUid,
	}
	err := gs.sendToPlayerSystem(updateReq)
	if err != nil {
		log.Error("handleDismissGuild failed to send to player system",
			log.Kv("from", gs.PID()),
			log.Kv("to", actor_def.PlayerSystemPID),
			log.Kv("guildId", req.GuildId),
			log.Err(err))
	}

	// 5. 删除公会数据
	delete(gs.guildDB.GuildDataMap, req.GuildId)
	dbhelper.SaveGuildData(gs.PID(), req.GuildId, gs.guildDB, func(err error) {
		if err != nil {
			log.Error("save guild failed", log.Kv("guild_id", req.GuildId), log.Err(err))
		}
	})

	// 从排行榜中移除公会
	gs.RemoveGuildFromRank(req.GuildId)

	// 6. 回复请求发起者
	if err := gs.sendDismissGuildResponse(msg.From(), uint32(error_code.ERROR_OK), req.GuildId); err != nil {
		log.Error("send dismiss guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		return
	}

	log.Info("guild dismissed successfully", log.Kv("guildId", req.GuildId), log.Kv("playerId", playerId), log.Kv("memberCount", len(memberUid)))
}

func (gs *GuildSystem) sendDismissGuildResponse(to actor.PID, code uint32, guildId int64) error {
	resp := &cs.G2PDismissGuildRsp{
		Errorcode: int32(code),
		GuildId:   guildId,
	}

	err := gs.sendToPlayer(to, resp)
	if err != nil {
		log.Error("send dismiss guild response failed", log.Kv("from", gs.PID()), log.Kv("to", to), log.Err(err))
	}
	return err
}

func (gs *GuildSystem) handleUpdateGuildMembersRsp(resp *cs.PS2GUpdateGuildMembersRsp) {
	if resp.Code != 0 {
		log.Error("handleUpdateGuildMembersRsp failed",
			log.Kv("guildId", resp.GuildId),
			log.Kv("code", resp.Code),
			log.Kv("failedUid", resp.FailedUids))
	}
}

// handleGetGuildDetail 处理获取公会详情的请求
func (gs *GuildSystem) handleGetGuildDetail(msg *actor.Message) {
	req := msg.Data.(*cs.P2GGetGuildDetail)
	playerId := msg.Uid

	// 查找指定公会
	guild, exists := gs.guildDB.GuildDataMap[req.GuildId]
	if !exists {
		// 公会不存在
		if err := gs.sendGetGuildDetailResponse(msg.From(), error_code.ERROR_NO_GUILD, nil, nil, 0, 0); err != nil {
			log.Error("send get guild detail response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		log.Error("guild not exists", log.Kv("guildId", req.GuildId), log.Kv("playerId", playerId))
		return
	}

	// 构建公会详情信息
	guildInfo := &public.PBGuildDetailInfo{
		Name:              guild.Name,
		Level:             guild.Level,
		IconId:            guild.IconId,
		Id:                guild.GuildId,
		Notice:            guild.Notice,
		Announcement:      guild.Announcement,
		PresidentUid:      guild.LeaderId,
		MemberCount:       guild.MemberCount,
		MemberMaxCount:    guild.MaxMember,
		FreeJoin:          guild.FreeJoin,
		ReqStage:          guild.ReqStage,
		TodayJoinedCount:  guild.TodayJoinedCount,
		DailyMaxJoinLimit: DailyMaxJoinCountPerGuild,
		Contribution:      guild.Contribution,
	}

	// 构建成员列表
	memberList := make([]*public.PBGuildMember, 0, len(guild.Members))
	for uid, member := range guild.Members {
		memberList = append(memberList, &public.PBGuildMember{
			PlatformID:   int64(uid),
			Contribution: member.Contribution,
			Gpos:         member.Position,
		})
	}

	// 获取请求者在公会中的职位和贡献
	position := public.GuildPosition(0)
	contribution := int64(0)
	if member, ok := guild.Members[playerId]; ok {
		position = member.Position
		contribution = member.Contribution
		log.Debug("got player guild info", log.Kv("playerId", playerId), log.Kv("guildId", req.GuildId),
			log.Kv("position", position), log.Kv("contribution", contribution))
	} else {
		log.Debug("player not in this guild", log.Kv("playerId", playerId), log.Kv("guildId", req.GuildId))
	}

	// 发送响应
	if err := gs.sendGetGuildDetailResponse(msg.From(), error_code.ERROR_OK, guildInfo, memberList, position, contribution); err != nil {
		log.Error("send get guild detail response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
	}
}

// sendGetGuildDetailResponse 发送获取公会详情的响应
func (gs *GuildSystem) sendGetGuildDetailResponse(to actor.PID, code error_code.Code, guildInfo *public.PBGuildDetailInfo, memberList []*public.PBGuildMember, playerPosition public.GuildPosition, playerContribution int64) error {
	resp := &cs.G2PGetGuildDetailRsp{
		Errorcode:           int32(code),
		GuildInfo:           guildInfo,
		MemberList:          memberList,
		PlayerGuildPosition: playerPosition,
		PlayerContribution:  playerContribution,
	}

	err := actor.Send(gs.PID(), to, &actor.Message{
		Id:   uint32(def.MsgId_Actor_GuildSystem2Player),
		Data: resp,
	})
	if err != nil {
		log.Error("send get guild detail response failed", log.Kv("from", gs.PID()), log.Kv("to", to), log.Err(err))
	}
	return err
}

// handleGetRecommendList 处理获取推荐公会列表的请求
func (gs *GuildSystem) handleGetRecommendList(msg *actor.Message) {
	// 这里忽略请求参数，直接返回符合加入条件的公会列表
	// 筛选可推荐的公会列表
	recommendList := make([]*public.PBGuildRecommend, 0)
	for _, guild := range gs.guildDB.GuildDataMap {
		// 筛选条件：1. 允许加入 2. 人数未满 3. 今日入盟数未超上限
		if guild.MemberCount < guild.MaxMember && guild.TodayJoinedCount < DailyMaxJoinCountPerGuild {
			// 获取会长信息
			baseInfo := gs.getPlayerBaseInfo(guild.LeaderId, int32(global.ServerId))
			if baseInfo == nil {
				log.Warn("failed to get the name of the president.", log.Kv("leaderId", guild.LeaderId))
				continue
			}

			masterName := baseInfo.Name
			log.Info("successfully get the name of the president", log.Kv("name", masterName), log.Kv("leaderId", guild.LeaderId))

			// 添加到推荐列表
			recommendList = append(recommendList, &public.PBGuildRecommend{
				Id:                guild.GuildId,
				Name:              guild.Name,
				IconId:            guild.IconId,
				Level:             guild.Level,
				FreeJoin:          guild.FreeJoin,
				MemberCount:       guild.MemberCount,
				MemberMaxCount:    guild.MaxMember,
				ReqStage:          guild.ReqStage,
				PresidentNickName: masterName,
				HasApply:          false,
			})
		}
	}

	// 随机打乱列表
	rand.Shuffle(len(recommendList), func(i, j int) {
		recommendList[i], recommendList[j] = recommendList[j], recommendList[i]
	})

	// 发送响应
	if err := gs.sendGetRecommendListResponse(msg.From(), error_code.ERROR_OK, recommendList); err != nil {
		log.Error("send get recommend list response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
	}
}

// sendGetRecommendListResponse 发送获取推荐公会列表的响应
func (gs *GuildSystem) sendGetRecommendListResponse(to actor.PID, code error_code.Code, guildList []*public.PBGuildRecommend) error {
	resp := &cs.G2PGetRecommendListRsp{
		Errorcode: int32(code),
		GuildList: guildList,
	}

	err := gs.sendToPlayer(to, resp)
	if err != nil {
		log.Error("send get recommend list response failed", log.Kv("from", gs.PID()), log.Kv("to", to), log.Err(err))
	}
	return err
}

func (gs *GuildSystem) isPlayerInGuild(playerId uint64) bool {
	for _, guild := range gs.guildDB.GuildDataMap {
		if _, exists := guild.Members[playerId]; exists {
			return true
		}
	}
	return false
}

func (gs *GuildSystem) generateGuildId() int64 {
	return rand.Int63()
}

func (gs *GuildSystem) sendCreateGuildResponse(to actor.PID, success bool, guildId int64, name string, position int32) error {
	resp := &cs.G2PGuildCreated{
		Success:  success,
		GuildId:  guildId,
		Name:     name,
		Position: position,
	}

	err := actor.Send(gs.PID(), to, &actor.Message{
		Id:   uint32(def.MsgId_Actor_GuildSystem2Player),
		Data: resp,
	})
	if err != nil {
		log.Error("send create guild response failed", log.Kv("from", gs.PID()), log.Kv("to", to), log.Err(err))
	}
	return err
}

func (gs *GuildSystem) sendLeaveGuildResponse(to actor.PID, success bool) error {
	resp := &cs.G2PLeaveGuild{
		Success: success,
	}

	err := actor.Send(gs.PID(), to, &actor.Message{
		Id:   uint32(def.MsgId_Actor_GuildSystem2Player),
		Data: resp,
	})
	if err != nil {
		log.Error("send leave guild response failed", log.Kv("from", gs.PID()), log.Kv("to", to), log.Err(err))
	}
	return err
}

// handleFastJoinGuild 处理快速加入公会请求
func (gs *GuildSystem) handleFastJoinGuild(msg *actor.Message) {
	// 快速加入公会功能会随机选择一个可加入的公会
	req, ok := msg.Data.(*cs.P2GFastJoinGuild)
	if !ok {
		log.Error("invalid message data", log.Kv("msgId", msg.Id))
		if err := gs.sendFastJoinGuildResponse(msg.From(), error_code.ERROR_PARAMS, 0); err != nil {
			log.Error("send fast join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	playerId := msg.Uid

	// 检查玩家是否已在公会中
	if gs.isPlayerInGuild(playerId) {
		log.Error("player already in guild", log.Kv("playerId", playerId))
		if err := gs.sendFastJoinGuildResponse(msg.From(), error_code.ERROR_GUILD_ALREADY_JOIN, 0); err != nil {
			log.Error("send fast join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	log.Debug("players quickly join the guild current level", log.Kv("playerId", playerId), log.Kv("level", req.Level))

	// 随机选择一个可加入的公会
	availableGuilds := make([]int64, 0)
	for guildId, guild := range gs.guildDB.GuildDataMap {
		// 检查是否是开放加入、人数是否未满、今日招新是否未达上限
		if guild.FreeJoin && guild.MemberCount < guild.MaxMember && guild.TodayJoinedCount < DailyMaxJoinCountPerGuild {
			// 检查玩家是否满足公会的等级/主线要求
			// TODO: 读取公会的reqStage字段并与玩家的stage比较
			// 暂时简化处理，设定玩家等级/主线进度符合要求
			meetRequirement := true // 默认符合要求

			// 如果公会有进度/等级要求且玩家不满足，跳过该公会
			// 简化处理：使用玩家等级比较公会的ReqStage
			if guild.ReqStage > 0 && req.Level < uint32(guild.ReqStage) {
				meetRequirement = false
			}

			// 如果满足条件，添加到可用公会列表
			if meetRequirement {
				availableGuilds = append(availableGuilds, guildId)
			}
		}
	}

	// 如果没有可用的公会
	if len(availableGuilds) == 0 {
		log.Debug("no available guild for fast join", log.Kv("playerId", playerId))
		if err := gs.sendFastJoinGuildResponse(msg.From(), error_code.ERROR_GUILD_NO_SUITABLE, 0); err != nil {
			log.Error("send fast join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 随机选择一个公会
	rnd := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomIndex := rnd.Intn(len(availableGuilds))
	guildId := availableGuilds[randomIndex]
	guild := gs.guildDB.GuildDataMap[guildId]

	// 添加成员
	guild.Members[playerId] = &dbstruct.GuildMember{
		PlayerId:     playerId,
		JoinTime:     time.Now().Unix(),
		Contribution: 0,
		Position:     public.GuildPosition_GuildPosition_Normal,
	}
	guild.MemberCount++

	// 增加今日入盟计数
	guild.TodayJoinedCount++
	log.Debug("guild's todayJoinedCount has increased", log.Kv("guildId", guildId), log.Kv("guildName", guild.Name), log.Kv("todayJoinedCount", guild.TodayJoinedCount))

	// 保存公会数据
	dbhelper.SaveGuildData(gs.PID(), guild.GuildId, gs.guildDB, func(err error) {
		if err != nil {
			log.Error("save guild failed", log.Kv("guild_id", guild.GuildId), log.Err(err))
		}
	})

	// 通知玩家系统，更新玩家公会归属
	notifyReq := &cs.G2PSNotifyPlayerJoinedGuild{
		Uid:        playerId,
		GuildId:    guildId,
		GuildName:  guild.Name,
		Position:   public.GuildPosition_GuildPosition_Normal,
		GuildLevel: guild.Level,
	}

	err := gs.sendToPlayerSystem(notifyReq)
	if err != nil {
		log.Error("send notification to player system failed",
			log.Kv("from", gs.PID()),
			log.Kv("to", actor_def.PlayerSystemPID),
			log.Kv("msgType", "G2PSNotifyPlayerJoinedGuild"),
			log.Err(err))
	} else {
		log.Debug("notify player system: player fast joined guild",
			log.Kv("playerId", playerId),
			log.Kv("guildId", guildId),
			log.Kv("guildName", guild.Name))
	}

	// 发送响应
	uint64GuildId := uint64(guildId) // 转换类型
	if err := gs.sendFastJoinGuildResponse(msg.From(), error_code.ERROR_OK, uint64GuildId); err != nil {
		log.Error("send fast join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
	}
}

// sendFastJoinGuildResponse 发送快速加入公会的响应
func (gs *GuildSystem) sendFastJoinGuildResponse(to actor.PID, code error_code.Code, guildId uint64) error {
	// 构建并发送加入公会的通用响应
	success := code == error_code.ERROR_OK
	name := ""
	position := int32(public.GuildPosition_GuildPosition_Normal)

	if success && guildId > 0 {
		if guild, ok := gs.guildDB.GuildDataMap[int64(guildId)]; ok {
			name = guild.Name
		}
	}

	resp := &cs.G2PJoinGuild{
		Success:  success,
		GuildId:  int64(guildId),
		Name:     name,
		Position: position,
	}

	err := actor.Send(gs.PID(), to, &actor.Message{
		Id:   uint32(def.MsgId_Actor_GuildSystem2Player),
		Data: resp,
	})
	if err != nil {
		log.Error("send fast join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", to), log.Err(err))
	}
	return err
}

// handleApplyJoinGuild 处理申请加入公会请求
func (gs *GuildSystem) handleApplyJoinGuild(msg *actor.Message) {
	data, ok := msg.Data.(*cs.P2GApplyJoinGuild)
	if !ok {
		log.Error("invalid message data", log.Kv("msgId", msg.Id))
		if err := gs.sendApplyJoinGuildResponse(msg.From(), error_code.ERROR_PARAMS, 0); err != nil {
			log.Error("send apply join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	playerId := msg.Uid
	guildId := data.GetGuildId()

	// 检查公会是否存在
	guild, exists := gs.guildDB.GuildDataMap[guildId]
	if !exists {
		log.Error("guild not exists", log.Kv("guildId", guildId))
		if err := gs.sendApplyJoinGuildResponse(msg.From(), error_code.ERROR_NO_GUILD, 0); err != nil {
			log.Error("send apply join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 检查公会成员数量
	if guild.MemberCount >= guild.MaxMember {
		log.Error("guild member is full", log.Kv("guildId", guildId))
		// 发送申请失败响应 - 公会已满
		if err := gs.sendApplyJoinGuildResponse(msg.From(), error_code.ERROR_GUILD_FULL, 0); err != nil {
			log.Error("send apply join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 检查玩家是否已在公会中
	for _, member := range guild.Members {
		if member.PlayerId == playerId {
			log.Error("player already in guild", log.Kv("playerId", playerId), log.Kv("guildId", guildId))
			if err := gs.sendApplyJoinGuildResponse(msg.From(), error_code.HAVE_GUILD, 0); err != nil {
				log.Error("send apply join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			}
			return
		}
	}

	// 添加申请记录
	if guild.Applications == nil {
		guild.Applications = make(map[uint64]*dbstruct.GuildApplicationEntry)
	}

	// 添加申请记录
	guild.Applications[playerId] = &dbstruct.GuildApplicationEntry{
		PlayerId:       playerId,
		ApplyTimestamp: time.Now().Unix(),
		Status:         public.GuildApplicationStatus_APPLICATION_STATUS_PENDING,
	}

	// 保存公会数据
	dbhelper.SaveGuildData(gs.PID(), guild.GuildId, gs.guildDB, func(err error) {
		if err != nil {
			log.Error("save guild failed", log.Kv("guild_id", guild.GuildId), log.Err(err))
		}
	})

	// 发送响应
	uint64GuildId := uint64(guildId) // 转换类型
	if err := gs.sendApplyJoinGuildResponse(msg.From(), error_code.ERROR_OK, uint64GuildId); err != nil {
		log.Error("send apply join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
	}
}

// handleGuildDonate 处理公会捐献请求
func (gs *GuildSystem) handleGuildDonate(msg *actor.Message) {
	req := msg.Data.(*cs.P2GGuildDonate)
	playerId := msg.Uid

	// 调试日志
	log.Debug("handle guild donate",
		log.Kv("playerId", playerId),
		log.Kv("guildId", req.GuildId),
		log.Kv("donateOpt", req.DonateOpt),
		log.Kv("expGain", req.GuildExpGained),
		log.Kv("contribGain", req.GuildContributionGained))

	// 获取公会数据
	guildData, exists := gs.guildDB.GuildDataMap[req.GuildId]
	if !exists {
		// 公会不存在，发送错误响应
		err := gs.sendGuildDonateResponse(msg.From(), error_code.ERROR_NO_GUILD, req.DonateOpt, 0, 0, 0)
		if err != nil {
			log.Error("send guild donate response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		log.Error("guild not found", log.Kv("guildId", req.GuildId), log.Kv("playerId", playerId))
		return
	}

	// 如果 DonateOpt 为 0，表示这是一个查询请求，仅返回数据而不执行捐献操作
	if req.DonateOpt == 0 {
		log.Debug("guild donate query only", log.Kv("playerId", playerId), log.Kv("guildId", req.GuildId))
		// 直接发送响应给玩家，不做任何修改
		err := gs.sendGuildDonateResponse(msg.From(), error_code.ERROR_OK, req.DonateOpt, guildData.Level, guildData.Exp, guildData.Contribution)
		if err != nil {
			log.Error("send guild donate query response failed", log.Kv("guild_id", guildData.GuildId), log.Err(err))
		}
		return
	}

	// 捐献请求，更新公会数据
	guildData.Exp += req.GuildExpGained
	guildData.Contribution += req.GuildContributionGained

	// 判断公会升级
	levelUpped := false

	// TODO 升级限制暂时写死为50经验升级
	for {
		constCurrentLevelMaxExp := int64(50)

		// 如果经验足够，则升级
		if guildData.Exp >= constCurrentLevelMaxExp {
			guildData.Exp -= constCurrentLevelMaxExp
			guildData.Level++

			levelUpped = true

			log.Info("guild level up",
				log.Kv("guildId", guildData.GuildId),
				log.Kv("guildName", guildData.Name),
				log.Kv("newLevel", guildData.Level))
		} else {
			break
		}
	}

	// 保存公会数据
	dbhelper.SaveGuildData(gs.PID(), guildData.GuildId, gs.guildDB, func(err error) {
		if err != nil {
			log.Error("save guild failed after donate", log.Kv("guild_id", guildData.GuildId), log.Err(err))
		}
	})

	// 更新公会排行榜信息
	gs.UpdateAllGuildRankInfo(guildData)

	// 发送响应给玩家
	err := gs.sendGuildDonateResponse(msg.From(), error_code.ERROR_OK, req.DonateOpt, guildData.Level, guildData.Exp, guildData.Contribution)
	if err != nil {
		log.Error("send G2PGuildDonateRsp failed", log.Kv("guild_id", guildData.GuildId), log.Err(err))
	}

	// 如果公会升级了，需要通知所有成员
	if levelUpped {
		// 要要汇总所有成员ID
		uids := make([]uint64, 0, len(guildData.Members))
		for uid := range guildData.Members {
			uids = append(uids, uid)
		}

		// 发送消息到PlayerSystem通知所有成员
		notifyReq := &cs.G2PSBatchUpdatePlayerGuildStatusReq{
			GuildId:       guildData.GuildId,
			Uids:          uids,
			ActionType:    public.GuildSystemInternalActionType_INTERNAL_ACTION_GUILD_LEVEL_UP_NOTIFY_MEMBERS,
			NewGuildLevel: guildData.Level,
			NewMaxMembers: guildData.MaxMember,
		}

		if err := gs.sendToPlayerSystem(notifyReq); err != nil {
			log.Error("notify guild level up failed", log.Kv("guildId", guildData.GuildId), log.Err(err))
		}
	}
}

// sendGuildDonateResponse 发送公会捐献响应
func (gs *GuildSystem) sendGuildDonateResponse(to actor.PID, code error_code.Code, opt int32, newGuildLevel int32, newGuildExp int64, newGuildTotalContribution int64) error {
	resp := &cs.G2PGuildDonateRsp{
		Errorcode:                 int32(code),
		NewGuildLevel:             newGuildLevel,
		NewGuildExp:               newGuildExp,
		NewGuildTotalContribution: newGuildTotalContribution,
		Opt:                       opt,
	}

	return actor.Send(gs.PID(), to, &actor.Message{
		Id:   uint32(def.MsgId_Actor_GuildSystem2Player),
		Data: resp,
	})
}

// sendApplyJoinGuildResponse 发送申请加入公会的响应
func (gs *GuildSystem) sendApplyJoinGuildResponse(to actor.PID, code error_code.Code, guildId uint64) error {
	resp := &cs.G2PJoinGuildRsp{
		Errorcode: int32(code),
		GuildId:   int64(guildId),
	}

	err := actor.Send(gs.PID(), to, &actor.Message{
		Id:   uint32(def.MsgId_Actor_GuildSystem2Player),
		Data: resp,
	})
	if err != nil {
		log.Error("send join guild response failed", log.Kv("from", gs.PID()), log.Kv("to", to), log.Err(err))
	}
	return err
}

// handleEditGuildInfo 处理编辑公会信息的请求
func (gs *GuildSystem) handleEditGuildInfo(msg *actor.Message) {
	data, ok := msg.Data.(*cs.P2GEditGuildInfo)
	if !ok {
		log.Error("invalid message data", log.Kv("msgId", msg.Id))
		if err := gs.sendEditGuildInfoResponse(msg.From(), error_code.ERROR_PARAMS, 0, public.GuildOpt_GuildOpt_None); err != nil {
			log.Error("send edit guild info response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	playerId := msg.Uid
	guildId := data.GetGuildId()

	// 检查公会是否存在
	guild, exists := gs.guildDB.GuildDataMap[guildId]
	if !exists {
		log.Error("guild not exists", log.Kv("guildId", guildId))
		if err := gs.sendEditGuildInfoResponse(msg.From(), error_code.ERROR_NO_GUILD, guildId, data.Opt); err != nil {
			log.Error("send edit guild info response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 检查上传者是否有权限修改公会信息（会长或副会长）
	hasPermission := false
	if member, ok := guild.Members[playerId]; ok {
		// 会长或者副会长有权限
		if member.Position >= public.GuildPosition_GuildPosition_Vice {
			hasPermission = true
		}
	}

	if !hasPermission {
		log.Error("player has no permission to edit guild info", log.Kv("playerId", playerId), log.Kv("guildId", guildId))
		if err := gs.sendEditGuildInfoResponse(msg.From(), error_code.ERROR_GUILD_NO_PERMISSION, guildId, data.Opt); err != nil {
			log.Error("send edit guild info response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 更新公会信息
	if data.GetName() != "" && data.GetName() != guild.Name {
		guild.Name = data.GetName()
	}
	if data.GetNotice() != "" && data.GetNotice() != guild.Notice {
		guild.Notice = data.GetNotice()
	}
	if data.GetAnnouncement() != "" && data.GetAnnouncement() != guild.Announcement {
		guild.Announcement = data.GetAnnouncement()
	}
	if data.GetIconId() > 0 {
		guild.IconId = data.GetIconId()
	}
	// 更新加入设置
	if data.GetFreeJoin() != guild.FreeJoin {
		guild.FreeJoin = data.GetFreeJoin()
	}
	if data.GetReqStage() >= 0 && data.GetReqStage() != guild.ReqStage {
		guild.ReqStage = data.GetReqStage()
	}

	// 如果有修改，则保存公会数据
	// 异步保存单个公会数据
	dbhelper.SaveGuildData(gs.PID(), guild.GuildId, gs.guildDB, func(err error) {
		if err != nil {
			log.Error("save guild failed after editing info", log.Kv("guild_id", guild.GuildId), log.Err(err))
		}
	})

	// 更新公会基本信息到排行榜服务
	gs.SyncGuildInfo(guild)

	// 发送编辑公会信息响应
	if err := gs.sendEditGuildInfoResponse(msg.From(), error_code.ERROR_OK, guild.GuildId, data.Opt); err != nil {
		log.Error("send edit guild info response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
	}
}

// sendToPlayer 发送消息到Player，封装通用的错误处理逻辑
func (gs *GuildSystem) sendToPlayer(to actor.PID, msg proto.Message) error {
	err := actor.Send(gs.PID(), to, &actor.Message{
		Id:   uint32(def.MsgId_Actor_GuildSystem2Player),
		Data: msg,
	})
	if err != nil {
		log.Error("send to player failed", log.Kv("from", gs.PID()), log.Kv("to", to), log.Err(err))
	}
	return err
}

// sendEditGuildInfoResponse 发送编辑公会信息的响应
func (gs *GuildSystem) sendEditGuildInfoResponse(to actor.PID, code error_code.Code, guildId int64, opt public.GuildOpt) error {
	// 初始化默认的空值
	name := ""
	notice := ""
	iconId := int32(0)
	freeJoin := false
	reqStage := int32(0)
	announcement := ""

	// 如果公会存在，从公会中读取相关信息
	if code == error_code.ERROR_OK && guildId > 0 {
		if guild, ok := gs.guildDB.GuildDataMap[guildId]; ok {
			name = guild.Name
			notice = guild.Notice
			iconId = guild.IconId
			freeJoin = guild.FreeJoin
			reqStage = guild.ReqStage
			announcement = guild.Announcement
		}
	}

	resp := &cs.G2PEditGuildInfoRsp{
		Errorcode:    int32(code),
		Opt:          opt,
		Name:         name,
		Notice:       notice,
		IconId:       iconId,
		FreeJoin:     freeJoin,
		ReqStage:     reqStage,
		Announcement: announcement,
	}

	return gs.sendToPlayer(to, resp)
}

// handleGetApplyList 处理获取公会申请列表的请求
func (gs *GuildSystem) handleGetApplyList(msg *actor.Message) {
	data, ok := msg.Data.(*cs.P2GGetApplyList)
	if !ok {
		log.Error("invalid message data", log.Kv("msgId", msg.Id))
		if err := gs.sendGetApplyListResponse(msg.From(), error_code.ERROR_PARAMS, nil); err != nil {
			log.Error("send get apply list response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	playerId := msg.Uid
	guildId := data.GetGuildId()

	// 检查公会是否存在
	guild, exists := gs.guildDB.GuildDataMap[guildId]
	if !exists {
		log.Error("guild not exists", log.Kv("guildId", guildId))
		if err := gs.sendGetApplyListResponse(msg.From(), error_code.ERROR_NO_GUILD, nil); err != nil {
			log.Error("send get apply list response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 验证权限（只有会长或副会长可以查看申请列表）
	hasPermission := false
	if member, ok := guild.Members[playerId]; ok {
		if member.Position >= public.GuildPosition_GuildPosition_Vice {
			hasPermission = true
		}
	}

	if !hasPermission {
		log.Error("player has no permission to view apply list", log.Kv("playerId", playerId), log.Kv("guildId", guildId))
		if err := gs.sendGetApplyListResponse(msg.From(), error_code.ERROR_GUILD_NO_PERMISSION, nil); err != nil {
			log.Error("send get apply list response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 获取申请列表
	applyList := make([]*public.PBGuildApply, 0)
	if guild.Applications != nil {
		for _, app := range guild.Applications {
			// 只显示状态为pending的申请
			if app.Status == public.GuildApplicationStatus_APPLICATION_STATUS_PENDING {
				applyList = append(applyList, &public.PBGuildApply{
					PlatformID: int64(app.PlayerId),
					ApplyTime:  app.ApplyTimestamp,
				})
			}
		}
	}

	// 返回申请列表
	if err := gs.sendGetApplyListResponse(msg.From(), error_code.ERROR_OK, applyList); err != nil {
		log.Error("send get apply list response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
	}
}

// sendGetApplyListResponse 发送获取公会申请列表的响应
func (gs *GuildSystem) sendGetApplyListResponse(to actor.PID, code error_code.Code, applyList []*public.PBGuildApply) error {
	resp := &cs.G2PGetApplyListRsp{
		Errorcode: int32(code),
		ApplyList: applyList,
	}

	err := actor.Send(gs.PID(), to, &actor.Message{
		Id:   uint32(def.MsgId_Actor_GuildSystem2Player),
		Data: resp,
	})
	if err != nil {
		log.Error("send get apply list response failed", log.Kv("from", gs.PID()), log.Kv("to", to), log.Err(err))
	}
	return err
}

// handleProcessApplication 处理公会申请的请求（批准或拒绝）
func (gs *GuildSystem) handleProcessApplication(msg *actor.Message) {
	data, ok := msg.Data.(*cs.P2GProcessApplication)
	if !ok {
		log.Error("invalid message data", log.Kv("msgId", msg.Id))
		if err := gs.sendProcessApplicationResponse(msg.From(), error_code.ERROR_PARAMS, 0, public.GuildOpt_GuildOpt_None); err != nil {
			log.Error("send process application response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	operatorId := data.GetOperatorUid()
	guildId := data.GetGuildId()
	applicantId := data.GetTargetUid()
	action := data.GetAction()

	// 判断操作类型
	isApprove := action == public.GuildOpt_GuildOpt_ApplyMgrAgree
	isRejectAll := action == public.GuildOpt_GuildOpt_ApplyMgrRejectAll

	// 检查公会是否存在
	guild, exists := gs.guildDB.GuildDataMap[guildId]
	if !exists {
		log.Error("guild not exists", log.Kv("guildId", guildId))
		if err := gs.sendProcessApplicationResponse(msg.From(), error_code.ERROR_NO_GUILD, applicantId, action); err != nil {
			log.Error("send process application response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 验证权限（只有会长或副会长可以处理申请）
	hasPermission := false
	if member, ok := guild.Members[operatorId]; ok {
		if member.Position >= public.GuildPosition_GuildPosition_Vice {
			hasPermission = true
		}
	}

	if !hasPermission {
		log.Error("player has no permission to process application", log.Kv("operatorId", operatorId), log.Kv("guildId", guildId))
		if err := gs.sendProcessApplicationResponse(msg.From(), error_code.ERROR_GUILD_NO_PERMISSION, applicantId, action); err != nil {
			log.Error("send process application response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 处理一键拒绝所有申请
	if isRejectAll {
		rejectedCount := 0

		// 遍历所有待处理的申请
		for uid, application := range guild.Applications {
			// 只处理待审核的申请
			if application.Status == public.GuildApplicationStatus_APPLICATION_STATUS_PENDING {
				// 更新申请状态为已拒绝
				application.Status = public.GuildApplicationStatus_APPLICATION_STATUS_REJECTED

				// 通知玩家系统，移除申请者的待处理申请
				notifyReq := &cs.G2PSNotifyRemovePendingApply{
					Uid:     uid,
					GuildId: guildId,
					Opt:     action,
				}

				err := gs.sendToPlayerSystem(notifyReq)
				if err != nil {
					log.Error("send notification to player system failed",
						log.Kv("from", gs.PID()),
						log.Kv("to", actor_def.PlayerSystemPID),
						log.Kv("msgType", "G2PSNotifyRemovePendingApply"),
						log.Err(err))
				}

				rejectedCount++
			}
		}

		log.Debug("rejected all pending applications",
			log.Kv("guildId", guildId),
			log.Kv("guildName", guild.Name),
			log.Kv("rejectedCount", rejectedCount))

		// 保存公会数据
		dbhelper.SaveGuildData(gs.PID(), guild.GuildId, gs.guildDB, func(err error) {
			if err != nil {
				log.Error("save guild failed", log.Kv("guild_id", guild.GuildId), log.Err(err))
			}
		})

		// 发送处理结果响应
		if err := gs.sendProcessApplicationResponse(msg.From(), error_code.ERROR_OK, 0, action); err != nil {
			log.Error("send process application response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 处理单个申请
	// 查找对应申请
	application, ok := guild.Applications[applicantId]
	if !ok {
		log.Error("application not exists", log.Kv("guildId", guildId), log.Kv("applicantId", applicantId))
		if err := gs.sendProcessApplicationResponse(msg.From(), error_code.ERROR_PARAMS, applicantId, action); err != nil {
			log.Error("send process application response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	if isApprove {
		// 如果批准申请

		// 检查公会是否已满
		if guild.MemberCount >= guild.MaxMember {
			log.Error("guild member count exceed", log.Kv("guildId", guildId))
			if err := gs.sendProcessApplicationResponse(msg.From(), error_code.ERROR_GUILD_FULL, applicantId, action); err != nil {
				log.Error("send process application response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			}
			return
		}

		// 检查公会今日招新是否达到上限
		if guild.TodayJoinedCount >= DailyMaxJoinCountPerGuild {
			log.Error("guild daily join count exceed", log.Kv("guildId", guildId), log.Kv("todayJoinedCount", guild.TodayJoinedCount))
			if err := gs.sendProcessApplicationResponse(msg.From(), error_code.ERROR_GUILD_DAILY_JOIN_LIMIT, applicantId, action); err != nil {
				log.Error("send process application response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			}
			return
		}

		// 检查申请者是否已经在其他公会
		if gs.isPlayerInGuild(applicantId) {
			log.Error("player already in guild", log.Kv("playerId", applicantId))
			if err := gs.sendProcessApplicationResponse(msg.From(), error_code.ERROR_ALREADY_IN_GUILD, applicantId, action); err != nil {
				log.Error("send process application response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			}
			return
		}

		// 将玩家添加到公会
		guild.Members[applicantId] = &dbstruct.GuildMember{
			PlayerId:     applicantId,
			Position:     public.GuildPosition_GuildPosition_Normal,
			JoinTime:     time.Now().Unix(),
			Contribution: 0,
		}
		guild.MemberCount++

		// 增加今日入盟计数
		guild.TodayJoinedCount++
		log.Debug("guild TodayJoinedCount has increased", log.Kv("guildId", guildId), log.Kv("guildName", guild.Name), log.Kv("todayJoinedCount", guild.TodayJoinedCount))

		// 更新申请状态为已批准
		application.Status = public.GuildApplicationStatus_APPLICATION_STATUS_APPROVED

		// 通知玩家系统，申请者已加入公会
		notifyReq := &cs.G2PSNotifyPlayerJoinedGuild{
			Uid:        applicantId,
			GuildId:    guildId,
			GuildName:  guild.Name,
			Position:   public.GuildPosition_GuildPosition_Normal,
			GuildLevel: guild.Level,
		}

		err := gs.sendToPlayerSystem(notifyReq)
		if err != nil {
			log.Error("send notification to player system failed",
				log.Kv("from", gs.PID()),
				log.Kv("to", actor_def.PlayerSystemPID),
				log.Kv("msgType", "G2PSNotifyPlayerJoinedGuild"),
				log.Err(err))
		} else {
			log.Debug("notify player system that player has joined the guild",
				log.Kv("applicantId", applicantId),
				log.Kv("guildId", guildId),
				log.Kv("guildName", guild.Name))
		}
	} else {
		// 如果拒绝申请，更新申请状态为已拒绝
		application.Status = public.GuildApplicationStatus_APPLICATION_STATUS_REJECTED

		// 通知玩家系统，移除申请者的待处理申请
		notifyReq := &cs.G2PSNotifyRemovePendingApply{
			Uid:     applicantId,
			GuildId: guildId,
			Opt:     action,
		}

		err := gs.sendToPlayerSystem(notifyReq)
		if err != nil {
			log.Error("send notification to player system failed",
				log.Kv("from", gs.PID()),
				log.Kv("to", actor_def.PlayerSystemPID),
				log.Kv("msgType", "G2PSNotifyRemovePendingApply"),
				log.Err(err))
		} else {
			log.Debug("notify the player of the system's pending application to remove the player",
				log.Kv("applicantId", applicantId),
				log.Kv("guildId", guildId))
		}
	}

	// 保存公会数据
	dbhelper.SaveGuildData(gs.PID(), guild.GuildId, gs.guildDB, func(err error) {
		if err != nil {
			log.Error("save guild failed", log.Kv("guild_id", guild.GuildId), log.Err(err))
		}
	})

	// 发送处理结果响应
	if err := gs.sendProcessApplicationResponse(msg.From(), error_code.ERROR_OK, applicantId, action); err != nil {
		log.Error("send process application response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
	}
}

// buildGuildMemberInfo 构建公会成员信息
func (gs *GuildSystem) buildGuildMemberInfo(targetUid uint64) *public.PBGuildMember {
	// 查找用户所在的公会信息，获取贡献值和职位
	contribution := int64(0)
	position := public.GuildPosition_GuildPosition_Normal

	// 遍历所有公会，寻找该成员的信息
	for _, guild := range gs.guildDB.GuildDataMap {
		if member, ok := guild.Members[targetUid]; ok {
			contribution = member.Contribution
			position = member.Position
			break
		}
	}

	// 创建成员信息对象
	return &public.PBGuildMember{
		PlatformID:   int64(targetUid),
		Contribution: contribution,
		Gpos:         position,
	}
}

// sendProcessApplicationResponse 发送处理公会申请的响应
func (gs *GuildSystem) sendProcessApplicationResponse(to actor.PID, code error_code.Code, targetUid uint64, opt public.GuildOpt) error {
	// 初始化响应对象
	resp := &cs.G2PProcessApplicationRsp{
		Errorcode: int32(code),
		TargetUid: targetUid,
		Opt:       opt,
	}

	// 只有在成功的情况下，才需要填充成员信息
	if code == error_code.ERROR_OK && targetUid > 0 {
		if member := gs.buildGuildMemberInfo(targetUid); member != nil {
			resp.Member = member
		}
	}

	return gs.sendToPlayer(to, resp)
}

// handleMemberAction 处理对公会成员的操作（踢出、设置职位等）
func (gs *GuildSystem) handleMemberAction(msg *actor.Message) {
	data, ok := msg.Data.(*cs.P2GMemberAction)
	if !ok {
		log.Error("invalid message data", log.Kv("msgId", msg.Id))
		// 在消息无效的情况下，使用默认的操作类型和0作为目标ID
		if err := gs.sendMemberActionResponse(msg.From(), error_code.ERROR_PARAMS, public.GuildOpt_GuildOpt_None, 0); err != nil {
			log.Error("send member action response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 使用消息中定义的字段获取操作者ID，而不是msg.Uid
	operatorId := data.GetOperatorUid()
	guildId := data.GetGuildId()
	targetId := data.GetTargetUid()
	action := data.GetAction()
	newPosition := data.GetTargetPosition()

	// 检查公会是否存在
	guild, exists := gs.guildDB.GuildDataMap[guildId]
	if !exists {
		log.Error("guild not exists", log.Kv("guildId", guildId))
		if err := gs.sendMemberActionResponse(msg.From(), error_code.ERROR_NO_GUILD, action, targetId); err != nil {
			log.Error("send member action response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 校验操作者权限
	operatorPosition := public.GuildPosition_GuildPosition_Normal // 默认为普通成员
	if member, ok := guild.Members[operatorId]; ok {
		operatorPosition = member.Position
	} else {
		log.Error("operator not in guild", log.Kv("operatorId", operatorId), log.Kv("guildId", guildId))
		if err := gs.sendMemberActionResponse(msg.From(), error_code.ERROR_PARAMS, action, targetId); err != nil {
			log.Error("send member action response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 检查处理者权限
	if operatorPosition < public.GuildPosition_GuildPosition_Vice {
		log.Error("no permission to process member action", log.Kv("playerId", operatorId), log.Kv("position", operatorPosition))
		if err := gs.sendMemberActionResponse(msg.From(), error_code.ERROR_GUILD_NO_PERMISSION, action, targetId); err != nil {
			log.Error("send member action response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 校验目标成员是否存在
	targetMember, ok := guild.Members[targetId]
	if !ok {
		log.Error("target member not in guild", log.Kv("targetId", targetId), log.Kv("guildId", guildId))
		if err := gs.sendMemberActionResponse(msg.From(), error_code.ERROR_PARAMS, action, targetId); err != nil {
			log.Error("send member action response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 不能对自己进行操作
	if operatorId == targetId {
		log.Error("cannot perform action on self", log.Kv("operatorId", operatorId))
		if err := gs.sendMemberActionResponse(msg.From(), error_code.ERROR_PARAMS, action, targetId); err != nil {
			log.Error("send member action response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 根据操作类型处理
	switch action {
	case public.GuildOpt_GuildOpt_MemberMgrKick:
		// 踢出成员
		// 只有会长和副会长可以踢人，且副会长不能踢会长和其他副会长
		if operatorPosition < public.GuildPosition_GuildPosition_Vice ||
			(operatorPosition == public.GuildPosition_GuildPosition_Vice && targetMember.Position >= public.GuildPosition_GuildPosition_Vice) {
			log.Error("no permission to kick this member",
				log.Kv("operatorId", operatorId),
				log.Kv("operatorPos", operatorPosition),
				log.Kv("targetId", targetId),
				log.Kv("targetPos", targetMember.Position))
			if err := gs.sendMemberActionResponse(msg.From(), error_code.ERROR_GUILD_NO_PERMISSION, action, targetId); err != nil {
				log.Error("send member action response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			}
			return
		}

		// 从公会移除成员
		delete(guild.Members, targetId)
		guild.MemberCount--

	case public.GuildOpt_GuildOpt_MemberMgrVice,
		public.GuildOpt_GuildOpt_MemberMgrElite,
		public.GuildOpt_GuildOpt_MemberMgrNormal: // 设置副会长或精英或普通成员
		// 只有会长可以设置副会长职位
		if operatorPosition != public.GuildPosition_GuildPosition_President && action == public.GuildOpt_GuildOpt_MemberMgrVice {
			log.Error("only president can set position", log.Kv("operatorId", operatorId))
			if err := gs.sendMemberActionResponse(msg.From(), error_code.ERROR_GUILD_NO_PERMISSION, action, targetId); err != nil {
				log.Error("send member action response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			}
			return
		}

		// 更新成员职位
		targetMember.Position = newPosition

	case public.GuildOpt_GuildOpt_MemberMgrPresident: // 设置为会长(转让会长职位)
		// 转让会长
		// 只有会长可以转让会长身份
		if operatorPosition != public.GuildPosition_GuildPosition_President {
			log.Error("only president can transfer leadership", log.Kv("operatorId", operatorId))
			if err := gs.sendMemberActionResponse(msg.From(), error_code.ERROR_GUILD_NO_PERMISSION, action, targetId); err != nil {
				log.Error("send member action response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			}
			return
		}

		// 任命后变为普通成员
		guild.Members[operatorId].Position = public.GuildPosition_GuildPosition_Normal

		// 目标成员变为会长
		targetMember.Position = public.GuildPosition_GuildPosition_President

		// 更新公会会长ID
		guild.LeaderId = targetId

	default:
		log.Error("unknown action", log.Kv("action", action))
		if err := gs.sendMemberActionResponse(msg.From(), error_code.ERROR_PARAMS, action, targetId); err != nil {
			log.Error("send member action response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		return
	}

	// 保存公会数据
	dbhelper.SaveGuildData(gs.PID(), guild.GuildId, gs.guildDB, func(err error) {
		if err != nil {
			log.Error("save guild failed", log.Kv("guild_id", guild.GuildId), log.Err(err))
		}
	})

	// 发送操作结果响应
	if err := gs.sendMemberActionResponse(msg.From(), error_code.ERROR_OK, action, targetId); err != nil {
		log.Error("send member action response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
	}

	// 通知被操作玩家位置变化
	resp := &cs.G2PSNotifyPlayerPositionChanged{
		Uid:         targetId,
		GuildId:     guild.GuildId,
		NewPosition: newPosition,
		Opt:         action,
	}
	err := gs.sendToPlayerSystem(resp)
	if err != nil {
		log.Error("send NotifyPlayerPositionChanged failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
	}
}

func (gs *GuildSystem) sendMemberActionResponse(to actor.PID, code error_code.Code, action public.GuildOpt, targetUid uint64) error {
	// 初始化响应对象
	resp := &cs.G2PMemberActionRsp{
		Errorcode: int32(code),
		Action:    action,
		TargetUid: targetUid,
	}

	// 只有在成功的情况下且目标用户ID有效时，才填充成员信息
	if code == error_code.ERROR_OK && targetUid > 0 {
		if member := gs.buildGuildMemberInfo(targetUid); member != nil {
			resp.Member = member
		}
	}

	return gs.sendToPlayer(to, resp)
}

// handleGMUpdateGuildAttrs 处理GM更新公会属性的请求（经验/等级）
func (gs *GuildSystem) handleGMUpdateGuildAttrs(msg *actor.Message) {
	req := msg.Data.(*cs.P2GGMUpdateGuildAttrs)
	guildId := req.GuildId
	exp := req.ExpToAdd
	level := req.TargetLevel

	// 验证公会是否存在
	guild, exists := gs.guildDB.GuildDataMap[guildId]
	if !exists {
		if err := gs.sendGMCommonResponse(msg.From(), error_code.ERROR_PARAMS); err != nil {
			log.Error("send GM update guild attrs response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
		}
		log.Error("guild not exist", log.Kv("guildId", guildId))
		return
	}

	// 记录当前值用于记录变更
	oldLevel := guild.Level
	oldExp := guild.Exp

	// 处理经验值设置 (如果传入-1则不修改此属性)
	if exp != -1 {
		guild.Exp = exp
		log.Info("guild exp updated", log.Kv("guildId", guildId), log.Kv("oldExp", oldExp), log.Kv("newExp", exp))
	}

	// 处理等级设置 (如果传入-1则不修改此属性)
	if level != -1 {
		// 验证等级范围
		if level < 1 || level > 10 { // 假设等级范围为1-10
			if err := gs.sendGMCommonResponse(msg.From(), error_code.ERROR_PARAMS); err != nil {
				log.Error("send GM update guild attrs response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
			}
			log.Error("invalid guild level", log.Kv("guildId", guildId), log.Kv("level", level))
			return
		}

		// 更新公会等级和相关属性
		guild.Level = level
		guild.MaxMember = 50 + (level-1)*10 // 假设每级增加10个最大成员数
		log.Info("guild level set", log.Kv("guildId", guildId), log.Kv("oldLevel", oldLevel), log.Kv("newLevel", level))
	}

	// 保存到数据库
	dbhelper.SaveGuildData(gs.PID(), guildId, gs.guildDB, func(err error) {
		if err != nil {
			log.Error("save guild failed after updating attributes", log.Kv("guild_id", guildId), log.Err(err))
		}
	})

	// 发送响应
	if err := gs.sendGMCommonResponse(msg.From(), error_code.ERROR_OK); err != nil {
		log.Error("send GM update guild attrs response failed", log.Kv("from", gs.PID()), log.Kv("to", msg.From()), log.Err(err))
	}
}

// sendGMCommonResponse 发送通用GM命令响应
func (gs *GuildSystem) sendGMCommonResponse(to actor.PID, code error_code.Code) error {
	resp := &cs.G2PCommonGMRsp{
		Errorcode: int32(code),
	}

	return gs.sendToPlayer(to, resp)
}

// sendToPlayerSystem 发送消息到玩家系统，并处理可能的错误
func (gs *GuildSystem) sendToPlayerSystem(data proto.Message) error {
	err := actor.Send(gs.PID(), actor_def.PlayerSystemPID, &actor.Message{
		Id:   uint32(def.MsgId_Actor_GuildSystem2PlayerSystem),
		Data: data,
	})
	if err != nil {
		log.Error("send to player system failed",
			log.Kv("from", gs.PID()),
			log.Kv("to", actor_def.PlayerSystemPID),
			log.Kv("msgType", fmt.Sprintf("%T", data)),
			log.Err(err))
	}
	return err
}

// SyncGuildInfo 同步公会基本信息到排行榜
func (gs *GuildSystem) SyncGuildInfo(guild *dbstruct.GuildData) {
	rankClient := grpcMgr.GetRankService()
	if rankClient == nil {
		log.Error("rank service client not found", log.Kv("guildId", guild.GuildId))
		return
	}

	// 获取会长信息
	masterName := ""
	baseInfo := gs.getPlayerBaseInfo(guild.LeaderId, int32(global.ServerId))
	if baseInfo != nil {
		masterName = baseInfo.Name
		log.Info("successfully get the name of the president", log.Kv("name", masterName), log.Kv("leaderId", guild.LeaderId))
	} else {
		log.Warn("failed to get the name of the president.", log.Kv("leaderId", guild.LeaderId))
	}

	//// 构建公会基本信息
	//guildInfo := &rankv1.RankGuildInfo{
	//	Guid:        uint64(guild.GuildId),
	//	Zwid:        int32(global.ServerId),
	//	GuildName:   guild.Name,
	//	Level:       guild.Level,
	//	MasterGuid:  guild.LeaderId,
	//	MemberCount: int32(len(guild.Members)),
	//	GuildIcon:   fmt.Sprintf("%d", guild.IconId),
	//	MasterName:  masterName,
	//	Notice:      guild.Notice,
	//}
	//
	//// 调用排行榜服务同步公会信息
	//_, err := rankClient.SyncGuildInfo(context.Background(), &rankv1.SyncGuildInfoRequest{
	//	Info: guildInfo,
	//})
	//
	//if err != nil {
	//	log.Error("sync guild info to rank service failed", log.Kv("guildId", guild.GuildId), log.Err(err))
	//} else {
	//	log.Info("sync guild info to rank service success", log.Kv("guildId", guild.GuildId))
	//}
}

// UpdateGuildRankInfo 更新指定类型的公会排行榜信息
func (gs *GuildSystem) UpdateGuildRankInfo(guild *dbstruct.GuildData, rankType rankv1.RankType, rankValue int64) {
	rankClient := grpcMgr.GetRankService()
	if rankClient == nil {
		log.Error("rank service client not found", log.Kv("guildId", guild.GuildId))
		return
	}

	// 构建更新信息
	var rankValues []*rankv1.RankValueInfo
	rankValues = append(rankValues, &rankv1.RankValueInfo{
		RankType:  int32(rankType),
		RankValue: rankValue,
	})

	//// 调用排行榜服务更新排行榜数据
	//_, err := rankClient.UpdateGuildRankInfo(context.Background(), &rankv1.UpdateGuildRankRequest{
	//	Guid:    uint64(guild.GuildId),
	//	Zwid:    int32(global.ServerId),
	//	Kserver: int32(global.ServerId),
	//	Info:    rankValues,
	//})
	//
	//if err != nil {
	//	log.Error("update guild rank info failed", log.Kv("guildId", guild.GuildId), log.Kv("rankType", rankType), log.Err(err))
	//} else {
	//	log.Info("update guild rank info success", log.Kv("guildId", guild.GuildId), log.Kv("rankType", rankType))
	//}
}

// UpdateAllGuildRankInfo 更新所有公会排行榜信息
func (gs *GuildSystem) UpdateAllGuildRankInfo(guild *dbstruct.GuildData) {
	//// 更新公会等级排行
	//gs.UpdateGuildRankInfo(guild, rankv1.RankType_GuildLevel, int64(guild.Level))
	//
	//// 更新公会捐献度排行
	//gs.UpdateGuildRankInfo(guild, rankv1.RankType_GuildContribution, guild.Contribution)
}

// getPlayerBaseInfo 获取玩家基本信息
func (gs *GuildSystem) getPlayerBaseInfo(playerId uint64, zoneWorldId int32) *playerInfov1.PlayerBaseInfo {
	playerClient := grpcMgr.GetPlayerService()
	if playerClient == nil {
		log.Error("player service client not found", log.Kv("playerId", playerId))
		return nil
	}

	// 设置EPlayerInfo的位掩码，请求玩家基本信息
	// 位掩码中设置第0位(PlayerBaseInfoPis=1)为1
	ePlayerInfo := uint64(1 << 0)

	// 调用playerinfo服务获取玩家信息
	playerInfoReply, err := playerClient.GetPlayerInfo(context.Background(), &playerInfov1.GetPlayerInfoRequest{
		AccountGuid: &playerInfov1.AccountGuid_PISct{
			Guid:        playerId,
			ZoneWorldId: zoneWorldId,
		},
		EPlayerInfo: ePlayerInfo,
	})

	if err != nil {
		log.Error("get player info failed", log.Kv("playerId", playerId), log.Err(err))
		return nil
	}

	if playerInfoReply == nil || playerInfoReply.PlayerInfo == nil || playerInfoReply.PlayerInfo.PlayerBaseInfo_PI == nil {
		log.Error("player info response invalid", log.Kv("playerId", playerId))
		return nil
	}

	return playerInfoReply.PlayerInfo.PlayerBaseInfo_PI
}

// RemoveGuildFromRank 从排行榜中移除公会
func (gs *GuildSystem) RemoveGuildFromRank(guildId int64) {
	rankClient := grpcMgr.GetRankService()
	if rankClient == nil {
		log.Error("rank service client not found", log.Kv("guildId", guildId))
		return
	}

	//// 构建移除请求
	//removeInfo := &rankv1.RemoveInfo{
	//	Guid:    uint64(guildId),
	//	Zwid:    int32(global.ServerId),
	//	Kserver: int32(global.ServerId),
	//}
	//
	//_, err := rankClient.RemoveGuild(context.Background(), &rankv1.RemoveGuildRequest{
	//	Guild: removeInfo,
	//})
	//
	//if err != nil {
	//	log.Error("remove guild from rank failed", log.Kv("guildId", guildId), log.Err(err))
	//} else {
	//	log.Info("remove guild from rank success", log.Kv("guildId", guildId))
	//}
}
