server:
  http:
    addr: 0.0.0.0:8004
    timeout: 25
  grpc:
    addr: 0.0.0.0:9004
    timeout: 25

data:
  redis:
    addr: 10.1.8.64:6379
    passwd: ""
    read_timeout: 10
    write_timeout: 10

log:
  level: -1       # 日志级别: -1=DEBUG, 0=INFO, 1=WARN, 2=ERROR, 3=DPanic, 4=Panic, 5=Fatal
  console: true

registry:
  etcd:
    endpoints:
      - "10.1.8.64:2379"
    timeout: 5
    username: ""
    password: ""
    prefix: "/jijichao"
