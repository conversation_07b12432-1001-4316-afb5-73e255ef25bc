﻿using LiteFrame.Framework;
using LiteFrame.Game;
using System;

namespace LiteFrame.Game
{
    public static class MessageAddressExpandMethod
    {
        //public static void SetAsClientProxy(this MessageAddress self)
        //{
        //    self.Init();
        //    self.m_eType = MessageAddress.EType.ClientProxy;
        //}

        //public static void SetAsStage(this MessageAddress self, int inZwid, ushort nServerNum)
        //{
        //    self.Init();
        //    self.m_eType = MessageAddress.EType.Stage;
        //    self.m_nServerNum = nServerNum;
        //    self.m_nZwid = inZwid;

        //    NetWorkHelper.CheckPacketAddress(MessageAddress.EType.Stage, inZwid, nServerNum);
        //}

        //public static void SetAsService(this MessageAddress self, int inZwid, ushort nServerNum, ServiceID serviceID)
        //{
        //    self.Init();
        //    self.m_eType = MessageAddress.EType.Service;
        //    self.m_nServerNum = nServerNum;
        //    self.m_nServiceType = (byte)serviceID.m_eType;
        //    self.m_nServiceIndex = serviceID.m_nIndex;
        //    self.m_nZwid = inZwid;

        //    NetWorkHelper.CheckPacketAddress(MessageAddress.EType.Service, inZwid, nServerNum);
        //}

        //public static void SetAsOtherServerService(this MessageAddress self, int inZwid, ushort nServerNum, ServiceID serviceID)
        //{
        //    self.Init();
        //    self.m_eType = MessageAddress.EType.Service;
        //    self.m_nServerNum = nServerNum;
        //    self.m_nServiceType = (byte)serviceID.m_eType;
        //    self.m_nServiceIndex = serviceID.m_nIndex;
        //    self.m_nZwid = inZwid;
        //    NetWorkHelper.CheckPacketOtherServerAddress(MessageAddress.EType.Service, inZwid, nServerNum);
        //}


        //public static void SetAsOtherServerGuid(this MessageAddress self, int inZwid, ushort nServerNum, long guid)
        //{
        //    self.Init();
        //    self.m_eType = MessageAddress.EType.GamePlayer;
        //    self.m_nServerNum = nServerNum;
        //    self.m_nGUID = guid;
        //    self.m_nZwid = inZwid;
        //    //{
        //    //    self.m_nBakZwid = ServerNumHelper.ZoneWorldId;
        //    //    self.m_nBakServerNum = ServerNumHelper.ServerNum;

        //    //}


        //    NetWorkHelper.CheckPacketOtherServerAddress(MessageAddress.EType.GamePlayer, inZwid, nServerNum);
        //}



        //public static void SetAsOtherGlobalMgr(this MessageAddress self, int inZwid, ushort nToServerNum,
        // GlobalManagerType globalMgrType)
        //{
        //    self.Init();

        //    self.m_eType = MessageAddress.EType.GlobalMgr;
        //    self.m_nServerNum = nToServerNum;
        //    self.m_nGlobalMgrType = (byte)globalMgrType;
        //    self.m_nZwid = inZwid;

        //    NetWorkHelper.CheckPacketOtherServerAddress(MessageAddress.EType.GlobalMgr, inZwid, nToServerNum);
        //}


        public static void SetAsGlobalMgr(this MessageAddress self, int inZwid, ushort nToServerNum, int globalMgrType)
        {
            self.Init();

            self.m_eType = MessageAddress.EType.GlobalMgr;
            self.m_nServerNum = nToServerNum;
            self.m_nGlobalMgrType = (byte)globalMgrType;
            self.m_nZwid = inZwid;

            //NetWorkHelper.CheckPacketAddress(MessageAddress.EType.GlobalMgr, inZwid, nToServerNum);
        }

        public static void SetAsScene(this MessageAddress self, int inZwid, ushort nToServerNum, ushort nSceneID)
        {
            self.Init();
            self.m_eType = MessageAddress.EType.Scene;
            self.m_nZwid = inZwid;
            self.m_nServerNum = nToServerNum;
            self.m_nSceneID = nSceneID;

            //NetWorkHelper.CheckPacketAddress(MessageAddress.EType.Scene, ServerNumHelper.ZoneWorldId, ServerNumHelper.ServerNum);
        }

        //public static void SetAsGuid(this MessageAddress self, int inZwid, ushort nToServerNum, long guid)
        //{
        //    self.Init();
        //    self.m_eType = MessageAddress.EType.GamePlayer;
        //    self.m_nZwid = inZwid;
        //    self.m_nServerNum = nToServerNum;
        //    self.m_nGUID = guid;

        //    NetWorkHelper.CheckPacketAddress(MessageAddress.EType.Scene, ServerNumHelper.ZoneWorldId, ServerNumHelper.ServerNum);
        //}

        //public static void SetAsOtherZoneWorldGuid(this MessageAddress self, int nZwid, ushort nToServerNum,
        //    long inGuid, int inBakZwid)
        //{
        //    self.Init();
        //    self.m_eType = MessageAddress.EType.GamePlayer;

        //    //这三个
        //    self.m_nZwid = nZwid;
        //    self.m_nServerNum = nToServerNum;
        //    self.m_nGUID = inGuid;

        //    self.m_nBakZwid = inBakZwid;
        //    self.m_nBakServerNum = ServerNumHelper.ServerNum;
        //    //NetWorkHelper.CheckPacketAddress(MessageAddress.EType.Scene, ServerNumHelper.ZoneWorldId, ServerNumHelper.ServerNum);
        //}

        //public static void SetAsLoginPlayer(this MessageAddress self, uint nPlayerID)
        //{
        //    self.Init();
        //    self.m_eType = MessageAddress.EType.LoginPlayer;
        //    self.m_nServerNum = ServerNumHelper.Login;
        //    self.m_nPlayerID = nPlayerID;

        //    NetWorkHelper.CheckPacketAddress(MessageAddress.EType.LoginPlayer, ServerNumHelper.ZoneWorldId, self.m_nServerNum);
        //}

        //public static void SetAsGMT(this MessageAddress self, int nZwid, ushort nToServerNum)
        //{
        //    self.Init();
        //    self.m_eType = MessageAddress.EType.GMT;
        //    self.m_nServerNum = ServerNumHelper.GMT;

        //    NetWorkHelper.CheckPacketAddress(MessageAddress.EType.GMT, ServerNumHelper.ZoneWorldId, self.m_nServerNum);
        //}

        //public static void SetAsGamePlayer(this MessageAddress self, int inZwid,
        //    ushort inServerNum, uint nPlayerID)
        //{
        //    self.Init();

        //    self.m_eType = MessageAddress.EType.GamePlayer;
        //    self.m_nServerNum = inServerNum;
        //    self.m_nZwid = inZwid;
        //    self.m_nPlayerID = nPlayerID;

        //    NetWorkHelper.CheckPacketAddress(MessageAddress.EType.GamePlayer, ServerNumHelper.ZoneWorldId, self.m_nServerNum);
        //}
    }
}
