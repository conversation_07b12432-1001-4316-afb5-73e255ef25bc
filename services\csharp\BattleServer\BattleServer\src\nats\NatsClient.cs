﻿using NATS.Client.Core;
using BattleServer.Service;
using Serilog;

namespace BattleServer.Nats;

/// <summary>
/// 全局 NATS 客户端
/// </summary>
public static class NatsClient
{
    /// <summary>
    /// GameService 客户端，用于向 GameServer 发送消息
    /// </summary>
    public static GameServiceClient GameServiceClient { get; private set; }

    /// <summary>
    /// 初始化 NATS RPC 客户端
    /// </summary>
    /// <param name="natsConnection">NATS 连接</param>
    public static void InitNatsRpcClient(NatsConnection natsConnection)
    {
        GameServiceClient = new GameServiceClient(natsConnection);
        Log.Information("[NatsClient] NATS RPC client initialized");
    }
}

/// <summary>
/// GameService 客户端
/// </summary>
public class GameServiceClient
{
    private readonly NatsConnection _connection;

    public GameServiceClient(NatsConnection connection)
    {
        _connection = connection ?? throw new ArgumentNullException(nameof(connection));
    }

    /// <summary>
    /// 发送战斗状态变化消息
    /// </summary>
    /// <param name="req">战斗状态变化请求</param>
    /// <param name="serverId">目标服务器ID</param>
    /// <returns>发送任务</returns>
    public async Task OnBattleStateChanged(BattleStateChangeReq req, string serverId)
    {
        try
        {
            var subject = $"/{serverId}/natsrpc.GameService/OnBattleStateChanged";

            Log.Information("[GameServiceClient] Preparing to send OnBattleStateChanged via NATS");
            Log.Information("[GameServiceClient] Subject: {Subject}", subject);
            Log.Information("[GameServiceClient] BattleId: {BattleId}, State: {State}, RemainTime: {RemainTime}ms, Round: {Round}",
                req.BattleId, req.State, req.RemainTimeMs, req.RoundCount);
            Log.Information("[GameServiceClient] Target GameServer ID: {ServerId}", serverId);

            await _connection.PublishAsync(subject, req);

            Log.Information("[GameServiceClient] Successfully sent OnBattleStateChanged to GameServer {ServerId}", serverId);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "[GameServiceClient] CRITICAL ERROR: Failed to send OnBattleStateChanged to GameServer {ServerId}", serverId);
            Log.Error("[GameServiceClient] This will cause GameServer to be out of sync with battle state!");
            throw;
        }
    }

    /// <summary>
    /// 发送回合开始消息
    /// </summary>
    /// <param name="req">回合开始请求</param>
    /// <param name="serverId">目标服务器ID</param>
    /// <returns>发送任务</returns>
    public async Task RoundStart(RoundStartReq req, string serverId)
    {
        try
        {
            var subject = $"/{serverId}/natsrpc.GameService/RoundStart";

            Log.Information("[GameServiceClient] Preparing to send RoundStart via NATS");
            Log.Information("[GameServiceClient] Subject: {Subject}", subject);
            Log.Information("[GameServiceClient] Player UID: {Uid}, Buffers: {BufferCount}, BoardInfo: {BoardCount}",
                req.Uid, req.Buffers.Count, req.PlayerBoards.Count);
            Log.Information("[GameServiceClient] Target GameServer ID: {ServerId}", serverId);

            await _connection.PublishAsync(subject, req);

            Log.Information("[GameServiceClient] Successfully sent RoundStart to GameServer {ServerId} for player {Uid}",
                serverId, req.Uid);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "[GameServiceClient] CRITICAL ERROR: Failed to send RoundStart to GameServer {ServerId} for player {Uid}",
                serverId, req.Uid);
            throw;
        }
    }

    /// <summary>
    /// 发送战斗开始消息
    /// </summary>
    /// <param name="req">战斗开始请求</param>
    /// <param name="serverId">目标服务器ID</param>
    /// <returns>发送任务</returns>
    public async Task RoundBattleStart(RoundBattleStartReq req, string serverId)
    {
        try
        {
            var subject = $"/{serverId}/natsrpc.GameService/RoundBattleStart";
            await _connection.PublishAsync(subject, req);

            Log.Information("[GameServiceClient] Sent RoundBattleStart - Uid: {Uid}, Seed: {Seed}, Teams: {TeamCount}, ServerId: {ServerId}",
                req.Uid, req.Seed, req.Team.Count, serverId);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "[GameServiceClient] Failed to send RoundBattleStart");
            throw;
        }
    }

    /// <summary>
    /// 发送回合战斗结束消息
    /// </summary>
    /// <param name="req">回合战斗结束请求</param>
    /// <param name="serverId">目标服务器ID</param>
    /// <returns>发送任务</returns>
    public async Task RoundBattleEnd(RoundBattleEndReq req, string serverId)
    {
        try
        {
            var subject = $"/{serverId}/natsrpc.GameService/RoundBattleEnd";
            await _connection.PublishAsync(subject, req);

            Log.Information("[GameServiceClient] Sent RoundBattleEnd - Uid: {Uid}, Winner: {WinUid}, Loser: {LoseUid}, ServerId: {ServerId}",
                req.Uid, req.WinUid, req.LoseUid, serverId);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "[GameServiceClient] Failed to send RoundBattleEnd");
            throw;
        }
    }

    /// <summary>
    /// 发送整场战斗结束消息
    /// </summary>
    /// <param name="req">战斗结束请求</param>
    /// <param name="serverId">目标服务器ID</param>
    /// <returns>发送任务</returns>
    public async Task BattleEnd(BattleEndReq req, string serverId)
    {
        try
        {
            var subject = $"/{serverId}/natsrpc.GameService/BattleEnd";
            await _connection.PublishAsync(subject, req);

            Log.Information("[GameServiceClient] Sent BattleEnd - Uid: {Uid}, BattleId: {BattleId}, Rank: {Rank}, WinStreak: {WinStreak}, Heroes: {HeroCount}, ServerId: {ServerId}",
                req.Uid, req.BattleId, req.Rank, req.WinStreak, req.Heros.Count, serverId);
        }
        catch (Exception ex)
        {
            Log.Error(ex, "[GameServiceClient] Failed to send BattleEnd");
            throw;
        }
    }
}
