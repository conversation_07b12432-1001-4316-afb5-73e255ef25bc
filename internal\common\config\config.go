package config

import (
	"time"
)

type EtcdConfig struct {
	Endpoints      []string `yaml:"endpoints"`
	Username       string   `yaml:"username"`
	Password       string   `yaml:"password"`
	DialTimeout    int      `yaml:"dial_timeout"`
	RequestTimeout int      `yaml:"request_timeout"`
	Prefix         string   `yaml:"prefix"`
	UsePrefix      bool     `yaml:"use_prefix"`
	TTL            int      `yaml:"ttl"`
}

type NatsConfig struct {
	Servers        string `yaml:"servers"`
	User           string `yaml:"user"`
	Pwd            string `yaml:"pwd"`
	RequestTimeOut int    `yaml:"request_time_out"`
	ReconnectWait  int    `yaml:"reconnect_wait"`
	MaxReconnects  int    `yaml:"max_reconnects"`
}

type MongoConfig struct {
	URL            string        `yaml:"url"`
	User           string        `yaml:"user"`
	Password       string        `yaml:"password"`
	Database       string        `yaml:"db"`
	AuthType       string        `yaml:"auth_type"`
	AuthSource     string        `yaml:"auth_source"`
	OperateTimeout time.Duration `yaml:"operate_timeout"`
	ConnectTimeout time.Duration `yaml:"connect_timeout"`
}

type LogConfig struct {
	Console       bool   `yaml:"console"`
	FilePath      string `yaml:"filepath"`
	Level         string `yaml:"level"`
	Rotate        bool   `yaml:"rotate"`
	RotateSize    int    `yaml:"rotate_size"`
	MaxAge        int    `yaml:"max_age"`
	MaxBackups    int    `yaml:"max_backups"`
	Compress      bool   `yaml:"compress"`
	RotateDaily   bool   `yaml:"rotate_daily"`
	BILogDir      string `yaml:"bi_log_dir"`
	BINormVersion string `yaml:"bi_norm_version"`
}
type DBConfig struct {
	Mongo *MongoConfig `yaml:"mongo"`
	Redis *RedisConfig `yaml:"redis"`
}

type PXXConfig struct {
	PXXEnable bool   `yaml:"pxx_enable"`
	ServerUrl string `yaml:"server_url"`
}

//type CommonConfig struct {
//	EtcdReg    EtcdConfig `yaml:"etcd_reg"`
//	EtcdConfig EtcdConfig `yaml:"etcd_config"`
//	NatsConfig NatsConfig `yaml:"nats_config"`
//}

type Config struct {
	Etcd EtcdConfig `yaml:"etcd"`
}

type NewConfig struct {
	Config Config     `yaml:"config"`
	Nats   NatsConfig `yaml:"nats"`
}

type RedisConfig struct {
	Addr           string `yaml:"addr"`
	Password       string `yaml:"passwd"`
	DB             int    `yaml:"db"`
	ReadTimeout    int    `yaml:"read_timeout"`
	WriteTimeout   int    `yaml:"write_timeout"`
	MaxConnections int    `yaml:"max_connections"`
	MaxIdle        int    `yaml:"max_idle"`
	IdleTimeout    int    `yaml:"idle_timeout"`
}
