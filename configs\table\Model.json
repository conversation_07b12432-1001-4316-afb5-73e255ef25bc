[{"ID": 1, "AssName": "sceneroot", "AB_Name": "sceneroot", "Type": 0, "EditorPath": "/CharacterResource/Common/", "Radius": 0, "HurtAnims": [], "HurtFusionAnim": -1, "SizeType": 0, "Node": -1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 2, "AssName": "blood", "AB_Name": "blood", "Type": 0, "EditorPath": "/CharacterResource/Common/", "Radius": 0, "HurtAnims": [], "HurtFusionAnim": -1, "SizeType": 0, "Node": -1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 3, "AssName": "damage", "AB_Name": "damage", "Type": 0, "EditorPath": "/CharacterResource/Common/", "Radius": 0, "HurtAnims": [], "HurtFusionAnim": -1, "SizeType": 0, "Node": -1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 4, "AssName": "playerroot", "AB_Name": "playerroot", "Type": 0, "EditorPath": "/CharacterResource/Common/", "Radius": 0, "HurtAnims": [], "HurtFusionAnim": -1, "SizeType": 0, "Node": -1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 5, "AssName": "monsterroot", "AB_Name": "monsterroot", "Type": 0, "EditorPath": "/CharacterResource/Common/", "Radius": 0, "HurtAnims": [], "HurtFusionAnim": -1, "SizeType": 0, "Node": -1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 6, "AssName": "rtdyuiplayer", "AB_Name": "rtdyuiplayer", "Type": 0, "EditorPath": "/CharacterResource/Common/", "Radius": 0, "HurtAnims": [], "HurtFusionAnim": -1, "SizeType": 0, "Node": -1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 7, "AssName": "dialog", "AB_Name": "dialog", "Type": 0, "EditorPath": "/CharacterResource/Common/", "Radius": 0, "HurtAnims": [], "HurtFusionAnim": -1, "SizeType": 0, "Node": -1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 10, "AssName": "mainscene", "AB_Name": "mainscene", "Type": 1, "EditorPath": "/CharacterResource/Scene/", "Radius": 10, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 1, "HeadInfoBloodHeight": 1, "HeadTitleHeightY": 1, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 11, "AssName": "mons_01", "AB_Name": "mons_01", "Type": 3, "EditorPath": "/CharacterResource/Monster/", "Radius": 3, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 2, "Physics": 0, "Scale": 0.3, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [0.7, 0.7], "StartAlphaTime": 0.5, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": 20005, "HurtEffectId": 2001, "HurtSound": -1, "HeadInfoDamageHeight": 1, "HeadInfoBloodHeight": 1.7, "HeadTitleHeightY": 2, "HeadPointHeightXY": [0, 2.4, 0], "BodyPointHeightXY": [0, 2, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 1, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 12, "AssName": "mons_02", "AB_Name": "mons_02", "Type": 3, "EditorPath": "/CharacterResource/Monster/", "Radius": 3, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 2, "Physics": 0, "Scale": 0.3, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [0.7, 0.7], "StartAlphaTime": 0.5, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": 20005, "HurtEffectId": 2001, "HurtSound": -1, "HeadInfoDamageHeight": 1, "HeadInfoBloodHeight": 1.7, "HeadTitleHeightY": 2, "HeadPointHeightXY": [0, 2, 0], "BodyPointHeightXY": [0, 1.6, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 1, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 13, "AssName": "mons_03", "AB_Name": "mons_03", "Type": 3, "EditorPath": "/CharacterResource/Monster/", "Radius": 3, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 2, "Physics": 0, "Scale": 0.3, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [0.7, 0.7], "StartAlphaTime": 0.5, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": 20005, "HurtEffectId": 2001, "HurtSound": -1, "HeadInfoDamageHeight": 1, "HeadInfoBloodHeight": 1.7, "HeadTitleHeightY": 2, "HeadPointHeightXY": [0, 2.4, 0], "BodyPointHeightXY": [0, 2, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 1, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 50, "AssName": "zhandou01wall", "AB_Name": "zhandou01wall", "Type": 1, "EditorPath": "/CharacterResource/Scene/", "Radius": 10, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 1, "HeadInfoBloodHeight": 1, "HeadTitleHeightY": 1, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 51, "AssName": "zhandou01wall_wet", "AB_Name": "zhandou01wall_wet", "Type": 1, "EditorPath": "/CharacterResource/Scene/", "Radius": 10, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 1, "HeadInfoBloodHeight": 1, "HeadTitleHeightY": 1, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 100, "AssName": "zhandou01", "AB_Name": "zhandou01", "Type": 1, "EditorPath": "/CharacterResource/Scene/", "Radius": 100, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 101, "AssName": "zhandou01_wet", "AB_Name": "zhandou01_wet", "Type": 1, "EditorPath": "/CharacterResource/Scene/", "Radius": 100, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": 52001, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 150, "AssName": "robot01", "AB_Name": "robot01", "Type": 2, "EditorPath": "/CharacterResource/Hero/", "Radius": 0.6, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 2, "Physics": 0, "Scale": 0.05, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 1.8, "HeadInfoBloodHeight": 1.8, "HeadTitleHeightY": 2.6, "HeadPointHeightXY": [0, 0.6, 0.2], "BodyPointHeightXY": [0, 0.3, 0.2], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 1, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 1.6, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 200, "AssName": "bella_back", "AB_Name": "bella_back", "Type": 2, "EditorPath": "/CharacterResource/Hero/", "Radius": 1.2, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 2, "Physics": 0, "Scale": 0.12, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 1.8, "HeadInfoBloodHeight": 1.8, "HeadTitleHeightY": 2.6, "HeadPointHeightXY": [0, 1.6, 0.2], "BodyPointHeightXY": [0, 1, 0.2], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 1, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 1.6, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 201, "AssName": "bella_front", "AB_Name": "bella_front", "Type": 2, "EditorPath": "/CharacterResource/Hero/", "Radius": 1.2, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 2, "Physics": 0, "Scale": 0.12, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 2.2, "HeadInfoBloodHeight": 2.2, "HeadTitleHeightY": 3, "HeadPointHeightXY": [0, 1.6, 0.2], "BodyPointHeightXY": [0, 1, 0.2], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 1.6, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 202, "AssName": "bella_back_wet", "AB_Name": "bella_back_wet", "Type": 2, "EditorPath": "/CharacterResource/Hero/", "Radius": 1.2, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 2, "Physics": 0, "Scale": 0.12, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 2, "HeadInfoBloodHeight": 2, "HeadTitleHeightY": 2.8, "HeadPointHeightXY": [0, 1.6, 0.2], "BodyPointHeightXY": [0, 1, 0.2], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 1.4, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 203, "AssName": "bella_front_wet", "AB_Name": "bella_front_wet", "Type": 2, "EditorPath": "/CharacterResource/Hero/", "Radius": 1.2, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 2, "Physics": 0, "Scale": 0.12, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 2.8, "HeadInfoBloodHeight": 2.8, "HeadTitleHeightY": 3.6, "HeadPointHeightXY": [0, 1.6, 0.2], "BodyPointHeightXY": [0, 1, 0.2], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 2.4, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 300, "AssName": "mons_01", "AB_Name": "mons_01", "Type": 3, "EditorPath": "/CharacterResource/Monster/", "Radius": 1.5, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 2, "Physics": 0, "Scale": 0.14, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [0.7, 0.7], "StartAlphaTime": 0.5, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": 2001, "HurtSound": -1, "HeadInfoDamageHeight": 1, "HeadInfoBloodHeight": 1.7, "HeadTitleHeightY": 2, "HeadPointHeightXY": [0, 1.4, 0], "BodyPointHeightXY": [0, 0.8, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 1, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 301, "AssName": "mons_02", "AB_Name": "mons_02", "Type": 3, "EditorPath": "/CharacterResource/Monster/", "Radius": 1.5, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 2, "Physics": 0, "Scale": 0.14, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [0.7, 0.7], "StartAlphaTime": 0.5, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": 2001, "HurtSound": -1, "HeadInfoDamageHeight": 1, "HeadInfoBloodHeight": 1.7, "HeadTitleHeightY": 2, "HeadPointHeightXY": [0, 1, 0], "BodyPointHeightXY": [0, 0.4, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 1, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 302, "AssName": "mons_03", "AB_Name": "mons_03", "Type": 3, "EditorPath": "/CharacterResource/Monster/", "Radius": 1.6, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 2, "Physics": 0, "Scale": 0.14, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [0.7, 0.7], "StartAlphaTime": 0.5, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": 2001, "HurtSound": -1, "HeadInfoDamageHeight": 1, "HeadInfoBloodHeight": 1.75, "HeadTitleHeightY": 2, "HeadPointHeightXY": [0, 1.6, 0], "BodyPointHeightXY": [0, 1, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 1, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 400, "AssName": "dropicon", "AB_Name": "dropicon", "Type": 4, "EditorPath": "/CharacterResource/Drop/", "Radius": 0, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 1.6, 0.2], "BodyPointHeightXY": [0, 1, 0.2], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 401, "AssName": "frontsight", "AB_Name": "frontsight", "Type": 4, "EditorPath": "/CharacterResource/Common/", "Radius": 0, "HurtAnims": [6], "HurtFusionAnim": -1, "SizeType": 0, "Node": 1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 1.6, 0.2], "BodyPointHeightXY": [0, 1, 0.2], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 500, "AssName": "bella_lh", "AB_Name": "bella_lh", "Type": 2, "EditorPath": "/CharacterResource/Hero/", "Radius": 0, "HurtAnims": [], "HurtFusionAnim": -1, "SizeType": 0, "Node": -1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 600, "AssName": "bella_tj", "AB_Name": "bella_tj", "Type": 2, "EditorPath": "/CharacterResource/Hero/", "Radius": 0, "HurtAnims": [], "HurtFusionAnim": -1, "SizeType": 0, "Node": -1, "Physics": 0, "Scale": 1, "Rotation": [0, 0, 0], "Location": [0, 0, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": -1, "HurtSound": -1, "HeadInfoDamageHeight": 0, "HeadInfoBloodHeight": 0, "HeadTitleHeightY": 0, "HeadPointHeightXY": [0, 0, 0], "BodyPointHeightXY": [0, 0, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 0, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 0, "ChatBubbleIds": [], "ChatBubbleTypes": []}, {"ID": 100001, "AssName": "boss_01", "AB_Name": "boss_01", "Type": 3, "EditorPath": "/CharacterResource/Monster/", "Radius": 4.8, "HurtAnims": [], "HurtFusionAnim": 10, "SizeType": 2, "Node": 2, "Physics": 0, "Scale": 0.4, "Rotation": [0, 0, 0], "Location": [0.1, -2, 0], "RTScaleValues": [1, 1], "StartAlphaTime": 0.5, "StartEffectId": -1, "StartSoundId": -1, "DeadEffectId": -1, "DeadSound": -1, "HurtEffectId": 2001, "HurtSound": -1, "HeadInfoDamageHeight": 6, "HeadInfoBloodHeight": 6, "HeadTitleHeightY": 6, "HeadPointHeightXY": [0, 5, 0], "BodyPointHeightXY": [0, 3.4, 0], "UIModelEffectScale": 1, "HeadOffset": 0, "FootOffset": 0, "DeathSlowShot": 1, "IsShowWeapon": 0, "ChatBubbleOffsetX": 0, "ChatBubbleOffsetY": 2.4, "ChatBubbleIds": [1002912, 1002913], "ChatBubbleTypes": [0, 0]}]