# 游戏服数据库架构说明

### 一、整体架构设计

#### 1.1 核心架构图

![](images/db_arch.png)

#### 1.2 分层设计

**项目采用基于 Actor 模型的分布式架构,数据层采用三层架构设计**:

```
Interface(IDatabase) -> Factory(存储工厂) -> Implementation(MongoDB实现)
```

核心特点:

1. 每个玩家作为独立 Actor,拥有自己的消息队列和 Goroutine
2. 采用工厂模式实现数据存储的可插拔设计
3. 通过统一接口规范化数据访问
4. 实现了同步/异步两种数据访问模式

##### 1.2.1 接口层 (Interface Layer)

```go
// IDatabase 定义存储接口
type IDatabase interface {
    Ping(ctx context.Context) error
    Close() error
    Stats() interface{}
}
```

这是顶层接口设计，定义了数据库操作的基本规范：

- 健康检查 (Ping)
- 资源清理 (Close)
- 状态统计 (Stats)

##### 1.2.2 工厂层 (Factory Layer)

```go
type Factory interface {
    Create(conf *Config) (IDatabase, error)
}

// 工厂注册机制
func Register(name string, factory Factory) {
    factoryMu.Lock()
    defer factoryMu.Unlock()
    factories[name] = factory
}
```

工厂层的主要职责：

- 管理存储实现的注册
- 创建具体的存储实例
- 提供统一的创建接口

##### 1.2.3 实现层 (Implementation Layer)

```go
type Mongo struct {
    client *mongo.Client
    db     *mongo.Database
    conf   *db.Config
    stats  *Stats
}

func (f *mongoFactory) Create(conf *db.Config) (db.IDatabase, error) {
    // MongoDB实例创建逻辑
}
```

#### 1.3 Actor模型应用

##### 1.3.1 玩家Actor设计

```go
type Player struct {
    uid    uint64
    db     *dbstruct.UserDB
    ...
}
```

每个玩家作为独立Actor：

- 拥有独立的消息队列
- 保证数据访问的线程安全
- 支持异步数据操作

##### 1.3.2 DAO Actor设计

```go
type DAO struct {
    *actor.Dispatcher
    Db            db.IDatabase
    totalRegister int
    maxRegister   int
    ...
}
```

DAO Actor负责：

- 处理所有数据库操作请求
- 管理数据库连接
- 实现数据访问控制

#### 1.4 关键设计特点

1. 松耦合设计

- 通过接口分离实现细节
- 工厂模式支持多种存储实现
- Actor模型实现组件隔离

2. 线程安全保证

- Actor模型保证数据访问安全
- 锁机制保护共享资源
- 消息传递代替直接调用

3. 可维护性

- 清晰的职责划分
- 标准化的接口定义
- 模块化的实现方式

### 二、数据模型设计

采用分层的数据结构设计:

```
UserDB (玩家数据主结构)
├── BaseDB (基础信息)
├── GameDB (游戏数据)
└── ExtraDB (扩展数据) 

GameData (服务器全局数据)
```

- 使用 Protocol Buffers 实现数据序列化
- 支持数据分片和增量更新
- 结构可扩展性强

### 三、核心组件

1. 数据访问层(DAO)

- 处理所有数据库操作
- 实现数据的CRUD接口
- 管理数据缓存

2. 存储工厂(Factory)

- 负责创建具体的数据库实例
- 支持注册多种存储实现
- 提供统一的创建接口

3. 集合封装(Collection)

- 封装 MongoDB 集合操作
- 提供高级查询接口
- 实现索引管理

4. 配置管理(Config)

- 统一的配置结构
- 支持选项模式配置
- 连接池管理

### 四、数据存储策略

1. 定时存储机制

```
Copy玩家数据存储触发条件:
- 定时触发(每60秒检查)
- 数据变更触发
- 玩家下线触发
```

2. 缓存管理

```
Copy缓存策略:
- 在线玩家始终缓存
- 离线玩家限时缓存
- LRU 淘汰机制
```

### 五、业务流程

![](images/db_oper.png)

### 六、性能优化设计

1. 批量操作优化

- 合并多次写操作
- 批量提交事务
- 异步写入

2. 索引优化

- 合理设计索引
- 避免过多索引
- 监控索引使用

3. 连接池管理

- 动态连接池
- 连接复用
- 超时控制

### 七、可用性设计

1. 错误处理

- 完整的错误处理链
- 数据一致性保证
- 异常恢复机制

1. 监控告警

- 性能指标监控
- 错误日志记录
- 状态监控告警