## 开发流程

### 添加玩家模块

1. 在`servers/gameserver/internal/logic/player/module`添加模块文件夹，并添加模块文件
2. 模块结构体以模块名命名，例：

```
type Hero struct {
	db     *dbstruct.Hero
	player iplayer.Player
}

func New(p iplayer.Player) *Hero {
	return &Hero{
		player: p,
	}
}
```

3. 在`servers/gameserver/internal/logic/player/player.go`中`Player`结构体增加模块，例子：

```
type Player struct {
	item      *item.Item           // 道具
	drama     *drama.Drama         // 剧本
	hero      *hero.Hero           // 伙伴
	formation *formation.Formation // 编队
}
```

4. 在`servers/gameserver/internal/logic/player/player_module.go`中注册模块，实现模块接口，例子：

```
func (p *Player) initModuleGroup() {
	p.item = item.New(p)
	p.register(p.item)

	p.drama = drama.New(p)
	p.register(p.drama)

	p.hero = hero.New(p)
	p.register(p.hero)

	p.formation = formation.New(p)
	p.register(p.formation)
}
```

5. 如果模块需要对外开放接口，需要将接口定义在`servers/gameserver/internal/logic/player/player_imp.go`
   使用`script/gen_interface.sh`生成接口到`servers/gameserver/internal/logic/player/iplayer/player_gen.go`

### 单元测试

* 在`servers/gameserver/internal/logic/player/test`中增加对应模块的单元测试

### 接口调试

* 使用`tools/cmd/debug_client`进行web接口调试
