#!/bin/bash

# Record start time
start_time=$(date +%T.%N)

# Dynamically get project name =========================
pushd "$(dirname "$0")" > /dev/null
cd ../../..
PROJECT_NAME=$(basename "$(pwd)")
popd > /dev/null

# Configuration file parameter handling ================
CONFIG_FLAG=0
if [ "$1" = "include_config" ]; then
    CONFIG_FLAG=1
    echo "[INFO] Configuration packaging mode enabled"
fi

# Timestamp generation =================================
datetime=$(date +%Y%m%H%M%S)

# Configure other parameters ==========================
MOD_DIR="../cmd"
DEPLOY_DIR="deploy_build"

# ==================== Cross-Compilation Setup ====================
export GOOS=linux
export GOARCH=amd64
export CGO_ENABLED=1
BUILD_VERSION="v1.0.1"

# Version information injection
BUILD_LDFLAGS="-s -w"
BUILD_LDFLAGS="${BUILD_LDFLAGS} -X './common/version.BuildVersion=${BUILD_VERSION}'"

# Timestamp generation
BUILD_TIME=$(date +%Y%m%d-%H%M%S)
BUILD_LDFLAGS="${BUILD_LDFLAGS} -X './common/version.BuildTime=${BUILD_TIME}'"

# Git commit information
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null)
if [ -z "$GIT_COMMIT" ]; then
    GIT_COMMIT="unknown"
fi
BUILD_LDFLAGS="${BUILD_LDFLAGS} -X './common/version.CommitID=${GIT_COMMIT}'"

# Create build directory ===============================
BUILD_DIR="${DEPLOY_DIR}/${PROJECT_NAME}_${BUILD_VERSION}_${datetime}"
mkdir -p "$BUILD_DIR"

# ==================== Service Compilation Process ====================
echo "[INFO] Starting service compilation..."

# Dependency synchronization
# echo "[INFO] Synchronizing dependencies..."
# go mod tidy -v
# go mod vendor -v

# Directory traversal logic
find "$MOD_DIR" -type d | while read -r dir; do
    if [ -f "$dir/main.go" ]; then
        service_dir="$dir"
        service_name=$(basename "$dir")

        # Create output directory
        output_dir_name="${PROJECT_NAME}_${service_name}_${BUILD_VERSION}_${datetime}"
        output_dir="${BUILD_DIR}/tmp/${output_dir_name}"
        mkdir -p "$output_dir"

        echo "[INFO] Compiling service: $service_name"

        # Build command with module path
        go build \
            -trimpath \
            -gcflags "all=-N -l" \
            -ldflags "$BUILD_LDFLAGS" \
            -o "${output_dir}/${service_name}" \
            "${dir}"
        if [ $? -ne 0 ]; then
            echo "[ERROR] Service compilation failed: $service_name"
            rm -rf "$output_dir"
        else
            echo "[SUCCESS] Service compilation success: $service_name"
            zip_name="${output_dir_name}.zip"
            echo "[INFO] Generating $zip_name"
            zip -j "${BUILD_DIR}/${zip_name}" "${output_dir}/${service_name}"
        fi
    fi
done

# Conditional config processing ========================
if [ "$CONFIG_FLAG" -eq 1 ]; then
    echo "[INFO] Integrating configuration files..."
    cp -r ../configs "${BUILD_DIR}/configs"
fi

# Calculate execution time =============================
end_time=$(date +%T.%N)
start_seconds=$(date -d "$start_time" +%s.%N)
end_seconds=$(date -d "$end_time" +%s.%N)
elapsed_seconds=$(echo "$end_seconds - $start_seconds" | bc)
elapsed_formatted=$(printf "%.3f" "$elapsed_seconds")

# Cleanup and output ===================================
rm -rf "${BUILD_DIR}/tmp"
echo "[SUCCESS] Build completed! Location: $(pwd)/${BUILD_DIR}"
echo "Total build time: ${elapsed_formatted}s"
xdg-open "$(pwd)/${BUILD_DIR}" 2>/dev/null || open "$(pwd)/${BUILD_DIR}" 2>/dev/null || true

# End pause
read -p "Press Enter to continue..."
