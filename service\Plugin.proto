syntax = "proto3";

package pb_plugin;
option go_package = "liteframe/internal/common/protos/pb_plugin";

import "DBProtocol.proto";

message PluginRequest {
    int32 msg_id = 1;
    bytes data = 2;
    dbstruct.UserDB user = 3;
}

message PluginResponse {
    int32 msg_id = 1;
    bytes data = 2;
    dbstruct.UserDB user = 3;
}

message RpcListRequest {
}

message RpcListResponse {
    repeated uint32 rpc_list = 1;
}

service GamePlugin {
    rpc Request(PluginRequest) returns (PluginResponse);
    rpc RpcList(RpcListRequest) returns (RpcListResponse);
}
