//
// @Author:zhouchen
// @Description: relation对API层的实现
// @Data: Created in 20:01 2023/6/6

package service

import (
	"context"
	v1 "liteframe/api/microservices/rank/v1"
	"liteframe/internal/microservices/rank/biz"

	"github.com/jinzhu/copier"
)

func (s *RankService) SyncRoleInfo(ctx context.Context, req *v1.SyncRoleInfoRequest) (*v1.SyncRoleInfoReply, error) {
	roleData := &biz.RankRoleData{}
	copier.Copy(roleData, &req.Info)

	err := s.clu.SyncRoleInfo(ctx, roleData)

	if err != nil {
		return nil, err
	}

	return &v1.SyncRoleInfoReply{
		Result: 1,
	}, nil
}

func (s *RankService) UpdateRoleRankInfo(ctx context.Context, req *v1.UpdateRoleRankRequest) (*v1.UpdateRoleRankReply, error) {
	rankData := []biz.RankValueData{}
	for _, info := range req.Info {
		rankData = append(rankData, biz.RankValueData{
			RankType:  info.RankType,
			RankValue: info.RankValue,
		})
	}

	err := s.clu.UpdateRoleRankInfo(ctx, &biz.RoleRankValueInfo{
		Uid:     req.GetGuid(),
		Zwid:    req.GetZwid(),
		KServer: req.GetKserver(),
		Data:    rankData,
	})

	if err != nil {
		return nil, err
	}

	return &v1.UpdateRoleRankReply{
		Result: 1,
	}, nil
}

func (s *RankService) GetRankListData(ctx context.Context, req *v1.GetRankListReq) (*v1.GetRankListReply, error) {
	param := &biz.RankListReqParam{}
	copier.Copy(param, req)

	var err error
	var data []*v1.RankDataInfo
	var selfInfo *v1.RankDataInfo
	if req.RankGroup == int32(v1.RankGroupType_RoleRank) {
		data, selfInfo, err = s.clu.GetRoleListData(ctx, param)
	} else {
		return nil, nil
	}

	if err != nil {
		return nil, err
	}

	reply := &v1.GetRankListReply{}
	copier.Copy(reply, param)
	reply.Data = data
	reply.SelfInfo = selfInfo

	s.log.Infof("GetRankListData, Size:", len(data))

	return reply, nil
}
