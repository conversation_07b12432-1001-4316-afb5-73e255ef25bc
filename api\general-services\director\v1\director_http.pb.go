// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.23.2
// source: general-services/director/v1/director.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationDirectorGetDirectorData = "/director.v1.Director/GetDirectorData"

type DirectorHTTPServer interface {
	GetDirectorData(context.Context, *GetDirectorReq) (*GetDirectorReply, error)
}

func RegisterDirectorHTTPServer(s *http.Server, srv DirectorHTTPServer) {
	r := s.Route("/")
	r.GET("/api/director/getdirector/{platform}/{channel}/{version}", _Director_GetDirectorData0_HTTP_Handler(srv))
}

func _Director_GetDirectorData0_HTTP_Handler(srv DirectorHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetDirectorReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationDirectorGetDirectorData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetDirectorData(ctx, req.(*GetDirectorReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetDirectorReply)
		return ctx.Result(200, reply)
	}
}

type DirectorHTTPClient interface {
	GetDirectorData(ctx context.Context, req *GetDirectorReq, opts ...http.CallOption) (rsp *GetDirectorReply, err error)
}

type DirectorHTTPClientImpl struct {
	cc *http.Client
}

func NewDirectorHTTPClient(client *http.Client) DirectorHTTPClient {
	return &DirectorHTTPClientImpl{client}
}

func (c *DirectorHTTPClientImpl) GetDirectorData(ctx context.Context, in *GetDirectorReq, opts ...http.CallOption) (*GetDirectorReply, error) {
	var out GetDirectorReply
	pattern := "/api/director/getdirector/{platform}/{channel}/{version}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationDirectorGetDirectorData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
