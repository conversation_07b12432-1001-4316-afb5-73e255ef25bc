//
// @Author:zhouchen
// @Description: biz逻辑部分包括领域对象，repo定义等等
// @Data: Created in 21:10 2023/6/6

package biz

import (
	"context"
	v1 "liteframe/api/microservices/rank/v1"

	"github.com/go-kratos/kratos/v2/log"
)

var (
// ErrUserNotFound is user not found.
// ErrUserNotFound = errors.NotFound(userv1.ErrorReason_USER_NOT_FOUND.String(), "user not found")
)

type RankRoleData struct {
	Uid        uint64 `redis:"Uid"`
	Zwid       int32  `redis:"Zwid"`
	KServer    int32  `redis:"KServer"`
	Name       string `redis:"Name"`
	Level      int32  `redis:"Level"`
	FightPoint int64  `redis:"FightPoint"`
}

type RankValueData struct {
	RankType  int32
	RankValue int64
}

// RankService 的领域对象定义
type RoleRankValueInfo struct {
	Uid     uint64
	Zwid    int32
	KServer int32
	Data    []RankValueData
}

type RankListReqParam struct {
	Uid       uint64
	RankGroup int32 //排行榜大类 0：角色 1：公会 2：队伍
	RankType  int32 //排行榜类型
	RankFlag  int32 //三级页签 0：总榜 1：好友 2：全服
	RankPage  int32 //分页 0：全部  （总共100行&每页50行）
}

type RemoveData struct {
	Uid     uint64
	Zwid    int32
	KServer int32
}

type RankServiceRepo interface {
	SyncRoleInfo(context.Context, *RankRoleData) error
	UpdateRoleRankInfo(context.Context, *RoleRankValueInfo) error
	GetRoleListData(context.Context, *RankListReqParam) ([]*v1.RankDataInfo, *v1.RankDataInfo, error)
}

type RankServiceUsecase struct {
	clr RankServiceRepo
	log *log.Helper
}

func NewRankServiceUsecase(clr RankServiceRepo, logger log.Logger) *RankServiceUsecase {
	return &RankServiceUsecase{clr: clr, log: log.NewHelper(logger)}
}

func (ru *RankServiceUsecase) SyncRoleInfo(ctx context.Context, clg *RankRoleData) error {
	return ru.clr.SyncRoleInfo(ctx, clg)
}

func (ru *RankServiceUsecase) UpdateRoleRankInfo(ctx context.Context, clg *RoleRankValueInfo) error {
	return ru.clr.UpdateRoleRankInfo(ctx, clg)
}

func (ru *RankServiceUsecase) GetRoleListData(ctx context.Context, param *RankListReqParam) ([]*v1.RankDataInfo, *v1.RankDataInfo, error) {
	return ru.clr.GetRoleListData(ctx, param)
}
