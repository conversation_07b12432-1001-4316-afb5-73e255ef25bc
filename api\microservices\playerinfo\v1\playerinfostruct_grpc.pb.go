//GrpcAddressType:PlayerInfoServer
//GrpcServerType:all

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.1
// source: microservices/playerinfo/v1/playerinfostruct.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PlayerInfoService_SyncPlayerInfo_FullMethodName    = "/Aurora.PlayerInfoServer.PlayerInfoService/SyncPlayerInfo"
	PlayerInfoService_GetPlayerInfo_FullMethodName     = "/Aurora.PlayerInfoServer.PlayerInfoService/GetPlayerInfo"
	PlayerInfoService_GetPlayerInfoList_FullMethodName = "/Aurora.PlayerInfoServer.PlayerInfoService/GetPlayerInfoList"
)

// PlayerInfoServiceClient is the client API for PlayerInfoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ServiceStart
type PlayerInfoServiceClient interface {
	// 同步玩家信息
	SyncPlayerInfo(ctx context.Context, in *SyncPlayerInfoRequest, opts ...grpc.CallOption) (*SyncPlayerInfoReply, error)
	// 查询玩家信息
	GetPlayerInfo(ctx context.Context, in *GetPlayerInfoRequest, opts ...grpc.CallOption) (*GetPlayerInfoReply, error)
	// 查询玩家信息
	GetPlayerInfoList(ctx context.Context, in *GetPlayerInfoListRequest, opts ...grpc.CallOption) (*GetPlayerInfoListReply, error)
}

type playerInfoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPlayerInfoServiceClient(cc grpc.ClientConnInterface) PlayerInfoServiceClient {
	return &playerInfoServiceClient{cc}
}

func (c *playerInfoServiceClient) SyncPlayerInfo(ctx context.Context, in *SyncPlayerInfoRequest, opts ...grpc.CallOption) (*SyncPlayerInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncPlayerInfoReply)
	err := c.cc.Invoke(ctx, PlayerInfoService_SyncPlayerInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playerInfoServiceClient) GetPlayerInfo(ctx context.Context, in *GetPlayerInfoRequest, opts ...grpc.CallOption) (*GetPlayerInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPlayerInfoReply)
	err := c.cc.Invoke(ctx, PlayerInfoService_GetPlayerInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *playerInfoServiceClient) GetPlayerInfoList(ctx context.Context, in *GetPlayerInfoListRequest, opts ...grpc.CallOption) (*GetPlayerInfoListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPlayerInfoListReply)
	err := c.cc.Invoke(ctx, PlayerInfoService_GetPlayerInfoList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PlayerInfoServiceServer is the server API for PlayerInfoService service.
// All implementations must embed UnimplementedPlayerInfoServiceServer
// for forward compatibility.
//
// ServiceStart
type PlayerInfoServiceServer interface {
	// 同步玩家信息
	SyncPlayerInfo(context.Context, *SyncPlayerInfoRequest) (*SyncPlayerInfoReply, error)
	// 查询玩家信息
	GetPlayerInfo(context.Context, *GetPlayerInfoRequest) (*GetPlayerInfoReply, error)
	// 查询玩家信息
	GetPlayerInfoList(context.Context, *GetPlayerInfoListRequest) (*GetPlayerInfoListReply, error)
	mustEmbedUnimplementedPlayerInfoServiceServer()
}

// UnimplementedPlayerInfoServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPlayerInfoServiceServer struct{}

func (UnimplementedPlayerInfoServiceServer) SyncPlayerInfo(context.Context, *SyncPlayerInfoRequest) (*SyncPlayerInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncPlayerInfo not implemented")
}
func (UnimplementedPlayerInfoServiceServer) GetPlayerInfo(context.Context, *GetPlayerInfoRequest) (*GetPlayerInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlayerInfo not implemented")
}
func (UnimplementedPlayerInfoServiceServer) GetPlayerInfoList(context.Context, *GetPlayerInfoListRequest) (*GetPlayerInfoListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPlayerInfoList not implemented")
}
func (UnimplementedPlayerInfoServiceServer) mustEmbedUnimplementedPlayerInfoServiceServer() {}
func (UnimplementedPlayerInfoServiceServer) testEmbeddedByValue()                           {}

// UnsafePlayerInfoServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PlayerInfoServiceServer will
// result in compilation errors.
type UnsafePlayerInfoServiceServer interface {
	mustEmbedUnimplementedPlayerInfoServiceServer()
}

func RegisterPlayerInfoServiceServer(s grpc.ServiceRegistrar, srv PlayerInfoServiceServer) {
	// If the following call pancis, it indicates UnimplementedPlayerInfoServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PlayerInfoService_ServiceDesc, srv)
}

func _PlayerInfoService_SyncPlayerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncPlayerInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayerInfoServiceServer).SyncPlayerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlayerInfoService_SyncPlayerInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayerInfoServiceServer).SyncPlayerInfo(ctx, req.(*SyncPlayerInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlayerInfoService_GetPlayerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayerInfoServiceServer).GetPlayerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlayerInfoService_GetPlayerInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayerInfoServiceServer).GetPlayerInfo(ctx, req.(*GetPlayerInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PlayerInfoService_GetPlayerInfoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPlayerInfoListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlayerInfoServiceServer).GetPlayerInfoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlayerInfoService_GetPlayerInfoList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlayerInfoServiceServer).GetPlayerInfoList(ctx, req.(*GetPlayerInfoListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PlayerInfoService_ServiceDesc is the grpc.ServiceDesc for PlayerInfoService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PlayerInfoService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Aurora.PlayerInfoServer.PlayerInfoService",
	HandlerType: (*PlayerInfoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncPlayerInfo",
			Handler:    _PlayerInfoService_SyncPlayerInfo_Handler,
		},
		{
			MethodName: "GetPlayerInfo",
			Handler:    _PlayerInfoService_GetPlayerInfo_Handler,
		},
		{
			MethodName: "GetPlayerInfoList",
			Handler:    _PlayerInfoService_GetPlayerInfoList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "microservices/playerinfo/v1/playerinfostruct.proto",
}
