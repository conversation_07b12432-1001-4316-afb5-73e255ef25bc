//
// @Author:zhouchen
// @Description: relation数据库操作合集
// @Data: Created in 16:56 2023/6/7

package data

import (
	"context"
	v1 "liteframe/api/microservices/rank/v1"
	"liteframe/internal/microservices/rank/biz"
	"strconv"
	"strings"
	"time"

	"github.com/fatih/structs"
	"github.com/go-redis/redis/v8"
	"github.com/jinzhu/copier"

	"github.com/go-kratos/kratos/v2/log"
)

var _ biz.RankServiceRepo = (*rankServiceRepo)(nil)

type rankServiceRepo struct {
	data *Data
	log  *log.Helper
}

type RankFlagType int32

const (
	LocalRank   RankFlagType = 0
	FriendRank  RankFlagType = 1
	KServerRank RankFlagType = 2
	AllRank     RankFlagType = 3
)

var roleRankCacheKey = func(uid uint64) string {
	return "roleRank#" + strconv.FormatUint(uid, 10)
}

func NewRankServiceRepo(data *Data, logger log.Logger) biz.RankServiceRepo {
	return &rankServiceRepo{
		data: data,
		log:  log.<PERSON><PERSON>(logger),
	}
}

func (r *rankServiceRepo) SyncRoleInfo(ctx context.Context, data *biz.RankRoleData) error {
	key := "rankRoleInfo#" + strconv.FormatUint(data.Uid, 10)
	inrM := structs.Map(data)
	err := r.data.redisCli.HMSet(ctx, key, inrM).Err()
	if err != nil {
		r.log.Errorf("fail to SyncRoleInfo cache:redis.HMSet(%v) error(%v)", key, err)
		return err
	}
	return nil
}

func (r *rankServiceRepo) UpdateRoleRankInfo(ctx context.Context, data *biz.RoleRankValueInfo) error {
	key := roleRankCacheKey(data.Uid)
	rankInfo := map[string]interface{}{}
	for _, info := range data.Data {
		strType := strconv.FormatInt(int64(info.RankType), 10)
		rankInfo[strType] = info.RankValue
	}
	err := r.data.redisCli.HMSet(ctx, key, rankInfo).Err()
	if err != nil {
		r.log.Errorf("fail to UpdateRoleRankInfo cache:redis.HMSet(%v) error(%v)", key, err)
		return err
	}

	r.AddLocalSet(ctx, "roleRank", data.Uid)
	r.AddKServerSet(ctx, "roleRank", data.Uid, data.KServer)
	r.AddAllSet(ctx, "roleRank", data.Uid)

	return nil
}

func (r *rankServiceRepo) AddLocalSet(ctx context.Context, key string, uid uint64) error {
	localKey := key
	localData := strconv.FormatUint(uid, 10)
	err := r.data.redisCli.SAdd(ctx, localKey, localData).Err()
	if err != nil {
		r.log.Errorf("fail to AddLocalSet cache:redis.HMSet(%v) error(%v)", localKey, err)
		return err
	}
	return nil
}

func (r *rankServiceRepo) AddKServerSet(ctx context.Context, key string, uid uint64, kserver int32) error {
	localKey := key + "#" + strconv.FormatInt(int64(kserver), 10)
	localData := strconv.FormatUint(uid, 10)
	err := r.data.redisCli.SAdd(ctx, localKey, localData).Err()
	if err != nil {
		r.log.Errorf("fail to AddLocalSet cache:redis.HMSet(%v) error(%v)", localKey, err)
		return err
	}
	return nil
}

func (r *rankServiceRepo) AddAllSet(ctx context.Context, key string, uid uint64) error {
	localKey := key
	localData := strconv.FormatUint(uid, 10)
	err := r.data.redisCli.SAdd(ctx, localKey, localData).Err()
	if err != nil {
		r.log.Errorf("fail to AddAllSet cache:redis.HMSet(%v) error(%v)", localKey, err)
		return err
	}
	return nil
}

func (r *rankServiceRepo) RemLocalSet(ctx context.Context, key string, uid uint64, zwid int32) error {
	localKey := key + "#" + strconv.FormatInt(int64(zwid), 10)
	localData := strconv.FormatUint(uid, 10)
	err := r.data.redisCli.SRem(ctx, localKey, localData).Err()
	if err != nil {
		r.log.Errorf("fail to AddLocalSet cache:redis.HMSet(%v) error(%v)", localKey, err)
		return err
	}
	return nil
}

func (r *rankServiceRepo) RemKServerSet(ctx context.Context, key string, uid uint64, zwid int32, kserver int32) error {
	localKey := key + "#" + strconv.FormatInt(int64(kserver), 10)
	localData := strconv.FormatInt(int64(zwid), 10) + "#" + strconv.FormatUint(uid, 10)
	err := r.data.redisCli.SRem(ctx, localKey, localData).Err()
	if err != nil {
		r.log.Errorf("fail to AddLocalSet cache:redis.HMSet(%v) error(%v)", localKey, err)
		return err
	}
	return nil
}

func (r *rankServiceRepo) RemAllSet(ctx context.Context, key string, uid uint64, zwid int32) error {
	localKey := key
	localData := strconv.FormatInt(int64(zwid), 10) + "#" + strconv.FormatUint(uid, 10)
	err := r.data.redisCli.SRem(ctx, localKey, localData).Err()
	if err != nil {
		r.log.Errorf("fail to AddAllSet cache:redis.HMSet(%v) error(%v)", localKey, err)
		return err
	}
	return nil
}

func (r *rankServiceRepo) UpdateCacheData(ctx context.Context, cacheName string, rankFlag string, sort *redis.Sort, cacheCnt int) error {
	count, err := r.data.redisCli.ZCard(ctx, cacheName).Result()
	if err != nil {
		log.Fatalf("Error checking sorted set: %v", err)
		return err
	}

	if count > 0 {
		return nil
	}

	data, err := r.data.redisCli.Sort(ctx, rankFlag, sort).Result()
	if err != nil {
		r.log.Errorf("fail to GetRankListData cache:redis.HMGet(%v) error(%v)", rankFlag, err)
		return err
	}

	pipeline := r.data.redisCli.Pipeline()
	for i := 0; i < len(data)/2 && i < cacheCnt; i++ {
		tempUid := strings.Split(data[i*2], "#")
		tempVal := data[i*2+1]

		if tempVal == "" {
			continue
		}

		score, _ := strconv.ParseFloat(tempVal, 10)
		uid := uint64(0)
		if len(tempUid) > 1 {
			uid, _ = strconv.ParseUint(tempUid[1], 10, 64)
		} else {
			uid, _ = strconv.ParseUint(tempUid[0], 10, 64)
		}

		pipeline.ZAdd(ctx, cacheName, &redis.Z{
			Score:  score,
			Member: uid,
		})
	}

	pipeline.Expire(ctx, cacheName, 60*time.Second)

	_, err = pipeline.Exec(ctx)
	if err != nil {
		log.Fatalf("Error setting expire: %v", err)
	}

	return nil
}

func (r *rankServiceRepo) CalcRankValue(rankType int32, mixValue int64) (int64, int64) {
	rankValue := mixValue
	rankValue2 := int64(-1)
	if rankType != int32(v1.RankType_FightPoint) {
		rankValue = mixValue >> 32
	}
	return rankValue, rankValue2
}

func (r *rankServiceRepo) GetTestRoleListData(ctx context.Context, param *biz.RankListReqParam) ([]*v1.RankDataInfo, *v1.RankDataInfo, error) {
	rankDatas := make([]*v1.RankDataInfo, 0)
	var selfRank *v1.RankDataInfo

	if param.RankGroup != int32(v1.RankGroupType_RoleRank) {
		return rankDatas, selfRank, nil
	}

	//get role info
	pipeline := r.data.redisCli.Pipeline()
	pipelineResult := []*redis.StringStringMapCmd{}
	for i := 0; i < 50; i++ {
		rankRoleKey := "rankRoleInfo#" + strconv.FormatUint(param.Uid, 10)
		pipelineResult = append(pipelineResult, pipeline.HGetAll(ctx, rankRoleKey))
	}
	_, _ = pipeline.Exec(ctx)

	for _, role := range pipelineResult {
		var roleInfo biz.RankRoleData
		err := role.Scan(&roleInfo)
		if err != nil || roleInfo.Uid <= 0 {
			continue
		}

		selfRank = &v1.RankDataInfo{
			Role: &v1.RoleRankInfo{
				Rank: -1,
			},
			RankValue:  0,
			RankValue2: -1,
		}
		copier.Copy(&selfRank.Role, &roleInfo)

		rankDatas = append(rankDatas, selfRank)
	}

	return rankDatas, selfRank, nil
}

func (r *rankServiceRepo) GetRoleListData(ctx context.Context, param *biz.RankListReqParam) ([]*v1.RankDataInfo, *v1.RankDataInfo, error) {
	rankDatas := make([]*v1.RankDataInfo, 0)
	var selfRank *v1.RankDataInfo

	if param.RankGroup != int32(v1.RankGroupType_RoleRank) {
		return rankDatas, selfRank, nil
	}

	//get role info
	rankRoleKey := "rankRoleInfo#" + strconv.FormatUint(param.Uid, 10)
	var selfRoleInfo biz.RankRoleData
	err := r.data.redisCli.HGetAll(ctx, rankRoleKey).Scan(&selfRoleInfo)
	if err != nil || selfRoleInfo.Uid <= 0 {
		return rankDatas, selfRank, err
	}

	rankGroup := "roleRank"
	rankType := param.RankType
	rankFlag := ""
	strBy := ""
	cacheRank := ""
	maxRankCount := 100
	if param.RankFlag == int32(KServerRank) {
		rankFlag = rankGroup + "#" + strconv.FormatInt(int64(selfRoleInfo.KServer), 10)
		strBy = rankFlag + "#*->" + strconv.FormatInt(int64(rankType), 10)
		cacheRank = "cacheRoleRank#KServer#" + strconv.FormatInt(int64(selfRoleInfo.KServer), 10) + "#" + strconv.FormatInt(int64(param.RankType), 10)
		maxRankCount = 150
	} else if param.RankFlag == int32(AllRank) {
		rankFlag = rankGroup
		strBy = rankFlag + "#*->" + strconv.FormatInt(int64(rankType), 10)
		cacheRank = "cacheRoleRank#All" + "#" + strconv.FormatInt(int64(param.RankType), 10)
		maxRankCount = 200
	}

	getArr := []string{}
	getArr = append(getArr, "#", strBy)

	sort := redis.Sort{
		By:     strBy,
		Offset: 0,
		Count:  int64(maxRankCount),
		Get:    getArr,
		Order:  "Desc",
	}

	err = r.UpdateCacheData(ctx, cacheRank, rankFlag, &sort, maxRankCount)
	if err != nil {
		r.log.Errorf("fail to UpdateCacheData cache:redis.HMGet(%v) error(%v)", strBy, err)
		return nil, selfRank, err
	}

	rankStart := 0
	rankStop := 100
	if param.RankPage > 0 {
		rankStart = 30 * int(param.RankPage-1)
		rankStop = rankStart + 30
	}

	data, err := r.data.redisCli.ZRevRangeWithScores(ctx, cacheRank, int64(rankStart), int64(rankStop)).Result()
	if err != nil {
		r.log.Errorf("fail to ZRange")
		return nil, selfRank, err
	}

	pipeline := r.data.redisCli.Pipeline()
	pipelineResult := make(map[uint64]*redis.StringStringMapCmd)
	for i := 0; i < len(data); i++ {
		uidStr := data[i].Member.(string)
		uid, _ := strconv.ParseUint(uidStr, 10, 64)
		rankRoleKey := "rankRoleInfo#" + strconv.FormatUint(uid, 10)
		pipelineResult[uid] = pipeline.HGetAll(ctx, rankRoleKey)
	}
	_, _ = pipeline.Exec(ctx)

	roleResult := make(map[uint64]*biz.RankRoleData)
	for i, rankIdx := 0, rankStart+1; i < len(data); i++ {
		uidStr := data[i].Member.(string)
		uid, _ := strconv.ParseUint(uidStr, 10, 64)

		mixValue := int64(data[i].Score)

		rankValue, rankValue2 := r.CalcRankValue(rankType, mixValue)

		roleInfo, ok := roleResult[uid]
		if !ok {
			continue
		}

		info := v1.RoleRankInfo{}
		copier.Copy(&info, &roleInfo)
		info.Rank = int32(rankIdx)
		rankIdx++

		rankDatas = append(rankDatas, &v1.RankDataInfo{
			Role:       &info,
			RankValue:  rankValue,
			RankValue2: rankValue2,
		})
	}

	selfRank = &v1.RankDataInfo{
		Role: &v1.RoleRankInfo{
			Rank: -1,
		},
		RankValue:  0,
		RankValue2: -1,
	}
	copier.Copy(&selfRank.Role, &selfRoleInfo)

	selfIdx, err := r.data.redisCli.ZRevRank(ctx, cacheRank, strconv.FormatUint(param.Uid, 10)).Result()
	if err == nil && selfIdx >= 0 {
		selfValue, err := r.data.redisCli.ZScore(ctx, cacheRank, strconv.FormatUint(param.Uid, 10)).Result()
		if err == nil {
			selfRank.RankValue, selfRank.RankValue2 = r.CalcRankValue(rankType, int64(selfValue))
		}
		selfRank.Role.Rank = int32(selfIdx) + 1
	} else {
		selfKey := "roleRank#" + strconv.FormatUint(selfRank.Role.Uid, 10)
		tempValue := 0
		err := r.data.redisCli.HMGet(ctx, selfKey).Scan(tempValue)
		if err != nil {
			selfRank.RankValue, selfRank.RankValue2 = r.CalcRankValue(rankType, int64(tempValue))
		}
	}

	return rankDatas, selfRank, nil
}
