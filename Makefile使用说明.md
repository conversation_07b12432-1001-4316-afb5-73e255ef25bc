# LiteFrame Makefile 使用说明

## 概述

统一的Makefile支持Linux环境构建（包括WSL），包含matchserver，并提供自包含的BattleServer构建。

## 核心特性

### 🎯 Linux统一环境
- **统一Linux平台** (包括WSL环境)
- **无文件后缀** (Linux可执行文件)
- **Linux .NET RID** (linux-x64)

### 🚀 统一构建命令
- **单个服务构建**: `make gameserver`, `make battleserver`
- **批量构建**: `make gamelogic_build`, `make build_all`

### 📦 自包含发布
- **BattleServer**: 自动生成自包含的单文件可执行程序
- **Go服务**: 包含所有依赖的静态链接二进制文件

## 基本使用

### 查看帮助信息
```bash
make
# 或
make info
# 或
make help
```

### 构建单个服务

#### 游戏逻辑服务
```bash
make gameserver      # 游戏服务器
make matchserver     # 匹配服务器 (新增)
make webserver       # Web服务器
make payserver       # 支付服务器
```

#### C# 战斗服务器
```bash
make battleserver           # 自包含版本 (推荐)
make battleserver_framework # 框架依赖版本
```

#### 通用服务
```bash
make announcement    # 公告服务
make charlist        # 角色列表服务
make serverlist      # 服务器列表服务
make textdetect      # 文本检测服务
make director        # 导演服务
```

#### 微服务
```bash
make chat           # 聊天服务
make friend         # 好友服务
make playerinfo     # 玩家信息服务
make rank           # 排行榜服务
make redeemcode     # 兑换码服务
```

### 批量构建

#### 按类别构建
```bash
make gamelogic_build    # 所有游戏逻辑服务 (包含matchserver)
make general_build      # 所有通用服务
make micro_build        # 所有微服务
```

#### 便捷构建组合
```bash
make core_services      # 核心服务 (gameserver, matchserver, webserver, battleserver)
make game_services      # 游戏相关服务 (gamelogic + battleserver)
make build_all          # 所有服务
```

### 构建命令

所有构建都针对Linux环境（包括WSL）：
```bash
make gameserver       # 单个服务
make gamelogic_build  # 游戏逻辑服务
make battleserver     # BattleServer
make build_all        # 所有服务
```

## 实际使用示例

### 日常开发 (推荐)
```bash
# 构建核心游戏服务
make core_services

# 或只构建游戏相关服务
make game_services
```

### 完整构建
```bash
# 所有服务
make build_all
```

### 单独测试某个服务
```bash
# 快速构建并测试gameserver
make gameserver
./bin/gameserver --help

# 快速构建并测试battleserver
make battleserver
./bin/BattleServer.exe --help
```

### 生产部署准备
```bash
# 清理旧文件
make clean

# 构建生产版本
make build_all

# 检查生成的文件
ls -la bin/
```

## 输出文件

所有构建的可执行文件都输出到 `bin/` 目录：

```
bin/
├── gameserver
├── matchserver
├── webserver
├── payserver
├── BattleServer
├── announcement
├── charlist
├── serverlist
├── textdetect
├── director
├── chat
├── friend
├── playerinfo
├── rank
└── redeemcode
```

## 高级功能

### 依赖管理
```bash
make vendor     # 生成vendor包
make deps       # 使用vendor依赖
```

### 代码生成
```bash
make api_all        # 生成所有API
make proto_all      # 生成所有proto
make wire_all       # 生成依赖注入代码
```

### 测试
```bash
make test_all       # 运行所有测试
```

### 清理
```bash
make clean          # 清理构建产物
```

## 环境要求

### Go 环境
- **Go 1.24.0+**
- **CGO支持** (某些依赖需要)

### C# 环境
- **.NET 9.0 SDK**

### 系统工具
- **make** (Linux环境，包括WSL)
- **git** (版本信息获取)

## 故障排除

### 常见问题

#### 1. "make: command not found"
```bash
# 在Linux/WSL中安装make
sudo apt-get install make
# 或
sudo yum install make
```

#### 2. "CGO compilation failed"
```bash
# 确保安装了C编译器
# Linux: gcc
sudo apt-get install gcc
# 或
sudo yum install gcc
```

#### 3. ".NET SDK not found"
```bash
# 下载安装 .NET 9.0 SDK
# https://dotnet.microsoft.com/download
```

#### 4. "vendor directory not found"
```bash
# 生成vendor依赖
make vendor

# 或解压现有vendor
make deps
```

### 调试技巧

#### 查看详细构建过程
```bash
# 显示详细信息
make gameserver VERBOSE=1

# 或直接使用go build查看
go build -v -x cmd/game-logic/gameserver/main.go
```

#### 检查平台检测
```bash
make info
```

#### 单步构建测试
```bash
# 测试Go环境
go version

# 测试.NET环境
dotnet --version

# 测试vendor依赖
ls vendor/
```

## 性能优化

### 并行构建
```bash
# 使用多核并行构建
make -j4 build_all
```

### 增量构建
- Makefile会自动检测依赖变化
- 只重新构建修改过的服务

### 缓存优化
- Go模块缓存: `$GOPATH/pkg/mod`
- .NET包缓存: `~/.nuget/packages`

## 最佳实践

### 开发阶段
1. 使用 `make core_services` 快速构建核心服务
2. 使用 `make clean` 定期清理构建产物
3. 在Linux环境（包括WSL）中进行开发和测试

### 测试阶段
1. 使用 `make build_all` 构建完整服务
2. 使用 `make test_all` 运行所有测试
3. 验证所有服务正常启动

### 生产部署
1. 使用 `make build_all` 构建生产版本
2. 验证所有依赖都已包含
3. 测试服务在目标环境正常运行

## 与旧版本的区别

### 新增功能
- ✅ **matchserver支持** - 现在包含在gamelogic_build中
- ✅ **Linux统一环境** - 专注Linux环境（包括WSL）
- ✅ **统一构建命令** - make battleserver直接生成自包含版本
- ✅ **便捷构建组合** - core_services, game_services等
- ✅ **详细构建信息** - make info显示当前配置

### 改进功能
- 🔄 **更好的错误处理** - 构建失败时提供清晰信息
- 🔄 **更快的构建速度** - 优化了构建参数和依赖
- 🔄 **更清晰的输出** - 显示正在构建的服务
- 🔄 **简化平台支持** - 移除Windows特定逻辑，统一Linux环境

### 保持兼容
- ✅ **原有命令仍可用** - make gameserver, make webserver等
- ✅ **vendor机制不变** - 仍支持vendor依赖管理
- ✅ **输出目录不变** - 仍输出到bin/目录
