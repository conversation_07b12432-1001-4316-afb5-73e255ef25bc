package main

import (
	"context"
	"flag"
	"fmt"
	"github.com/go-kratos/kratos/contrib/registry/etcd/v2"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	clientv3 "go.etcd.io/etcd/client/v3"
	_ "go.uber.org/automaxprocs"
	"liteframe/internal/common/constant"
	"liteframe/internal/general-services/director/boot"
	"liteframe/internal/general-services/director/conf"
	"os"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name = constant.ServiceNameDirector
	// Version is the version of the compiled software.
	Version       string
	ServerVersion string
	ClientVersion string

	id, _   = os.Hostname()
	PidFile = constant.ServiceNameDirector + ".pid"
)

func newApp(logger log.Logger, hs *http.Server, r *etcd.Registry, meta map[string]string) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(meta),
		kratos.Logger(logger),
		kratos.Server(
			hs,
		),
		kratos.Registrar(r),
		kratos.AfterStart(afterStart),
	)
}

func afterStart(context.Context) error {
	pid := fmt.Sprintf("%d", os.Getpid())
	f, err := os.OpenFile(PidFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		panic(err)
	}
	err = os.WriteFile(PidFile, []byte(pid), 0)
	if err != nil {
		panic(err)
	}
	err = f.Close()
	if err != nil {
		panic(err)
	}
	return nil
}

var (
	flagconf    string
	localConfig string
)

func init() {
	// 获取命令行参数
	flag.StringVar(&flagconf, "conf", "../configs/config.yaml", "config path, eg: -conf config.yaml")
	flag.StringVar(&localConfig, "localconf", "", "config path, eg: -localconf director.yaml")
}

func main() {
	flag.Parse()

	// 加载本地配置
	bootstrapConf := config.New(config.WithSource(file.NewSource(flagconf)))
	if err := bootstrapConf.Load(); err != nil {
		panic(err)
	}
	var bc conf.Bootstrap
	if err := bootstrapConf.Scan(&bc); err != nil {
		panic(err)
	}

	// 加载远程配置
	client, err := clientv3.New(clientv3.Config{
		Endpoints: bc.Config.Etcd.Endpoints,
		Username:  bc.Config.Etcd.Username,
		Password:  bc.Config.Etcd.Password,
	})
	if err != nil {
		panic(err)
	}
	defer client.Close()
	// 加载etcd中的配置
	boot.NewBootConf().LoadEtcdConfig(client, &bc)

	// 初始化log
	logger := boot.NewBootLog(&bc).Run()

	//logger.Log("Starting service: name=%s, version=%s", bc.GetName(), Version)

	metaData := map[string]string{}
	metaData["ServerVersion"] = ServerVersion
	metaData["ClientVersion"] = ClientVersion

	app, cleanup, err := wireApp(bc.Server, bc.Config, bc.Registry, logger, client, metaData)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err = app.Run(); err != nil {
		panic(err)
	}
}
