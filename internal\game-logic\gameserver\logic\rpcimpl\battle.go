package rpcimpl

import (
	"context"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/game-logic/gameserver/logic/player"
)

type battleService struct {
	defaultService
}

func (g *battleService) MatchBattle(ctx context.Context, p *player.Player, in *cs.CLMatchReq) (out *cs.LCMatchRsp) {
	out = p.GetBattle().Match(ctx, in)
	return out
}

func (g *battleService) RoundBattleStart(ctx context.Context, p *player.Player, in *cs.CLRoundBattleStartReq) (out *cs.LCRoundBattleStartResp) {
	out = p.GetBattle().RoundBattleStart(ctx, in)
	return out
}

func (g *battleService) SelectBuffer(ctx context.Context, p *player.Player, in *cs.CLSelectBufferReq) (out *cs.LCSelectBufferResp) {
	out = p.GetBattle().SelectBuffer(ctx, in)
	return out
}

func (g *battleService) MergeHero(ctx context.Context, p *player.Player, in *cs.CLMergeReq) (out *cs.LCMergeRsp) {
	out = p.GetBattle().MergeHero(ctx, in)
	return out
}

func (g *battleService) BattleReady(ctx context.Context, p *player.Player, in *cs.CLReadyReq) (out *cs.LCReadyRsp) {
	out = p.GetBattle().BattleReady(ctx, in)
	return out
}

func (g *battleService) RoundBattleEnd(ctx context.Context, p *player.Player, in *cs.CLRoundBattleEndReq) (out *cs.LCRoundBattleEndResp) {
	out = p.GetBattle().RoundBattleEnd(ctx, in)
	return out
}

func (g *battleService) ClaimAdReward(ctx context.Context, p *player.Player, in *cs.CLClaimAdRewardReq) (out *cs.LCClaimAdRewardRsp) {
	out = p.GetBattle().ClaimAdReward(ctx, in)
	return out
}
