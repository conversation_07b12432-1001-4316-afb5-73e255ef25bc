// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"liteframe/internal/microservices/friend/biz"
	"liteframe/internal/microservices/friend/conf"
	"liteframe/internal/microservices/friend/data"
	"liteframe/internal/microservices/friend/registry"
	"liteframe/internal/microservices/friend/server"
	"liteframe/internal/microservices/friend/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init serverlist application.
func wireApp(confServer *conf.Server, confData *conf.Data, bootstrap *conf.Bootstrap, logger log.Logger, arg map[string]string) (*kratos.App, func(), error) {
	cmdable := data.NewRedisCmd(confData, logger)
	dataData, err := data.NewData(cmdable, logger)
	if err != nil {
		return nil, nil, err
	}
	confRegistry := registry.ProvideRegistry(bootstrap)
	etcdRegistry := registry.NewEtcdRegistry(confRegistry)
	textDetectClient := data.NewTextDetectClient(etcdRegistry)
	chatServiceClient := data.NewChatServiceClient(etcdRegistry)
	friendserviceRepo := data.NewFriendserviceRepo(dataData, logger, textDetectClient, chatServiceClient)
	friendserviceUsecase := biz.NewFriendserviceUsecase(friendserviceRepo, logger, bootstrap)
	friendserviceService := service.NewFriendserviceServiceService(friendserviceUsecase)
	grpcServer := server.NewGRPCServer(confServer, friendserviceService, logger)
	httpServer := server.NewHTTPServer(confServer, friendserviceService, logger)
	app := newApp(logger, grpcServer, httpServer, etcdRegistry, arg)
	return app, func() {
	}, nil
}
