//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"liteframe/internal/general-services/serverlist/biz"
	"liteframe/internal/general-services/serverlist/conf"
	"liteframe/internal/general-services/serverlist/registry"
	"liteframe/internal/general-services/serverlist/server"
	"liteframe/internal/general-services/serverlist/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	clientv3 "go.etcd.io/etcd/client/v3"
)

// Global variable to store usecase reference for configuration changes
var (
	globalServerListUsecase *biz.ServerlistUsecase
)

// wireApp init serverlist application.
func wireApp(*conf.Server, *conf.Data, *conf.Bootstrap, log.Logger, *clientv3.Client, map[string]string) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, biz.ProviderSet, service.ProviderSet, registry.ProviderSet, newApp))
}
