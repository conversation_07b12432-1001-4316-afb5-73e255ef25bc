using System;
using System.Collections.Generic;
using System.Linq;
using BattleServer.Game.Core;
using BattleServer.Game.Board;
using BattleServer.Game.Player;
using BattleServer.Service;
using LiteFrame.Framework;
using Game.Core;

namespace BattleServer.Game.Battle
{
    /// <summary>
    /// 战斗实例管理器 - 管理4人自走棋中的2个2v2战斗实例
    /// </summary>
    public class BattleInstanceManager : BattleComponentBase
    {
        private Dictionary<string, BattleInstance> _instances;
        private Dictionary<long, string> _playerToInstance;
        private PlayerManager _playerManager; // 新增：玩家管理器引用
        private string _logName;

        public BattleInstanceManager()
        {
            _instances = new Dictionary<string, BattleInstance>();
            _playerToInstance = new Dictionary<long, string>();
            _logName = "BattleInstanceManager";
        }

        /// <summary>
        /// 设置玩家管理器引用
        /// </summary>
        public void SetPlayerManager(PlayerManager playerManager)
        {
            _playerManager = playerManager;
        }

        protected override void OnInitialize()
        {
            _instances.Clear();
            _playerToInstance.Clear();
            Log.Info($"[{_logName}] Initialized for battle {BattleId}");
        }

        protected override void OnClear()
        {
            foreach (var instance in _instances.Values)
            {
                instance.Dispose();
            }
            _instances.Clear();
            _playerToInstance.Clear();
        }

        /// <summary>
        /// 根据对手配对创建战斗实例
        /// </summary>
        public void CreateInstances(Dictionary<long, long> opponentPairs)
        {
            _instances.Clear();
            _playerToInstance.Clear();

            var processedPlayers = new HashSet<long>();
            int instanceIndex = 1;

            foreach (var pair in opponentPairs)
            {
                var playerId = pair.Key;
                var opponentId = pair.Value;

                // 避免重复处理同一对玩家（但不包括AI对战的情况）
                if (processedPlayers.Contains(playerId))
                    continue;

                var instanceId = $"{BattleId}_{instanceIndex}";
                List<long> playerIds;

                // 检查是否是与淘汰玩家的配对
                bool isEliminatedOpponent = (opponentId != 0 && _playerManager != null && _playerManager.IsPlayerEliminated(opponentId));
                bool isEliminatedPlayer = (_playerManager != null && _playerManager.IsPlayerEliminated(playerId));

                if (opponentId == 0)
                {
                    // 与AI对战：只有一个真实玩家
                    playerIds = new List<long> { playerId };
                    Log.Info($"[{_logName}] Created instance {instanceId} for player {playerId} vs AI");
                }
                else if (isEliminatedOpponent || isEliminatedPlayer)
                {
                    // 与淘汰玩家对战：包含淘汰玩家但仍创建实例
                    if (processedPlayers.Contains(opponentId))
                        continue;

                    playerIds = new List<long> { playerId, opponentId };
                    processedPlayers.Add(opponentId);

                    if (isEliminatedOpponent)
                    {
                        Log.Info($"[{_logName}] Created instance {instanceId} for active player {playerId} vs eliminated player {opponentId}");
                    }
                    else
                    {
                        Log.Info($"[{_logName}] Created instance {instanceId} for eliminated player {playerId} vs active player {opponentId}");
                    }
                }
                else
                {
                    // 正常对战：两个活跃玩家
                    if (processedPlayers.Contains(opponentId))
                        continue;

                    playerIds = new List<long> { playerId, opponentId };
                    processedPlayers.Add(opponentId);
                    Log.Info($"[{_logName}] Created instance {instanceId} for active players {playerId} vs {opponentId}");
                }

                var instance = new BattleInstance(instanceId, playerIds, BattleId);
                instance.SetPlayerManager(_playerManager); // 设置玩家管理器引用
                _instances[instanceId] = instance;

                _playerToInstance[playerId] = instanceId;
                if (opponentId != 0)
                {
                    _playerToInstance[opponentId] = instanceId;
                }

                processedPlayers.Add(playerId);
                instanceIndex++;
            }

            Log.Info($"[{_logName}] Created {_instances.Count} battle instances for {processedPlayers.Count} players");
        }

        /// <summary>
        /// 根据玩家ID获取对应的战斗实例
        /// </summary>
        public BattleInstance? GetInstanceByPlayerId(long playerId)
        {
            if (_playerToInstance.TryGetValue(playerId, out var instanceId))
            {
                return _instances.GetValueOrDefault(instanceId);
            }
            return null;
        }

        /// <summary>
        /// 检查所有实例是否都完成战斗
        /// </summary>
        public bool AllInstancesFinished()
        {
            return _instances.Values.All(i => i.IsFinished);
        }

        /// <summary>
        /// 获取所有未完成的战斗实例
        /// </summary>
        public List<BattleInstance> GetUnfinishedInstances()
        {
            return _instances.Values.Where(i => !i.IsFinished).ToList();
        }

        /// <summary>
        /// 获取所有已完成的战斗实例
        /// </summary>
        public List<BattleInstance> GetFinishedInstances()
        {
            return _instances.Values.Where(i => i.IsFinished).ToList();
        }

        /// <summary>
        /// 检查是否所有实例都已完成（别名方法）
        /// </summary>
        public bool AreAllInstancesFinished()
        {
            return AllInstancesFinished();
        }

        /// <summary>
        /// 开始所有实例的战斗
        /// </summary>
        public void StartAllBattles()
        {
            foreach (var instance in _instances.Values)
            {
                instance.StartBattle();
            }
            Log.Info($"[{_logName}] Started all {_instances.Count} battle instances");
        }

        /// <summary>
        /// 获取所有实例的战斗结果
        /// </summary>
        public Dictionary<long, bool> GetAllBattleResults()
        {
            var results = new Dictionary<long, bool>();
            
            foreach (var instance in _instances.Values)
            {
                if (instance.IsFinished && instance.Result != null)
                {
                    foreach (var playerId in instance.PlayerIds)
                    {
                        results[playerId] = (playerId == instance.Result.WinnerId);
                    }
                }
            }
            
            return results;
        }

        /// <summary>
        /// 获取实例数量
        /// </summary>
        public int InstanceCount => _instances.Count;

        /// <summary>
        /// 路由玩家的合并英雄操作到对应的战斗实例（包含移动和合成）
        /// </summary>
        public MergeHeroResp MergeHero(long playerId, int fromGridId, int toGridId, IList<PBMoveOperation> moves = null)
        {
            if (!_playerToInstance.TryGetValue(playerId, out string? instanceId))
            {
                Log.Warning($"[{_logName}] Player {playerId} not found in any battle instance");
                return new MergeHeroResp { Code = -3, From = fromGridId, To = toGridId, NewHero = null };
            }

            if (!_instances.TryGetValue(instanceId, out BattleInstance? instance))
            {
                Log.Warning($"[{_logName}] Battle instance {instanceId} not found");
                return new MergeHeroResp { Code = -4, From = fromGridId, To = toGridId, NewHero = null };
            }

            return instance.MergeHero(playerId, fromGridId, toGridId, moves);
        }

        /// <summary>
        /// 获取玩家所在的战斗实例
        /// </summary>
        public BattleInstance? GetPlayerInstance(long playerId)
        {
            if (_playerToInstance.TryGetValue(playerId, out string? instanceId) &&
                _instances.TryGetValue(instanceId, out BattleInstance? instance))
            {
                return instance;
            }
            return null;
        }

        /// <summary>
        /// 路由玩家的添加实体操作到对应的战斗实例
        /// </summary>
        public bool AddEntity(long playerId, int configId, int row, int col)
        {
            var instance = GetPlayerInstance(playerId);
            if (instance == null)
            {
                Log.Warning($"[{_logName}] Player {playerId} not found in any battle instance");
                return false;
            }

            return instance.AddEntity(configId, playerId, row, col);
        }

        /// <summary>
        /// 路由玩家的移动实体操作到对应的战斗实例
        /// </summary>
        public bool MoveEntity(long playerId, int fromRow, int fromCol, int toRow, int toCol)
        {
            var instance = GetPlayerInstance(playerId);
            if (instance == null)
            {
                Log.Warning($"[{_logName}] Player {playerId} not found in any battle instance");
                return false;
            }

            return instance.MoveEntity(fromRow, fromCol, toRow, toCol);
        }
    }

    /// <summary>
    /// 战斗实例 - 代表一个2v2战斗
    /// </summary>
    public class BattleInstance : IDisposable
    {
        public string InstanceId { get; }
        public List<long> PlayerIds { get; }
        public bool IsFinished { get; private set; }
        public BattleResult? Result { get; private set; }
        public CheckerBoard CheckerBoard { get; }
        private PlayerManager? _playerManager; // 新增：玩家管理器引用

        public BattleInstance(string instanceId, List<long> playerIds, long battleId)
        {
            InstanceId = instanceId;
            PlayerIds = new List<long>(playerIds);
            IsFinished = false;
            CheckerBoard = new CheckerBoard();

            // 初始化CheckerBoard
            CheckerBoard.Initialize(battleId);

            Log.Info($"[BattleInstance] {InstanceId} created with players: {string.Join(", ", playerIds)}");
        }

        /// <summary>
        /// 设置玩家管理器引用
        /// </summary>
        public void SetPlayerManager(PlayerManager playerManager)
        {
            _playerManager = playerManager;
        }

        /// <summary>
        /// 开始战斗
        /// </summary>
        public void StartBattle()
        {
            IsFinished = false;

            // 检查是否有淘汰玩家参与
            bool hasEliminatedPlayer = false;
            long eliminatedPlayerId = 0;
            long activePlayerId = 0;

            if (_playerManager != null)
            {
                foreach (var playerId in PlayerIds)
                {
                    if (_playerManager.IsPlayerEliminated(playerId))
                    {
                        hasEliminatedPlayer = true;
                        eliminatedPlayerId = playerId;
                    }
                    else
                    {
                        activePlayerId = playerId;
                    }
                }
            }

            if (hasEliminatedPlayer && PlayerIds.Count == 2)
            {
                // 与淘汰玩家对战：活跃玩家自动获胜
                Log.Info($"[BattleInstance] {InstanceId} battle with eliminated player {eliminatedPlayerId}, active player {activePlayerId} auto-wins");
                FinishBattle(activePlayerId);
            }
            else
            {
                Log.Info($"[BattleInstance] {InstanceId} battle started");
            }
        }

        /// <summary>
        /// 结束战斗
        /// </summary>
        public void FinishBattle(long winnerId)
        {
            IsFinished = true;

            // 找到败者ID
            long loserId = 0;
            if (PlayerIds.Count == 2)
            {
                loserId = PlayerIds.First(id => id != winnerId);
            }
            else if (PlayerIds.Count == 1)
            {
                // 只有一个玩家（与AI对战），败者设为0
                loserId = 0;
            }

            Result = new BattleResult(winnerId, loserId);
            Log.Info($"[BattleInstance] {InstanceId} battle finished, winner: {winnerId}, loser: {loserId}");
        }

        /// <summary>
        /// 合并英雄操作（包含移动和合成）
        /// </summary>
        public MergeHeroResp MergeHero(long playerId, int fromGridId, int toGridId, IList<PBMoveOperation> moves = null)
        {
            try
            {
                // 1. 权限验证：检查玩家是否有权限操作指定的GridID
                var (fromRow, fromCol) = GridHelper.GridIdToCoord(fromGridId);
                var (toRow, toCol) = GridHelper.GridIdToCoord(toGridId);

                // 验证源位置权限
                var fromEntity = CheckerBoard.GetEntityAt(fromRow, fromCol);
                if (fromEntity.EntityId == 0)
                {
                    return new MergeHeroResp
                    {
                        Code = -10, // 源位置无英雄
                        From = fromGridId,
                        To = toGridId,
                        NewHero = null
                    };
                }

                if (fromEntity.OwnerId != playerId)
                {
                    Log.Warning($"[BattleInstance] {InstanceId} Player {playerId} attempted to operate entity {fromEntity.EntityId} owned by {fromEntity.OwnerId} at GridID {fromGridId}");
                    return new MergeHeroResp
                    {
                        Code = -11, // 无权限操作源位置
                        From = fromGridId,
                        To = toGridId,
                        NewHero = null
                    };
                }

                // 验证目标位置权限（如果有实体）
                var toEntity = CheckerBoard.GetEntityAt(toRow, toCol);
                if (toEntity.EntityId != 0 && toEntity.OwnerId != playerId)
                {
                    Log.Warning($"[BattleInstance] {InstanceId} Player {playerId} attempted to operate entity {toEntity.EntityId} owned by {toEntity.OwnerId} at GridID {toGridId}");
                    return new MergeHeroResp
                    {
                        Code = -12, // 无权限操作目标位置
                        From = fromGridId,
                        To = toGridId,
                        NewHero = null
                    };
                }

                // 2. 先处理所有移动操作，确保服务器棋盘状态与客户端同步
                if (moves != null && moves.Count > 0)
                {
                    Log.Info($"[BattleInstance] {InstanceId} Processing {moves.Count} move operations before merge for player {playerId}");

                    foreach (var move in moves)
                    {
                        // 验证移动操作的权限
                        var (moveFromRow, moveFromCol) = GridHelper.GridIdToCoord(move.FromGridId);
                        var (moveToRow, moveToCol) = GridHelper.GridIdToCoord(move.ToGridId);

                        var moveFromEntity = CheckerBoard.GetEntityAt(moveFromRow, moveFromCol);
                        if (moveFromEntity.EntityId != 0 && moveFromEntity.OwnerId != playerId)
                        {
                            Log.Warning($"[BattleInstance] {InstanceId} Player {playerId} attempted to move entity owned by {moveFromEntity.OwnerId}, skipping move {move.FromGridId} -> {move.ToGridId}");
                            continue;
                        }

                        // 执行移动操作
                        bool moveSuccess = CheckerBoard.MoveEntity(moveFromRow, moveFromCol, moveToRow, moveToCol);
                        Log.Info($"[BattleInstance] {InstanceId} Move operation: {move.FromGridId} -> {move.ToGridId}, success: {moveSuccess}");
                    }
                }

                // 3. 重新获取实体（移动操作后可能已变化）
                fromEntity = CheckerBoard.GetEntityAt(fromRow, fromCol);
                toEntity = CheckerBoard.GetEntityAt(toRow, toCol);

                // 再次验证权限（移动操作后可能已变化）
                if (fromEntity.EntityId == 0)
                {
                    return new MergeHeroResp
                    {
                        Code = -13, // 移动后源位置无英雄
                        From = fromGridId,
                        To = toGridId,
                        NewHero = null
                    };
                }

                if (fromEntity.OwnerId != playerId)
                {
                    Log.Warning($"[BattleInstance] {InstanceId} After moves, player {playerId} no longer owns entity at GridID {fromGridId}");
                    return new MergeHeroResp
                    {
                        Code = -14, // 移动后无权限操作源位置
                        From = fromGridId,
                        To = toGridId,
                        NewHero = null
                    };
                }

                bool success;
                int newHeroId = fromEntity.ConfigId;

                if (toEntity.EntityId == 0)
                {
                    // 目标位置为空，执行移动操作
                    success = CheckerBoard.MoveEntity(fromRow, fromCol, toRow, toCol);
                }
                else
                {
                    // 目标位置有英雄，再次验证目标权限
                    if (toEntity.OwnerId != playerId)
                    {
                        Log.Warning($"[BattleInstance] {InstanceId} After moves, player {playerId} cannot operate target entity at GridID {toGridId}");
                        return new MergeHeroResp
                        {
                            Code = -15, // 移动后无权限操作目标位置
                            From = fromGridId,
                            To = toGridId,
                            NewHero = null
                        };
                    }

                    // 检查是否可以合成
                    if (fromEntity.ConfigId == toEntity.ConfigId &&
                        fromEntity.StarLevel == toEntity.StarLevel &&
                        toEntity.StarLevel < 3)
                    {
                        // 使用TryMergeEntities方法，它包含完整的权限验证和OwnerId检查
                        success = CheckerBoard.TryMergeEntities(fromRow, fromCol, toRow, toCol);
                        if (success)
                        {
                            // 合成成功，获取合成后的实体信息
                            var mergedEntity = CheckerBoard.GetEntityAt(toRow, toCol);
                            newHeroId = mergedEntity.ConfigId;
                        }
                    }
                    else
                    {
                        // 不能合成，尝试交换位置（但需要验证权限）
                        success = CheckerBoard.SwapEntities(fromRow, fromCol, toRow, toCol);
                    }
                }

                if (success)
                {
                    // 创建新英雄信息对象
                    PBBattleHeroInfo newHero = null;
                    if (newHeroId > 0)
                    {
                        // 获取合成后的英雄实体信息
                        var mergedEntity = CheckerBoard.GetEntityAt(toRow, toCol);
                        if (mergedEntity != null)
                        {
                            // 从玩家阵容中获取英雄的局外养成信息（Level和AwakeLevel）
                            PBBattleHeroInfo lineupHeroInfo = null;
                            if (_playerManager != null)
                            {
                                lineupHeroInfo = _playerManager.GetLineupHeroInfo(playerId, mergedEntity.ConfigId);
                            }

                            newHero = new PBBattleHeroInfo
                            {
                                Id = mergedEntity.ConfigId,
                                Level = lineupHeroInfo?.Level ?? 1, // 使用阵容中的真实等级
                                StarLevel = mergedEntity.StarLevel, // 使用合成后的星级（局内变化）
                                AwakeLevel = lineupHeroInfo?.AwakeLevel ?? 0 // 使用阵容中的真实觉醒等级
                            };
                        }
                    }

                    return new MergeHeroResp
                    {
                        Code = 0,
                        From = fromGridId,
                        To = toGridId,
                        NewHero = newHero
                    };
                }
                else
                {
                    return new MergeHeroResp
                    {
                        Code = -1,
                        From = fromGridId,
                        To = toGridId,
                        NewHero = null
                    };
                }
            }
            catch (Exception ex)
            {
                Log.Error($"[BattleInstance] {InstanceId} MergeHero failed: {ex.Message}");
                return new MergeHeroResp
                {
                    Code = -2,
                    From = fromGridId,
                    To = toGridId,
                    NewHero = null
                };
            }
        }

        /// <summary>
        /// 在棋盘上添加实体
        /// </summary>
        public bool AddEntity(int configId, long ownerId, int row, int col)
        {
            var entity = CheckerBoard.CreateAndPlaceEntity(configId, ownerId, row, col);
            return entity.EntityId != 0;
        }

        /// <summary>
        /// 移动棋盘上的实体
        /// </summary>
        public bool MoveEntity(int fromRow, int fromCol, int toRow, int toCol)
        {
            return CheckerBoard.MoveEntity(fromRow, fromCol, toRow, toCol);
        }

        /// <summary>
        /// 获取指定位置的实体
        /// </summary>
        public Entity GetEntityAt(int row, int col)
        {
            return CheckerBoard.GetEntityAt(row, col);
        }

        /// <summary>
        /// 检查玩家是否属于此实例
        /// </summary>
        public bool ContainsPlayer(long playerId)
        {
            return PlayerIds.Contains(playerId);
        }

        public void Dispose()
        {
            CheckerBoard?.Dispose();
        }
    }

    /// <summary>
    /// 战斗结果
    /// </summary>
    public class BattleResult
    {
        public long WinnerId { get; }
        public long LoserId { get; }

        public BattleResult(long winnerId, long loserId)
        {
            WinnerId = winnerId;
            LoserId = loserId;
        }
    }

    /// <summary>
    /// 格子ID与坐标转换工具
    /// 槽位编号规则：从下往上，从左往右的顺序递增，从1开始编号
    /// 例如：(1,1)=1, (1,2)=2, (1,3)=3, (1,6)=6, (2,1)=7, (2,2)=8...
    /// </summary>
    public static class GridHelper
    {
        public const int BOARD_WIDTH = 6;
        public const int BOARD_HEIGHT = 10; // 修正为与BattleConfig.Board.RowCount一致

        /// <summary>
        /// 格子ID转坐标 (ID从1开始)
        /// 编号规则：从下往上，从左往右递增
        /// </summary>
        public static (int row, int col) GridIdToCoord(int gridId)
        {
            gridId--; // 转为0基索引
            int row = gridId / BOARD_WIDTH + 1;
            int col = gridId % BOARD_WIDTH + 1;
            return (row, col);
        }

        /// <summary>
        /// 坐标转格子ID
        /// 编号规则：从下往上，从左往右递增
        /// </summary>
        public static int CoordToGridId(int row, int col)
        {
            return (row - 1) * BOARD_WIDTH + (col - 1) + 1;
        }
    }
}
