//GrpcAddressType:RedeemCode
//GrpcServerType:server

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.1
// source: microservices/redeemcode/v1/RedeemCode.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RedeemCodeType int32

const (
	RedeemCodeType_ePersonOnlyCode RedeemCodeType = 0
	RedeemCodeType_eGlobalOnlyCode RedeemCodeType = 1
)

// Enum value maps for RedeemCodeType.
var (
	RedeemCodeType_name = map[int32]string{
		0: "ePersonOnlyCode",
		1: "eGlobalOnlyCode",
	}
	RedeemCodeType_value = map[string]int32{
		"ePersonOnlyCode": 0,
		"eGlobalOnlyCode": 1,
	}
)

func (x RedeemCodeType) Enum() *RedeemCodeType {
	p := new(RedeemCodeType)
	*p = x
	return p
}

func (x RedeemCodeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RedeemCodeType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_redeemcode_v1_RedeemCode_proto_enumTypes[0].Descriptor()
}

func (RedeemCodeType) Type() protoreflect.EnumType {
	return &file_microservices_redeemcode_v1_RedeemCode_proto_enumTypes[0]
}

func (x RedeemCodeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RedeemCodeType.Descriptor instead.
func (RedeemCodeType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_redeemcode_v1_RedeemCode_proto_rawDescGZIP(), []int{0}
}

type RedeemCodeResultType int32

const (
	RedeemCodeResultType_eCanReward            RedeemCodeResultType = 0
	RedeemCodeResultType_eHaveReward           RedeemCodeResultType = 1 // 使用次数超过上限
	RedeemCodeResultType_eRedeemCodeNotOpen    RedeemCodeResultType = 2 // 未到开放时间
	RedeemCodeResultType_eRedeemCodeExpired    RedeemCodeResultType = 3 // 已过期
	RedeemCodeResultType_eRedeemCodeRedisError RedeemCodeResultType = 4 // redis错误
	RedeemCodeResultType_eRedeemCodeError      RedeemCodeResultType = 5 // 礼包码错误
	RedeemCodeResultType_eUserExchangeToMax    RedeemCodeResultType = 6 // 玩家在此批次使用礼包码次数达到上限
	RedeemCodeResultType_eLevelTooLow          RedeemCodeResultType = 7 // 玩家等级不足
	RedeemCodeResultType_eRoleCreateTimeError  RedeemCodeResultType = 8 // 玩家创建时间不在限定时间内
)

// Enum value maps for RedeemCodeResultType.
var (
	RedeemCodeResultType_name = map[int32]string{
		0: "eCanReward",
		1: "eHaveReward",
		2: "eRedeemCodeNotOpen",
		3: "eRedeemCodeExpired",
		4: "eRedeemCodeRedisError",
		5: "eRedeemCodeError",
		6: "eUserExchangeToMax",
		7: "eLevelTooLow",
		8: "eRoleCreateTimeError",
	}
	RedeemCodeResultType_value = map[string]int32{
		"eCanReward":            0,
		"eHaveReward":           1,
		"eRedeemCodeNotOpen":    2,
		"eRedeemCodeExpired":    3,
		"eRedeemCodeRedisError": 4,
		"eRedeemCodeError":      5,
		"eUserExchangeToMax":    6,
		"eLevelTooLow":          7,
		"eRoleCreateTimeError":  8,
	}
)

func (x RedeemCodeResultType) Enum() *RedeemCodeResultType {
	p := new(RedeemCodeResultType)
	*p = x
	return p
}

func (x RedeemCodeResultType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RedeemCodeResultType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_redeemcode_v1_RedeemCode_proto_enumTypes[1].Descriptor()
}

func (RedeemCodeResultType) Type() protoreflect.EnumType {
	return &file_microservices_redeemcode_v1_RedeemCode_proto_enumTypes[1]
}

func (x RedeemCodeResultType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RedeemCodeResultType.Descriptor instead.
func (RedeemCodeResultType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_redeemcode_v1_RedeemCode_proto_rawDescGZIP(), []int{1}
}

type RedeemCodeBatchInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BatchId             string          `protobuf:"bytes,1,opt,name=BatchId,proto3" json:"BatchId,omitempty"`                          // 批次Id
	CodeBatchName       string          `protobuf:"bytes,2,opt,name=CodeBatchName,proto3" json:"CodeBatchName,omitempty"`              // 批次名
	CreateNum           int32           `protobuf:"varint,3,opt,name=CreateNum,proto3" json:"CreateNum,omitempty"`                     // 生成礼包码的个数
	ExchangeMaxNum      int32           `protobuf:"varint,4,opt,name=ExchangeMaxNum,proto3" json:"ExchangeMaxNum,omitempty"`           // 每个礼包码最大可兑换次数
	UserExchangeMaxNum  int32           `protobuf:"varint,5,opt,name=UserExchangeMaxNum,proto3" json:"UserExchangeMaxNum,omitempty"`   // 每个用户在此批次能用几个礼包码
	RoleLevel           int32           `protobuf:"varint,6,opt,name=RoleLevel,proto3" json:"RoleLevel,omitempty"`                     // 玩家等级限制
	ValidityStartTime   int64           `protobuf:"varint,7,opt,name=ValidityStartTime,proto3" json:"ValidityStartTime,omitempty"`     // 有效期开始时间
	ExpirationTime      int64           `protobuf:"varint,8,opt,name=ExpirationTime,proto3" json:"ExpirationTime,omitempty"`           // 兑换截止时间
	CreateRoleStartTime int64           `protobuf:"varint,9,opt,name=CreateRoleStartTime,proto3" json:"CreateRoleStartTime,omitempty"` // 角色创建时间限制
	CreateRoleEndTime   int64           `protobuf:"varint,10,opt,name=CreateRoleEndTime,proto3" json:"CreateRoleEndTime,omitempty"`
	Items               map[int32]int32 `protobuf:"bytes,11,rep,name=Items,proto3" json:"Items,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 道具ID -> 数量
	RedeemCodes         []string        `protobuf:"bytes,12,rep,name=RedeemCodes,proto3" json:"RedeemCodes,omitempty"`                                                                               // 礼包码 可不传 不传则随机生成
	CodeBatchNote       string          `protobuf:"bytes,13,opt,name=CodeBatchNote,proto3" json:"CodeBatchNote,omitempty"`                                                                           // 注释
}

func (x *RedeemCodeBatchInfo) Reset() {
	*x = RedeemCodeBatchInfo{}
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RedeemCodeBatchInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedeemCodeBatchInfo) ProtoMessage() {}

func (x *RedeemCodeBatchInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedeemCodeBatchInfo.ProtoReflect.Descriptor instead.
func (*RedeemCodeBatchInfo) Descriptor() ([]byte, []int) {
	return file_microservices_redeemcode_v1_RedeemCode_proto_rawDescGZIP(), []int{0}
}

func (x *RedeemCodeBatchInfo) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

func (x *RedeemCodeBatchInfo) GetCodeBatchName() string {
	if x != nil {
		return x.CodeBatchName
	}
	return ""
}

func (x *RedeemCodeBatchInfo) GetCreateNum() int32 {
	if x != nil {
		return x.CreateNum
	}
	return 0
}

func (x *RedeemCodeBatchInfo) GetExchangeMaxNum() int32 {
	if x != nil {
		return x.ExchangeMaxNum
	}
	return 0
}

func (x *RedeemCodeBatchInfo) GetUserExchangeMaxNum() int32 {
	if x != nil {
		return x.UserExchangeMaxNum
	}
	return 0
}

func (x *RedeemCodeBatchInfo) GetRoleLevel() int32 {
	if x != nil {
		return x.RoleLevel
	}
	return 0
}

func (x *RedeemCodeBatchInfo) GetValidityStartTime() int64 {
	if x != nil {
		return x.ValidityStartTime
	}
	return 0
}

func (x *RedeemCodeBatchInfo) GetExpirationTime() int64 {
	if x != nil {
		return x.ExpirationTime
	}
	return 0
}

func (x *RedeemCodeBatchInfo) GetCreateRoleStartTime() int64 {
	if x != nil {
		return x.CreateRoleStartTime
	}
	return 0
}

func (x *RedeemCodeBatchInfo) GetCreateRoleEndTime() int64 {
	if x != nil {
		return x.CreateRoleEndTime
	}
	return 0
}

func (x *RedeemCodeBatchInfo) GetItems() map[int32]int32 {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *RedeemCodeBatchInfo) GetRedeemCodes() []string {
	if x != nil {
		return x.RedeemCodes
	}
	return nil
}

func (x *RedeemCodeBatchInfo) GetCodeBatchNote() string {
	if x != nil {
		return x.CodeBatchNote
	}
	return ""
}

type AddRedeemCodeResult struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RedeemCodeId string `protobuf:"bytes,1,opt,name=RedeemCodeId,proto3" json:"RedeemCodeId,omitempty"` // 礼包码
	Result       int32  `protobuf:"varint,2,opt,name=Result,proto3" json:"Result,omitempty"`            // 添加礼包码结果
}

func (x *AddRedeemCodeResult) Reset() {
	*x = AddRedeemCodeResult{}
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddRedeemCodeResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddRedeemCodeResult) ProtoMessage() {}

func (x *AddRedeemCodeResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddRedeemCodeResult.ProtoReflect.Descriptor instead.
func (*AddRedeemCodeResult) Descriptor() ([]byte, []int) {
	return file_microservices_redeemcode_v1_RedeemCode_proto_rawDescGZIP(), []int{1}
}

func (x *AddRedeemCodeResult) GetRedeemCodeId() string {
	if x != nil {
		return x.RedeemCodeId
	}
	return ""
}

func (x *AddRedeemCodeResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type AddBatchRedeemCodeRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RedeemCodeId []string `protobuf:"bytes,1,rep,name=RedeemCodeId,proto3" json:"RedeemCodeId,omitempty"` // 礼包码数组
	Result       int32    `protobuf:"varint,2,opt,name=Result,proto3" json:"Result,omitempty"`            // 添加礼包码结果
}

func (x *AddBatchRedeemCodeRes) Reset() {
	*x = AddBatchRedeemCodeRes{}
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddBatchRedeemCodeRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBatchRedeemCodeRes) ProtoMessage() {}

func (x *AddBatchRedeemCodeRes) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBatchRedeemCodeRes.ProtoReflect.Descriptor instead.
func (*AddBatchRedeemCodeRes) Descriptor() ([]byte, []int) {
	return file_microservices_redeemcode_v1_RedeemCode_proto_rawDescGZIP(), []int{2}
}

func (x *AddBatchRedeemCodeRes) GetRedeemCodeId() []string {
	if x != nil {
		return x.RedeemCodeId
	}
	return nil
}

func (x *AddBatchRedeemCodeRes) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type CodeBatchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BatchId string `protobuf:"bytes,1,opt,name=BatchId,proto3" json:"BatchId,omitempty"`
}

func (x *CodeBatchReq) Reset() {
	*x = CodeBatchReq{}
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CodeBatchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CodeBatchReq) ProtoMessage() {}

func (x *CodeBatchReq) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CodeBatchReq.ProtoReflect.Descriptor instead.
func (*CodeBatchReq) Descriptor() ([]byte, []int) {
	return file_microservices_redeemcode_v1_RedeemCode_proto_rawDescGZIP(), []int{3}
}

func (x *CodeBatchReq) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

type DeleteCodeBatchRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BatchId string `protobuf:"bytes,1,opt,name=BatchId,proto3" json:"BatchId,omitempty"`
	Result  int32  `protobuf:"varint,2,opt,name=Result,proto3" json:"Result,omitempty"` //删除结果
}

func (x *DeleteCodeBatchRes) Reset() {
	*x = DeleteCodeBatchRes{}
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCodeBatchRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCodeBatchRes) ProtoMessage() {}

func (x *DeleteCodeBatchRes) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCodeBatchRes.ProtoReflect.Descriptor instead.
func (*DeleteCodeBatchRes) Descriptor() ([]byte, []int) {
	return file_microservices_redeemcode_v1_RedeemCode_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteCodeBatchRes) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

func (x *DeleteCodeBatchRes) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

type GetRedeemCodeRewardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RedeemCodeId     string `protobuf:"bytes,1,opt,name=RedeemCodeId,proto3" json:"RedeemCodeId,omitempty"`          // 礼包码Id
	PlayerGuid       uint64 `protobuf:"varint,2,opt,name=PlayerGuid,proto3" json:"PlayerGuid,omitempty"`             // 玩家Id
	PlayerCreateTime int64  `protobuf:"varint,3,opt,name=PlayerCreateTime,proto3" json:"PlayerCreateTime,omitempty"` // 玩家创建时间
	PlayerLevel      uint32 `protobuf:"varint,4,opt,name=PlayerLevel,proto3" json:"PlayerLevel,omitempty"`           // 玩家等级
	ServerId         uint64 `protobuf:"varint,5,opt,name=ServerId,proto3" json:"ServerId,omitempty"`                 // 服务器Id
}

func (x *GetRedeemCodeRewardReq) Reset() {
	*x = GetRedeemCodeRewardReq{}
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRedeemCodeRewardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRedeemCodeRewardReq) ProtoMessage() {}

func (x *GetRedeemCodeRewardReq) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRedeemCodeRewardReq.ProtoReflect.Descriptor instead.
func (*GetRedeemCodeRewardReq) Descriptor() ([]byte, []int) {
	return file_microservices_redeemcode_v1_RedeemCode_proto_rawDescGZIP(), []int{5}
}

func (x *GetRedeemCodeRewardReq) GetRedeemCodeId() string {
	if x != nil {
		return x.RedeemCodeId
	}
	return ""
}

func (x *GetRedeemCodeRewardReq) GetPlayerGuid() uint64 {
	if x != nil {
		return x.PlayerGuid
	}
	return 0
}

func (x *GetRedeemCodeRewardReq) GetPlayerCreateTime() int64 {
	if x != nil {
		return x.PlayerCreateTime
	}
	return 0
}

func (x *GetRedeemCodeRewardReq) GetPlayerLevel() uint32 {
	if x != nil {
		return x.PlayerLevel
	}
	return 0
}

func (x *GetRedeemCodeRewardReq) GetServerId() uint64 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

type GetRedeemCodeRewardReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result     RedeemCodeResultType `protobuf:"varint,1,opt,name=Result,proto3,enum=Aurora.PlayerInfoServer.RedeemCodeResultType" json:"Result,omitempty"`
	DropPackID int32                `protobuf:"varint,2,opt,name=DropPackID,proto3" json:"DropPackID,omitempty"`
}

func (x *GetRedeemCodeRewardReply) Reset() {
	*x = GetRedeemCodeRewardReply{}
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRedeemCodeRewardReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRedeemCodeRewardReply) ProtoMessage() {}

func (x *GetRedeemCodeRewardReply) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRedeemCodeRewardReply.ProtoReflect.Descriptor instead.
func (*GetRedeemCodeRewardReply) Descriptor() ([]byte, []int) {
	return file_microservices_redeemcode_v1_RedeemCode_proto_rawDescGZIP(), []int{6}
}

func (x *GetRedeemCodeRewardReply) GetResult() RedeemCodeResultType {
	if x != nil {
		return x.Result
	}
	return RedeemCodeResultType_eCanReward
}

func (x *GetRedeemCodeRewardReply) GetDropPackID() int32 {
	if x != nil {
		return x.DropPackID
	}
	return 0
}

type GetRedeemCodeItemsRep struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result RedeemCodeResultType `protobuf:"varint,1,opt,name=Result,proto3,enum=Aurora.PlayerInfoServer.RedeemCodeResultType" json:"Result,omitempty"`
	Items  map[int32]int32      `protobuf:"bytes,2,rep,name=Items,proto3" json:"Items,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"` // 道具ID -> 数量
}

func (x *GetRedeemCodeItemsRep) Reset() {
	*x = GetRedeemCodeItemsRep{}
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRedeemCodeItemsRep) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRedeemCodeItemsRep) ProtoMessage() {}

func (x *GetRedeemCodeItemsRep) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRedeemCodeItemsRep.ProtoReflect.Descriptor instead.
func (*GetRedeemCodeItemsRep) Descriptor() ([]byte, []int) {
	return file_microservices_redeemcode_v1_RedeemCode_proto_rawDescGZIP(), []int{7}
}

func (x *GetRedeemCodeItemsRep) GetResult() RedeemCodeResultType {
	if x != nil {
		return x.Result
	}
	return RedeemCodeResultType_eCanReward
}

func (x *GetRedeemCodeItemsRep) GetItems() map[int32]int32 {
	if x != nil {
		return x.Items
	}
	return nil
}

var File_microservices_redeemcode_v1_RedeemCode_proto protoreflect.FileDescriptor

var file_microservices_redeemcode_v1_RedeemCode_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x6d, 0x69, 0x63, 0x72, 0x6f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f,
	0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x63, 0x6f, 0x64, 0x65, 0x2f, 0x76, 0x31, 0x2f, 0x52, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x17,
	0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf0, 0x04, 0x0a, 0x13, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x43, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x6f, 0x64, 0x65, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x43, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x26, 0x0a, 0x0e, 0x45,
	0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x61, 0x78, 0x4e, 0x75, 0x6d, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x61, 0x78,
	0x4e, 0x75, 0x6d, 0x12, 0x2e, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x4d, 0x61, 0x78, 0x4e, 0x75, 0x6d, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x12, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x4d, 0x61, 0x78,
	0x4e, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x52, 0x6f, 0x6c, 0x65, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x52, 0x6f, 0x6c, 0x65, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x2c, 0x0a, 0x11, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x26, 0x0a, 0x0e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d,
	0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x11, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65,
	0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4d, 0x0a, 0x05, 0x49, 0x74, 0x65, 0x6d, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68,
	0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x05, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x43, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x52, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x24, 0x0a, 0x0d, 0x43, 0x6f, 0x64, 0x65,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4e, 0x6f, 0x74, 0x65, 0x1a, 0x38,
	0x0a, 0x0a, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x51, 0x0a, 0x13, 0x41, 0x64, 0x64, 0x52,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x22, 0x0a, 0x0c, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64,
	0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x53, 0x0a, 0x15, 0x41,
	0x64, 0x64, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64,
	0x65, 0x52, 0x65, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f,
	0x64, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x28, 0x0a, 0x0c, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71,
	0x12, 0x18, 0x0a, 0x07, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x22, 0x46, 0x0a, 0x12, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73,
	0x12, 0x18, 0x0a, 0x07, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0xc6, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x22, 0x0a,
	0x0c, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x75, 0x69,
	0x64, 0x12, 0x2a, 0x0a, 0x10, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x0b, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x1a, 0x0a, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x08, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x49, 0x64, 0x22, 0x81, 0x01, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x45, 0x0a, 0x06, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x44, 0x72, 0x6f, 0x70, 0x50, 0x61, 0x63, 0x6b, 0x49, 0x44, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0a, 0x44, 0x72, 0x6f, 0x70, 0x50, 0x61, 0x63, 0x6b, 0x49, 0x44, 0x22,
	0xe9, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x12, 0x45, 0x0a, 0x06, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x41, 0x75, 0x72, 0x6f,
	0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x4f, 0x0a, 0x05, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x39, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x2e,
	0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x1a, 0x38, 0x0a, 0x0a, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0x3a, 0x0a, 0x0e, 0x52,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x13, 0x0a,
	0x0f, 0x65, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x4f, 0x6e, 0x6c, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x65, 0x47, 0x6c, 0x6f, 0x62, 0x61, 0x6c, 0x4f, 0x6e, 0x6c,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x10, 0x01, 0x2a, 0xdc, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x0e, 0x0a, 0x0a, 0x65, 0x43, 0x61, 0x6e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x10, 0x00,
	0x12, 0x0f, 0x0a, 0x0b, 0x65, 0x48, 0x61, 0x76, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x10,
	0x01, 0x12, 0x16, 0x0a, 0x12, 0x65, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65,
	0x4e, 0x6f, 0x74, 0x4f, 0x70, 0x65, 0x6e, 0x10, 0x02, 0x12, 0x16, 0x0a, 0x12, 0x65, 0x52, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x10,
	0x03, 0x12, 0x19, 0x0a, 0x15, 0x65, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x65, 0x64, 0x69, 0x73, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10,
	0x65, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x65, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x54, 0x6f, 0x4d, 0x61, 0x78, 0x10, 0x06, 0x12, 0x10, 0x0a, 0x0c, 0x65, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x54, 0x6f, 0x6f, 0x4c, 0x6f, 0x77, 0x10, 0x07, 0x12, 0x18, 0x0a, 0x14,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x10, 0x08, 0x32, 0xc2, 0x05, 0x0a, 0x0a, 0x52, 0x65, 0x64, 0x65, 0x65,
	0x6d, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x12, 0x41, 0x64, 0x64, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2c, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64,
	0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x2e, 0x2e, 0x41, 0x75, 0x72,
	0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2e, 0x41, 0x64, 0x64, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x18, 0x3a, 0x01, 0x2a, 0x22, 0x13, 0x2f, 0x67, 0x6d, 0x74, 0x2f, 0x72, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x63, 0x6f, 0x64, 0x65, 0x2f, 0x61, 0x64, 0x64, 0x12, 0x90, 0x01, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x25, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x64, 0x65,
	0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x2c, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72,
	0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d,
	0x2f, 0x67, 0x6d, 0x74, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x63, 0x6f, 0x64, 0x65, 0x2f,
	0x67, 0x65, 0x74, 0x2f, 0x7b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x7d, 0x12, 0x95, 0x01,
	0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x25, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61,
	0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2e, 0x43, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x2b,
	0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x22, 0x28, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x22, 0x2a, 0x20, 0x2f, 0x67, 0x6d, 0x74, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x63, 0x6f, 0x64, 0x65, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x2f, 0x7b, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x49, 0x64, 0x7d, 0x12, 0x77, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65,
	0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2f, 0x2e, 0x41, 0x75,
	0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x2e, 0x2e, 0x41,
	0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d,
	0x43, 0x6f, 0x64, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x65, 0x70, 0x22, 0x00, 0x12, 0x7b,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x2f, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e, 0x50,
	0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2e,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x31, 0x2e, 0x41, 0x75, 0x72, 0x6f, 0x72, 0x61, 0x2e,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x42, 0x21, 0x5a, 0x1f, 0x67,
	0x61, 0x6d, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65,
	0x64, 0x65, 0x65, 0x6d, 0x63, 0x6f, 0x64, 0x65, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_microservices_redeemcode_v1_RedeemCode_proto_rawDescOnce sync.Once
	file_microservices_redeemcode_v1_RedeemCode_proto_rawDescData = file_microservices_redeemcode_v1_RedeemCode_proto_rawDesc
)

func file_microservices_redeemcode_v1_RedeemCode_proto_rawDescGZIP() []byte {
	file_microservices_redeemcode_v1_RedeemCode_proto_rawDescOnce.Do(func() {
		file_microservices_redeemcode_v1_RedeemCode_proto_rawDescData = protoimpl.X.CompressGZIP(file_microservices_redeemcode_v1_RedeemCode_proto_rawDescData)
	})
	return file_microservices_redeemcode_v1_RedeemCode_proto_rawDescData
}

var file_microservices_redeemcode_v1_RedeemCode_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_microservices_redeemcode_v1_RedeemCode_proto_goTypes = []any{
	(RedeemCodeType)(0),              // 0: Aurora.PlayerInfoServer.RedeemCodeType
	(RedeemCodeResultType)(0),        // 1: Aurora.PlayerInfoServer.RedeemCodeResultType
	(*RedeemCodeBatchInfo)(nil),      // 2: Aurora.PlayerInfoServer.RedeemCodeBatchInfo
	(*AddRedeemCodeResult)(nil),      // 3: Aurora.PlayerInfoServer.AddRedeemCodeResult
	(*AddBatchRedeemCodeRes)(nil),    // 4: Aurora.PlayerInfoServer.AddBatchRedeemCodeRes
	(*CodeBatchReq)(nil),             // 5: Aurora.PlayerInfoServer.CodeBatchReq
	(*DeleteCodeBatchRes)(nil),       // 6: Aurora.PlayerInfoServer.DeleteCodeBatchRes
	(*GetRedeemCodeRewardReq)(nil),   // 7: Aurora.PlayerInfoServer.GetRedeemCodeRewardReq
	(*GetRedeemCodeRewardReply)(nil), // 8: Aurora.PlayerInfoServer.GetRedeemCodeRewardReply
	(*GetRedeemCodeItemsRep)(nil),    // 9: Aurora.PlayerInfoServer.GetRedeemCodeItemsRep
	nil,                              // 10: Aurora.PlayerInfoServer.RedeemCodeBatchInfo.ItemsEntry
	nil,                              // 11: Aurora.PlayerInfoServer.GetRedeemCodeItemsRep.ItemsEntry
}
var file_microservices_redeemcode_v1_RedeemCode_proto_depIdxs = []int32{
	10, // 0: Aurora.PlayerInfoServer.RedeemCodeBatchInfo.Items:type_name -> Aurora.PlayerInfoServer.RedeemCodeBatchInfo.ItemsEntry
	1,  // 1: Aurora.PlayerInfoServer.GetRedeemCodeRewardReply.Result:type_name -> Aurora.PlayerInfoServer.RedeemCodeResultType
	1,  // 2: Aurora.PlayerInfoServer.GetRedeemCodeItemsRep.Result:type_name -> Aurora.PlayerInfoServer.RedeemCodeResultType
	11, // 3: Aurora.PlayerInfoServer.GetRedeemCodeItemsRep.Items:type_name -> Aurora.PlayerInfoServer.GetRedeemCodeItemsRep.ItemsEntry
	2,  // 4: Aurora.PlayerInfoServer.RedeemCode.AddBatchRedeemCode:input_type -> Aurora.PlayerInfoServer.RedeemCodeBatchInfo
	5,  // 5: Aurora.PlayerInfoServer.RedeemCode.GetBatchRedeemCode:input_type -> Aurora.PlayerInfoServer.CodeBatchReq
	5,  // 6: Aurora.PlayerInfoServer.RedeemCode.DeleteBatchRedeemCode:input_type -> Aurora.PlayerInfoServer.CodeBatchReq
	7,  // 7: Aurora.PlayerInfoServer.RedeemCode.GetRedeemCodeItems:input_type -> Aurora.PlayerInfoServer.GetRedeemCodeRewardReq
	7,  // 8: Aurora.PlayerInfoServer.RedeemCode.GetRedeemCodeReward:input_type -> Aurora.PlayerInfoServer.GetRedeemCodeRewardReq
	4,  // 9: Aurora.PlayerInfoServer.RedeemCode.AddBatchRedeemCode:output_type -> Aurora.PlayerInfoServer.AddBatchRedeemCodeRes
	2,  // 10: Aurora.PlayerInfoServer.RedeemCode.GetBatchRedeemCode:output_type -> Aurora.PlayerInfoServer.RedeemCodeBatchInfo
	6,  // 11: Aurora.PlayerInfoServer.RedeemCode.DeleteBatchRedeemCode:output_type -> Aurora.PlayerInfoServer.DeleteCodeBatchRes
	9,  // 12: Aurora.PlayerInfoServer.RedeemCode.GetRedeemCodeItems:output_type -> Aurora.PlayerInfoServer.GetRedeemCodeItemsRep
	8,  // 13: Aurora.PlayerInfoServer.RedeemCode.GetRedeemCodeReward:output_type -> Aurora.PlayerInfoServer.GetRedeemCodeRewardReply
	9,  // [9:14] is the sub-list for method output_type
	4,  // [4:9] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_microservices_redeemcode_v1_RedeemCode_proto_init() }
func file_microservices_redeemcode_v1_RedeemCode_proto_init() {
	if File_microservices_redeemcode_v1_RedeemCode_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_microservices_redeemcode_v1_RedeemCode_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_microservices_redeemcode_v1_RedeemCode_proto_goTypes,
		DependencyIndexes: file_microservices_redeemcode_v1_RedeemCode_proto_depIdxs,
		EnumInfos:         file_microservices_redeemcode_v1_RedeemCode_proto_enumTypes,
		MessageInfos:      file_microservices_redeemcode_v1_RedeemCode_proto_msgTypes,
	}.Build()
	File_microservices_redeemcode_v1_RedeemCode_proto = out.File
	file_microservices_redeemcode_v1_RedeemCode_proto_rawDesc = nil
	file_microservices_redeemcode_v1_RedeemCode_proto_goTypes = nil
	file_microservices_redeemcode_v1_RedeemCode_proto_depIdxs = nil
}
