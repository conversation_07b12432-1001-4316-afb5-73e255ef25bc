@echo off
setlocal enabledelayedexpansion

:: Record start time
set start_time=%time%

:: Dynamically get project name =========================
pushd %~dp0
cd ..\..\..\
for %%I in (.) do set "PROJECT_NAME=%%~nxI"
popd

:: Configuration file parameter handling ================
set "CONFIG_FLAG=0"
if /i "%1"=="include_config" (
    set "CONFIG_FLAG=1"
    echo [INFO] Configuration packaging mode enabled
)

:: Timestamp generation =================================
for /f "tokens=1-3 delims=-/ " %%a in ("!date!") do (
    set "year=%%b"
    set "month=%%c"
)
set "datetime=!year!!month!!time:~0,2!!time:~3,2!!time:~6,2!"
chcp 65001 > nul

:: Configure other parameters ==========================
set "MOD_DIR=..\cmd"
set "DEPLOY_DIR=deploy_build"

:: ==================== Cross-Compilation Setup ====================

set "GOOS=linux"
set "GOARCH=amd64"
set "CGO_ENABLED=0"
set "BUILD_VERSION=v1.0.1"

:: Version information injection
set "BUILD_LDFLAGS=-s -w"
set "BUILD_LDFLAGS=!BUILD_LDFLAGS! -X './common/version..BuildVersion=%BUILD_VERSION%'"

:: Timestamp generation
for /f "tokens=2 delims==" %%G in ('wmic os get localdatetime /value') do (
    set "BUILD_TIME=%%G"
    set "BUILD_TIME=!BUILD_TIME:~0,8!-!BUILD_TIME:~8,6!"
)
set "BUILD_LDFLAGS=!BUILD_LDFLAGS! -X './common/version..BuildTime=!BUILD_TIME!'"

:: Git commit information
for /f %%g in ('git rev-parse --short HEAD 2^>nul') do set "GIT_COMMIT=%%g"
if not defined GIT_COMMIT set "GIT_COMMIT=unknown"
set "BUILD_LDFLAGS=!BUILD_LDFLAGS! -X './common/version..CommitID=!GIT_COMMIT!'"

:: Create build directory ===============================
set "BUILD_DIR=%DEPLOY_DIR%\%PROJECT_NAME%_%BUILD_VERSION%_%datetime%"
mkdir "%BUILD_DIR%" >nul 2>&1

:: ==================== Service Compilation Process ====================
echo [INFO] Starting service compilation...

:: Dependency synchronization
:: echo [INFO] Synchronizing dependencies...
:: go mod tidy -v
:: go mod vendor -v

:: Directory traversal logic
for /r "%MOD_DIR%" %%d in (.) do (
    :: if "1"=="2" (
    if exist "%%d\main.go" (
        set "service_dir=%%d"
        set "service_name=%%~nxd"

        :: Create output directory
        set "output_dir_name=%PROJECT_NAME%_!service_name!_%BUILD_VERSION%_%datetime%"
        set "output_dir=%BUILD_DIR%\tmp\%output_dir_name%"
        if not exist "!output_dir!" mkdir "!output_dir!"

        echo [INFO] Compiling service: !service_name!

        :: Build command with module path
        go build ^
            -trimpath ^
            -gcflags "all=-N -l" ^
            -ldflags "!BUILD_LDFLAGS!" ^
            -o "!output_dir!\!service_name!" ^
            "%%d\main.go"
        if errorlevel 1 (
            echo [ERROR] Service compilation failed: !service_name!
            rmdir "!output_dir!"
        ) else (
            echo [SUCCESS] Service compilation success: !service_name!
            set "zip_name=!output_dir_name!.zip"
            echo [INFO] Generating !zip_name!
            makecab "!output_dir!\!service_name!" "%BUILD_DIR%\!zip_name!"
        )
    )
)

:: Conditional config processing ========================
if "%CONFIG_FLAG%"=="1" (
    echo [INFO] Integrating configuration files...
    robocopy "..\configs" "%BUILD_DIR%\configs" /E /NFL /NDL /NJH >nul
)

:: Calculate execution time =============================

set end_time=%time%
set options="tokens=1-4 delims=:.,"
for /f %options% %%a in ("%start_time%") do set start_h=%%a&set /a start_m=100%%b %% 100&set /a start_s=100%%c %% 100&set /a start_ms=100%%d %% 100
for /f %options% %%a in ("%end_time%") do set end_h=%%a&set /a end_m=100%%b %% 100&set /a end_s=100%%c %% 100&set /a end_ms=100%%d %% 100

set /a hours=%end_h%-%start_h%
set /a mins=%end_m%-%start_m%
set /a secs=%end_s%-%start_s%
set /a ms=%end_ms%-%start_ms%

if %ms% lss 0 set /a secs = %secs% - 1 & set /a ms = 1000%ms%
if %secs% lss 0 set /a mins = %mins% - 1 & set /a secs = 60%secs%
if %mins% lss 0 set /a hours = %hours% - 1 & set /a mins = 60%mins%
if %hours% lss 0 set /a hours = 24%hours%

:: Cleanup and output ===================================

rd /s /q "%BUILD_DIR%\tmp" >nul 2>&1
echo [SUCCESS] Build completed! Location: %CD%\%BUILD_DIR%
echo Total build time: %hours%:%mins%:%secs%.%ms%s
explorer "%CD%\%BUILD_DIR%"

:: End pause
pause