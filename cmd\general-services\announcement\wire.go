//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"context"
	"liteframe/internal/general-services/announcement/biz"
	"liteframe/internal/general-services/announcement/conf"
	"liteframe/internal/general-services/announcement/data"
	"liteframe/internal/general-services/announcement/registry"
	"liteframe/internal/general-services/announcement/server"
	"liteframe/internal/general-services/announcement/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// 全局变量存储usecase引用，用于配置变更时调用
var (
	globalAnnouncementUsecase *biz.AnnouncementUsecase
)

// SetAnnouncementUsecase 提供用于获取usecase的函数
func SetAnnouncementUsecase(u *biz.AnnouncementUsecase) {
	globalAnnouncementUsecase = u
}

// UpdateAnnouncementConfig 提供用于更新配置的函数
func UpdateAnnouncementConfig(ctx context.Context, bootstrap *conf.Bootstrap) error {
	if biz.GlobalAnnouncementUsecase != nil {
		return biz.GlobalAnnouncementUsecase.UpdateConfig(ctx, bootstrap)
	}
	return nil
}

// wireApp init announcement application.
func wireApp(*conf.Server, *conf.Data, *conf.Bootstrap, log.Logger, map[string]string) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, registry.ProviderSet, newApp))
}
