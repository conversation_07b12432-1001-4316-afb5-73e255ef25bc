// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.23.2
// source: microservices/rank/v1/error_rank.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorReason int32

const (
	ErrorReason_GREETER_UNSPECIFIED ErrorReason = 0
	ErrorReason_USER_NOT_FOUND      ErrorReason = 1
)

// Enum value maps for ErrorReason.
var (
	ErrorReason_name = map[int32]string{
		0: "GREETER_UNSPECIFIED",
		1: "USER_NOT_FOUND",
	}
	ErrorReason_value = map[string]int32{
		"GREETER_UNSPECIFIED": 0,
		"USER_NOT_FOUND":      1,
	}
)

func (x ErrorReason) Enum() *ErrorReason {
	p := new(ErrorReason)
	*p = x
	return p
}

func (x ErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_rank_v1_error_rank_proto_enumTypes[0].Descriptor()
}

func (ErrorReason) Type() protoreflect.EnumType {
	return &file_microservices_rank_v1_error_rank_proto_enumTypes[0]
}

func (x ErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrorReason.Descriptor instead.
func (ErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_microservices_rank_v1_error_rank_proto_rawDescGZIP(), []int{0}
}

var File_microservices_rank_v1_error_rank_proto protoreflect.FileDescriptor

const file_microservices_rank_v1_error_rank_proto_rawDesc = "" +
	"\n" +
	"&microservices/rank/v1/error_rank.proto\x12\x0erankservice.v1*:\n" +
	"\vErrorReason\x12\x17\n" +
	"\x13GREETER_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eUSER_NOT_FOUND\x10\x01B\x1eZ\x1cserver/api/rankservice/v1;v1b\x06proto3"

var (
	file_microservices_rank_v1_error_rank_proto_rawDescOnce sync.Once
	file_microservices_rank_v1_error_rank_proto_rawDescData []byte
)

func file_microservices_rank_v1_error_rank_proto_rawDescGZIP() []byte {
	file_microservices_rank_v1_error_rank_proto_rawDescOnce.Do(func() {
		file_microservices_rank_v1_error_rank_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_microservices_rank_v1_error_rank_proto_rawDesc), len(file_microservices_rank_v1_error_rank_proto_rawDesc)))
	})
	return file_microservices_rank_v1_error_rank_proto_rawDescData
}

var file_microservices_rank_v1_error_rank_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_microservices_rank_v1_error_rank_proto_goTypes = []any{
	(ErrorReason)(0), // 0: rankservice.v1.ErrorReason
}
var file_microservices_rank_v1_error_rank_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_microservices_rank_v1_error_rank_proto_init() }
func file_microservices_rank_v1_error_rank_proto_init() {
	if File_microservices_rank_v1_error_rank_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_microservices_rank_v1_error_rank_proto_rawDesc), len(file_microservices_rank_v1_error_rank_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_microservices_rank_v1_error_rank_proto_goTypes,
		DependencyIndexes: file_microservices_rank_v1_error_rank_proto_depIdxs,
		EnumInfos:         file_microservices_rank_v1_error_rank_proto_enumTypes,
	}.Build()
	File_microservices_rank_v1_error_rank_proto = out.File
	file_microservices_rank_v1_error_rank_proto_goTypes = nil
	file_microservices_rank_v1_error_rank_proto_depIdxs = nil
}
