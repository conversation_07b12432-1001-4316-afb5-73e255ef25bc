package player

import (
	"context"
	"errors"
	"fmt"
	"liteframe/internal/common/protos/dbstruct"
	"strings"

	"liteframe/internal/common/constant"
	"liteframe/internal/common/constant/def"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/rpc_def"

	"liteframe/pkg/actor"
	"liteframe/pkg/log"

	"liteframe/internal/game-logic/gameserver/game_def"
	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/internal/game-logic/gameserver/gateway"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/internal/game-logic/gameserver/logic/system/dao/dbhelper"

	"go.mongodb.org/mongo-driver/mongo"
	"google.golang.org/protobuf/proto"
)

func (p *Player) initPlayerHandler(ctx context.Context, req *actor.Message) error {
	db, err := dbhelper.LoadUserSync(p.PID(), req.Uid)
	if err != nil { // player not exist
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil
		}
		log.Info("init player failed", log.Kv("uid", req.Uid), log.Err(err))
		return err
	}
	p.db = db
	p.validPlayer = true
	p.init(false)

	return nil
}

func (p *Player) kickOldLogin() {
	if p.client == nil {
		log.Error("gateway nil", log.Kv("uid", p.uid), log.Kv("sid", p.sid))
		return
	}

	p.notifyPlayerSystemOffline(p.sid) // 踢掉旧登录 此时的 sid 是老的 session id

	//p.NotifyKickPlayer(&cs.SCKick{Code: retcode.Code_LOGIN_REPEAT})
}

// notifyPlayerSystemOffline 通知system/player 玩家下线 sid 是当前的 sid
func (p *Player) notifyPlayerSystemOffline(sid uint64) {
	actor.SyncRequest(p.PID(), actor_def.PlayerSystemPID, &actor.Message{
		Id:   uint32(def.MsgId_Actor_PlayerOffline),
		Uid:  p.uid,
		Data: sid,
	})
}

func (p *Player) loginHandler(ctx context.Context, req *actor.Message) error {
	defer req.Response(actor.RespMessage{})
	packet := req.Data.(*gateway.ClientMessage)
	sid := req.Uid    // login and logout uid is client's session id
	if sid == p.sid { // rcv same session repeated login
		log.Error("repeated login", log.Kv("uid", p.uid), log.Kv("sid", sid))
		return nil
	}

	if p.online { // 已在线 处理顶号逻辑
		p.kickOldLogin()
		log.Info("replace login", log.Kv("uid", p.uid), log.Kv("old_sid", p.sid), log.Kv("new_sid", sid))

	}

	retMsg := &cs.LC_LOGIN_RET{}
	client := clientMgr.GetClient(sid)
	if client == nil {
		log.Error("gateway not exist", log.Kv("session_id", sid))
		return nil
	}

	msg := &cs.CL_LOGIN_REQ{}
	if err := proto.Unmarshal(packet.Data, msg); err != nil { // can't be here
		retMsg.Result = int32(error_code.ERROR_PARSE_LOGIN_MSG)
		client.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)
		log.Error("unmarshal failed", log.Kv("session_id", sid), log.Err(err))
		return err
	}

	tokens := strings.Split(msg.Token, ".")
	if len(tokens) < 4 { //
		retMsg.Result = int32(error_code.ERROR_ACCOUNT_EMPTY)
		client.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)
		log.Error("invalid token", log.Kv("session_id", sid))
		return nil
	}
	account := tokens[3]

	if len(account) == 0 { // account invalid can't be here
		retMsg.Result = int32(error_code.ERROR_ACCOUNT_EMPTY)
		client.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)
		err := fmt.Errorf("session id %d account empty", sid)
		log.Error("account empty", log.Err(err))
		return err
	}

	if !p.checkAccountMap(account) { // 检查账号是否分在当前服务器
		retMsg.Result = int32(error_code.Error_DisConnectCode_0001)
		client.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)
		err := fmt.Errorf("session id %d account server map not match", sid)
		log.Error(err.Error())
		return err
	}

	isNew := false
	if p.db == nil { // 未创角的玩家 或加载失败玩家  重新加载或创角
		db, n, err := dbhelper.LoadOrCreateUserSync(p.PID(), account, uint64(global.ServerId), p.uid)
		if err != nil {
			p.validPlayer = false
			retMsg.Result = int32(error_code.ERROR_LOAD_CREATE_USER)
			client.SendPBMessage(rpc_def.LC_LOGIN_RET, sid, retMsg)

			p.notifyPlayerSystemOffline(sid)

			log.Error("load or create user failed", log.Kv("account", account), log.Err(err))
			return err
		}
		p.db = db
		isNew = n
	}
	p.init(isNew)

	// 逻辑加在下面！！！
	p.validPlayer = true
	p.sid = sid
	p.lastReq = nil

	p.onLogin(global.Now().Unix(), false)
	p.client = client

	actor.Send(p.PID(), actor_def.PlayerSystemPID, &actor.Message{
		Id:  def.MsgId_Actor_PlayerOnline,
		Uid: p.uid,
		Data: &game_def.ClientInfo{
			Cid: p.sid,
		},
	})

	retMsg.Result = 0 // TODO 机器人修改后记得改回error ok
	retMsg.IsCreateRole = isNew
	p.SendToClient(rpc_def.LC_LOGIN_RET, retMsg, false)

	p.bag.SyncBagItemList()
	p.settings.OnLogin()
	p.shop.SynShopGiftData()
	p.firstCharge.SynFirstChargeData()
	p.SendUpdateServerTime()
	p.SynPlayerInfo2PlayerService()
	p.FuncOpen().OnLogin()
	p.Activity().OnLogin()
	p.Mission().OnLogin()
	p.CommonBoxReward().OnLogin()
	p.timeShop.SynTimeShopGiftData()
	p.questionnaire.SyncRewardQuestion()
	p.newGuide.SyncAllGuideInfo()
	p.guild.SyncGuildInfo()
	p.totalRecharge.syncTotalRecharge()
	p.seasonBuff.OnLogin()
	// 同步排行榜数据
	p.SyncRoleInfo()
	//p.UpdateRankInfo(rankv1.RankType_Level, int64(p.db.Base.Level))
	p.heavenlyDao.SyncHeavenlyDaoInfo()
	p.weekCardData.sendPacket()
	log.Info("player login success", log.Kv("uid", p.uid), log.Kv("sid", sid))

	loginData := map[string]interface{}{
		"serverid":  p.db.Base.ServerId,
		"roleid":    p.uid,
		"rolename":  p.db.Base.Name,
		"rolelevel": p.Level(),
		"deviceid":  p.db.Base.InitDeviceId,
	}

	if err := log.BILog("login", "2050", loginData); err != nil {
		log.Error("write login bi log failed", log.Err(err))
	}

	//user_snap_help.UpdateUserData(p.PID(), p.db)
	p.SendSystemMsg("欢迎" + p.db.Base.Name + "登录轻量游戏框架")
	return nil
}

// player logout
func (p *Player) logoutHandler(ctx context.Context, req *actor.Message) error {
	defer req.Response(actor.RespMessage{})
	if req.Uid != p.sid { // not same session, ignore( logout's packet Uid is gateway session id)
		log.Error("not same session logout", log.Kv("uid", p.uid), log.Kv("rsid", req.Uid), log.Kv("cur", p.sid))
		return nil
	}
	sid := p.sid

	p.playerLogout()

	log.Info("login out", log.Kv("uid", p.uid), log.Kv("sid", sid))
	return nil
}

func (p *Player) playerLogout() {
	sid := p.sid
	// clean session
	p.sid = 0
	p.client = nil
	p.online = false
	p.lastReq = nil
	p.db.Base.LastLogout = global.Now().Unix()

	// 清理战斗相关资源
	p.battle.cleanupBattleOnLogout()

	// 登出时更新排行榜数据
	//p.UpdateRankInfo(rankv1.RankType_Level, int64(p.db.Base.Level))
	p.SynPlayerInfo2PlayerService()

	p.saveToDB(true)
	p.notifyPlayerSystemOffline(sid)

	//user_snap_help.UpdateUserDataLogout(p.PID(), p.GetUserDB())
}

func (p *Player) asyncSaveHandler(ctx context.Context, req *actor.Message) error {
	p.saveToDB(false)
	req.Response(actor.RespMessage{})
	return nil
}

// actor exit
func (p *Player) exitHandler(ctx context.Context, req *actor.Message) error {
	p.saveToDB(true)
	req.Response(actor.RespMessage{})

	p.clearAccountServerMap() // 清除账号服务器映射
	return nil
}

func (p *Player) saveToDB(sync bool) {
	p.db.Base.LastSaveTime = global.Now().Unix()
	if sync {
		err := dbhelper.SaveUserSync(p.PID(), p.Uid(), p.db)
		if err != nil {
			log.Error("sync save user failed", log.Kv("uid", p.uid), log.Err(err))
		}
		return
	}
	dbhelper.SaveUser(p.PID(), p.uid, p.db, func(err error) {
		if err != nil {
			log.Error("save user failed", log.Kv("uid", p.uid), log.Err(err))

		}
	})
}

// gateway close kick player
func (p *Player) gatewayCloseHandler(ctx context.Context, req *actor.Message) error {
	if !p.validPlayer {
		return nil
	}

	p.playerLogout()
	log.Info("logout on gateway close", log.Kv("uid", p.uid), log.Kv("sid", p.sid))
	return nil
}

func (p *Player) crossDayHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		return errors.New("msg nil")
	}
	if msg.Data == nil {
		return errors.New("data nil")
	}
	data, ok := msg.Data.(actor_def.CrossData)
	if !ok {
		return errors.New("data error")
	}
	p.OnCrossDay(data.Natural, data.NowUnix)
	return nil
}

func (p *Player) crossWeekHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		return errors.New("msg nil")
	}
	if msg.Data == nil {
		return errors.New("data nil")
	}
	data, ok := msg.Data.(actor_def.CrossData)
	if !ok {
		return errors.New("data error")
	}
	p.OnCrossWeek(data.Natural)
	return nil
}

func (p *Player) crossMonthHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		return errors.New("msg nil")
	}
	if msg.Data == nil {
		return errors.New("data nil")
	}
	data, ok := msg.Data.(actor_def.CrossData)
	if !ok {
		return errors.New("data error")
	}
	p.OnCrossMonth(data.Natural)
	return nil
}

func (p *Player) stopGameHandler(ctx context.Context, req *actor.Message) error {
	defer req.Response(actor.RespMessage{})

	sid := p.sid
	p.playerLogout()
	log.Info("logout on game stop", log.Kv("uid", p.uid), log.Kv("sid", sid))
	return nil
}

// syncPlayerData 登录时主动同步玩家基础信息给客户端
func (p *Player) syncPlayerData() {
	msg := &cs.LC_PlayerData_Sync{
		ISReconnect:    false,
		PlayerBaseInfo: p.ToClient(),
	}

	p.SendToClient(rpc_def.LC_PlayerData_Sync, msg, false)
}

// OnGuildChange 处理公会信息变更
func (p *Player) OnGuildChange(rsp interface{}) error {
	log.Debug("OnGuildChange")
	// 由于类型兼容性问题，这里使用通用接口
	// 可能需要根据实际类型定义调整
	return nil
}

// OnGuildEvent 处理公会事件
func (p *Player) OnGuildEvent(rsp interface{}) error {
	log.Debug("OnGuildEvent")
	// 由于类型兼容性问题，这里使用通用接口
	// 可能需要根据实际类型定义调整
	return nil
}

func (p *Player) guildSystemMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		log.Error("msg nil")
		return nil
	}
	if msg.Data == nil {
		log.Error("msg.data nil")
		return nil
	}
	switch msg.Data.(type) {
	case *cs.G2PGuildCreated:
		return p.guild.OnGuildCreated(msg.Data.(*cs.G2PGuildCreated))
	case *cs.G2PCreateGuildRsp:
		return p.guild.OnCreateGuildRsp(msg.Data.(*cs.G2PCreateGuildRsp))
	case *cs.G2PJoinGuild:
		return p.guild.OnJoinGuild(msg.Data.(*cs.G2PJoinGuild))
	case *cs.G2PJoinGuildRsp:
		return p.guild.OnApplyGuildRsp(msg.Data.(*cs.G2PJoinGuildRsp))
	case *cs.G2PLeaveGuild:
		return p.guild.OnLeaveGuild(msg.Data.(*cs.G2PLeaveGuild))
	case *cs.G2PLeaveGuildRsp:
		return p.guild.OnLeaveGuildRsp(msg.Data.(*cs.G2PLeaveGuildRsp))
	case *cs.G2PDismissGuildRsp:
		return p.guild.OnDismissGuildRsp(uint32(msg.Data.(*cs.G2PDismissGuildRsp).Errorcode))
	case *cs.G2PPositionChanged:
		return p.guild.OnPositionChanged(msg.Data.(*cs.G2PPositionChanged))
	case *cs.G2PContributionChanged:
		return p.guild.OnContributionChanged(msg.Data.(*cs.G2PContributionChanged))
	case *cs.G2PGuildDonateRsp:
		return p.guild.OnGuildDonateRsp(msg.Data.(*cs.G2PGuildDonateRsp))

	case *cs.G2PGetRecommendListRsp:
		return p.guild.OnGetRecommendListRsp(msg.Data.(*cs.G2PGetRecommendListRsp))
	case *cs.G2PGetGuildDetailRsp:
		return p.guild.OnGetGuildDetailRsp(msg.Data.(*cs.G2PGetGuildDetailRsp))
	case *cs.G2PEditGuildInfoRsp:
		return p.guild.OnEditGuildInfoRsp(msg.Data.(*cs.G2PEditGuildInfoRsp))
	case *cs.G2PGetApplyListRsp:
		return p.guild.OnGetApplyListRsp(msg.Data.(*cs.G2PGetApplyListRsp))
	case *cs.G2PProcessApplicationRsp:
		return p.guild.OnProcessApplicationRsp(msg.Data.(*cs.G2PProcessApplicationRsp))
	case *cs.G2PMemberActionRsp:
		return p.guild.OnMemberActionRsp(msg.Data.(*cs.G2PMemberActionRsp))
	default:
		log.Error("unhandled guild system message type", log.Kv("type", fmt.Sprintf("%T", msg.Data)))
	}
	return nil
}

func (p *Player) playerSystemMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		log.Error("msg nil")
		return nil
	}
	if msg.Data == nil {
		log.Error("msg.data nil")
		return nil
	}
	switch msg.Data.(type) {
	case *cs.PS2PUpdateGuildStatusReq:
		return p.guild.OnUpdateGuildStatus(msg.Data.(*cs.PS2PUpdateGuildStatusReq))

	case *cs.PS2PUpdateGuildAffiliationReq:
		return p.guild.OnUpdateGuildAffiliation(msg.Data.(*cs.PS2PUpdateGuildAffiliationReq))

	case *cs.PS2PRemovePendingApplyReq:
		return p.guild.OnRemovePendingApply(msg.Data.(*cs.PS2PRemovePendingApplyReq))

	case *cs.PS2PDeliverReq:
		return p.OnDeliverHandler(msg.Data.(*cs.PS2PDeliverReq))

	case *dbstruct.MailData:
		// Handle global mail
		p.HandleGlobalMail(msg.Data.(*dbstruct.MailData))

	case *cs.PS2PQuestReq:
		return p.OnQuestHandler(msg.Data.(*cs.PS2PQuestReq))

	case *cs.SeasonResetReq:
		// 处理赛季重置消息
		p.trophy.HandleSeasonReset(msg.Data.(*cs.SeasonResetReq))
		return nil

	default:
		log.Error("msg.Data type not support", log.Kv("type", fmt.Sprintf("%T", msg.Data)))
		return nil
	}
	return nil
}

func (p *Player) paymentMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		log.Error("msg nil")
		return nil
	}
	if msg.Data == nil {
		log.Error("msg.data nil")
		return nil
	}
	switch msg.Data.(type) {
	case *cs.PM2PCreateOrderRsp:
		return p.OnCreateOrderRsp(msg.Data.(*cs.PM2PCreateOrderRsp))
	default:
		log.Error("msg.Data type not support")
		return nil
	}
}

func (p *Player) secondHandler(ctx context.Context, msg *actor.Message) error {
	p.FuncOpen().Tick()
	p.Activity().Tick()
	return nil
}

func (p *Player) globalMailHandler(ctx context.Context, msg *actor.Message) error {
	if msg.Data == nil {
		log.Error("player system message data nil", log.Kv("uid", p.uid))
		return nil
	}

	switch data := msg.Data.(type) {
	case *dbstruct.MailData:
		// 处理全局邮件
		p.HandleGlobalMail(data)
	default:
		log.Warn("unknown player system message type", log.Kv("uid", p.uid), log.Kv("type", fmt.Sprintf("%T", data)))
	}
	return nil
}

func (p *Player) clearAccountServerMap() {
	if redisClient == nil || p.db == nil || p.db.Base == nil { //分服架构 redisClient 为nil
		return
	}
	key := constant.AccountServerMapKey(p.db.Base.Account)
	redisClient.Del(key)
}

func (p *Player) checkAccountMap(account string) bool {
	if redisClient == nil { //分服架构 redisClient 为nil
		return true
	}
	key := constant.AccountServerMapKey(account)
	id, err := redisClient.GetInt(key)
	if err != nil {
		log.Error("get account server map failed", log.Kv("uid", p.uid), log.Kv("err", err.Error()))
		return false
	}
	if uint32(id) != global.ServerId {
		log.Error("account server map not match", log.Kv("uid", p.uid), log.Kv("db_id", id), log.Kv("server_id", global.ServerId))
		return false
	}

	return true
}

func (p *Player) arenaSystemMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		log.Error("msg nil")
		return nil
	}
	if msg.Data == nil {
		log.Error("msg.data nil")
		return nil
	}
	switch msg.Data.(type) {
	case *cs.AS2PArenaGetData:
		return p.arena.OnArenaGetData(msg.Data.(*cs.AS2PArenaGetData))
	case *cs.AS2PArenaChallengeResult:
		return p.arena.OnArenaChallengeResult(msg.Data.(*cs.AS2PArenaChallengeResult))
	default:
		log.Error("unhandled arena system message type", log.Kv("type", fmt.Sprintf("%T", msg.Data)))
	}
	return nil
}

func (p *Player) tipOffMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		log.Error("msg nil")
		return nil
	}
	if msg.Data == nil {
		log.Error("msg.data nil")
		return nil
	}
	switch msg.Data.(type) {
	case *cs.TS2PTipOff:
		p.TipOffRes(msg.Data.(*cs.TS2PTipOff).Result)
		return nil
	default:
		log.Error("msg.Data type not support")
		return nil
	}
}

func (p *Player) seasonBuffMessageHandler(ctx context.Context, msg *actor.Message) error {
	if msg == nil {
		log.Error("seasonBuffMessageHandler: msg nil")
		return nil
	}
	if msg.Data == nil {
		log.Error("seasonBuffMessageHandler: msg.data nil")
		return nil
	}

	switch msg.Data.(type) {
	case *cs.PS2PSeasonBuffSync:
		p.seasonBuff.HandlePlayerSystemSeasonBuffSync(msg.Data.(*cs.PS2PSeasonBuffSync))
		return nil
	case *cs.SB2PSeasonBuffRsp:
		p.seasonBuff.HandleSeasonBuffResponse(msg.Data.(*cs.SB2PSeasonBuffRsp))
		return nil
	default:
		log.Error("seasonBuffMessageHandler: unsupported message type", log.Kv("type", fmt.Sprintf("%T", msg.Data)))
		return nil
	}
}
