server:
  http:
    addr: 0.0.0.0:8013
    timeout: 25s

serverItem:
  - ID: 1 # 序号
    Platform: "Android" #平台
    Name: "PG" # 项目代号
    Channel: "test" # 项目渠道
    CurrentVersion: "9.9.9.9" # 当前版本
    StartVersion: "0.0.0.0" # 开始版本
    EndVersion: "9.9.9.9" # 结束版本
    NeedUpdateApp: FALSE  # 整包更新
    ServerListUrl: "http://111.229.183.202:8003/serverlist/api/serverlist/getserverlist" # 服务器列表地址
    AnnounceInfoUrl: "http://111.229.183.202:8001/announcement/api/announcement/getannouncement" #公告地址
    AppUrl: "http://10.12.20.76:1088/Director" # app地址
    ResUrl: "http://10.12.20.76:1088/Director" #资源服地址

  - ID: 2 # 序号
    Platform: "editor" #平台
    Name: "PG" # 项目代号
    Channel: "editor" # 项目渠道
    CurrentVersion: "9.9.9.9" # 当前版本
    StartVersion: "0.0.0.0" # 开始版本
    EndVersion: "9.9.9.9" # 结束版本
    NeedUpdateApp: FALSE  # 整包更新
    ServerListUrl: "http://111.229.183.202:8003/serverlist/api/serverlist/getserverlist" # 服务器列表地址
    AnnounceInfoUrl: "http://111.229.183.202:8001/announcement/api/announcement/getannouncement" #公告地址
    AppUrl: "http://10.12.20.76:1088/Director" # app地址
    ResUrl: "http://10.12.20.76:1088/Director" #资源服地址

  - ID: 3 # 序号
    Platform: "Android" #平台
    Name: "PG" # 项目代号
    Channel: "dabaoji" # 项目渠道
    CurrentVersion: "0.0.0.1" # 当前版本
    StartVersion: "0.0.0.0" # 开始版本
    EndVersion: "9.9.9.9" # 结束版本
    NeedUpdateApp: FALSE  # 整包更新
    ServerListUrl: "http://111.229.183.202:8003/serverlist/api/serverlist/getserverlist" # 服务器列表地址
    AnnounceInfoUrl: "http://111.229.183.202:8001/announcement/api/announcement/getannouncement" #公告地址
    AppUrl: "http://10.12.20.76:1088/Director" # app地址
    ResUrl: "http://10.12.20.76:1088/Director" #资源服地址