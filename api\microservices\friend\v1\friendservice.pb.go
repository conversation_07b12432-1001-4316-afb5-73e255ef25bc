//GrpcAddressType:FriendServer
//GrpcServerType:all

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.19.1
// source: microservices/friend/v1/friendservice.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Type:Http
// Type:Inner
type RelationType int32

const (
	RelationType_node   RelationType = 0
	RelationType_friend RelationType = 1
	RelationType_black  RelationType = 2
	RelationType_enemy  RelationType = 3
)

// Enum value maps for RelationType.
var (
	RelationType_name = map[int32]string{
		0: "node",
		1: "friend",
		2: "black",
		3: "enemy",
	}
	RelationType_value = map[string]int32{
		"node":   0,
		"friend": 1,
		"black":  2,
		"enemy":  3,
	}
)

func (x RelationType) Enum() *RelationType {
	p := new(RelationType)
	*p = x
	return p
}

func (x RelationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RelationType) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_friend_v1_friendservice_proto_enumTypes[0].Descriptor()
}

func (RelationType) Type() protoreflect.EnumType {
	return &file_microservices_friend_v1_friendservice_proto_enumTypes[0]
}

func (x RelationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RelationType.Descriptor instead.
func (RelationType) EnumDescriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{0}
}

// Type:Http
// Type:Inner
type FriendBaseEnum int32

const (
	FriendBaseEnum_remark        FriendBaseEnum = 0
	FriendBaseEnum_notAddFriend  FriendBaseEnum = 1
	FriendBaseEnum_recommendTeam FriendBaseEnum = 2
)

// Enum value maps for FriendBaseEnum.
var (
	FriendBaseEnum_name = map[int32]string{
		0: "remark",
		1: "notAddFriend",
		2: "recommendTeam",
	}
	FriendBaseEnum_value = map[string]int32{
		"remark":        0,
		"notAddFriend":  1,
		"recommendTeam": 2,
	}
)

func (x FriendBaseEnum) Enum() *FriendBaseEnum {
	p := new(FriendBaseEnum)
	*p = x
	return p
}

func (x FriendBaseEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FriendBaseEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_microservices_friend_v1_friendservice_proto_enumTypes[1].Descriptor()
}

func (FriendBaseEnum) Type() protoreflect.EnumType {
	return &file_microservices_friend_v1_friendservice_proto_enumTypes[1]
}

func (x FriendBaseEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FriendBaseEnum.Descriptor instead.
func (FriendBaseEnum) EnumDescriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{1}
}

// Type:Http
type AddFriendPlayerInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	TargetGuid    uint64                 `protobuf:"varint,2,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`       //目标玩家ID
	ServerId      int32                  `protobuf:"varint,3,opt,name=serverId,proto3" json:"serverId,omitempty"`           // 玩家所在服ID
	RelationType  int32                  `protobuf:"varint,4,opt,name=relationType,proto3" json:"relationType,omitempty"`   // 1:好友 2:黑名单 3:仇人
	OperationType int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddFriendPlayerInfo) Reset() {
	*x = AddFriendPlayerInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFriendPlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFriendPlayerInfo) ProtoMessage() {}

func (x *AddFriendPlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFriendPlayerInfo.ProtoReflect.Descriptor instead.
func (*AddFriendPlayerInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{0}
}

func (x *AddFriendPlayerInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *AddFriendPlayerInfo) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *AddFriendPlayerInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *AddFriendPlayerInfo) GetRelationType() int32 {
	if x != nil {
		return x.RelationType
	}
	return 0
}

func (x *AddFriendPlayerInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type AddFriendResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	TargetGuid    uint64                 `protobuf:"varint,3,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`       //目标玩家ID
	ServerId      int32                  `protobuf:"varint,4,opt,name=serverId,proto3" json:"serverId,omitempty"`           // 玩家所在服ID
	RelationType  int32                  `protobuf:"varint,5,opt,name=relationType,proto3" json:"relationType,omitempty"`   // 1:好友 2:黑名单 3:仇人
	OperationType int32                  `protobuf:"varint,6,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddFriendResult) Reset() {
	*x = AddFriendResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFriendResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFriendResult) ProtoMessage() {}

func (x *AddFriendResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFriendResult.ProtoReflect.Descriptor instead.
func (*AddFriendResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{1}
}

func (x *AddFriendResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *AddFriendResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *AddFriendResult) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *AddFriendResult) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *AddFriendResult) GetRelationType() int32 {
	if x != nil {
		return x.RelationType
	}
	return 0
}

func (x *AddFriendResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type AddFriendApplyInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`
	TargetGuid    uint64                 `protobuf:"varint,2,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddFriendApplyInfo) Reset() {
	*x = AddFriendApplyInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFriendApplyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFriendApplyInfo) ProtoMessage() {}

func (x *AddFriendApplyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFriendApplyInfo.ProtoReflect.Descriptor instead.
func (*AddFriendApplyInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{2}
}

func (x *AddFriendApplyInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *AddFriendApplyInfo) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

// Type:Http
type AddFriendApplyResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`
	TargetGuid    uint64                 `protobuf:"varint,3,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddFriendApplyResult) Reset() {
	*x = AddFriendApplyResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFriendApplyResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFriendApplyResult) ProtoMessage() {}

func (x *AddFriendApplyResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFriendApplyResult.ProtoReflect.Descriptor instead.
func (*AddFriendApplyResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{3}
}

func (x *AddFriendApplyResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *AddFriendApplyResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *AddFriendApplyResult) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

// Type:Http
type AddFriendApplyResult2 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Test01        int32                  `protobuf:"varint,1,opt,name=test01,proto3" json:"test01,omitempty"`
	Test02        uint64                 `protobuf:"varint,2,opt,name=test02,proto3" json:"test02,omitempty"`
	TargetGuid    uint64                 `protobuf:"varint,3,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddFriendApplyResult2) Reset() {
	*x = AddFriendApplyResult2{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddFriendApplyResult2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddFriendApplyResult2) ProtoMessage() {}

func (x *AddFriendApplyResult2) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddFriendApplyResult2.ProtoReflect.Descriptor instead.
func (*AddFriendApplyResult2) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{4}
}

func (x *AddFriendApplyResult2) GetTest01() int32 {
	if x != nil {
		return x.Test01
	}
	return 0
}

func (x *AddFriendApplyResult2) GetTest02() uint64 {
	if x != nil {
		return x.Test02
	}
	return 0
}

func (x *AddFriendApplyResult2) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

// Type:Http
type RemoveFriendApplyInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`
	TargetGuid    uint64                 `protobuf:"varint,2,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"` //为0表示一键拒绝 不为0表示单个拒绝
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveFriendApplyInfo) Reset() {
	*x = RemoveFriendApplyInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveFriendApplyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveFriendApplyInfo) ProtoMessage() {}

func (x *RemoveFriendApplyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveFriendApplyInfo.ProtoReflect.Descriptor instead.
func (*RemoveFriendApplyInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{5}
}

func (x *RemoveFriendApplyInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *RemoveFriendApplyInfo) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

// Type:Http
type RemoveFriendApplyResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`
	TargetGuid    uint64                 `protobuf:"varint,3,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveFriendApplyResult) Reset() {
	*x = RemoveFriendApplyResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveFriendApplyResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveFriendApplyResult) ProtoMessage() {}

func (x *RemoveFriendApplyResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveFriendApplyResult.ProtoReflect.Descriptor instead.
func (*RemoveFriendApplyResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{6}
}

func (x *RemoveFriendApplyResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *RemoveFriendApplyResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *RemoveFriendApplyResult) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

// Type:Http
type GetFriendApplyListInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFriendApplyListInfo) Reset() {
	*x = GetFriendApplyListInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFriendApplyListInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFriendApplyListInfo) ProtoMessage() {}

func (x *GetFriendApplyListInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFriendApplyListInfo.ProtoReflect.Descriptor instead.
func (*GetFriendApplyListInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{7}
}

func (x *GetFriendApplyListInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

// Type:Http
type FriendApplyInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Guid          uint64                 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`
	ServerId      int32                  `protobuf:"varint,2,opt,name=serverId,proto3" json:"serverId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FriendApplyInfo) Reset() {
	*x = FriendApplyInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FriendApplyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FriendApplyInfo) ProtoMessage() {}

func (x *FriendApplyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FriendApplyInfo.ProtoReflect.Descriptor instead.
func (*FriendApplyInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{8}
}

func (x *FriendApplyInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *FriendApplyInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

// Type:Http
type GetFriendApplyListResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`
	ApplyList     []*FriendApplyInfo     `protobuf:"bytes,3,rep,name=applyList,proto3" json:"applyList,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFriendApplyListResult) Reset() {
	*x = GetFriendApplyListResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFriendApplyListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFriendApplyListResult) ProtoMessage() {}

func (x *GetFriendApplyListResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFriendApplyListResult.ProtoReflect.Descriptor instead.
func (*GetFriendApplyListResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{9}
}

func (x *GetFriendApplyListResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GetFriendApplyListResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *GetFriendApplyListResult) GetApplyList() []*FriendApplyInfo {
	if x != nil {
		return x.ApplyList
	}
	return nil
}

// Type:Http
type ApproveFriendApplyInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`
	TargetGuid    uint64                 `protobuf:"varint,2,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"` //为0表示一键同意 不为0表示单个同意
	ServerId      int32                  `protobuf:"varint,3,opt,name=serverId,proto3" json:"serverId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApproveFriendApplyInfo) Reset() {
	*x = ApproveFriendApplyInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApproveFriendApplyInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApproveFriendApplyInfo) ProtoMessage() {}

func (x *ApproveFriendApplyInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApproveFriendApplyInfo.ProtoReflect.Descriptor instead.
func (*ApproveFriendApplyInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{10}
}

func (x *ApproveFriendApplyInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *ApproveFriendApplyInfo) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *ApproveFriendApplyInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

// Type:Http
type ApproveFriendApplyResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`
	TargetGuid    uint64                 `protobuf:"varint,3,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ApproveFriendApplyResult) Reset() {
	*x = ApproveFriendApplyResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApproveFriendApplyResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApproveFriendApplyResult) ProtoMessage() {}

func (x *ApproveFriendApplyResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApproveFriendApplyResult.ProtoReflect.Descriptor instead.
func (*ApproveFriendApplyResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{11}
}

func (x *ApproveFriendApplyResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *ApproveFriendApplyResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *ApproveFriendApplyResult) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

// Type:Http
type DelFriendPlayerInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	TargetGuid    uint64                 `protobuf:"varint,2,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`       //目标玩家ID
	ServerId      int32                  `protobuf:"varint,3,opt,name=serverId,proto3" json:"serverId,omitempty"`           // 玩家所在服ID
	RelationType  int32                  `protobuf:"varint,4,opt,name=relationType,proto3" json:"relationType,omitempty"`   // 1:好友 2:黑名单 3:仇人
	OperationType int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DelFriendPlayerInfo) Reset() {
	*x = DelFriendPlayerInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelFriendPlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelFriendPlayerInfo) ProtoMessage() {}

func (x *DelFriendPlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelFriendPlayerInfo.ProtoReflect.Descriptor instead.
func (*DelFriendPlayerInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{12}
}

func (x *DelFriendPlayerInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *DelFriendPlayerInfo) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *DelFriendPlayerInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *DelFriendPlayerInfo) GetRelationType() int32 {
	if x != nil {
		return x.RelationType
	}
	return 0
}

func (x *DelFriendPlayerInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type DelFriendResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	TargetGuid    uint64                 `protobuf:"varint,3,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`       //目标玩家ID
	ServerId      int32                  `protobuf:"varint,4,opt,name=serverId,proto3" json:"serverId,omitempty"`           // 玩家所在服ID
	RelationType  int32                  `protobuf:"varint,5,opt,name=relationType,proto3" json:"relationType,omitempty"`   // 1:好友 2:黑名单 3:仇人
	OperationType int32                  `protobuf:"varint,6,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DelFriendResult) Reset() {
	*x = DelFriendResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelFriendResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelFriendResult) ProtoMessage() {}

func (x *DelFriendResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelFriendResult.ProtoReflect.Descriptor instead.
func (*DelFriendResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{13}
}

func (x *DelFriendResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *DelFriendResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *DelFriendResult) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *DelFriendResult) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *DelFriendResult) GetRelationType() int32 {
	if x != nil {
		return x.RelationType
	}
	return 0
}

func (x *DelFriendResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type SetFriendPlayerInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	TargetGuid    uint64                 `protobuf:"varint,2,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`       //目标玩家ID
	ServerId      int32                  `protobuf:"varint,3,opt,name=serverId,proto3" json:"serverId,omitempty"`           // 玩家所在服ID
	RelationType  int32                  `protobuf:"varint,4,opt,name=relationType,proto3" json:"relationType,omitempty"`   // 1:好友 2:黑名单 3:仇人
	OperationType int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetFriendPlayerInfo) Reset() {
	*x = SetFriendPlayerInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetFriendPlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetFriendPlayerInfo) ProtoMessage() {}

func (x *SetFriendPlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetFriendPlayerInfo.ProtoReflect.Descriptor instead.
func (*SetFriendPlayerInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{14}
}

func (x *SetFriendPlayerInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *SetFriendPlayerInfo) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *SetFriendPlayerInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *SetFriendPlayerInfo) GetRelationType() int32 {
	if x != nil {
		return x.RelationType
	}
	return 0
}

func (x *SetFriendPlayerInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type SetFriendResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	TargetGuid    uint64                 `protobuf:"varint,3,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`       //目标玩家ID
	ServerId      int32                  `protobuf:"varint,4,opt,name=serverId,proto3" json:"serverId,omitempty"`           // 玩家所在服ID
	RelationType  int32                  `protobuf:"varint,5,opt,name=relationType,proto3" json:"relationType,omitempty"`   // 1:好友 2:黑名单 3:仇人
	OperationType int32                  `protobuf:"varint,6,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetFriendResult) Reset() {
	*x = SetFriendResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetFriendResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetFriendResult) ProtoMessage() {}

func (x *SetFriendResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetFriendResult.ProtoReflect.Descriptor instead.
func (*SetFriendResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{15}
}

func (x *SetFriendResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *SetFriendResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *SetFriendResult) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *SetFriendResult) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *SetFriendResult) GetRelationType() int32 {
	if x != nil {
		return x.RelationType
	}
	return 0
}

func (x *SetFriendResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type GetFriendPlayerListInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Guid          uint64                 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`                   //玩家ID
	ServerId      int32                  `protobuf:"varint,2,opt,name=serverId,proto3" json:"serverId,omitempty"`           // 玩家所在服ID
	RelationType  int32                  `protobuf:"varint,3,opt,name=relationType,proto3" json:"relationType,omitempty"`   //0 全部  1:好友 2:黑名单 3:仇人
	OperationType int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息  0 全部， 1 对应关系联系人， 2 我的关注列表， 3 关注我的列表， 4 我的分组列表， 5 好友关系组  6 第一次登录 尝试给关注我的好友发信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFriendPlayerListInfo) Reset() {
	*x = GetFriendPlayerListInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFriendPlayerListInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFriendPlayerListInfo) ProtoMessage() {}

func (x *GetFriendPlayerListInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFriendPlayerListInfo.ProtoReflect.Descriptor instead.
func (*GetFriendPlayerListInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{16}
}

func (x *GetFriendPlayerListInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *GetFriendPlayerListInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *GetFriendPlayerListInfo) GetRelationType() int32 {
	if x != nil {
		return x.RelationType
	}
	return 0
}

func (x *GetFriendPlayerListInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type FriendPlayerBase struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Guid          uint64                 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`               //guid
	ServerId      int32                  `protobuf:"varint,2,opt,name=serverId,proto3" json:"serverId,omitempty"`       //好友的服务器id
	FriendPoint   int32                  `protobuf:"varint,3,opt,name=friendPoint,proto3" json:"friendPoint,omitempty"` //好友度
	Remarks       string                 `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`          //好友备注
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FriendPlayerBase) Reset() {
	*x = FriendPlayerBase{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FriendPlayerBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FriendPlayerBase) ProtoMessage() {}

func (x *FriendPlayerBase) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FriendPlayerBase.ProtoReflect.Descriptor instead.
func (*FriendPlayerBase) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{17}
}

func (x *FriendPlayerBase) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *FriendPlayerBase) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *FriendPlayerBase) GetFriendPoint() int32 {
	if x != nil {
		return x.FriendPoint
	}
	return 0
}

func (x *FriendPlayerBase) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

// Type:Http
type FriendAttentionBase struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Guid          uint64                 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`         //guid
	ServerId      int32                  `protobuf:"varint,2,opt,name=serverId,proto3" json:"serverId,omitempty"` //好友的服务器id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FriendAttentionBase) Reset() {
	*x = FriendAttentionBase{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FriendAttentionBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FriendAttentionBase) ProtoMessage() {}

func (x *FriendAttentionBase) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FriendAttentionBase.ProtoReflect.Descriptor instead.
func (*FriendAttentionBase) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{18}
}

func (x *FriendAttentionBase) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *FriendAttentionBase) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

// Type:Http
type FriendGroupPlayer struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	GroupId         int32                  `protobuf:"varint,1,opt,name=groupId,proto3" json:"groupId,omitempty"`                        //组id
	GroupName       string                 `protobuf:"bytes,2,opt,name=groupName,proto3" json:"groupName,omitempty"`                     //分组名称
	GroupPlayerGuid []uint64               `protobuf:"varint,3,rep,packed,name=groupPlayerGuid,proto3" json:"groupPlayerGuid,omitempty"` //分组玩家的gui
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *FriendGroupPlayer) Reset() {
	*x = FriendGroupPlayer{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FriendGroupPlayer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FriendGroupPlayer) ProtoMessage() {}

func (x *FriendGroupPlayer) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FriendGroupPlayer.ProtoReflect.Descriptor instead.
func (*FriendGroupPlayer) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{19}
}

func (x *FriendGroupPlayer) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *FriendGroupPlayer) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *FriendGroupPlayer) GetGroupPlayerGuid() []uint64 {
	if x != nil {
		return x.GroupPlayerGuid
	}
	return nil
}

// Type:Http
type FriendGroupRelation struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RelationType  int32                  `protobuf:"varint,1,opt,name=relationType,proto3" json:"relationType,omitempty"`    //关系类型
	PlayerGuid    []uint64               `protobuf:"varint,2,rep,packed,name=playerGuid,proto3" json:"playerGuid,omitempty"` //玩家的gui
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FriendGroupRelation) Reset() {
	*x = FriendGroupRelation{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FriendGroupRelation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FriendGroupRelation) ProtoMessage() {}

func (x *FriendGroupRelation) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FriendGroupRelation.ProtoReflect.Descriptor instead.
func (*FriendGroupRelation) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{20}
}

func (x *FriendGroupRelation) GetRelationType() int32 {
	if x != nil {
		return x.RelationType
	}
	return 0
}

func (x *FriendGroupRelation) GetPlayerGuid() []uint64 {
	if x != nil {
		return x.PlayerGuid
	}
	return nil
}

// Type:Http
type GetFriendPlayerListInfoResult struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	Result             int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	Guid               uint64                 `protobuf:"varint,2,opt,name=guid,proto3" json:"guid,omitempty"`                            //请求人的guid
	FriendList         []*FriendPlayerBase    `protobuf:"bytes,3,rep,name=friendList,proto3" json:"friendList,omitempty"`                 //好友列表
	SelfAttentionList  []*FriendAttentionBase `protobuf:"bytes,4,rep,name=selfAttentionList,proto3" json:"selfAttentionList,omitempty"`   //我关注列表
	OtherAttentionList []*FriendAttentionBase `protobuf:"bytes,5,rep,name=otherAttentionList,proto3" json:"otherAttentionList,omitempty"` //关注我的列表
	FriendGroupList    []*FriendGroupPlayer   `protobuf:"bytes,6,rep,name=friendGroupList,proto3" json:"friendGroupList,omitempty"`       //好友分组列表
	FriendRelationList []*FriendGroupRelation `protobuf:"bytes,7,rep,name=friendRelationList,proto3" json:"friendRelationList,omitempty"` //关系组
	OperationType      int32                  `protobuf:"varint,8,opt,name=operationType,proto3" json:"operationType,omitempty"`          // 操作信息
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetFriendPlayerListInfoResult) Reset() {
	*x = GetFriendPlayerListInfoResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFriendPlayerListInfoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFriendPlayerListInfoResult) ProtoMessage() {}

func (x *GetFriendPlayerListInfoResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFriendPlayerListInfoResult.ProtoReflect.Descriptor instead.
func (*GetFriendPlayerListInfoResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{21}
}

func (x *GetFriendPlayerListInfoResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GetFriendPlayerListInfoResult) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *GetFriendPlayerListInfoResult) GetFriendList() []*FriendPlayerBase {
	if x != nil {
		return x.FriendList
	}
	return nil
}

func (x *GetFriendPlayerListInfoResult) GetSelfAttentionList() []*FriendAttentionBase {
	if x != nil {
		return x.SelfAttentionList
	}
	return nil
}

func (x *GetFriendPlayerListInfoResult) GetOtherAttentionList() []*FriendAttentionBase {
	if x != nil {
		return x.OtherAttentionList
	}
	return nil
}

func (x *GetFriendPlayerListInfoResult) GetFriendGroupList() []*FriendGroupPlayer {
	if x != nil {
		return x.FriendGroupList
	}
	return nil
}

func (x *GetFriendPlayerListInfoResult) GetFriendRelationList() []*FriendGroupRelation {
	if x != nil {
		return x.FriendRelationList
	}
	return nil
}

func (x *GetFriendPlayerListInfoResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type CreateFriendGroupInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	GroupName     string                 `protobuf:"bytes,2,opt,name=groupName,proto3" json:"groupName,omitempty"`          //分组名称
	OperationType int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateFriendGroupInfo) Reset() {
	*x = CreateFriendGroupInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateFriendGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFriendGroupInfo) ProtoMessage() {}

func (x *CreateFriendGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFriendGroupInfo.ProtoReflect.Descriptor instead.
func (*CreateFriendGroupInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{22}
}

func (x *CreateFriendGroupInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *CreateFriendGroupInfo) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *CreateFriendGroupInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type CreateFriendGroupResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	GroupId       int32                  `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"`             //分组id
	GroupName     string                 `protobuf:"bytes,4,opt,name=groupName,proto3" json:"groupName,omitempty"`          //分组名称
	OperationType int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateFriendGroupResult) Reset() {
	*x = CreateFriendGroupResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateFriendGroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFriendGroupResult) ProtoMessage() {}

func (x *CreateFriendGroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFriendGroupResult.ProtoReflect.Descriptor instead.
func (*CreateFriendGroupResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{23}
}

func (x *CreateFriendGroupResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *CreateFriendGroupResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *CreateFriendGroupResult) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *CreateFriendGroupResult) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *CreateFriendGroupResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type SetFriendGroupInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	GroupId       int32                  `protobuf:"varint,2,opt,name=groupId,proto3" json:"groupId,omitempty"`             //分组id
	GroupName     string                 `protobuf:"bytes,3,opt,name=groupName,proto3" json:"groupName,omitempty"`          //分组名称
	OperationType int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetFriendGroupInfo) Reset() {
	*x = SetFriendGroupInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetFriendGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetFriendGroupInfo) ProtoMessage() {}

func (x *SetFriendGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetFriendGroupInfo.ProtoReflect.Descriptor instead.
func (*SetFriendGroupInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{24}
}

func (x *SetFriendGroupInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *SetFriendGroupInfo) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *SetFriendGroupInfo) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *SetFriendGroupInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type SetFriendGroupResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	GroupId       int32                  `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"`             //分组id
	GroupName     string                 `protobuf:"bytes,4,opt,name=groupName,proto3" json:"groupName,omitempty"`          //分组名称
	OperationType int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetFriendGroupResult) Reset() {
	*x = SetFriendGroupResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetFriendGroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetFriendGroupResult) ProtoMessage() {}

func (x *SetFriendGroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetFriendGroupResult.ProtoReflect.Descriptor instead.
func (*SetFriendGroupResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{25}
}

func (x *SetFriendGroupResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *SetFriendGroupResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *SetFriendGroupResult) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *SetFriendGroupResult) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *SetFriendGroupResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type DelFriendGroupInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	GroupId       int32                  `protobuf:"varint,2,opt,name=groupId,proto3" json:"groupId,omitempty"`             //分组id
	OperationType int32                  `protobuf:"varint,3,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DelFriendGroupInfo) Reset() {
	*x = DelFriendGroupInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelFriendGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelFriendGroupInfo) ProtoMessage() {}

func (x *DelFriendGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelFriendGroupInfo.ProtoReflect.Descriptor instead.
func (*DelFriendGroupInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{26}
}

func (x *DelFriendGroupInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *DelFriendGroupInfo) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *DelFriendGroupInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type DelFriendGroupResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	GroupId       int32                  `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"`             //分组id
	OperationType int32                  `protobuf:"varint,4,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DelFriendGroupResult) Reset() {
	*x = DelFriendGroupResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelFriendGroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelFriendGroupResult) ProtoMessage() {}

func (x *DelFriendGroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelFriendGroupResult.ProtoReflect.Descriptor instead.
func (*DelFriendGroupResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{27}
}

func (x *DelFriendGroupResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *DelFriendGroupResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *DelFriendGroupResult) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *DelFriendGroupResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type AddPlayerFriendGroupInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	MyGuid         uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`                        //我的玩家ID
	GroupId        int32                  `protobuf:"varint,2,opt,name=groupId,proto3" json:"groupId,omitempty"`                      //分组id
	PlayerListGuid []uint64               `protobuf:"varint,3,rep,packed,name=playerListGuid,proto3" json:"playerListGuid,omitempty"` //guid list
	OperationType  int32                  `protobuf:"varint,4,opt,name=operationType,proto3" json:"operationType,omitempty"`          // 操作信息
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AddPlayerFriendGroupInfo) Reset() {
	*x = AddPlayerFriendGroupInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPlayerFriendGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPlayerFriendGroupInfo) ProtoMessage() {}

func (x *AddPlayerFriendGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPlayerFriendGroupInfo.ProtoReflect.Descriptor instead.
func (*AddPlayerFriendGroupInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{28}
}

func (x *AddPlayerFriendGroupInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *AddPlayerFriendGroupInfo) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *AddPlayerFriendGroupInfo) GetPlayerListGuid() []uint64 {
	if x != nil {
		return x.PlayerListGuid
	}
	return nil
}

func (x *AddPlayerFriendGroupInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type AddPlayerFriendGroupResult struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Result         int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid         uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`                        //我的玩家ID
	GroupId        int32                  `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"`                      //分组id
	PlayerListGuid []uint64               `protobuf:"varint,4,rep,packed,name=playerListGuid,proto3" json:"playerListGuid,omitempty"` //guid list
	OperationType  int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"`          // 操作信息
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AddPlayerFriendGroupResult) Reset() {
	*x = AddPlayerFriendGroupResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPlayerFriendGroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPlayerFriendGroupResult) ProtoMessage() {}

func (x *AddPlayerFriendGroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPlayerFriendGroupResult.ProtoReflect.Descriptor instead.
func (*AddPlayerFriendGroupResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{29}
}

func (x *AddPlayerFriendGroupResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *AddPlayerFriendGroupResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *AddPlayerFriendGroupResult) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *AddPlayerFriendGroupResult) GetPlayerListGuid() []uint64 {
	if x != nil {
		return x.PlayerListGuid
	}
	return nil
}

func (x *AddPlayerFriendGroupResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type SetFriendRemarksInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	TargetGuid    uint64                 `protobuf:"varint,2,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`       //目标玩家ID
	Remarks       string                 `protobuf:"bytes,3,opt,name=remarks,proto3" json:"remarks,omitempty"`              //好友备注
	OperationType int32                  `protobuf:"varint,4,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetFriendRemarksInfo) Reset() {
	*x = SetFriendRemarksInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetFriendRemarksInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetFriendRemarksInfo) ProtoMessage() {}

func (x *SetFriendRemarksInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetFriendRemarksInfo.ProtoReflect.Descriptor instead.
func (*SetFriendRemarksInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{30}
}

func (x *SetFriendRemarksInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *SetFriendRemarksInfo) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *SetFriendRemarksInfo) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *SetFriendRemarksInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type SetFriendRemarksResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	TargetGuid    uint64                 `protobuf:"varint,3,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`       //目标玩家ID
	Remarks       string                 `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`              //好友备注
	OperationType int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetFriendRemarksResult) Reset() {
	*x = SetFriendRemarksResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetFriendRemarksResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetFriendRemarksResult) ProtoMessage() {}

func (x *SetFriendRemarksResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetFriendRemarksResult.ProtoReflect.Descriptor instead.
func (*SetFriendRemarksResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{31}
}

func (x *SetFriendRemarksResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *SetFriendRemarksResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *SetFriendRemarksResult) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *SetFriendRemarksResult) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *SetFriendRemarksResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type SetSelfAttentionInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	TargetGuid    uint64                 `protobuf:"varint,2,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`       //目标玩家ID
	Type          int32                  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`                   //类型 1 加 2 减
	OperationType int32                  `protobuf:"varint,4,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetSelfAttentionInfo) Reset() {
	*x = SetSelfAttentionInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetSelfAttentionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetSelfAttentionInfo) ProtoMessage() {}

func (x *SetSelfAttentionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetSelfAttentionInfo.ProtoReflect.Descriptor instead.
func (*SetSelfAttentionInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{32}
}

func (x *SetSelfAttentionInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *SetSelfAttentionInfo) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *SetSelfAttentionInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SetSelfAttentionInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type SetSelfAttentionResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	TargetGuid    uint64                 `protobuf:"varint,3,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`       //目标玩家ID
	Type          int32                  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`                   //类型 1 加 2 减
	OperationType int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetSelfAttentionResult) Reset() {
	*x = SetSelfAttentionResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetSelfAttentionResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetSelfAttentionResult) ProtoMessage() {}

func (x *SetSelfAttentionResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetSelfAttentionResult.ProtoReflect.Descriptor instead.
func (*SetSelfAttentionResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{33}
}

func (x *SetSelfAttentionResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *SetSelfAttentionResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *SetSelfAttentionResult) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *SetSelfAttentionResult) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *SetSelfAttentionResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type GroupBaseInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	ServerId      int32                  `protobuf:"varint,2,opt,name=serverId,proto3" json:"serverId,omitempty"`           //服务器id
	GroupId       uint64                 `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"`             //群组id
	GroupName     string                 `protobuf:"bytes,4,opt,name=groupName,proto3" json:"groupName,omitempty"`          //群组名称
	GroupNotice   string                 `protobuf:"bytes,5,opt,name=groupNotice,proto3" json:"groupNotice,omitempty"`      //群组通知
	GroupType     int32                  `protobuf:"varint,6,opt,name=groupType,proto3" json:"groupType,omitempty"`         //群组类型
	Type          int32                  `protobuf:"varint,7,opt,name=type,proto3" json:"type,omitempty"`                   //类型 1 加 2 减 3 修改
	OperationType int32                  `protobuf:"varint,8,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroupBaseInfo) Reset() {
	*x = GroupBaseInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupBaseInfo) ProtoMessage() {}

func (x *GroupBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupBaseInfo.ProtoReflect.Descriptor instead.
func (*GroupBaseInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{34}
}

func (x *GroupBaseInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *GroupBaseInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *GroupBaseInfo) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GroupBaseInfo) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *GroupBaseInfo) GetGroupNotice() string {
	if x != nil {
		return x.GroupNotice
	}
	return ""
}

func (x *GroupBaseInfo) GetGroupType() int32 {
	if x != nil {
		return x.GroupType
	}
	return 0
}

func (x *GroupBaseInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *GroupBaseInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type CreateGroupResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	ServerId      int32                  `protobuf:"varint,3,opt,name=serverId,proto3" json:"serverId,omitempty"`           //服务器id
	GroupId       uint64                 `protobuf:"varint,4,opt,name=groupId,proto3" json:"groupId,omitempty"`             //群组id
	GroupName     string                 `protobuf:"bytes,5,opt,name=groupName,proto3" json:"groupName,omitempty"`          //群组名称
	GroupNotice   string                 `protobuf:"bytes,6,opt,name=groupNotice,proto3" json:"groupNotice,omitempty"`      //群组通知
	GroupType     int32                  `protobuf:"varint,7,opt,name=groupType,proto3" json:"groupType,omitempty"`         //群组类型
	Type          int32                  `protobuf:"varint,8,opt,name=type,proto3" json:"type,omitempty"`                   //类型 1 加 2 减 3 修改
	OperationType int32                  `protobuf:"varint,9,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGroupResult) Reset() {
	*x = CreateGroupResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupResult) ProtoMessage() {}

func (x *CreateGroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupResult.ProtoReflect.Descriptor instead.
func (*CreateGroupResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{35}
}

func (x *CreateGroupResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *CreateGroupResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *CreateGroupResult) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *CreateGroupResult) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *CreateGroupResult) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *CreateGroupResult) GetGroupNotice() string {
	if x != nil {
		return x.GroupNotice
	}
	return ""
}

func (x *CreateGroupResult) GetGroupType() int32 {
	if x != nil {
		return x.GroupType
	}
	return 0
}

func (x *CreateGroupResult) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *CreateGroupResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type RedisGroupBaseInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ServerId      int32                  `protobuf:"varint,1,opt,name=serverId,proto3" json:"serverId,omitempty"`      //服务器id
	GroupId       uint64                 `protobuf:"varint,2,opt,name=groupId,proto3" json:"groupId,omitempty"`        //群组id
	GroupName     string                 `protobuf:"bytes,3,opt,name=groupName,proto3" json:"groupName,omitempty"`     //群组名称
	GroupNotice   string                 `protobuf:"bytes,4,opt,name=groupNotice,proto3" json:"groupNotice,omitempty"` //群组通知
	GroupType     int32                  `protobuf:"varint,5,opt,name=groupType,proto3" json:"groupType,omitempty"`    //群组类型
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RedisGroupBaseInfo) Reset() {
	*x = RedisGroupBaseInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RedisGroupBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedisGroupBaseInfo) ProtoMessage() {}

func (x *RedisGroupBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedisGroupBaseInfo.ProtoReflect.Descriptor instead.
func (*RedisGroupBaseInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{36}
}

func (x *RedisGroupBaseInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *RedisGroupBaseInfo) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *RedisGroupBaseInfo) GetGroupName() string {
	if x != nil {
		return x.GroupName
	}
	return ""
}

func (x *RedisGroupBaseInfo) GetGroupNotice() string {
	if x != nil {
		return x.GroupNotice
	}
	return ""
}

func (x *RedisGroupBaseInfo) GetGroupType() int32 {
	if x != nil {
		return x.GroupType
	}
	return 0
}

// Type:Http
type RedisFriendItemKey struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Guid          uint64                 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`               //guid
	ServerId      int32                  `protobuf:"varint,2,opt,name=serverId,proto3" json:"serverId,omitempty"`       // 服务器id
	FriendPoint   int32                  `protobuf:"varint,3,opt,name=friendPoint,proto3" json:"friendPoint,omitempty"` //好感度
	Remarks       string                 `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`          //备注
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RedisFriendItemKey) Reset() {
	*x = RedisFriendItemKey{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RedisFriendItemKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RedisFriendItemKey) ProtoMessage() {}

func (x *RedisFriendItemKey) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RedisFriendItemKey.ProtoReflect.Descriptor instead.
func (*RedisFriendItemKey) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{37}
}

func (x *RedisFriendItemKey) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *RedisFriendItemKey) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *RedisFriendItemKey) GetFriendPoint() int32 {
	if x != nil {
		return x.FriendPoint
	}
	return 0
}

func (x *RedisFriendItemKey) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

// Type:Http
type ApplyGroupListInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	MyGuid         uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`                //我的玩家ID
	ServerId       int32                  `protobuf:"varint,2,opt,name=serverId,proto3" json:"serverId,omitempty"`            //服务器id
	GroupId        uint64                 `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"`              //群组id
	TargetGuid     uint64                 `protobuf:"varint,4,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`        //对方的guid
	TargetServerId string                 `protobuf:"bytes,5,opt,name=targetServerId,proto3" json:"targetServerId,omitempty"` // 对方的服务器id
	Type           int32                  `protobuf:"varint,6,opt,name=type,proto3" json:"type,omitempty"`                    //类型 1 邀请
	OperationType  int32                  `protobuf:"varint,7,opt,name=operationType,proto3" json:"operationType,omitempty"`  // 操作信息
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ApplyGroupListInfo) Reset() {
	*x = ApplyGroupListInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplyGroupListInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyGroupListInfo) ProtoMessage() {}

func (x *ApplyGroupListInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyGroupListInfo.ProtoReflect.Descriptor instead.
func (*ApplyGroupListInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{38}
}

func (x *ApplyGroupListInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *ApplyGroupListInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *ApplyGroupListInfo) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *ApplyGroupListInfo) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *ApplyGroupListInfo) GetTargetServerId() string {
	if x != nil {
		return x.TargetServerId
	}
	return ""
}

func (x *ApplyGroupListInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ApplyGroupListInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type ApplyGroupListResult struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Result         int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid         uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`                //我的玩家ID
	ServerId       int32                  `protobuf:"varint,3,opt,name=serverId,proto3" json:"serverId,omitempty"`            //服务器id
	GroupId        uint64                 `protobuf:"varint,4,opt,name=groupId,proto3" json:"groupId,omitempty"`              //群组id
	TargetGuid     uint64                 `protobuf:"varint,5,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`        //对方的guid
	TargetServerId string                 `protobuf:"bytes,6,opt,name=targetServerId,proto3" json:"targetServerId,omitempty"` // 对方的服务器id
	Type           int32                  `protobuf:"varint,7,opt,name=type,proto3" json:"type,omitempty"`                    //类型 1 邀请
	OperationType  int32                  `protobuf:"varint,8,opt,name=operationType,proto3" json:"operationType,omitempty"`  // 操作信息
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ApplyGroupListResult) Reset() {
	*x = ApplyGroupListResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplyGroupListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyGroupListResult) ProtoMessage() {}

func (x *ApplyGroupListResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyGroupListResult.ProtoReflect.Descriptor instead.
func (*ApplyGroupListResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{39}
}

func (x *ApplyGroupListResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *ApplyGroupListResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *ApplyGroupListResult) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *ApplyGroupListResult) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *ApplyGroupListResult) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *ApplyGroupListResult) GetTargetServerId() string {
	if x != nil {
		return x.TargetServerId
	}
	return ""
}

func (x *ApplyGroupListResult) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *ApplyGroupListResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type DelGroupPlayerInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	MyGuid         uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`                //我的玩家ID
	ServerId       int32                  `protobuf:"varint,2,opt,name=serverId,proto3" json:"serverId,omitempty"`            //服务器id
	GroupId        uint64                 `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"`              //群组id
	TargetGuid     uint64                 `protobuf:"varint,4,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`        //对方的guid
	TargetServerId string                 `protobuf:"bytes,5,opt,name=targetServerId,proto3" json:"targetServerId,omitempty"` // 对方的服务器id
	OperationType  int32                  `protobuf:"varint,6,opt,name=operationType,proto3" json:"operationType,omitempty"`  // 操作信息
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DelGroupPlayerInfo) Reset() {
	*x = DelGroupPlayerInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelGroupPlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelGroupPlayerInfo) ProtoMessage() {}

func (x *DelGroupPlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelGroupPlayerInfo.ProtoReflect.Descriptor instead.
func (*DelGroupPlayerInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{40}
}

func (x *DelGroupPlayerInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *DelGroupPlayerInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *DelGroupPlayerInfo) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *DelGroupPlayerInfo) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *DelGroupPlayerInfo) GetTargetServerId() string {
	if x != nil {
		return x.TargetServerId
	}
	return ""
}

func (x *DelGroupPlayerInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type DelGroupPlayerResult struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Result         int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid         uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`                //我的玩家ID
	ServerId       int32                  `protobuf:"varint,3,opt,name=serverId,proto3" json:"serverId,omitempty"`            //服务器id
	GroupId        uint64                 `protobuf:"varint,4,opt,name=groupId,proto3" json:"groupId,omitempty"`              //群组id
	TargetGuid     uint64                 `protobuf:"varint,5,opt,name=targetGuid,proto3" json:"targetGuid,omitempty"`        //对方的guid
	TargetServerId string                 `protobuf:"bytes,6,opt,name=targetServerId,proto3" json:"targetServerId,omitempty"` // 对方的服务器id
	OperationType  int32                  `protobuf:"varint,7,opt,name=operationType,proto3" json:"operationType,omitempty"`  // 操作信息
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DelGroupPlayerResult) Reset() {
	*x = DelGroupPlayerResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DelGroupPlayerResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DelGroupPlayerResult) ProtoMessage() {}

func (x *DelGroupPlayerResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DelGroupPlayerResult.ProtoReflect.Descriptor instead.
func (*DelGroupPlayerResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{41}
}

func (x *DelGroupPlayerResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *DelGroupPlayerResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *DelGroupPlayerResult) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *DelGroupPlayerResult) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *DelGroupPlayerResult) GetTargetGuid() uint64 {
	if x != nil {
		return x.TargetGuid
	}
	return 0
}

func (x *DelGroupPlayerResult) GetTargetServerId() string {
	if x != nil {
		return x.TargetServerId
	}
	return ""
}

func (x *DelGroupPlayerResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type PlayerInviteJoinGroupInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	ServerId      int32                  `protobuf:"varint,2,opt,name=serverId,proto3" json:"serverId,omitempty"`           //服务器id
	GroupId       uint64                 `protobuf:"varint,3,opt,name=groupId,proto3" json:"groupId,omitempty"`             //群组id
	Res           int32                  `protobuf:"varint,4,opt,name=res,proto3" json:"res,omitempty"`                     // 0 拒绝 1 通过
	OperationType int32                  `protobuf:"varint,5,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerInviteJoinGroupInfo) Reset() {
	*x = PlayerInviteJoinGroupInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerInviteJoinGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerInviteJoinGroupInfo) ProtoMessage() {}

func (x *PlayerInviteJoinGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerInviteJoinGroupInfo.ProtoReflect.Descriptor instead.
func (*PlayerInviteJoinGroupInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{42}
}

func (x *PlayerInviteJoinGroupInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *PlayerInviteJoinGroupInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *PlayerInviteJoinGroupInfo) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *PlayerInviteJoinGroupInfo) GetRes() int32 {
	if x != nil {
		return x.Res
	}
	return 0
}

func (x *PlayerInviteJoinGroupInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type PlayerInviteJoinGroupResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	MyGuid        uint64                 `protobuf:"varint,2,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	ServerId      int32                  `protobuf:"varint,3,opt,name=serverId,proto3" json:"serverId,omitempty"`           //服务器id
	GroupId       uint64                 `protobuf:"varint,4,opt,name=groupId,proto3" json:"groupId,omitempty"`             //群组id
	Res           int32                  `protobuf:"varint,5,opt,name=res,proto3" json:"res,omitempty"`                     // 0 拒绝 1 通过
	OperationType int32                  `protobuf:"varint,6,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayerInviteJoinGroupResult) Reset() {
	*x = PlayerInviteJoinGroupResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayerInviteJoinGroupResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayerInviteJoinGroupResult) ProtoMessage() {}

func (x *PlayerInviteJoinGroupResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayerInviteJoinGroupResult.ProtoReflect.Descriptor instead.
func (*PlayerInviteJoinGroupResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{43}
}

func (x *PlayerInviteJoinGroupResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *PlayerInviteJoinGroupResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *PlayerInviteJoinGroupResult) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *PlayerInviteJoinGroupResult) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *PlayerInviteJoinGroupResult) GetRes() int32 {
	if x != nil {
		return x.Res
	}
	return 0
}

func (x *PlayerInviteJoinGroupResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type GetGroupListInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的玩家ID
	ServerId      int32                  `protobuf:"varint,2,opt,name=serverId,proto3" json:"serverId,omitempty"`           //服务器id
	Type          int32                  `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`                   //1 我的 2 全服的  3 某个
	OperationType int32                  `protobuf:"varint,4,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	GroupId       uint64                 `protobuf:"varint,5,opt,name=groupId,proto3" json:"groupId,omitempty"`             //某个人群组信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroupListInfo) Reset() {
	*x = GetGroupListInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupListInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupListInfo) ProtoMessage() {}

func (x *GetGroupListInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupListInfo.ProtoReflect.Descriptor instead.
func (*GetGroupListInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{44}
}

func (x *GetGroupListInfo) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *GetGroupListInfo) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

func (x *GetGroupListInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *GetGroupListInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

func (x *GetGroupListInfo) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

// Type:Http
type ClientGroupPlayerInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Guid          uint64                 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`
	Type          int32                  `protobuf:"varint,2,opt,name=type,proto3" json:"type,omitempty"` // 1 群主 2 普通
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientGroupPlayerInfo) Reset() {
	*x = ClientGroupPlayerInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientGroupPlayerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientGroupPlayerInfo) ProtoMessage() {}

func (x *ClientGroupPlayerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientGroupPlayerInfo.ProtoReflect.Descriptor instead.
func (*ClientGroupPlayerInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{45}
}

func (x *ClientGroupPlayerInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *ClientGroupPlayerInfo) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

// Type:Http
type ClientGroupBase struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	GroupBase     *RedisGroupBaseInfo      `protobuf:"bytes,1,opt,name=groupBase,proto3" json:"groupBase,omitempty"`
	GroupPlayers  []*ClientGroupPlayerInfo `protobuf:"bytes,2,rep,name=groupPlayers,proto3" json:"groupPlayers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientGroupBase) Reset() {
	*x = ClientGroupBase{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientGroupBase) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientGroupBase) ProtoMessage() {}

func (x *ClientGroupBase) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientGroupBase.ProtoReflect.Descriptor instead.
func (*ClientGroupBase) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{46}
}

func (x *ClientGroupBase) GetGroupBase() *RedisGroupBaseInfo {
	if x != nil {
		return x.GroupBase
	}
	return nil
}

func (x *ClientGroupBase) GetGroupPlayers() []*ClientGroupPlayerInfo {
	if x != nil {
		return x.GroupPlayers
	}
	return nil
}

// Type:Http
type GetGroupListResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	OperationType int32                  `protobuf:"varint,2,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	GroupList     []*ClientGroupBase     `protobuf:"bytes,3,rep,name=groupList,proto3" json:"groupList,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroupListResult) Reset() {
	*x = GetGroupListResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupListResult) ProtoMessage() {}

func (x *GetGroupListResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupListResult.ProtoReflect.Descriptor instead.
func (*GetGroupListResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{47}
}

func (x *GetGroupListResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GetGroupListResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

func (x *GetGroupListResult) GetGroupList() []*ClientGroupBase {
	if x != nil {
		return x.GroupList
	}
	return nil
}

// Type:Http
type GetGroupPlayerListByGroupIdInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GroupId       uint64                 `protobuf:"varint,1,opt,name=groupId,proto3" json:"groupId,omitempty"`             //群组id
	OperationType int32                  `protobuf:"varint,2,opt,name=operationType,proto3" json:"operationType,omitempty"` // 操作信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroupPlayerListByGroupIdInfo) Reset() {
	*x = GetGroupPlayerListByGroupIdInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupPlayerListByGroupIdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupPlayerListByGroupIdInfo) ProtoMessage() {}

func (x *GetGroupPlayerListByGroupIdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupPlayerListByGroupIdInfo.ProtoReflect.Descriptor instead.
func (*GetGroupPlayerListByGroupIdInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{48}
}

func (x *GetGroupPlayerListByGroupIdInfo) GetGroupId() uint64 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GetGroupPlayerListByGroupIdInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

// Type:Http
type GetGroupPlayerListByGroupIdResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	PlayerList    []uint64               `protobuf:"varint,2,rep,packed,name=playerList,proto3" json:"playerList,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroupPlayerListByGroupIdResult) Reset() {
	*x = GetGroupPlayerListByGroupIdResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupPlayerListByGroupIdResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupPlayerListByGroupIdResult) ProtoMessage() {}

func (x *GetGroupPlayerListByGroupIdResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupPlayerListByGroupIdResult.ProtoReflect.Descriptor instead.
func (*GetGroupPlayerListByGroupIdResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{49}
}

func (x *GetGroupPlayerListByGroupIdResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GetGroupPlayerListByGroupIdResult) GetPlayerList() []uint64 {
	if x != nil {
		return x.PlayerList
	}
	return nil
}

// Type:Http
type SetFriendBaseInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Guid          uint64                 `protobuf:"varint,1,opt,name=guid,proto3" json:"guid,omitempty"`
	OperationType int32                  `protobuf:"varint,2,opt,name=operationType,proto3" json:"operationType,omitempty"` // 1 获取， 2 修改
	SetInfo       []int32                `protobuf:"varint,3,rep,packed,name=setInfo,proto3" json:"setInfo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetFriendBaseInfo) Reset() {
	*x = SetFriendBaseInfo{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetFriendBaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetFriendBaseInfo) ProtoMessage() {}

func (x *SetFriendBaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetFriendBaseInfo.ProtoReflect.Descriptor instead.
func (*SetFriendBaseInfo) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{50}
}

func (x *SetFriendBaseInfo) GetGuid() uint64 {
	if x != nil {
		return x.Guid
	}
	return 0
}

func (x *SetFriendBaseInfo) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

func (x *SetFriendBaseInfo) GetSetInfo() []int32 {
	if x != nil {
		return x.SetInfo
	}
	return nil
}

// Type:Http
type SetFriendBaseInfoResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Result        int32                  `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	OperationType int32                  `protobuf:"varint,2,opt,name=operationType,proto3" json:"operationType,omitempty"` // 1 获取， 2 修改
	SetInfo       []int32                `protobuf:"varint,3,rep,packed,name=setInfo,proto3" json:"setInfo,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetFriendBaseInfoResult) Reset() {
	*x = SetFriendBaseInfoResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetFriendBaseInfoResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetFriendBaseInfoResult) ProtoMessage() {}

func (x *SetFriendBaseInfoResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetFriendBaseInfoResult.ProtoReflect.Descriptor instead.
func (*SetFriendBaseInfoResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{51}
}

func (x *SetFriendBaseInfoResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *SetFriendBaseInfoResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

func (x *SetFriendBaseInfoResult) GetSetInfo() []int32 {
	if x != nil {
		return x.SetInfo
	}
	return nil
}

// Type:Http
type GetMyGroupInviteJoinGroupList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的guid
	OperationType int32                  `protobuf:"varint,2,opt,name=operationType,proto3" json:"operationType,omitempty"` //
	Result        int32                  `protobuf:"varint,3,opt,name=result,proto3" json:"result,omitempty"`
	ServerId      int32                  `protobuf:"varint,4,opt,name=serverId,proto3" json:"serverId,omitempty"` //服务器id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMyGroupInviteJoinGroupList) Reset() {
	*x = GetMyGroupInviteJoinGroupList{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMyGroupInviteJoinGroupList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyGroupInviteJoinGroupList) ProtoMessage() {}

func (x *GetMyGroupInviteJoinGroupList) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyGroupInviteJoinGroupList.ProtoReflect.Descriptor instead.
func (*GetMyGroupInviteJoinGroupList) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{52}
}

func (x *GetMyGroupInviteJoinGroupList) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *GetMyGroupInviteJoinGroupList) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

func (x *GetMyGroupInviteJoinGroupList) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GetMyGroupInviteJoinGroupList) GetServerId() int32 {
	if x != nil {
		return x.ServerId
	}
	return 0
}

// Type:Http
type GetMyGroupInviteJoinGroupListResult struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MyGuid        uint64                 `protobuf:"varint,1,opt,name=myGuid,proto3" json:"myGuid,omitempty"`               //我的guid
	OperationType int32                  `protobuf:"varint,2,opt,name=operationType,proto3" json:"operationType,omitempty"` //
	Result        int32                  `protobuf:"varint,3,opt,name=result,proto3" json:"result,omitempty"`
	GroupList     []uint64               `protobuf:"varint,4,rep,packed,name=groupList,proto3" json:"groupList,omitempty"` // 被邀请的群组id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMyGroupInviteJoinGroupListResult) Reset() {
	*x = GetMyGroupInviteJoinGroupListResult{}
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMyGroupInviteJoinGroupListResult) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMyGroupInviteJoinGroupListResult) ProtoMessage() {}

func (x *GetMyGroupInviteJoinGroupListResult) ProtoReflect() protoreflect.Message {
	mi := &file_microservices_friend_v1_friendservice_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMyGroupInviteJoinGroupListResult.ProtoReflect.Descriptor instead.
func (*GetMyGroupInviteJoinGroupListResult) Descriptor() ([]byte, []int) {
	return file_microservices_friend_v1_friendservice_proto_rawDescGZIP(), []int{53}
}

func (x *GetMyGroupInviteJoinGroupListResult) GetMyGuid() uint64 {
	if x != nil {
		return x.MyGuid
	}
	return 0
}

func (x *GetMyGroupInviteJoinGroupListResult) GetOperationType() int32 {
	if x != nil {
		return x.OperationType
	}
	return 0
}

func (x *GetMyGroupInviteJoinGroupListResult) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GetMyGroupInviteJoinGroupListResult) GetGroupList() []uint64 {
	if x != nil {
		return x.GroupList
	}
	return nil
}

var File_microservices_friend_v1_friendservice_proto protoreflect.FileDescriptor

const file_microservices_friend_v1_friendservice_proto_rawDesc = "" +
	"\n" +
	"+microservices/friend/v1/friendservice.proto\x12\x17Aurora.PlayerInfoServer\x1a\x1cgoogle/api/annotations.proto\"\xb3\x01\n" +
	"\x13AddFriendPlayerInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x02 \x01(\x04R\n" +
	"targetGuid\x12\x1a\n" +
	"\bserverId\x18\x03 \x01(\x05R\bserverId\x12\"\n" +
	"\frelationType\x18\x04 \x01(\x05R\frelationType\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"\xc7\x01\n" +
	"\x0fAddFriendResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x03 \x01(\x04R\n" +
	"targetGuid\x12\x1a\n" +
	"\bserverId\x18\x04 \x01(\x05R\bserverId\x12\"\n" +
	"\frelationType\x18\x05 \x01(\x05R\frelationType\x12$\n" +
	"\roperationType\x18\x06 \x01(\x05R\roperationType\"L\n" +
	"\x12AddFriendApplyInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x02 \x01(\x04R\n" +
	"targetGuid\"f\n" +
	"\x14AddFriendApplyResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x03 \x01(\x04R\n" +
	"targetGuid\"g\n" +
	"\x15AddFriendApplyResult2\x12\x16\n" +
	"\x06test01\x18\x01 \x01(\x05R\x06test01\x12\x16\n" +
	"\x06test02\x18\x02 \x01(\x04R\x06test02\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x03 \x01(\x04R\n" +
	"targetGuid\"O\n" +
	"\x15RemoveFriendApplyInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x02 \x01(\x04R\n" +
	"targetGuid\"i\n" +
	"\x17RemoveFriendApplyResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x03 \x01(\x04R\n" +
	"targetGuid\"0\n" +
	"\x16GetFriendApplyListInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\"A\n" +
	"\x0fFriendApplyInfo\x12\x12\n" +
	"\x04guid\x18\x01 \x01(\x04R\x04guid\x12\x1a\n" +
	"\bserverId\x18\x02 \x01(\x05R\bserverId\"\x92\x01\n" +
	"\x18GetFriendApplyListResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12F\n" +
	"\tapplyList\x18\x03 \x03(\v2(.Aurora.PlayerInfoServer.FriendApplyInfoR\tapplyList\"l\n" +
	"\x16ApproveFriendApplyInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x02 \x01(\x04R\n" +
	"targetGuid\x12\x1a\n" +
	"\bserverId\x18\x03 \x01(\x05R\bserverId\"j\n" +
	"\x18ApproveFriendApplyResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x03 \x01(\x04R\n" +
	"targetGuid\"\xb3\x01\n" +
	"\x13DelFriendPlayerInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x02 \x01(\x04R\n" +
	"targetGuid\x12\x1a\n" +
	"\bserverId\x18\x03 \x01(\x05R\bserverId\x12\"\n" +
	"\frelationType\x18\x04 \x01(\x05R\frelationType\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"\xc7\x01\n" +
	"\x0fDelFriendResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x03 \x01(\x04R\n" +
	"targetGuid\x12\x1a\n" +
	"\bserverId\x18\x04 \x01(\x05R\bserverId\x12\"\n" +
	"\frelationType\x18\x05 \x01(\x05R\frelationType\x12$\n" +
	"\roperationType\x18\x06 \x01(\x05R\roperationType\"\xb3\x01\n" +
	"\x13SetFriendPlayerInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x02 \x01(\x04R\n" +
	"targetGuid\x12\x1a\n" +
	"\bserverId\x18\x03 \x01(\x05R\bserverId\x12\"\n" +
	"\frelationType\x18\x04 \x01(\x05R\frelationType\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"\xc7\x01\n" +
	"\x0fSetFriendResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x03 \x01(\x04R\n" +
	"targetGuid\x12\x1a\n" +
	"\bserverId\x18\x04 \x01(\x05R\bserverId\x12\"\n" +
	"\frelationType\x18\x05 \x01(\x05R\frelationType\x12$\n" +
	"\roperationType\x18\x06 \x01(\x05R\roperationType\"\x93\x01\n" +
	"\x17GetFriendPlayerListInfo\x12\x12\n" +
	"\x04guid\x18\x01 \x01(\x04R\x04guid\x12\x1a\n" +
	"\bserverId\x18\x02 \x01(\x05R\bserverId\x12\"\n" +
	"\frelationType\x18\x03 \x01(\x05R\frelationType\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"~\n" +
	"\x10FriendPlayerBase\x12\x12\n" +
	"\x04guid\x18\x01 \x01(\x04R\x04guid\x12\x1a\n" +
	"\bserverId\x18\x02 \x01(\x05R\bserverId\x12 \n" +
	"\vfriendPoint\x18\x03 \x01(\x05R\vfriendPoint\x12\x18\n" +
	"\aremarks\x18\x04 \x01(\tR\aremarks\"E\n" +
	"\x13FriendAttentionBase\x12\x12\n" +
	"\x04guid\x18\x01 \x01(\x04R\x04guid\x12\x1a\n" +
	"\bserverId\x18\x02 \x01(\x05R\bserverId\"u\n" +
	"\x11FriendGroupPlayer\x12\x18\n" +
	"\agroupId\x18\x01 \x01(\x05R\agroupId\x12\x1c\n" +
	"\tgroupName\x18\x02 \x01(\tR\tgroupName\x12(\n" +
	"\x0fgroupPlayerGuid\x18\x03 \x03(\x04R\x0fgroupPlayerGuid\"Y\n" +
	"\x13FriendGroupRelation\x12\"\n" +
	"\frelationType\x18\x01 \x01(\x05R\frelationType\x12\x1e\n" +
	"\n" +
	"playerGuid\x18\x02 \x03(\x04R\n" +
	"playerGuid\"\xaa\x04\n" +
	"\x1dGetFriendPlayerListInfoResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x12\n" +
	"\x04guid\x18\x02 \x01(\x04R\x04guid\x12I\n" +
	"\n" +
	"friendList\x18\x03 \x03(\v2).Aurora.PlayerInfoServer.FriendPlayerBaseR\n" +
	"friendList\x12Z\n" +
	"\x11selfAttentionList\x18\x04 \x03(\v2,.Aurora.PlayerInfoServer.FriendAttentionBaseR\x11selfAttentionList\x12\\\n" +
	"\x12otherAttentionList\x18\x05 \x03(\v2,.Aurora.PlayerInfoServer.FriendAttentionBaseR\x12otherAttentionList\x12T\n" +
	"\x0ffriendGroupList\x18\x06 \x03(\v2*.Aurora.PlayerInfoServer.FriendGroupPlayerR\x0ffriendGroupList\x12\\\n" +
	"\x12friendRelationList\x18\a \x03(\v2,.Aurora.PlayerInfoServer.FriendGroupRelationR\x12friendRelationList\x12$\n" +
	"\roperationType\x18\b \x01(\x05R\roperationType\"s\n" +
	"\x15CreateFriendGroupInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1c\n" +
	"\tgroupName\x18\x02 \x01(\tR\tgroupName\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"\xa7\x01\n" +
	"\x17CreateFriendGroupResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x18\n" +
	"\agroupId\x18\x03 \x01(\x05R\agroupId\x12\x1c\n" +
	"\tgroupName\x18\x04 \x01(\tR\tgroupName\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"\x8a\x01\n" +
	"\x12SetFriendGroupInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x18\n" +
	"\agroupId\x18\x02 \x01(\x05R\agroupId\x12\x1c\n" +
	"\tgroupName\x18\x03 \x01(\tR\tgroupName\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"\xa4\x01\n" +
	"\x14SetFriendGroupResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x18\n" +
	"\agroupId\x18\x03 \x01(\x05R\agroupId\x12\x1c\n" +
	"\tgroupName\x18\x04 \x01(\tR\tgroupName\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"l\n" +
	"\x12DelFriendGroupInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x18\n" +
	"\agroupId\x18\x02 \x01(\x05R\agroupId\x12$\n" +
	"\roperationType\x18\x03 \x01(\x05R\roperationType\"\x86\x01\n" +
	"\x14DelFriendGroupResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x18\n" +
	"\agroupId\x18\x03 \x01(\x05R\agroupId\x12$\n" +
	"\roperationType\x18\x04 \x01(\x05R\roperationType\"\x9a\x01\n" +
	"\x18AddPlayerFriendGroupInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x18\n" +
	"\agroupId\x18\x02 \x01(\x05R\agroupId\x12&\n" +
	"\x0eplayerListGuid\x18\x03 \x03(\x04R\x0eplayerListGuid\x12$\n" +
	"\roperationType\x18\x04 \x01(\x05R\roperationType\"\xb4\x01\n" +
	"\x1aAddPlayerFriendGroupResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x18\n" +
	"\agroupId\x18\x03 \x01(\x05R\agroupId\x12&\n" +
	"\x0eplayerListGuid\x18\x04 \x03(\x04R\x0eplayerListGuid\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"\x8e\x01\n" +
	"\x14SetFriendRemarksInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x02 \x01(\x04R\n" +
	"targetGuid\x12\x18\n" +
	"\aremarks\x18\x03 \x01(\tR\aremarks\x12$\n" +
	"\roperationType\x18\x04 \x01(\x05R\roperationType\"\xa8\x01\n" +
	"\x16SetFriendRemarksResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x03 \x01(\x04R\n" +
	"targetGuid\x12\x18\n" +
	"\aremarks\x18\x04 \x01(\tR\aremarks\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"\x88\x01\n" +
	"\x14SetSelfAttentionInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x02 \x01(\x04R\n" +
	"targetGuid\x12\x12\n" +
	"\x04type\x18\x03 \x01(\x05R\x04type\x12$\n" +
	"\roperationType\x18\x04 \x01(\x05R\roperationType\"\xa2\x01\n" +
	"\x16SetSelfAttentionResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x03 \x01(\x04R\n" +
	"targetGuid\x12\x12\n" +
	"\x04type\x18\x04 \x01(\x05R\x04type\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"\xf5\x01\n" +
	"\rGroupBaseInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1a\n" +
	"\bserverId\x18\x02 \x01(\x05R\bserverId\x12\x18\n" +
	"\agroupId\x18\x03 \x01(\x04R\agroupId\x12\x1c\n" +
	"\tgroupName\x18\x04 \x01(\tR\tgroupName\x12 \n" +
	"\vgroupNotice\x18\x05 \x01(\tR\vgroupNotice\x12\x1c\n" +
	"\tgroupType\x18\x06 \x01(\x05R\tgroupType\x12\x12\n" +
	"\x04type\x18\a \x01(\x05R\x04type\x12$\n" +
	"\roperationType\x18\b \x01(\x05R\roperationType\"\x91\x02\n" +
	"\x11CreateGroupResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1a\n" +
	"\bserverId\x18\x03 \x01(\x05R\bserverId\x12\x18\n" +
	"\agroupId\x18\x04 \x01(\x04R\agroupId\x12\x1c\n" +
	"\tgroupName\x18\x05 \x01(\tR\tgroupName\x12 \n" +
	"\vgroupNotice\x18\x06 \x01(\tR\vgroupNotice\x12\x1c\n" +
	"\tgroupType\x18\a \x01(\x05R\tgroupType\x12\x12\n" +
	"\x04type\x18\b \x01(\x05R\x04type\x12$\n" +
	"\roperationType\x18\t \x01(\x05R\roperationType\"\xa8\x01\n" +
	"\x12RedisGroupBaseInfo\x12\x1a\n" +
	"\bserverId\x18\x01 \x01(\x05R\bserverId\x12\x18\n" +
	"\agroupId\x18\x02 \x01(\x04R\agroupId\x12\x1c\n" +
	"\tgroupName\x18\x03 \x01(\tR\tgroupName\x12 \n" +
	"\vgroupNotice\x18\x04 \x01(\tR\vgroupNotice\x12\x1c\n" +
	"\tgroupType\x18\x05 \x01(\x05R\tgroupType\"\x80\x01\n" +
	"\x12RedisFriendItemKey\x12\x12\n" +
	"\x04guid\x18\x01 \x01(\x04R\x04guid\x12\x1a\n" +
	"\bserverId\x18\x02 \x01(\x05R\bserverId\x12 \n" +
	"\vfriendPoint\x18\x03 \x01(\x05R\vfriendPoint\x12\x18\n" +
	"\aremarks\x18\x04 \x01(\tR\aremarks\"\xe4\x01\n" +
	"\x12ApplyGroupListInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1a\n" +
	"\bserverId\x18\x02 \x01(\x05R\bserverId\x12\x18\n" +
	"\agroupId\x18\x03 \x01(\x04R\agroupId\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x04 \x01(\x04R\n" +
	"targetGuid\x12&\n" +
	"\x0etargetServerId\x18\x05 \x01(\tR\x0etargetServerId\x12\x12\n" +
	"\x04type\x18\x06 \x01(\x05R\x04type\x12$\n" +
	"\roperationType\x18\a \x01(\x05R\roperationType\"\xfe\x01\n" +
	"\x14ApplyGroupListResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1a\n" +
	"\bserverId\x18\x03 \x01(\x05R\bserverId\x12\x18\n" +
	"\agroupId\x18\x04 \x01(\x04R\agroupId\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x05 \x01(\x04R\n" +
	"targetGuid\x12&\n" +
	"\x0etargetServerId\x18\x06 \x01(\tR\x0etargetServerId\x12\x12\n" +
	"\x04type\x18\a \x01(\x05R\x04type\x12$\n" +
	"\roperationType\x18\b \x01(\x05R\roperationType\"\xd0\x01\n" +
	"\x12DelGroupPlayerInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1a\n" +
	"\bserverId\x18\x02 \x01(\x05R\bserverId\x12\x18\n" +
	"\agroupId\x18\x03 \x01(\x04R\agroupId\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x04 \x01(\x04R\n" +
	"targetGuid\x12&\n" +
	"\x0etargetServerId\x18\x05 \x01(\tR\x0etargetServerId\x12$\n" +
	"\roperationType\x18\x06 \x01(\x05R\roperationType\"\xea\x01\n" +
	"\x14DelGroupPlayerResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1a\n" +
	"\bserverId\x18\x03 \x01(\x05R\bserverId\x12\x18\n" +
	"\agroupId\x18\x04 \x01(\x04R\agroupId\x12\x1e\n" +
	"\n" +
	"targetGuid\x18\x05 \x01(\x04R\n" +
	"targetGuid\x12&\n" +
	"\x0etargetServerId\x18\x06 \x01(\tR\x0etargetServerId\x12$\n" +
	"\roperationType\x18\a \x01(\x05R\roperationType\"\xa1\x01\n" +
	"\x19PlayerInviteJoinGroupInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1a\n" +
	"\bserverId\x18\x02 \x01(\x05R\bserverId\x12\x18\n" +
	"\agroupId\x18\x03 \x01(\x04R\agroupId\x12\x10\n" +
	"\x03res\x18\x04 \x01(\x05R\x03res\x12$\n" +
	"\roperationType\x18\x05 \x01(\x05R\roperationType\"\xbb\x01\n" +
	"\x1bPlayerInviteJoinGroupResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x16\n" +
	"\x06myGuid\x18\x02 \x01(\x04R\x06myGuid\x12\x1a\n" +
	"\bserverId\x18\x03 \x01(\x05R\bserverId\x12\x18\n" +
	"\agroupId\x18\x04 \x01(\x04R\agroupId\x12\x10\n" +
	"\x03res\x18\x05 \x01(\x05R\x03res\x12$\n" +
	"\roperationType\x18\x06 \x01(\x05R\roperationType\"\x9a\x01\n" +
	"\x10GetGroupListInfo\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12\x1a\n" +
	"\bserverId\x18\x02 \x01(\x05R\bserverId\x12\x12\n" +
	"\x04type\x18\x03 \x01(\x05R\x04type\x12$\n" +
	"\roperationType\x18\x04 \x01(\x05R\roperationType\x12\x18\n" +
	"\agroupId\x18\x05 \x01(\x04R\agroupId\"?\n" +
	"\x15ClientGroupPlayerInfo\x12\x12\n" +
	"\x04guid\x18\x01 \x01(\x04R\x04guid\x12\x12\n" +
	"\x04type\x18\x02 \x01(\x05R\x04type\"\xb0\x01\n" +
	"\x0fClientGroupBase\x12I\n" +
	"\tgroupBase\x18\x01 \x01(\v2+.Aurora.PlayerInfoServer.RedisGroupBaseInfoR\tgroupBase\x12R\n" +
	"\fgroupPlayers\x18\x02 \x03(\v2..Aurora.PlayerInfoServer.ClientGroupPlayerInfoR\fgroupPlayers\"\x9a\x01\n" +
	"\x12GetGroupListResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12$\n" +
	"\roperationType\x18\x02 \x01(\x05R\roperationType\x12F\n" +
	"\tgroupList\x18\x03 \x03(\v2(.Aurora.PlayerInfoServer.ClientGroupBaseR\tgroupList\"a\n" +
	"\x1fGetGroupPlayerListByGroupIdInfo\x12\x18\n" +
	"\agroupId\x18\x01 \x01(\x04R\agroupId\x12$\n" +
	"\roperationType\x18\x02 \x01(\x05R\roperationType\"[\n" +
	"!GetGroupPlayerListByGroupIdResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12\x1e\n" +
	"\n" +
	"playerList\x18\x02 \x03(\x04R\n" +
	"playerList\"g\n" +
	"\x11SetFriendBaseInfo\x12\x12\n" +
	"\x04guid\x18\x01 \x01(\x04R\x04guid\x12$\n" +
	"\roperationType\x18\x02 \x01(\x05R\roperationType\x12\x18\n" +
	"\asetInfo\x18\x03 \x03(\x05R\asetInfo\"q\n" +
	"\x17SetFriendBaseInfoResult\x12\x16\n" +
	"\x06result\x18\x01 \x01(\x05R\x06result\x12$\n" +
	"\roperationType\x18\x02 \x01(\x05R\roperationType\x12\x18\n" +
	"\asetInfo\x18\x03 \x03(\x05R\asetInfo\"\x91\x01\n" +
	"\x1dGetMyGroupInviteJoinGroupList\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12$\n" +
	"\roperationType\x18\x02 \x01(\x05R\roperationType\x12\x16\n" +
	"\x06result\x18\x03 \x01(\x05R\x06result\x12\x1a\n" +
	"\bserverId\x18\x04 \x01(\x05R\bserverId\"\x99\x01\n" +
	"#GetMyGroupInviteJoinGroupListResult\x12\x16\n" +
	"\x06myGuid\x18\x01 \x01(\x04R\x06myGuid\x12$\n" +
	"\roperationType\x18\x02 \x01(\x05R\roperationType\x12\x16\n" +
	"\x06result\x18\x03 \x01(\x05R\x06result\x12\x1c\n" +
	"\tgroupList\x18\x04 \x03(\x04R\tgroupList*:\n" +
	"\fRelationType\x12\b\n" +
	"\x04node\x10\x00\x12\n" +
	"\n" +
	"\x06friend\x10\x01\x12\t\n" +
	"\x05black\x10\x02\x12\t\n" +
	"\x05enemy\x10\x03*A\n" +
	"\x0eFriendBaseEnum\x12\n" +
	"\n" +
	"\x06remark\x10\x00\x12\x10\n" +
	"\fnotAddFriend\x10\x01\x12\x11\n" +
	"\rrecommendTeam\x10\x022\xb0\x1b\n" +
	"\rFriendservice\x12\xa5\x01\n" +
	"\x0eAddFriendApply\x12+.Aurora.PlayerInfoServer.AddFriendApplyInfo\x1a-.Aurora.PlayerInfoServer.AddFriendApplyResult\"7\x82\xd3\xe4\x93\x021:\x01*\",/gameserver/api/friendservice/addfriendapply\x12\xb5\x01\n" +
	"\x12ApproveFriendApply\x12/.Aurora.PlayerInfoServer.ApproveFriendApplyInfo\x1a1.Aurora.PlayerInfoServer.ApproveFriendApplyResult\";\x82\xd3\xe4\x93\x025:\x01*\"0/gameserver/api/friendservice/approvefriendapply\x12\xb1\x01\n" +
	"\x11RemoveFriendApply\x12..Aurora.PlayerInfoServer.RemoveFriendApplyInfo\x1a0.Aurora.PlayerInfoServer.RemoveFriendApplyResult\":\x82\xd3\xe4\x93\x024:\x01*\"//gameserver/api/friendservice/removefriendapply\x12\xb5\x01\n" +
	"\x12GetFriendApplyList\x12/.Aurora.PlayerInfoServer.GetFriendApplyListInfo\x1a1.Aurora.PlayerInfoServer.GetFriendApplyListResult\";\x82\xd3\xe4\x93\x025:\x01*\"0/gameserver/api/friendservice/getfriendapplylist\x12\xa3\x01\n" +
	"\x0fAddFriendPlayer\x12,.Aurora.PlayerInfoServer.AddFriendPlayerInfo\x1a(.Aurora.PlayerInfoServer.AddFriendResult\"8\x82\xd3\xe4\x93\x022:\x01*\"-/gameserver/api/friendservice/addfriendplayer\x12\xa3\x01\n" +
	"\x0fDelFriendPlayer\x12,.Aurora.PlayerInfoServer.DelFriendPlayerInfo\x1a(.Aurora.PlayerInfoServer.DelFriendResult\"8\x82\xd3\xe4\x93\x022:\x01*\"-/gameserver/api/friendservice/delfriendplayer\x12\xa3\x01\n" +
	"\x0fSetFriendPlayer\x12,.Aurora.PlayerInfoServer.SetFriendPlayerInfo\x1a(.Aurora.PlayerInfoServer.SetFriendResult\"8\x82\xd3\xe4\x93\x022:\x01*\"-/gameserver/api/friendservice/setfriendplayer\x12\xbd\x01\n" +
	"\x13GetFriendPlayerList\x120.Aurora.PlayerInfoServer.GetFriendPlayerListInfo\x1a6.Aurora.PlayerInfoServer.GetFriendPlayerListInfoResult\"<\x82\xd3\xe4\x93\x026:\x01*\"1/gameserver/api/friendservice/getfriendplayerlist\x12w\n" +
	"\x11CreateFriendGroup\x12..Aurora.PlayerInfoServer.CreateFriendGroupInfo\x1a0.Aurora.PlayerInfoServer.CreateFriendGroupResult\"\x00\x12n\n" +
	"\x0eSetFriendGroup\x12+.Aurora.PlayerInfoServer.SetFriendGroupInfo\x1a-.Aurora.PlayerInfoServer.SetFriendGroupResult\"\x00\x12n\n" +
	"\x0eDelFriendGroup\x12+.Aurora.PlayerInfoServer.DelFriendGroupInfo\x1a-.Aurora.PlayerInfoServer.DelFriendGroupResult\"\x00\x12\x80\x01\n" +
	"\x14AddPlayerFriendGroup\x121.Aurora.PlayerInfoServer.AddPlayerFriendGroupInfo\x1a3.Aurora.PlayerInfoServer.AddPlayerFriendGroupResult\"\x00\x12\x80\x01\n" +
	"\x14DelPlayerFriendGroup\x121.Aurora.PlayerInfoServer.AddPlayerFriendGroupInfo\x1a3.Aurora.PlayerInfoServer.AddPlayerFriendGroupResult\"\x00\x12t\n" +
	"\x10SetFriendRemarks\x12-.Aurora.PlayerInfoServer.SetFriendRemarksInfo\x1a/.Aurora.PlayerInfoServer.SetFriendRemarksResult\"\x00\x12t\n" +
	"\x10SetSelfAttention\x12-.Aurora.PlayerInfoServer.SetSelfAttentionInfo\x1a/.Aurora.PlayerInfoServer.SetSelfAttentionResult\"\x00\x12d\n" +
	"\fSetGroupBase\x12&.Aurora.PlayerInfoServer.GroupBaseInfo\x1a*.Aurora.PlayerInfoServer.CreateGroupResult\"\x00\x12n\n" +
	"\x0eApplyJoinGroup\x12+.Aurora.PlayerInfoServer.ApplyGroupListInfo\x1a-.Aurora.PlayerInfoServer.ApplyGroupListResult\"\x00\x12n\n" +
	"\x0eDelGroupPlayer\x12+.Aurora.PlayerInfoServer.DelGroupPlayerInfo\x1a-.Aurora.PlayerInfoServer.DelGroupPlayerResult\"\x00\x12\x83\x01\n" +
	"\x15PlayerInviteJoinGroup\x122.Aurora.PlayerInfoServer.PlayerInviteJoinGroupInfo\x1a4.Aurora.PlayerInfoServer.PlayerInviteJoinGroupResult\"\x00\x12\x9d\x01\n" +
	"\fGetGroupList\x12).Aurora.PlayerInfoServer.GetGroupListInfo\x1a+.Aurora.PlayerInfoServer.GetGroupListResult\"5\x82\xd3\xe4\x93\x02/:\x01*\"*/gameserver/api/friendservice/getgrouplist\x12\xd3\x01\n" +
	"\x1bGetGroupPlayerListByGroupId\x128.Aurora.PlayerInfoServer.GetGroupPlayerListByGroupIdInfo\x1a:.Aurora.PlayerInfoServer.GetGroupPlayerListByGroupIdResult\">\x82\xd3\xe4\x93\x028:\x01*\"3/gameserver/api/friendservice/getgrouplistbygroupid\x12\xb0\x01\n" +
	"\x14SetFriendBaseInfoRep\x12*.Aurora.PlayerInfoServer.SetFriendBaseInfo\x1a0.Aurora.PlayerInfoServer.SetFriendBaseInfoResult\":\x82\xd3\xe4\x93\x024:\x01*\"//gameserver/api/friendservice/setfriendbaseinfo\x12\xe0\x01\n" +
	" GetMyGroupInviteJoinGroupListRep\x126.Aurora.PlayerInfoServer.GetMyGroupInviteJoinGroupList\x1a<.Aurora.PlayerInfoServer.GetMyGroupInviteJoinGroupListResult\"F\x82\xd3\xe4\x93\x02@:\x01*\";/gameserver/api/friendservice/getmygroupinvitejoingrouplistB$Z\"gameserver/api/friendservice/v1;v1b\x06proto3"

var (
	file_microservices_friend_v1_friendservice_proto_rawDescOnce sync.Once
	file_microservices_friend_v1_friendservice_proto_rawDescData []byte
)

func file_microservices_friend_v1_friendservice_proto_rawDescGZIP() []byte {
	file_microservices_friend_v1_friendservice_proto_rawDescOnce.Do(func() {
		file_microservices_friend_v1_friendservice_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_microservices_friend_v1_friendservice_proto_rawDesc), len(file_microservices_friend_v1_friendservice_proto_rawDesc)))
	})
	return file_microservices_friend_v1_friendservice_proto_rawDescData
}

var file_microservices_friend_v1_friendservice_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_microservices_friend_v1_friendservice_proto_msgTypes = make([]protoimpl.MessageInfo, 54)
var file_microservices_friend_v1_friendservice_proto_goTypes = []any{
	(RelationType)(0),                           // 0: Aurora.PlayerInfoServer.RelationType
	(FriendBaseEnum)(0),                         // 1: Aurora.PlayerInfoServer.FriendBaseEnum
	(*AddFriendPlayerInfo)(nil),                 // 2: Aurora.PlayerInfoServer.AddFriendPlayerInfo
	(*AddFriendResult)(nil),                     // 3: Aurora.PlayerInfoServer.AddFriendResult
	(*AddFriendApplyInfo)(nil),                  // 4: Aurora.PlayerInfoServer.AddFriendApplyInfo
	(*AddFriendApplyResult)(nil),                // 5: Aurora.PlayerInfoServer.AddFriendApplyResult
	(*AddFriendApplyResult2)(nil),               // 6: Aurora.PlayerInfoServer.AddFriendApplyResult2
	(*RemoveFriendApplyInfo)(nil),               // 7: Aurora.PlayerInfoServer.RemoveFriendApplyInfo
	(*RemoveFriendApplyResult)(nil),             // 8: Aurora.PlayerInfoServer.RemoveFriendApplyResult
	(*GetFriendApplyListInfo)(nil),              // 9: Aurora.PlayerInfoServer.GetFriendApplyListInfo
	(*FriendApplyInfo)(nil),                     // 10: Aurora.PlayerInfoServer.FriendApplyInfo
	(*GetFriendApplyListResult)(nil),            // 11: Aurora.PlayerInfoServer.GetFriendApplyListResult
	(*ApproveFriendApplyInfo)(nil),              // 12: Aurora.PlayerInfoServer.ApproveFriendApplyInfo
	(*ApproveFriendApplyResult)(nil),            // 13: Aurora.PlayerInfoServer.ApproveFriendApplyResult
	(*DelFriendPlayerInfo)(nil),                 // 14: Aurora.PlayerInfoServer.DelFriendPlayerInfo
	(*DelFriendResult)(nil),                     // 15: Aurora.PlayerInfoServer.DelFriendResult
	(*SetFriendPlayerInfo)(nil),                 // 16: Aurora.PlayerInfoServer.SetFriendPlayerInfo
	(*SetFriendResult)(nil),                     // 17: Aurora.PlayerInfoServer.SetFriendResult
	(*GetFriendPlayerListInfo)(nil),             // 18: Aurora.PlayerInfoServer.GetFriendPlayerListInfo
	(*FriendPlayerBase)(nil),                    // 19: Aurora.PlayerInfoServer.FriendPlayerBase
	(*FriendAttentionBase)(nil),                 // 20: Aurora.PlayerInfoServer.FriendAttentionBase
	(*FriendGroupPlayer)(nil),                   // 21: Aurora.PlayerInfoServer.FriendGroupPlayer
	(*FriendGroupRelation)(nil),                 // 22: Aurora.PlayerInfoServer.FriendGroupRelation
	(*GetFriendPlayerListInfoResult)(nil),       // 23: Aurora.PlayerInfoServer.GetFriendPlayerListInfoResult
	(*CreateFriendGroupInfo)(nil),               // 24: Aurora.PlayerInfoServer.CreateFriendGroupInfo
	(*CreateFriendGroupResult)(nil),             // 25: Aurora.PlayerInfoServer.CreateFriendGroupResult
	(*SetFriendGroupInfo)(nil),                  // 26: Aurora.PlayerInfoServer.SetFriendGroupInfo
	(*SetFriendGroupResult)(nil),                // 27: Aurora.PlayerInfoServer.SetFriendGroupResult
	(*DelFriendGroupInfo)(nil),                  // 28: Aurora.PlayerInfoServer.DelFriendGroupInfo
	(*DelFriendGroupResult)(nil),                // 29: Aurora.PlayerInfoServer.DelFriendGroupResult
	(*AddPlayerFriendGroupInfo)(nil),            // 30: Aurora.PlayerInfoServer.AddPlayerFriendGroupInfo
	(*AddPlayerFriendGroupResult)(nil),          // 31: Aurora.PlayerInfoServer.AddPlayerFriendGroupResult
	(*SetFriendRemarksInfo)(nil),                // 32: Aurora.PlayerInfoServer.SetFriendRemarksInfo
	(*SetFriendRemarksResult)(nil),              // 33: Aurora.PlayerInfoServer.SetFriendRemarksResult
	(*SetSelfAttentionInfo)(nil),                // 34: Aurora.PlayerInfoServer.SetSelfAttentionInfo
	(*SetSelfAttentionResult)(nil),              // 35: Aurora.PlayerInfoServer.SetSelfAttentionResult
	(*GroupBaseInfo)(nil),                       // 36: Aurora.PlayerInfoServer.GroupBaseInfo
	(*CreateGroupResult)(nil),                   // 37: Aurora.PlayerInfoServer.CreateGroupResult
	(*RedisGroupBaseInfo)(nil),                  // 38: Aurora.PlayerInfoServer.RedisGroupBaseInfo
	(*RedisFriendItemKey)(nil),                  // 39: Aurora.PlayerInfoServer.RedisFriendItemKey
	(*ApplyGroupListInfo)(nil),                  // 40: Aurora.PlayerInfoServer.ApplyGroupListInfo
	(*ApplyGroupListResult)(nil),                // 41: Aurora.PlayerInfoServer.ApplyGroupListResult
	(*DelGroupPlayerInfo)(nil),                  // 42: Aurora.PlayerInfoServer.DelGroupPlayerInfo
	(*DelGroupPlayerResult)(nil),                // 43: Aurora.PlayerInfoServer.DelGroupPlayerResult
	(*PlayerInviteJoinGroupInfo)(nil),           // 44: Aurora.PlayerInfoServer.PlayerInviteJoinGroupInfo
	(*PlayerInviteJoinGroupResult)(nil),         // 45: Aurora.PlayerInfoServer.PlayerInviteJoinGroupResult
	(*GetGroupListInfo)(nil),                    // 46: Aurora.PlayerInfoServer.GetGroupListInfo
	(*ClientGroupPlayerInfo)(nil),               // 47: Aurora.PlayerInfoServer.ClientGroupPlayerInfo
	(*ClientGroupBase)(nil),                     // 48: Aurora.PlayerInfoServer.ClientGroupBase
	(*GetGroupListResult)(nil),                  // 49: Aurora.PlayerInfoServer.GetGroupListResult
	(*GetGroupPlayerListByGroupIdInfo)(nil),     // 50: Aurora.PlayerInfoServer.GetGroupPlayerListByGroupIdInfo
	(*GetGroupPlayerListByGroupIdResult)(nil),   // 51: Aurora.PlayerInfoServer.GetGroupPlayerListByGroupIdResult
	(*SetFriendBaseInfo)(nil),                   // 52: Aurora.PlayerInfoServer.SetFriendBaseInfo
	(*SetFriendBaseInfoResult)(nil),             // 53: Aurora.PlayerInfoServer.SetFriendBaseInfoResult
	(*GetMyGroupInviteJoinGroupList)(nil),       // 54: Aurora.PlayerInfoServer.GetMyGroupInviteJoinGroupList
	(*GetMyGroupInviteJoinGroupListResult)(nil), // 55: Aurora.PlayerInfoServer.GetMyGroupInviteJoinGroupListResult
}
var file_microservices_friend_v1_friendservice_proto_depIdxs = []int32{
	10, // 0: Aurora.PlayerInfoServer.GetFriendApplyListResult.applyList:type_name -> Aurora.PlayerInfoServer.FriendApplyInfo
	19, // 1: Aurora.PlayerInfoServer.GetFriendPlayerListInfoResult.friendList:type_name -> Aurora.PlayerInfoServer.FriendPlayerBase
	20, // 2: Aurora.PlayerInfoServer.GetFriendPlayerListInfoResult.selfAttentionList:type_name -> Aurora.PlayerInfoServer.FriendAttentionBase
	20, // 3: Aurora.PlayerInfoServer.GetFriendPlayerListInfoResult.otherAttentionList:type_name -> Aurora.PlayerInfoServer.FriendAttentionBase
	21, // 4: Aurora.PlayerInfoServer.GetFriendPlayerListInfoResult.friendGroupList:type_name -> Aurora.PlayerInfoServer.FriendGroupPlayer
	22, // 5: Aurora.PlayerInfoServer.GetFriendPlayerListInfoResult.friendRelationList:type_name -> Aurora.PlayerInfoServer.FriendGroupRelation
	38, // 6: Aurora.PlayerInfoServer.ClientGroupBase.groupBase:type_name -> Aurora.PlayerInfoServer.RedisGroupBaseInfo
	47, // 7: Aurora.PlayerInfoServer.ClientGroupBase.groupPlayers:type_name -> Aurora.PlayerInfoServer.ClientGroupPlayerInfo
	48, // 8: Aurora.PlayerInfoServer.GetGroupListResult.groupList:type_name -> Aurora.PlayerInfoServer.ClientGroupBase
	4,  // 9: Aurora.PlayerInfoServer.Friendservice.AddFriendApply:input_type -> Aurora.PlayerInfoServer.AddFriendApplyInfo
	12, // 10: Aurora.PlayerInfoServer.Friendservice.ApproveFriendApply:input_type -> Aurora.PlayerInfoServer.ApproveFriendApplyInfo
	7,  // 11: Aurora.PlayerInfoServer.Friendservice.RemoveFriendApply:input_type -> Aurora.PlayerInfoServer.RemoveFriendApplyInfo
	9,  // 12: Aurora.PlayerInfoServer.Friendservice.GetFriendApplyList:input_type -> Aurora.PlayerInfoServer.GetFriendApplyListInfo
	2,  // 13: Aurora.PlayerInfoServer.Friendservice.AddFriendPlayer:input_type -> Aurora.PlayerInfoServer.AddFriendPlayerInfo
	14, // 14: Aurora.PlayerInfoServer.Friendservice.DelFriendPlayer:input_type -> Aurora.PlayerInfoServer.DelFriendPlayerInfo
	16, // 15: Aurora.PlayerInfoServer.Friendservice.SetFriendPlayer:input_type -> Aurora.PlayerInfoServer.SetFriendPlayerInfo
	18, // 16: Aurora.PlayerInfoServer.Friendservice.GetFriendPlayerList:input_type -> Aurora.PlayerInfoServer.GetFriendPlayerListInfo
	24, // 17: Aurora.PlayerInfoServer.Friendservice.CreateFriendGroup:input_type -> Aurora.PlayerInfoServer.CreateFriendGroupInfo
	26, // 18: Aurora.PlayerInfoServer.Friendservice.SetFriendGroup:input_type -> Aurora.PlayerInfoServer.SetFriendGroupInfo
	28, // 19: Aurora.PlayerInfoServer.Friendservice.DelFriendGroup:input_type -> Aurora.PlayerInfoServer.DelFriendGroupInfo
	30, // 20: Aurora.PlayerInfoServer.Friendservice.AddPlayerFriendGroup:input_type -> Aurora.PlayerInfoServer.AddPlayerFriendGroupInfo
	30, // 21: Aurora.PlayerInfoServer.Friendservice.DelPlayerFriendGroup:input_type -> Aurora.PlayerInfoServer.AddPlayerFriendGroupInfo
	32, // 22: Aurora.PlayerInfoServer.Friendservice.SetFriendRemarks:input_type -> Aurora.PlayerInfoServer.SetFriendRemarksInfo
	34, // 23: Aurora.PlayerInfoServer.Friendservice.SetSelfAttention:input_type -> Aurora.PlayerInfoServer.SetSelfAttentionInfo
	36, // 24: Aurora.PlayerInfoServer.Friendservice.SetGroupBase:input_type -> Aurora.PlayerInfoServer.GroupBaseInfo
	40, // 25: Aurora.PlayerInfoServer.Friendservice.ApplyJoinGroup:input_type -> Aurora.PlayerInfoServer.ApplyGroupListInfo
	42, // 26: Aurora.PlayerInfoServer.Friendservice.DelGroupPlayer:input_type -> Aurora.PlayerInfoServer.DelGroupPlayerInfo
	44, // 27: Aurora.PlayerInfoServer.Friendservice.PlayerInviteJoinGroup:input_type -> Aurora.PlayerInfoServer.PlayerInviteJoinGroupInfo
	46, // 28: Aurora.PlayerInfoServer.Friendservice.GetGroupList:input_type -> Aurora.PlayerInfoServer.GetGroupListInfo
	50, // 29: Aurora.PlayerInfoServer.Friendservice.GetGroupPlayerListByGroupId:input_type -> Aurora.PlayerInfoServer.GetGroupPlayerListByGroupIdInfo
	52, // 30: Aurora.PlayerInfoServer.Friendservice.SetFriendBaseInfoRep:input_type -> Aurora.PlayerInfoServer.SetFriendBaseInfo
	54, // 31: Aurora.PlayerInfoServer.Friendservice.GetMyGroupInviteJoinGroupListRep:input_type -> Aurora.PlayerInfoServer.GetMyGroupInviteJoinGroupList
	5,  // 32: Aurora.PlayerInfoServer.Friendservice.AddFriendApply:output_type -> Aurora.PlayerInfoServer.AddFriendApplyResult
	13, // 33: Aurora.PlayerInfoServer.Friendservice.ApproveFriendApply:output_type -> Aurora.PlayerInfoServer.ApproveFriendApplyResult
	8,  // 34: Aurora.PlayerInfoServer.Friendservice.RemoveFriendApply:output_type -> Aurora.PlayerInfoServer.RemoveFriendApplyResult
	11, // 35: Aurora.PlayerInfoServer.Friendservice.GetFriendApplyList:output_type -> Aurora.PlayerInfoServer.GetFriendApplyListResult
	3,  // 36: Aurora.PlayerInfoServer.Friendservice.AddFriendPlayer:output_type -> Aurora.PlayerInfoServer.AddFriendResult
	15, // 37: Aurora.PlayerInfoServer.Friendservice.DelFriendPlayer:output_type -> Aurora.PlayerInfoServer.DelFriendResult
	17, // 38: Aurora.PlayerInfoServer.Friendservice.SetFriendPlayer:output_type -> Aurora.PlayerInfoServer.SetFriendResult
	23, // 39: Aurora.PlayerInfoServer.Friendservice.GetFriendPlayerList:output_type -> Aurora.PlayerInfoServer.GetFriendPlayerListInfoResult
	25, // 40: Aurora.PlayerInfoServer.Friendservice.CreateFriendGroup:output_type -> Aurora.PlayerInfoServer.CreateFriendGroupResult
	27, // 41: Aurora.PlayerInfoServer.Friendservice.SetFriendGroup:output_type -> Aurora.PlayerInfoServer.SetFriendGroupResult
	29, // 42: Aurora.PlayerInfoServer.Friendservice.DelFriendGroup:output_type -> Aurora.PlayerInfoServer.DelFriendGroupResult
	31, // 43: Aurora.PlayerInfoServer.Friendservice.AddPlayerFriendGroup:output_type -> Aurora.PlayerInfoServer.AddPlayerFriendGroupResult
	31, // 44: Aurora.PlayerInfoServer.Friendservice.DelPlayerFriendGroup:output_type -> Aurora.PlayerInfoServer.AddPlayerFriendGroupResult
	33, // 45: Aurora.PlayerInfoServer.Friendservice.SetFriendRemarks:output_type -> Aurora.PlayerInfoServer.SetFriendRemarksResult
	35, // 46: Aurora.PlayerInfoServer.Friendservice.SetSelfAttention:output_type -> Aurora.PlayerInfoServer.SetSelfAttentionResult
	37, // 47: Aurora.PlayerInfoServer.Friendservice.SetGroupBase:output_type -> Aurora.PlayerInfoServer.CreateGroupResult
	41, // 48: Aurora.PlayerInfoServer.Friendservice.ApplyJoinGroup:output_type -> Aurora.PlayerInfoServer.ApplyGroupListResult
	43, // 49: Aurora.PlayerInfoServer.Friendservice.DelGroupPlayer:output_type -> Aurora.PlayerInfoServer.DelGroupPlayerResult
	45, // 50: Aurora.PlayerInfoServer.Friendservice.PlayerInviteJoinGroup:output_type -> Aurora.PlayerInfoServer.PlayerInviteJoinGroupResult
	49, // 51: Aurora.PlayerInfoServer.Friendservice.GetGroupList:output_type -> Aurora.PlayerInfoServer.GetGroupListResult
	51, // 52: Aurora.PlayerInfoServer.Friendservice.GetGroupPlayerListByGroupId:output_type -> Aurora.PlayerInfoServer.GetGroupPlayerListByGroupIdResult
	53, // 53: Aurora.PlayerInfoServer.Friendservice.SetFriendBaseInfoRep:output_type -> Aurora.PlayerInfoServer.SetFriendBaseInfoResult
	55, // 54: Aurora.PlayerInfoServer.Friendservice.GetMyGroupInviteJoinGroupListRep:output_type -> Aurora.PlayerInfoServer.GetMyGroupInviteJoinGroupListResult
	32, // [32:55] is the sub-list for method output_type
	9,  // [9:32] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_microservices_friend_v1_friendservice_proto_init() }
func file_microservices_friend_v1_friendservice_proto_init() {
	if File_microservices_friend_v1_friendservice_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_microservices_friend_v1_friendservice_proto_rawDesc), len(file_microservices_friend_v1_friendservice_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   54,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_microservices_friend_v1_friendservice_proto_goTypes,
		DependencyIndexes: file_microservices_friend_v1_friendservice_proto_depIdxs,
		EnumInfos:         file_microservices_friend_v1_friendservice_proto_enumTypes,
		MessageInfos:      file_microservices_friend_v1_friendservice_proto_msgTypes,
	}.Build()
	File_microservices_friend_v1_friendservice_proto = out.File
	file_microservices_friend_v1_friendservice_proto_goTypes = nil
	file_microservices_friend_v1_friendservice_proto_depIdxs = nil
}
