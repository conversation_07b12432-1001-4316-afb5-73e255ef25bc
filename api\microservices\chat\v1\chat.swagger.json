{"swagger": "2.0", "info": {"title": "microservices/chat/v1/chat.proto", "version": "version not set"}, "tags": [{"name": "ChatService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {"PlayerInfoServerChatChannelType": {"type": "string", "enum": ["ChatType_Invalid", "ChatType_Message", "ChatType_BigEmoji", "ChatType_RedEnvelope", "ChatType_Voice"], "default": "ChatType_Invalid", "title": "Type:ClientWebSocket\nType:ServerWebSocket"}, "PlayerInfoServerChatMessage": {"type": "object", "properties": {"timeStamp": {"type": "string", "format": "int64"}, "msgId": {"type": "integer", "format": "int32"}, "zoneWorldId": {"type": "integer", "format": "int32"}, "senderGuid": {"type": "string", "format": "uint64"}, "senderPlayerInfo": {"$ref": "#/definitions/PlayerInfoServerPlayerBaseInfo"}, "messageType": {"$ref": "#/definitions/PlayerInfoServerMessageChannelType"}, "messageSubType": {"$ref": "#/definitions/PlayerInfoServerMessageSubType"}, "targetChatServer": {"$ref": "#/definitions/PlayerInfoServerTargetChatServer"}, "channelType": {"$ref": "#/definitions/PlayerInfoServerChatChannelType"}, "paramId1": {"type": "string", "format": "uint64"}, "paramId2": {"type": "string", "format": "int64"}, "message": {"type": "string"}, "itemList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerChatShareItem"}}, "headFrameId": {"type": "integer", "format": "int32"}, "headIconId": {"type": "string"}}, "title": "Type:ClientWebSocket\nType:ServerWebSocket\nWebSocketMessageIDWebSc.ChatMessage_ID"}, "PlayerInfoServerChatShareItem": {"type": "object", "properties": {"shareType": {"$ref": "#/definitions/PlayerInfoServerChatShareItemType"}, "guid": {"type": "string", "format": "uint64"}, "info": {"type": "string", "format": "byte"}}, "title": "Type:ClientWebSocket\nType:ServerWebSocket"}, "PlayerInfoServerChatShareItemType": {"type": "string", "enum": ["ShareType_Item", "ShareType_Pet", "ShareType_Mount", "ShareType_TagFriend", "ShareType_Coordinate", "ShareType_FormatClientStr", "ShareType_InviteGuild", "ShareType_TeamUp"], "default": "ShareType_Item", "title": "Type:ClientWebSocket\nType:ServerWebSocket"}, "PlayerInfoServerDispatchOperNoticeReply": {"type": "object", "properties": {"Result": {"type": "integer", "format": "int32"}, "OperType": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerMessageChannelType": {"type": "string", "enum": ["MessageType_Invalid", "MessageType_World", "MessageType_Team", "MessageType_Raid", "MessageType_Guild", "MessageType_Near", "MessageType_Speaker", "MessageType_PrivateChat", "MessageType_GroupChat", "MessageType_Recruit", "MessageType_System", "MessageType_Scene", "MessageType_GongGao"], "default": "MessageType_Invalid", "description": "- MessageType_World: LocalServer,ZoneServer\n - MessageType_Team: GlobalServer\n - MessageType_Raid: GlobalServer\n - MessageType_Guild: LocalServer\n - MessageType_Near: GlobalServer\n - MessageType_Speaker: LocalServer,ZoneServer\n - MessageType_PrivateChat: GlobalServer\n - MessageType_GroupChat: GlobalServer\n - MessageType_Recruit: LocalServer,ZoneServer\n - MessageType_System: LocalServer,ZoneServer,GlobalServer\n - MessageType_Scene: GlobalServer\n - MessageType_GongGao: LocalServer,ZoneServer,GlobalServer", "title": "Type:ClientWebSocket\nType:ServerWebSocket"}, "PlayerInfoServerMessageSubType": {"type": "string", "enum": ["MessageSubType_Invalid", "MessageSubType_Broadcast", "MessageSubType_Private", "MessageSubType_Team", "MessageSubType_Guild", "MessageSubType_Near", "MessageSubType_Scene", "MessageSubType_Broadcast_Save"], "default": "MessageSubType_Invalid", "title": "Type:ClientWebSocket\nType:ServerWebSocket"}, "PlayerInfoServerPetFlairInfo_PISct": {"type": "object", "properties": {"AllAddValue": {"type": "integer", "format": "int32"}, "BaseValue": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPetGeniusInfo_PISct": {"type": "object", "properties": {"Amend": {"type": "number", "format": "float"}, "ID": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPetInfoAttr_PISct": {"type": "object", "properties": {"BasicAttrList": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}, "CombatModelID": {"type": "integer", "format": "int32"}, "EvolveLevel": {"type": "integer", "format": "int32"}, "FightPoint": {"type": "integer", "format": "int32"}, "Flair": {"type": "object", "additionalProperties": {"$ref": "#/definitions/PlayerInfoServerPetFlairInfo_PISct"}}, "GeniusCount": {"type": "integer", "format": "int32"}, "GeniusList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerPetGeniusInfo_PISct"}}, "GUID": {"type": "string", "format": "uint64"}, "GWTHRate": {"type": "number", "format": "float"}, "IsFightPet": {"type": "integer", "format": "int32"}, "IsLocked": {"type": "boolean"}, "Level": {"type": "integer", "format": "int32"}, "LevelEXP": {"type": "string", "format": "int64"}, "Name": {"type": "string"}, "PetID": {"type": "integer", "format": "int32"}, "Quality": {"type": "integer", "format": "int32"}, "SkillBookList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerPetSkillBookInfo_PISct"}}, "StarLevel": {"type": "integer", "format": "int32"}, "XDValueAGI": {"type": "integer", "format": "int32"}, "XDValueCON": {"type": "integer", "format": "int32"}, "XDValueINT": {"type": "integer", "format": "int32"}, "XDValueSTR": {"type": "integer", "format": "int32"}, "XianDan": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPetSkillBookInfo_PISct": {"type": "object", "properties": {"Level": {"type": "integer", "format": "int32"}, "SkillBookID": {"type": "integer", "format": "int32"}, "Slot": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPlayerBaseInfo": {"type": "object", "properties": {"FightPoint": {"type": "string", "format": "int64"}, "HenshinPetId": {"type": "integer", "format": "int32"}, "Level": {"type": "integer", "format": "int32"}, "Name": {"type": "string"}, "SceneId": {"type": "integer", "format": "int32"}, "Sex": {"type": "integer", "format": "int32"}, "UnitType": {"type": "integer", "format": "int32"}, "ForbiddenLoginEndTime": {"type": "string", "format": "uint64"}, "Guid": {"type": "string", "format": "uint64"}, "ServerNum": {"type": "integer", "format": "int64"}, "ZoneWorldID": {"type": "integer", "format": "int64"}, "HenshinPet": {"$ref": "#/definitions/PlayerInfoServerPetInfoAttr_PISct"}, "PetID": {"type": "integer", "format": "int32"}, "SelfTeamIndexId": {"type": "integer", "format": "int32"}, "TeamId": {"type": "string", "format": "uint64"}, "TeamJobTitle": {"type": "integer", "format": "int32"}, "TeamMemCount": {"type": "integer", "format": "int32"}, "TeamPlayerCount": {"type": "integer", "format": "int32"}, "TeamType": {"type": "integer", "format": "int32"}, "GuildGuid": {"type": "string", "format": "uint64"}, "GuildName": {"type": "string"}, "HeadIcon": {"type": "integer", "format": "int32"}, "HeadFrame": {"type": "integer", "format": "int32"}, "IsOnline": {"type": "boolean"}, "LastLoginTime": {"type": "string", "format": "int64"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerSendChatMessageReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}, "title": "The response message containing the message"}, "PlayerInfoServerTargetChatServer": {"type": "string", "enum": ["TargetServer_Invalid", "TargetServer_LocalServer", "TargerServer_ZoneServer", "TargetServer_GlobalServer", "TargetServer_Proxy"], "default": "TargetServer_Invalid", "description": "- TargetServer_LocalServer: 本服\n - TargerServer_ZoneServer: 区服\n - TargetServer_GlobalServer: 跨服", "title": "Type:ClientWebSocket\nType:ServerWebSocket"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}