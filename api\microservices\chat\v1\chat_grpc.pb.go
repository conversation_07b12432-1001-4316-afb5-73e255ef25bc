//下面这行很重要，是表明你这个Service用于哪个URL的，见游戏服的GrpcAddressType
//GrpcAddressType:ChatLocalServer
//GrpcAddressType:ChatZoneServer
//GrpcAddressType:ChatGlobalServer
//GrpcServerType:all

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.19.1
// source: microservices/chat/v1/chat.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ChatService_SendChatMessage_FullMethodName    = "/Aurora.PlayerInfoServer.ChatService/SendChatMessage"
	ChatService_BrocastChatMessage_FullMethodName = "/Aurora.PlayerInfoServer.ChatService/BrocastChatMessage"
	ChatService_OperNoticeProxy_FullMethodName    = "/Aurora.PlayerInfoServer.ChatService/OperNoticeProxy"
)

// ChatServiceClient is the client API for ChatService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ServiceStart
type ChatServiceClient interface {
	SendChatMessage(ctx context.Context, in *SendChatMessageRequest, opts ...grpc.CallOption) (*SendChatMessageReply, error)
	BrocastChatMessage(ctx context.Context, in *G2G_ChatMessage, opts ...grpc.CallOption) (*SendChatMessageReply, error)
	OperNoticeProxy(ctx context.Context, in *DispatchOperNoticeInfo, opts ...grpc.CallOption) (*DispatchOperNoticeReply, error)
}

type chatServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewChatServiceClient(cc grpc.ClientConnInterface) ChatServiceClient {
	return &chatServiceClient{cc}
}

func (c *chatServiceClient) SendChatMessage(ctx context.Context, in *SendChatMessageRequest, opts ...grpc.CallOption) (*SendChatMessageReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendChatMessageReply)
	err := c.cc.Invoke(ctx, ChatService_SendChatMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatServiceClient) BrocastChatMessage(ctx context.Context, in *G2G_ChatMessage, opts ...grpc.CallOption) (*SendChatMessageReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendChatMessageReply)
	err := c.cc.Invoke(ctx, ChatService_BrocastChatMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chatServiceClient) OperNoticeProxy(ctx context.Context, in *DispatchOperNoticeInfo, opts ...grpc.CallOption) (*DispatchOperNoticeReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DispatchOperNoticeReply)
	err := c.cc.Invoke(ctx, ChatService_OperNoticeProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChatServiceServer is the server API for ChatService service.
// All implementations must embed UnimplementedChatServiceServer
// for forward compatibility.
//
// ServiceStart
type ChatServiceServer interface {
	SendChatMessage(context.Context, *SendChatMessageRequest) (*SendChatMessageReply, error)
	BrocastChatMessage(context.Context, *G2G_ChatMessage) (*SendChatMessageReply, error)
	OperNoticeProxy(context.Context, *DispatchOperNoticeInfo) (*DispatchOperNoticeReply, error)
	mustEmbedUnimplementedChatServiceServer()
}

// UnimplementedChatServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedChatServiceServer struct{}

func (UnimplementedChatServiceServer) SendChatMessage(context.Context, *SendChatMessageRequest) (*SendChatMessageReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendChatMessage not implemented")
}
func (UnimplementedChatServiceServer) BrocastChatMessage(context.Context, *G2G_ChatMessage) (*SendChatMessageReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BrocastChatMessage not implemented")
}
func (UnimplementedChatServiceServer) OperNoticeProxy(context.Context, *DispatchOperNoticeInfo) (*DispatchOperNoticeReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OperNoticeProxy not implemented")
}
func (UnimplementedChatServiceServer) mustEmbedUnimplementedChatServiceServer() {}
func (UnimplementedChatServiceServer) testEmbeddedByValue()                     {}

// UnsafeChatServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChatServiceServer will
// result in compilation errors.
type UnsafeChatServiceServer interface {
	mustEmbedUnimplementedChatServiceServer()
}

func RegisterChatServiceServer(s grpc.ServiceRegistrar, srv ChatServiceServer) {
	// If the following call pancis, it indicates UnimplementedChatServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ChatService_ServiceDesc, srv)
}

func _ChatService_SendChatMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendChatMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatServiceServer).SendChatMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChatService_SendChatMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatServiceServer).SendChatMessage(ctx, req.(*SendChatMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatService_BrocastChatMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(G2G_ChatMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatServiceServer).BrocastChatMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChatService_BrocastChatMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatServiceServer).BrocastChatMessage(ctx, req.(*G2G_ChatMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChatService_OperNoticeProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DispatchOperNoticeInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatServiceServer).OperNoticeProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChatService_OperNoticeProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatServiceServer).OperNoticeProxy(ctx, req.(*DispatchOperNoticeInfo))
	}
	return interceptor(ctx, in, info, handler)
}

// ChatService_ServiceDesc is the grpc.ServiceDesc for ChatService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ChatService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "Aurora.PlayerInfoServer.ChatService",
	HandlerType: (*ChatServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendChatMessage",
			Handler:    _ChatService_SendChatMessage_Handler,
		},
		{
			MethodName: "BrocastChatMessage",
			Handler:    _ChatService_BrocastChatMessage_Handler,
		},
		{
			MethodName: "OperNoticeProxy",
			Handler:    _ChatService_OperNoticeProxy_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "microservices/chat/v1/chat.proto",
}
