{"swagger": "2.0", "info": {"title": "general-services/announcement/v1/announcement.proto", "version": "version not set"}, "tags": [{"name": "Announcement"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/announcement/api/announcement/addannouncement": {"post": {"operationId": "Announcement_AddAnnouncementItemData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1GetAnnouncementListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1NoticeItem"}}], "tags": ["Announcement"]}}, "/announcement/api/announcement/delannouncement/{noticeid}": {"get": {"operationId": "Announcement_DelAnnouncementItemData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1GetAnnouncementListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "noticeid", "in": "path", "required": true, "type": "string"}], "tags": ["Announcement"]}}, "/announcement/api/announcement/getannouncement": {"get": {"operationId": "Announcement_GetAnnouncementListData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1GetAnnouncementListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "noticeid", "in": "query", "required": false, "type": "string"}], "tags": ["Announcement"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "v1GetAnnouncementListReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "autoOpen": {"type": "integer", "format": "int32"}, "noticelistdata": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1NoticeItem"}}}, "title": "Type:Http"}, "v1NoticeItem": {"type": "object", "properties": {"NoticeId": {"type": "string"}, "Title": {"type": "string"}, "Priority": {"type": "string"}, "Context1": {"type": "string"}, "Context2": {"type": "string"}, "Context3": {"type": "string"}, "CreateTime": {"type": "string", "format": "int64"}, "LastTime": {"type": "string", "format": "int64"}, "AutoOpen": {"type": "integer", "format": "int32"}, "BgUrl": {"type": "string"}}, "title": "Type:Http"}}}