{"swagger": "2.0", "info": {"title": "general-services/serverlist/v1/serverlist.proto", "version": "version not set"}, "tags": [{"name": "Serverlist"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/serverlist/api/serverlist/getiswhitelist/{addrip}/{serverid}": {"get": {"operationId": "Serverlist_GetIsWhitelist", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1GetIsWhitelistReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "addrip", "in": "path", "required": true, "type": "string"}, {"name": "serverid", "in": "path", "required": true, "type": "string"}], "tags": ["Serverlist"]}}, "/serverlist/api/serverlist/getserverlist/{targetPlatformId}": {"get": {"operationId": "Serverlist_GetServerListData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1GetServerListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "targetPlatformId", "in": "path", "required": true, "type": "string"}], "tags": ["Serverlist"]}}, "/serverlist/api/serverlist/setserverinfo": {"post": {"operationId": "Serverlist_SetServerInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1KratosServerInfo"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/v1KratosServerInfo"}}], "tags": ["Serverlist"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "v1GetIsWhitelistReply": {"type": "object", "properties": {"whiteresult": {"type": "integer", "format": "int32"}, "serverstate": {"type": "integer", "format": "int32"}}}, "v1GetServerListReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "serverlistdata": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1ServerListData"}}}}, "v1KratosServerInfo": {"type": "object", "properties": {"ServerPlayerCount": {"type": "string"}, "ServerStatus": {"type": "string"}, "ServerId": {"type": "integer", "format": "int32"}}}, "v1ServerListData": {"type": "object", "properties": {"Ip": {"type": "string", "description": "登录服IP地址（兼容旧字段，实际对应loginIp）", "title": "string BoNo = 1;\n string Expand = 2;"}, "Port": {"type": "integer", "format": "int32", "title": "登录服端口（兼容旧字段，实际对应loginPort）"}, "Recommend": {"type": "integer", "format": "int32"}, "ServerId": {"type": "integer", "format": "int32"}, "ServerName": {"type": "string"}, "Status": {"type": "integer", "format": "int32", "description": "服务器状态：1=流畅，2=拥挤，3=爆满，4=未上线，5=维护", "title": "string ServerSep = 8;"}, "ServerBigId": {"type": "integer", "format": "int32"}, "ServerInfo": {"$ref": "#/definitions/v1KratosServerInfo", "title": "string TargetPlatformId = 11;\n string MaxPlayerCount = 12;\n string LoginNewPlayer = 13;"}, "ServerOpenTime": {"type": "string", "format": "int64", "description": "开服时间（Unix时间戳）", "title": "string GameServerIp = 15;     // 游戏服IP地址\n string GameServerPort = 16;   // 游戏服端口"}, "IsNew": {"type": "integer", "format": "int32", "title": "是否新服"}}}}}