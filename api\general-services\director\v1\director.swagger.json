{"swagger": "2.0", "info": {"title": "general-services/director/v1/director.proto", "version": "version not set"}, "tags": [{"name": "Director"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/director/getdirector/{platform}/{channel}/{version}": {"get": {"operationId": "Director_GetDirectorData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1GetDirectorReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "platform", "in": "path", "required": true, "type": "string"}, {"name": "channel", "in": "path", "required": true, "type": "string"}, {"name": "version", "in": "path", "required": true, "type": "string"}], "tags": ["Director"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "v1DirectorData": {"type": "object", "properties": {"serverListUrl": {"type": "string"}, "announceInfoUrl": {"type": "string"}, "apUrl": {"type": "string"}, "resUrl": {"type": "string"}, "resVersionMd5": {"type": "string"}, "serverVersion": {"type": "string"}}}, "v1GetDirectorReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/v1DirectorData"}}}}}