[{"ID": 1, "Rank": 1084624, "RankRewards": [973101], "RankTopType": 1, "ActivityId": 40001}, {"ID": 2, "Rank": 1084625, "RankRewards": [973102], "RankTopType": 2, "ActivityId": 40001}, {"ID": 3, "Rank": 1084626, "RankRewards": [973103], "RankTopType": 3, "ActivityId": 40001}, {"ID": 4, "Rank": 1084627, "RankRewards": [973104], "RankTopType": 4, "ActivityId": 40001}, {"ID": 5, "Rank": 1084628, "RankRewards": [973105], "RankTopType": 4, "ActivityId": 40001}, {"ID": 6, "Rank": 1084629, "RankRewards": [973106], "RankTopType": 5, "ActivityId": 40001}, {"ID": 7, "Rank": 1084630, "RankRewards": [973107], "RankTopType": 5, "ActivityId": 40001}, {"ID": 8, "Rank": 1084631, "RankRewards": [973108], "RankTopType": 5, "ActivityId": 40001}, {"ID": 9, "Rank": 1084632, "RankRewards": [973109], "RankTopType": 5, "ActivityId": 40001}, {"ID": 10, "Rank": 1084633, "RankRewards": [973110], "RankTopType": 6, "ActivityId": 40001}, {"ID": 11, "Rank": 1084624, "RankRewards": [973131], "RankTopType": 1, "ActivityId": 40002}, {"ID": 12, "Rank": 1084625, "RankRewards": [973132], "RankTopType": 2, "ActivityId": 40002}, {"ID": 13, "Rank": 1084626, "RankRewards": [973133], "RankTopType": 3, "ActivityId": 40002}, {"ID": 14, "Rank": 1084627, "RankRewards": [973134], "RankTopType": 4, "ActivityId": 40002}, {"ID": 15, "Rank": 1084628, "RankRewards": [973135], "RankTopType": 4, "ActivityId": 40002}, {"ID": 16, "Rank": 1084629, "RankRewards": [973136], "RankTopType": 5, "ActivityId": 40002}, {"ID": 17, "Rank": 1084630, "RankRewards": [973137], "RankTopType": 5, "ActivityId": 40002}, {"ID": 18, "Rank": 1084631, "RankRewards": [973138], "RankTopType": 5, "ActivityId": 40002}, {"ID": 19, "Rank": 1084632, "RankRewards": [973139], "RankTopType": 5, "ActivityId": 40002}, {"ID": 20, "Rank": 1084633, "RankRewards": [973140], "RankTopType": 6, "ActivityId": 40002}, {"ID": 21, "Rank": 1084624, "RankRewards": [973151], "RankTopType": 1, "ActivityId": 40003}, {"ID": 22, "Rank": 1084625, "RankRewards": [973152], "RankTopType": 2, "ActivityId": 40003}, {"ID": 23, "Rank": 1084626, "RankRewards": [973153], "RankTopType": 3, "ActivityId": 40003}, {"ID": 24, "Rank": 1084627, "RankRewards": [973154], "RankTopType": 4, "ActivityId": 40003}, {"ID": 25, "Rank": 1084628, "RankRewards": [973155], "RankTopType": 4, "ActivityId": 40003}, {"ID": 26, "Rank": 1084629, "RankRewards": [973156], "RankTopType": 5, "ActivityId": 40003}, {"ID": 27, "Rank": 1084630, "RankRewards": [973157], "RankTopType": 5, "ActivityId": 40003}, {"ID": 28, "Rank": 1084631, "RankRewards": [973158], "RankTopType": 5, "ActivityId": 40003}, {"ID": 29, "Rank": 1084632, "RankRewards": [973159], "RankTopType": 5, "ActivityId": 40003}, {"ID": 30, "Rank": 1084633, "RankRewards": [973160], "RankTopType": 6, "ActivityId": 40003}, {"ID": 31, "Rank": 1084624, "RankRewards": [973171], "RankTopType": 1, "ActivityId": 40004}, {"ID": 32, "Rank": 1084625, "RankRewards": [973172], "RankTopType": 2, "ActivityId": 40004}, {"ID": 33, "Rank": 1084626, "RankRewards": [973173], "RankTopType": 3, "ActivityId": 40004}, {"ID": 34, "Rank": 1084627, "RankRewards": [973174], "RankTopType": 4, "ActivityId": 40004}, {"ID": 35, "Rank": 1084628, "RankRewards": [973175], "RankTopType": 4, "ActivityId": 40004}, {"ID": 36, "Rank": 1084629, "RankRewards": [973176], "RankTopType": 5, "ActivityId": 40004}, {"ID": 37, "Rank": 1084630, "RankRewards": [973177], "RankTopType": 5, "ActivityId": 40004}, {"ID": 38, "Rank": 1084631, "RankRewards": [973178], "RankTopType": 5, "ActivityId": 40004}, {"ID": 39, "Rank": 1084632, "RankRewards": [973179], "RankTopType": 5, "ActivityId": 40004}, {"ID": 40, "Rank": 1084633, "RankRewards": [973180], "RankTopType": 6, "ActivityId": 40004}]