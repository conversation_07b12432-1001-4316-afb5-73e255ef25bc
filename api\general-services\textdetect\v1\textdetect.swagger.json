{"swagger": "2.0", "info": {"title": "general-services/textdetect/v1/textdetect.proto", "version": "version not set"}, "tags": [{"name": "TextDetect"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {}, "definitions": {"PlayerInfoServerDetectType": {"type": "string", "enum": ["DetectType_Normal", "DetectType_JinYan"], "default": "DetectType_Normal", "description": "- DetectType_Normal: 正常\n - DetectType_JinYan: 禁言", "title": "Type:Http"}, "PlayerInfoServerDoTextDetectReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "textDetectedItems": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerTextDetectedItem"}}}}, "PlayerInfoServerHadesResult": {"type": "object", "properties": {"Status": {"type": "boolean"}, "Id": {"type": "string", "format": "uint64"}, "Json": {"$ref": "#/definitions/PlayerInfoServerHadesTextDetected"}}}, "PlayerInfoServerHadesTextDetected": {"type": "object", "properties": {"Result": {"type": "string", "format": "uint64"}, "RetMsg": {"type": "string"}, "TextID": {"type": "string"}, "EvilType": {"type": "string", "format": "uint64"}}}, "PlayerInfoServerTextDetectItem": {"type": "object", "properties": {"index": {"type": "string", "format": "uint64"}, "zoneWorldId": {"type": "integer", "format": "int32"}, "guid": {"type": "string", "format": "uint64"}, "detectType": {"$ref": "#/definitions/PlayerInfoServerDetectType"}, "text": {"type": "string"}}}, "PlayerInfoServerTextDetectedItem": {"type": "object", "properties": {"index": {"type": "string", "format": "uint64"}, "hadesResult": {"$ref": "#/definitions/PlayerInfoServerHadesResult"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}