{"swagger": "2.0", "info": {"title": "microservices/rank/v1/rank.proto", "version": "version not set"}, "tags": [{"name": "RankService"}, {"name": "RankListHttp"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/rankservice/ranklist": {"post": {"operationId": "RankListHttp_GetRankListData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerGetRankListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerGetRankListReq"}}], "tags": ["RankListHttp"]}}}, "definitions": {"PlayerInfoServerGetRankListReply": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "rankGroup": {"type": "integer", "format": "int32"}, "rankType": {"type": "integer", "format": "int32"}, "rankFlag": {"type": "integer", "format": "int32"}, "rankPage": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerRankDataInfo"}}, "selfInfo": {"$ref": "#/definitions/PlayerInfoServerRankDataInfo"}}, "title": "Type:Http"}, "PlayerInfoServerGetRankListReq": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "rankGroup": {"type": "integer", "format": "int32", "title": "排行榜大类 0：角色 1：公会 2：队伍 3:单个幻灵"}, "rankType": {"type": "integer", "format": "int32", "title": "排行榜类型"}, "rankFlag": {"type": "integer", "format": "int32", "title": "三级页签 0：总榜 1：好友"}, "rankPage": {"type": "integer", "format": "int32", "title": "分页 0：全部  （总共100行&每页50行）"}}, "title": "Type:Http"}, "PlayerInfoServerRankDataInfo": {"type": "object", "properties": {"role": {"$ref": "#/definitions/PlayerInfoServerRoleRankInfo"}, "rankValue": {"type": "string", "format": "int64"}, "rankValue2": {"type": "string", "format": "int64"}}, "title": "Type:Http"}, "PlayerInfoServerRankRoleInfo": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "kserver": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "level": {"type": "integer", "format": "int32"}, "fightPoint": {"type": "string", "format": "int64"}}}, "PlayerInfoServerRankValueInfo": {"type": "object", "properties": {"rankType": {"type": "integer", "format": "int32"}, "rankValue": {"type": "string", "format": "int64"}}}, "PlayerInfoServerRemoveInfo": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "kserver": {"type": "integer", "format": "int32"}}, "title": "Type:Inner"}, "PlayerInfoServerRemoveRoleReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "PlayerInfoServerRoleRankInfo": {"type": "object", "properties": {"uid": {"type": "string", "format": "uint64"}, "name": {"type": "string"}, "level": {"type": "integer", "format": "int32"}, "fightPoint": {"type": "string", "format": "int64"}, "rank": {"type": "integer", "format": "int32"}, "tilte": {"type": "integer", "format": "int32"}}, "title": "Type:Http"}, "PlayerInfoServerSyncRoleInfoReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}, "title": "The response message containing the message"}, "PlayerInfoServerUpdateRoleRankReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}