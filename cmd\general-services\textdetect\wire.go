//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"liteframe/internal/general-services/textdetect/conf"
	"liteframe/internal/general-services/textdetect/registry"
	"liteframe/internal/general-services/textdetect/server"
	"liteframe/internal/general-services/textdetect/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

//// 添加提供者函数
//func provideRegistrar(client *v3.Client) registry.Registrar {
//	return etcd.New(client)
//}

// wireApp init textdetect application.
func wireApp(*conf.YiDunConfig, *conf.BadWords, *conf.Server, *conf.Registry, log.Logger, map[string]string) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, service.ProviderSet, registry.ProviderSet, newApp))
}
