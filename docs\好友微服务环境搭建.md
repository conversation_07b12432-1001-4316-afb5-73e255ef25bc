�ο��ĵ�:../Kratos΢����֮һ--�ܹ��ͻ���.md
1 ��װ Go ����{
	(ע��:Ŀ¼�в�Ҫ���ո�)1.1 ��װ:go1.20.4.windows-amd64.msi �� C:\ProgramFiles\Go\
	1.2 ���û�������{
		GOPATH:%USERPROFILE%\go(�Զ������)(�Ժ���Ҫ����Դ�����ص���Ŀ¼)
	}
	1.2 �鿴go�汾��:������������:go version
	1.3 �鿴go��������:������������:go env{
		���ù��ڴ���,���򹤾߰����ز���{
			������������:go env -w GO111MODULE=on
			������������:go env -w GOPROXY=https://goproxy.cn,direct
		}
	}
	/*1.4 ��д:hello.go �ļ�{
		package main
	
		import "fmt"
		 
		func main() {
			fmt.Println("Hello, World!")
		}
	}
	1.5 ����:go run hello.go*/
}
2 ���� protoc.exe{
	(ע��:Ŀ¼�в�Ҫ���ո�)1���� protoc.exe �� C:\ProgramFiles\Go\bin\ Ŀ¼��
}
3 ��װ bash �ն�(������ windows ������ִ�� make ����){
	(ע��:Ŀ¼�в�Ҫ���ո�)1��װ:Git\Git-2.41.0-64-bit.exe �� C:\ProgramFiles\Git{
		��ѡ:Additional icons\On the Desktop
	}
	��ѹ:Git\make-4.4.1-without-guile-w32-bin.zip �� make-4.4.1-without-guile-w32-bin,����:make-4.4.1-without-guile-w32-bin\Ŀ¼�µ�����������:C:\ProgramFiles\Git\mingw64\ Ŀ¼��
}
4 ��װ goland{
	(ע��:Ŀ¼�в�Ҫ���ո�)1��װ:goland\goland-2023.3.6.exe �� C:\ProgramFiles\JetBrains\GoLand 2023.3.6{
		��ѡ:GoLand
		��ѡ:���"bin"�ļ��е�PATH
		��ѡ:.go
	}
	��������"GoLand 2023.3.6"��ݷ�ʽ->(��һ������)ѡ��:Do not import settings->OK->Start Free 30-Day Trial->�ر�
	��ѹgoland\�����-winϵͳ.zip ����ǰ�ļ��еõ� .\goland\�����-winϵͳ\ Ŀ¼,˫�� .\goland\�����-winϵͳ\GoLand����.vbs �������
	��һ����Ŀ:��������"GoLand 2023.3.6"��ݷ�ʽ->Open->ѡ��һ����Ŀ
	����{
		��������"GoLand 2023.3.6"��ݷ�ʽ->Files �˵�-Settings...->Plugins->������������:chinese->ѡ��:Chinese(Simplified)Languange Pack/�������԰�->Install->Restart IDE->Restart
	}
	�ն��趨{
		��������"GoLand 2023.3.6"��ݷ�ʽ->�ļ� �˵�->����->����->�ն�->Shell·��:C:\ProgramFiles\Git\bin\bash.exe
	}
}
��Ŀ����趨(�Ժ���΢����Ϊ��){
	GOROOT�趨{
		��������"GoLand 2023.3.6"��ݷ�ʽ->�ļ� �˵�->����->Go->GOROOT->���SDK...->����...->ѡ��:C:\ProgramFiles\Go
	}
	Go�����趨{ 
		��������"GoLand 2023.3.6"��ݷ�ʽ->�Ϸ��м�ƫ�Ҵ���"����/��������"�����˵�->�༭����...->Go����->ѡ��һ��(û�����Լ����һ��){
			�����·��(E):{
				Ӧ��ָ����main.go�ļ����ڵ�Ŀ¼(CardFrame\Server\kratos\kratos\gameserver\app\friendservice\cmd\friendservice),������д����Ϊ:�����б�ʾ��Ŀ��Ŀ¼(����:CardFrame\Server\kratos)�Ĳ����滻��"kratos"(����:kratos/app/friendservice/cmd/friendservice)
			}
			����Ŀ¼(W){
				Ӧ����ָ"Makefile"�ļ����ڵ�Ŀ¼(����:CardFrame\Server\kratos\kratos\gameserver)
			}
			����ʵ��(P){
				�����ļ�Ŀ¼(����:CardFrame\Server\kratos\kratos\gameserver/app/friendservice/configs)��Ե��ǹ���Ŀ¼,������д����Ϊ:�����б�ʾ����Ŀ¼(����:CardFrame\Server\kratos\kratos\gameserver)�Ĳ����滻��"."(����:-conf .\app\friendservice\configs)
			}	
		}
	}
	��Ŀ����{
		��������"GoLand 2023.3.6"��ݷ�ʽ->���½��ն�{
			ע��:һ��Ҫ������ǰ�Ĺ���Ŀ¼(����:CardFrame\Server\kratos\kratos\gameserver){
				cd CardFrame\Server\kratos\kratos\gameserver
			}
			��ʼ������:make init(ִֻ��һ�ξͿ���)
			����ָ��{
				make api
				make proto
				make wire
				make build
			}
		}
	}
	��Ŀ����{
		CardFrame\Server\kratos\kratos\gameserver\app\friendservice\configs\config.yaml
	}
	��Ŀ����{
		��������"GoLand 2023.3.6"��ݷ�ʽ->�Ϸ��м�ƫ�Ҵ���"����"��"����"��ť
	}
��Ϸ������{
	��Ϸ�������ļ���ַ��CardFrame\Server\liteframe\bin\etc\gameserver.yaml �е� friend_service�ֶ�
	�ں���΢������������£���Ϸ������ʹ�ú���ģ��ӿڣ�CardFrame\Server\liteframe\servers\gameserver\internal\logic\player\module\friends\friends.go�����к�������߼�����
}
}