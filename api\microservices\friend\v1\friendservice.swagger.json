{"swagger": "2.0", "info": {"title": "microservices/friend/v1/friendservice.proto", "version": "version not set"}, "tags": [{"name": "Friendservice"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/gameserver/api/friendservice/addfriendapply": {"post": {"operationId": "Friendservice_AddFriendApply", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerAddFriendApplyResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerAddFriendApplyInfo"}}], "tags": ["Friendservice"]}}, "/gameserver/api/friendservice/addfriendplayer": {"post": {"operationId": "Friendservice_AddFriendPlayer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerAddFriendResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerAddFriendPlayerInfo"}}], "tags": ["Friendservice"]}}, "/gameserver/api/friendservice/approvefriendapply": {"post": {"operationId": "Friendservice_ApproveFriendApply", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerApproveFriendApplyResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerApproveFriendApplyInfo"}}], "tags": ["Friendservice"]}}, "/gameserver/api/friendservice/delfriendplayer": {"post": {"operationId": "Friendservice_DelFriendPlayer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerDelFriendResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerDelFriendPlayerInfo"}}], "tags": ["Friendservice"]}}, "/gameserver/api/friendservice/getfriendapplylist": {"post": {"operationId": "Friendservice_GetFriendApplyList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerGetFriendApplyListResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerGetFriendApplyListInfo"}}], "tags": ["Friendservice"]}}, "/gameserver/api/friendservice/getfriendplayerlist": {"post": {"operationId": "Friendservice_GetFriendPlayerList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerGetFriendPlayerListInfoResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerGetFriendPlayerListInfo"}}], "tags": ["Friendservice"]}}, "/gameserver/api/friendservice/getgrouplist": {"post": {"operationId": "Friendservice_GetGroupList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerGetGroupListResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerGetGroupListInfo"}}], "tags": ["Friendservice"]}}, "/gameserver/api/friendservice/getgrouplistbygroupid": {"post": {"operationId": "Friendservice_GetGroupPlayerListByGroupId", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerGetGroupPlayerListByGroupIdResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerGetGroupPlayerListByGroupIdInfo"}}], "tags": ["Friendservice"]}}, "/gameserver/api/friendservice/getmygroupinvitejoingrouplist": {"post": {"operationId": "Friendservice_GetMyGroupInviteJoinGroupListRep", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerGetMyGroupInviteJoinGroupListResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerGetMyGroupInviteJoinGroupList"}}], "tags": ["Friendservice"]}}, "/gameserver/api/friendservice/removefriendapply": {"post": {"operationId": "Friendservice_RemoveFriendApply", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerRemoveFriendApplyResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerRemoveFriendApplyInfo"}}], "tags": ["Friendservice"]}}, "/gameserver/api/friendservice/setfriendbaseinfo": {"post": {"operationId": "Friendservice_SetFriendBaseInfoRep", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerSetFriendBaseInfoResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerSetFriendBaseInfo"}}], "tags": ["Friendservice"]}}, "/gameserver/api/friendservice/setfriendplayer": {"post": {"operationId": "Friendservice_SetFriendPlayer", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerSetFriendResult"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerSetFriendPlayerInfo"}}], "tags": ["Friendservice"]}}}, "definitions": {"PlayerInfoServerAddFriendApplyInfo": {"type": "object", "properties": {"myGuid": {"type": "string", "format": "uint64"}, "targetGuid": {"type": "string", "format": "uint64"}}, "title": "Type:Http"}, "PlayerInfoServerAddFriendApplyResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64"}, "targetGuid": {"type": "string", "format": "uint64"}}, "title": "Type:Http"}, "PlayerInfoServerAddFriendPlayerInfo": {"type": "object", "properties": {"myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "targetGuid": {"type": "string", "format": "uint64", "title": "目标玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "玩家所在服ID"}, "relationType": {"type": "integer", "format": "int32", "title": "1:好友 2:黑名单 3:仇人"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerAddFriendResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "targetGuid": {"type": "string", "format": "uint64", "title": "目标玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "玩家所在服ID"}, "relationType": {"type": "integer", "format": "int32", "title": "1:好友 2:黑名单 3:仇人"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerAddPlayerFriendGroupResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "groupId": {"type": "integer", "format": "int32", "title": "分组id"}, "playerListGuid": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "guid list"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerApplyGroupListResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "服务器id"}, "groupId": {"type": "string", "format": "uint64", "title": "群组id"}, "targetGuid": {"type": "string", "format": "uint64", "title": "对方的guid"}, "targetServerId": {"type": "string", "title": "对方的服务器id"}, "type": {"type": "integer", "format": "int32", "title": "类型 1 邀请"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerApproveFriendApplyInfo": {"type": "object", "properties": {"myGuid": {"type": "string", "format": "uint64"}, "targetGuid": {"type": "string", "format": "uint64", "title": "为0表示一键同意 不为0表示单个同意"}, "serverId": {"type": "integer", "format": "int32"}}, "title": "Type:Http"}, "PlayerInfoServerApproveFriendApplyResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64"}, "targetGuid": {"type": "string", "format": "uint64"}}, "title": "Type:Http"}, "PlayerInfoServerClientGroupBase": {"type": "object", "properties": {"groupBase": {"$ref": "#/definitions/PlayerInfoServerRedisGroupBaseInfo"}, "groupPlayers": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerClientGroupPlayerInfo"}}}, "title": "Type:Http"}, "PlayerInfoServerClientGroupPlayerInfo": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64"}, "type": {"type": "integer", "format": "int32", "title": "1 群主 2 普通"}}, "title": "Type:Http"}, "PlayerInfoServerCreateFriendGroupResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "groupId": {"type": "integer", "format": "int32", "title": "分组id"}, "groupName": {"type": "string", "title": "分组名称"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerCreateGroupResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "服务器id"}, "groupId": {"type": "string", "format": "uint64", "title": "群组id"}, "groupName": {"type": "string", "title": "群组名称"}, "groupNotice": {"type": "string", "title": "群组通知"}, "groupType": {"type": "integer", "format": "int32", "title": "群组类型"}, "type": {"type": "integer", "format": "int32", "title": "类型 1 加 2 减 3 修改"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerDelFriendGroupResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "groupId": {"type": "integer", "format": "int32", "title": "分组id"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerDelFriendPlayerInfo": {"type": "object", "properties": {"myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "targetGuid": {"type": "string", "format": "uint64", "title": "目标玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "玩家所在服ID"}, "relationType": {"type": "integer", "format": "int32", "title": "1:好友 2:黑名单 3:仇人"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerDelFriendResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "targetGuid": {"type": "string", "format": "uint64", "title": "目标玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "玩家所在服ID"}, "relationType": {"type": "integer", "format": "int32", "title": "1:好友 2:黑名单 3:仇人"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerDelGroupPlayerResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "服务器id"}, "groupId": {"type": "string", "format": "uint64", "title": "群组id"}, "targetGuid": {"type": "string", "format": "uint64", "title": "对方的guid"}, "targetServerId": {"type": "string", "title": "对方的服务器id"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerFriendApplyInfo": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64"}, "serverId": {"type": "integer", "format": "int32"}}, "title": "Type:Http"}, "PlayerInfoServerFriendAttentionBase": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64", "title": "guid"}, "serverId": {"type": "integer", "format": "int32", "title": "好友的服务器id"}}, "title": "Type:Http"}, "PlayerInfoServerFriendGroupPlayer": {"type": "object", "properties": {"groupId": {"type": "integer", "format": "int32", "title": "组id"}, "groupName": {"type": "string", "title": "分组名称"}, "groupPlayerGuid": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "分组玩家的gui"}}, "title": "Type:Http"}, "PlayerInfoServerFriendGroupRelation": {"type": "object", "properties": {"relationType": {"type": "integer", "format": "int32", "title": "关系类型"}, "playerGuid": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "玩家的gui"}}, "title": "Type:Http"}, "PlayerInfoServerFriendPlayerBase": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64", "title": "guid"}, "serverId": {"type": "integer", "format": "int32", "title": "好友的服务器id"}, "friendPoint": {"type": "integer", "format": "int32", "title": "好友度"}, "remarks": {"type": "string", "title": "好友备注"}}, "title": "Type:Http"}, "PlayerInfoServerGetFriendApplyListInfo": {"type": "object", "properties": {"myGuid": {"type": "string", "format": "uint64"}}, "title": "Type:Http"}, "PlayerInfoServerGetFriendApplyListResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64"}, "applyList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerFriendApplyInfo"}}}, "title": "Type:Http"}, "PlayerInfoServerGetFriendPlayerListInfo": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64", "title": "玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "玩家所在服ID"}, "relationType": {"type": "integer", "format": "int32", "title": "0 全部  1:好友 2:黑名单 3:仇人"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息  0 全部， 1 对应关系联系人， 2 我的关注列表， 3 关注我的列表， 4 我的分组列表， 5 好友关系组  6 第一次登录 尝试给关注我的好友发信息"}}, "title": "Type:Http"}, "PlayerInfoServerGetFriendPlayerListInfoResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "guid": {"type": "string", "format": "uint64", "title": "请求人的guid"}, "friendList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerFriendPlayerBase"}, "title": "好友列表"}, "selfAttentionList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerFriendAttentionBase"}, "title": "我关注列表"}, "otherAttentionList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerFriendAttentionBase"}, "title": "关注我的列表"}, "friendGroupList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerFriendGroupPlayer"}, "title": "好友分组列表"}, "friendRelationList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerFriendGroupRelation"}, "title": "关系组"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerGetGroupListInfo": {"type": "object", "properties": {"myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "服务器id"}, "type": {"type": "integer", "format": "int32", "title": "1 我的 2 全服的  3 某个"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}, "groupId": {"type": "string", "format": "uint64", "title": "某个人群组信息"}}, "title": "Type:Http"}, "PlayerInfoServerGetGroupListResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}, "groupList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerClientGroupBase"}}}, "title": "Type:Http"}, "PlayerInfoServerGetGroupPlayerListByGroupIdInfo": {"type": "object", "properties": {"groupId": {"type": "string", "format": "uint64", "title": "群组id"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerGetGroupPlayerListByGroupIdResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "playerList": {"type": "array", "items": {"type": "string", "format": "uint64"}}}, "title": "Type:Http"}, "PlayerInfoServerGetMyGroupInviteJoinGroupList": {"type": "object", "properties": {"myGuid": {"type": "string", "format": "uint64", "title": "我的guid"}, "operationType": {"type": "integer", "format": "int32"}, "result": {"type": "integer", "format": "int32"}, "serverId": {"type": "integer", "format": "int32", "title": "服务器id"}}, "title": "Type:Http"}, "PlayerInfoServerGetMyGroupInviteJoinGroupListResult": {"type": "object", "properties": {"myGuid": {"type": "string", "format": "uint64", "title": "我的guid"}, "operationType": {"type": "integer", "format": "int32"}, "result": {"type": "integer", "format": "int32"}, "groupList": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "被邀请的群组id"}}, "title": "Type:Http"}, "PlayerInfoServerPlayerInviteJoinGroupResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "服务器id"}, "groupId": {"type": "string", "format": "uint64", "title": "群组id"}, "res": {"type": "integer", "format": "int32", "title": "0 拒绝 1 通过"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerRedisGroupBaseInfo": {"type": "object", "properties": {"serverId": {"type": "integer", "format": "int32", "title": "服务器id"}, "groupId": {"type": "string", "format": "uint64", "title": "群组id"}, "groupName": {"type": "string", "title": "群组名称"}, "groupNotice": {"type": "string", "title": "群组通知"}, "groupType": {"type": "integer", "format": "int32", "title": "群组类型"}}, "title": "Type:Http"}, "PlayerInfoServerRemoveFriendApplyInfo": {"type": "object", "properties": {"myGuid": {"type": "string", "format": "uint64"}, "targetGuid": {"type": "string", "format": "uint64", "title": "为0表示一键拒绝 不为0表示单个拒绝"}}, "title": "Type:Http"}, "PlayerInfoServerRemoveFriendApplyResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64"}, "targetGuid": {"type": "string", "format": "uint64"}}, "title": "Type:Http"}, "PlayerInfoServerSetFriendBaseInfo": {"type": "object", "properties": {"guid": {"type": "string", "format": "uint64"}, "operationType": {"type": "integer", "format": "int32", "title": "1 获取， 2 修改"}, "setInfo": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "title": "Type:Http"}, "PlayerInfoServerSetFriendBaseInfoResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "operationType": {"type": "integer", "format": "int32", "title": "1 获取， 2 修改"}, "setInfo": {"type": "array", "items": {"type": "integer", "format": "int32"}}}, "title": "Type:Http"}, "PlayerInfoServerSetFriendGroupResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "groupId": {"type": "integer", "format": "int32", "title": "分组id"}, "groupName": {"type": "string", "title": "分组名称"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerSetFriendPlayerInfo": {"type": "object", "properties": {"myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "targetGuid": {"type": "string", "format": "uint64", "title": "目标玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "玩家所在服ID"}, "relationType": {"type": "integer", "format": "int32", "title": "1:好友 2:黑名单 3:仇人"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerSetFriendRemarksResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "targetGuid": {"type": "string", "format": "uint64", "title": "目标玩家ID"}, "remarks": {"type": "string", "title": "好友备注"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerSetFriendResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "targetGuid": {"type": "string", "format": "uint64", "title": "目标玩家ID"}, "serverId": {"type": "integer", "format": "int32", "title": "玩家所在服ID"}, "relationType": {"type": "integer", "format": "int32", "title": "1:好友 2:黑名单 3:仇人"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "PlayerInfoServerSetSelfAttentionResult": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "myGuid": {"type": "string", "format": "uint64", "title": "我的玩家ID"}, "targetGuid": {"type": "string", "format": "uint64", "title": "目标玩家ID"}, "type": {"type": "integer", "format": "int32", "title": "类型 1 加 2 减"}, "operationType": {"type": "integer", "format": "int32", "title": "操作信息"}}, "title": "Type:Http"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}