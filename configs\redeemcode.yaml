server:
  grpc:
    addr: 0.0.0.0:9015
    timeout: 25s
  http:
    addr: 0.0.0.0:8015
    timeout: 25s

data:
  redis:
    addr: 10.1.8.64:6379
    passwd: ""
    read_timeout: 10s
    write_timeout: 10s
  log:
    level: -1       # 日志级别: -1=DEBUG, 0=INFO, 1=WARN, 2=ERROR, 3=DPanic, 4=Panic, 5=Fatal
    console: true   # 是否输出到控制台

redeemCodeGroup:
  redeemCodeItemDic:
    "11111":
        codeId: "11111" # 兑换码Id
        codeType: 1 # 兑换码类型
        ValidityStartTime: 1731272400 # 兑换码生效开始时间
        expirationTime: 1731704400 # 兑换码截止时间
        dropPackID: 3086 # 掉落包Id
    "122222":
        codeId: "122222" # 兑换码Id
        codeType: 1 # 兑换码类型
        ValidityStartTime: 1731272400 # 兑换码生效开始时间
        expirationTime: 1731704400 # 兑换码截止时间
        dropPackID: 3087

etcdconfig:
  isOpen: 1                      # 控制是否使用etcd，0=不使用，1=使用
  endpoints:
    - "10.1.8.64:2379"
  dial_timeout: 5s
  username: ""
  password: ""
  prefix: "/redeemcode"
  keys:
    - key: "/config"
      watchName: "redeemCodeGroup"
      serverType: 15

registry:
  etcd:
    endpoints: ["10.1.8.64:2379"]
    username: ""
    password: ""
    timeout: 5
    prefix: "/lisirui"