server:
  http:
    addr: 0.0.0.0:8013
    timeout: 25
  grpc:
    addr: 0.0.0.0:9011
    timeout: 25
data:
  redis:
    addr: 127.0.0.1:6379
    passwd: ""
    read_timeout: 10
    write_timeout: 10
log:
  level: -1       # 日志级别: -1=DEBUG, 0=INFO, 1=WARN, 2=ERROR, 3=DPanic, 4=Panic, 5=Fatal
  console: true   # 是否输出到控制台

friendconfig:
  selfattentionmax: 100 #我都关注列表上限
  friendgroupmax: 3 #最大分组
  groupmax: 3 #自定义群组 大小
  groupplayermax: 3 #自定义群组 人数上限
  friendrelationmax: 3 #关系类型
  friendmax: 30 #好友数量上限
  friendapplymax: 30 #好友申请列表上限
  friendapplyday: 3 #好友每日申请上限
  friendgroupplayermax: 30 #每个分组人数上限
  isTextDetect: 0 #是否屏蔽字檢查
  relationtypitemlist: #每个关系的人数上限
    - relationType : 1 #好友关系类型 1=好友，2=黑名单，3=陌生人
      relationCount : 30

    - relationType: 2
      relationCount: 30

    - relationType: 3
      relationCount: 66

registry:
  etcd:
    endpoints:
      - "10.1.8.64:2379"
#      - "127.0.0.1:2379"
    username: ""
    password: ""
    timeout: 5
    prefix: "/zhuruntao"