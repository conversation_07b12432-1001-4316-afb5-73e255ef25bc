package player

import (
	"context"
	rankv1 "liteframe/api/microservices/rank/v1"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/pkg/log"
)

// SyncRoleInfo 同步角色基本信息到排行榜
func (p *Player) SyncRoleInfo() {
	// 获取排行榜服务客户端
	rankClient := grpcMgr.GetRankService()
	if rankClient == nil {
		log.Error("rank service client not found", log.Kv("uid", p.uid))
		return
	}

	// 构建角色基本信息，根据BaseDB的字段
	roleInfo := &rankv1.RankRoleInfo{
		Uid:        p.db.Base.Uid,
		Kserver:    int32(global.ServerId),
		Name:       p.db.Base.Name,
		Level:      int32(p.db.Base.Level), // 等级字段，Level转为int32
		FightPoint: 0,                      // TODO
	}

	if roleInfo.Uid == 0 || roleInfo.Name == "" {
		log.Error("invalid role info", log.Kv("Guid", roleInfo.Uid), log.Kv("Name", roleInfo.Name))
		return
	}

	// 调用排行榜服务同步角色信息
	_, err := rankClient.SyncRoleInfo(context.Background(), &rankv1.SyncRoleInfoRequest{
		Info: roleInfo,
	})

	if err != nil {
		log.Error("sync role info to rank service failed", log.Kv("uid", p.uid), log.Err(err))
	} else {
		log.Info("sync role info to rank service success", log.Kv("uid", p.uid))
	}
}

// UpdateRankInfo 更新指定类型的排行榜信息
func (p *Player) UpdateRankInfo(rankType rankv1.RankType, rankValue int64) {
	rankClient := grpcMgr.GetRankService()
	if rankClient == nil {
		log.Error("rank service client not found", log.Kv("uid", p.uid))
		return
	}

	// 构建更新信息
	var rankValues []*rankv1.RankValueInfo

	// 添加排行榜数据
	rankValues = append(rankValues, &rankv1.RankValueInfo{
		RankType:  int32(rankType),
		RankValue: rankValue,
	})

	// 调用排行榜服务更新排行榜数据
	_, err := rankClient.UpdateRoleRankInfo(context.Background(), &rankv1.UpdateRoleRankRequest{
		Uid:     p.db.Base.Uid,
		Kserver: int32(p.db.Base.ServerId),
		Info:    rankValues,
	})

	if err != nil {
		log.Error("update rank info failed", log.Kv("uid", p.uid), log.Kv("rankType", rankType), log.Err(err))
	} else {
		log.Info("update rank info success", log.Kv("uid", p.uid), log.Kv("rankType", rankType))
	}
}

// UpdateAllRankInfo 更新所有排行榜信息
func (p *Player) UpdateAllRankInfo() {
	//p.UpdateRankInfo(rankv1.RankType_Level, int64(p.db.Base.Level))

	// 未来如果有其他排行榜类型，可以在这里添加
}
