// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"go.etcd.io/etcd/client/v3"
	"liteframe/internal/general-services/serverlist/biz"
	"liteframe/internal/general-services/serverlist/conf"
	"liteframe/internal/general-services/serverlist/registry"
	"liteframe/internal/general-services/serverlist/server"
	"liteframe/internal/general-services/serverlist/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init serverlist application.
func wireApp(confServer *conf.Server, data *conf.Data, bootstrap *conf.Bootstrap, logger log.Logger, client *clientv3.Client, arg map[string]string) (*kratos.App, func(), error) {
	serverlistUsecase := biz.NewServerlistUsecase(logger, bootstrap, client)
	serverlistService := service.NewServerlistService(serverlistUsecase)
	grpcServer := server.NewGRPCServer(confServer, serverlistService, logger)
	httpServer := server.NewHTTPServer(confServer, serverlistService, logger)
	config := registry.ProvideRegistry(bootstrap)
	etcdRegistry := registry.NewEtcdRegistry(client, config)
	app := newApp(logger, grpcServer, httpServer, etcdRegistry, arg)
	return app, func() {
	}, nil
}

// wire.go:

// Global variable to store usecase reference for configuration changes
var (
	globalServerListUsecase *biz.ServerlistUsecase
)
