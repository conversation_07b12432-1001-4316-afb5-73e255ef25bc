// @Author:zhouchen
// @Description: 消息包文件定义
// @Data: Created in 20:01 2023/6/6

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.1
// source: general-services/serverlist/v1/serverlist.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Serverlist_GetServerListData_FullMethodName = "/serverlist.v1.Serverlist/GetServerListData"
	Serverlist_GetIsWhitelist_FullMethodName    = "/serverlist.v1.Serverlist/GetIsWhitelist"
	Serverlist_SetServerInfo_FullMethodName     = "/serverlist.v1.Serverlist/SetServerInfo"
)

// ServerlistClient is the client API for Serverlist service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// The relation service definition.
type ServerlistClient interface {
	GetServerListData(ctx context.Context, in *GetServerListReq, opts ...grpc.CallOption) (*GetServerListReply, error)
	GetIsWhitelist(ctx context.Context, in *GetIsWhitelistReq, opts ...grpc.CallOption) (*GetIsWhitelistReply, error)
	SetServerInfo(ctx context.Context, in *KratosServerInfo, opts ...grpc.CallOption) (*KratosServerInfo, error)
}

type serverlistClient struct {
	cc grpc.ClientConnInterface
}

func NewServerlistClient(cc grpc.ClientConnInterface) ServerlistClient {
	return &serverlistClient{cc}
}

func (c *serverlistClient) GetServerListData(ctx context.Context, in *GetServerListReq, opts ...grpc.CallOption) (*GetServerListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServerListReply)
	err := c.cc.Invoke(ctx, Serverlist_GetServerListData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverlistClient) GetIsWhitelist(ctx context.Context, in *GetIsWhitelistReq, opts ...grpc.CallOption) (*GetIsWhitelistReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIsWhitelistReply)
	err := c.cc.Invoke(ctx, Serverlist_GetIsWhitelist_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *serverlistClient) SetServerInfo(ctx context.Context, in *KratosServerInfo, opts ...grpc.CallOption) (*KratosServerInfo, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(KratosServerInfo)
	err := c.cc.Invoke(ctx, Serverlist_SetServerInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ServerlistServer is the server API for Serverlist service.
// All implementations must embed UnimplementedServerlistServer
// for forward compatibility.
//
// The relation service definition.
type ServerlistServer interface {
	GetServerListData(context.Context, *GetServerListReq) (*GetServerListReply, error)
	GetIsWhitelist(context.Context, *GetIsWhitelistReq) (*GetIsWhitelistReply, error)
	SetServerInfo(context.Context, *KratosServerInfo) (*KratosServerInfo, error)
	mustEmbedUnimplementedServerlistServer()
}

// UnimplementedServerlistServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedServerlistServer struct{}

func (UnimplementedServerlistServer) GetServerListData(context.Context, *GetServerListReq) (*GetServerListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServerListData not implemented")
}
func (UnimplementedServerlistServer) GetIsWhitelist(context.Context, *GetIsWhitelistReq) (*GetIsWhitelistReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIsWhitelist not implemented")
}
func (UnimplementedServerlistServer) SetServerInfo(context.Context, *KratosServerInfo) (*KratosServerInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetServerInfo not implemented")
}
func (UnimplementedServerlistServer) mustEmbedUnimplementedServerlistServer() {}
func (UnimplementedServerlistServer) testEmbeddedByValue()                    {}

// UnsafeServerlistServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ServerlistServer will
// result in compilation errors.
type UnsafeServerlistServer interface {
	mustEmbedUnimplementedServerlistServer()
}

func RegisterServerlistServer(s grpc.ServiceRegistrar, srv ServerlistServer) {
	// If the following call pancis, it indicates UnimplementedServerlistServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Serverlist_ServiceDesc, srv)
}

func _Serverlist_GetServerListData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServerListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerlistServer).GetServerListData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Serverlist_GetServerListData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerlistServer).GetServerListData(ctx, req.(*GetServerListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Serverlist_GetIsWhitelist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIsWhitelistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerlistServer).GetIsWhitelist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Serverlist_GetIsWhitelist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerlistServer).GetIsWhitelist(ctx, req.(*GetIsWhitelistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Serverlist_SetServerInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KratosServerInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ServerlistServer).SetServerInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Serverlist_SetServerInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ServerlistServer).SetServerInfo(ctx, req.(*KratosServerInfo))
	}
	return interceptor(ctx, in, info, handler)
}

// Serverlist_ServiceDesc is the grpc.ServiceDesc for Serverlist service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Serverlist_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "serverlist.v1.Serverlist",
	HandlerType: (*ServerlistServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetServerListData",
			Handler:    _Serverlist_GetServerListData_Handler,
		},
		{
			MethodName: "GetIsWhitelist",
			Handler:    _Serverlist_GetIsWhitelist_Handler,
		},
		{
			MethodName: "SetServerInfo",
			Handler:    _Serverlist_SetServerInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "general-services/serverlist/v1/serverlist.proto",
}
