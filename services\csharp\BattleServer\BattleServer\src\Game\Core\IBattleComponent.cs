using System;

namespace BattleServer.Game.Core
{
    /// <summary>
    /// 战斗组件基础接口
    /// </summary>
    public interface IBattleComponent : IDisposable
    {
        /// <summary>
        /// 战斗ID
        /// </summary>
        long BattleId { get; }

        /// <summary>
        /// 组件是否已初始化
        /// </summary>
        bool IsInitialized { get; }

        /// <summary>
        /// 初始化组件
        /// </summary>
        /// <param name="battleId">战斗ID</param>
        void Initialize(long battleId);

        /// <summary>
        /// 清理组件资源
        /// </summary>
        void Clear();
    }

    /// <summary>
    /// 可更新的战斗组件接口
    /// </summary>
    public interface IUpdatableBattleComponent : IBattleComponent
    {
        /// <summary>
        /// 每帧更新
        /// </summary>
        /// <param name="deltaTimeMs">时间增量(毫秒)</param>
        void Update(int deltaTimeMs);
    }

    /// <summary>
    /// 战斗组件基类
    /// </summary>
    public abstract class BattleComponentBase : IBattleComponent
    {
        public long BattleId { get; private set; }
        public bool IsInitialized { get; private set; }

        protected string LogName { get; private set; }

        public virtual void Initialize(long battleId)
        {
            if (IsInitialized)
            {
                throw new InvalidOperationException($"{GetType().Name} is already initialized");
            }

            BattleId = battleId;
            LogName = $"{GetType().Name}_{battleId}";
            IsInitialized = true;

            OnInitialize();
        }

        /// <summary>
        /// 子类重写此方法进行具体初始化
        /// </summary>
        protected virtual void OnInitialize() { }

        public virtual void Clear()
        {
            if (!IsInitialized) return;

            OnClear();
            IsInitialized = false;
            BattleId = 0;
            LogName = null;
        }

        /// <summary>
        /// 子类重写此方法进行具体清理
        /// </summary>
        protected virtual void OnClear() { }

        public virtual void Dispose()
        {
            Clear();
        }

        protected void ThrowIfNotInitialized()
        {
            if (!IsInitialized)
            {
                throw new InvalidOperationException($"{GetType().Name} is not initialized");
            }
        }
    }
}
