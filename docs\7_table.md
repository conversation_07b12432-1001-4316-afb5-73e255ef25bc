# 表格生成

### 1. 表格字段映射：

* 映射规则定义在：`script/table_fields.json`
* 映射类型定义在：`common/table/table_gen/table_type.go`

默认字段类型使用表格里的类型，可在parse_fields中指定使用自定义类型：
commonFields定义通用表映射，特定表在独立键值对，优先级更高
SourceType：需要映射的源类型
TargetType：目标字段映射类型

``` json
    "parse_fields": [
      {
        "SourceType": "String",
        "TargetType": "string"
      }
    ]
```

### 2. 表格生成：

* 运行`script/gen_table.sh`生成表格相关
* 表格结构按策划txt自动生成在`common/table/table_gen`

### 3. 表格处理：

* 如果需要对表进行二次分析处理，需要在table.go->preProcess方法中添加各自表格的处理逻辑，具体规则可参考已有逻辑

### 4. 静态配置setting表格处理：

* 静态配置区别于普通配表，使用json进行解析，字段类型需要定义到：`common/table/setting_table_helper.go`
* 在`common/table/setting_table_helper.go`的`settingTableProcess`方法中添加`addSetting`即可
* 具体用法可参考现有逻辑
