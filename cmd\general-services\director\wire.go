//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	clientv3 "go.etcd.io/etcd/client/v3"
	"liteframe/internal/general-services/director/biz"
	"liteframe/internal/general-services/director/conf"
	"liteframe/internal/general-services/director/registry"
	"liteframe/internal/general-services/director/server"
	"liteframe/internal/general-services/director/service"
)

// wireApp init director application.
func wireApp(*conf.Server, *conf.Config, *conf.Registry, log.Logger, *clientv3.Client, map[string]string) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, biz.ProviderSet, service.ProviderSet, registry.ProviderSet, newApp))
}
