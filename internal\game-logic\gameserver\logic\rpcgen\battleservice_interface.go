// Code generated by rpcparse. DO NOT EDIT.

package rpcgen

import (
	"context"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/game-logic/gameserver/logic/player"
)

type BattleServiceInterface interface {
	RPCHandler
	// userbattlematch
	MatchBattle(ctx context.Context, sender *player.Player, in *cs.CLMatchReq) (out *cs.LCMatchRsp)
	// userroundbattlestart
	RoundBattleStart(ctx context.Context, sender *player.Player, in *cs.CLRoundBattleStartReq) (out *cs.LCRoundBattleStartResp)
	// userreadybattle
	BattleReady(ctx context.Context, sender *player.Player, in *cs.CLReadyReq) (out *cs.LCReadyRsp)
	// userendbattle
	RoundBattleEnd(ctx context.Context, sender *player.Player, in *cs.CLRoundBattleEndReq) (out *cs.LCRoundBattleEndResp)
	// userselectbuffer
	SelectBuffer(ctx context.Context, sender *player.Player, in *cs.CLSelectBufferReq) (out *cs.LCSelectBufferResp)
	// usermergehero
	MergeHero(ctx context.Context, sender *player.Player, in *cs.CLMergeReq) (out *cs.LCMergeRsp)
	// claimadreward
	ClaimAdReward(ctx context.Context, sender *player.Player, in *cs.CLClaimAdRewardReq) (out *cs.LCClaimAdRewardRsp)
}
