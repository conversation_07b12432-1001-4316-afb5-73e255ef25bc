//下面这行很重要，是表明你这个Service用于哪个URL的，见游戏服的GrpcAddressType
//GrpcAddressType:CharList

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.1
// source: general-services/charlist/v1/charlist.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CharListMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Zwid     int32  `protobuf:"varint,1,opt,name=zwid,proto3" json:"zwid,omitempty"`
	Account  string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
	CharGuid uint64 `protobuf:"varint,3,opt,name=charGuid,proto3" json:"charGuid,omitempty"`
	CharName string `protobuf:"bytes,4,opt,name=charName,proto3" json:"charName,omitempty"`
	Level    int32  `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
}

func (x *CharListMsg) Reset() {
	*x = CharListMsg{}
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CharListMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CharListMsg) ProtoMessage() {}

func (x *CharListMsg) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CharListMsg.ProtoReflect.Descriptor instead.
func (*CharListMsg) Descriptor() ([]byte, []int) {
	return file_general_services_charlist_v1_charlist_proto_rawDescGZIP(), []int{0}
}

func (x *CharListMsg) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

func (x *CharListMsg) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *CharListMsg) GetCharGuid() uint64 {
	if x != nil {
		return x.CharGuid
	}
	return 0
}

func (x *CharListMsg) GetCharName() string {
	if x != nil {
		return x.CharName
	}
	return ""
}

func (x *CharListMsg) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

type SaveCharListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CharList *CharListMsg `protobuf:"bytes,1,opt,name=charList,proto3" json:"charList,omitempty"`
}

func (x *SaveCharListRequest) Reset() {
	*x = SaveCharListRequest{}
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveCharListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCharListRequest) ProtoMessage() {}

func (x *SaveCharListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCharListRequest.ProtoReflect.Descriptor instead.
func (*SaveCharListRequest) Descriptor() ([]byte, []int) {
	return file_general_services_charlist_v1_charlist_proto_rawDescGZIP(), []int{1}
}

func (x *SaveCharListRequest) GetCharList() *CharListMsg {
	if x != nil {
		return x.CharList
	}
	return nil
}

// The response message containing the message
type SaveCharListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result int32 `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *SaveCharListReply) Reset() {
	*x = SaveCharListReply{}
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SaveCharListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaveCharListReply) ProtoMessage() {}

func (x *SaveCharListReply) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaveCharListReply.ProtoReflect.Descriptor instead.
func (*SaveCharListReply) Descriptor() ([]byte, []int) {
	return file_general_services_charlist_v1_charlist_proto_rawDescGZIP(), []int{2}
}

func (x *SaveCharListReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

// Type:Http
type GetCharListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *GetCharListReq) Reset() {
	*x = GetCharListReq{}
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCharListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCharListReq) ProtoMessage() {}

func (x *GetCharListReq) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCharListReq.ProtoReflect.Descriptor instead.
func (*GetCharListReq) Descriptor() ([]byte, []int) {
	return file_general_services_charlist_v1_charlist_proto_rawDescGZIP(), []int{3}
}

func (x *GetCharListReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

// Type:Http
type CharListDataMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account  string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	CharGuid uint64 `protobuf:"varint,2,opt,name=charGuid,proto3" json:"charGuid,omitempty"`
	CharName string `protobuf:"bytes,3,opt,name=charName,proto3" json:"charName,omitempty"`
	Level    int32  `protobuf:"varint,4,opt,name=level,proto3" json:"level,omitempty"`
}

func (x *CharListDataMsg) Reset() {
	*x = CharListDataMsg{}
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CharListDataMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CharListDataMsg) ProtoMessage() {}

func (x *CharListDataMsg) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CharListDataMsg.ProtoReflect.Descriptor instead.
func (*CharListDataMsg) Descriptor() ([]byte, []int) {
	return file_general_services_charlist_v1_charlist_proto_rawDescGZIP(), []int{4}
}

func (x *CharListDataMsg) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *CharListDataMsg) GetCharGuid() uint64 {
	if x != nil {
		return x.CharGuid
	}
	return 0
}

func (x *CharListDataMsg) GetCharName() string {
	if x != nil {
		return x.CharName
	}
	return ""
}

func (x *CharListDataMsg) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

// Type:Http
type ZwidDataMsg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Zwid         int32              `protobuf:"varint,1,opt,name=zwid,proto3" json:"zwid,omitempty"`
	CharListData []*CharListDataMsg `protobuf:"bytes,2,rep,name=charListData,proto3" json:"charListData,omitempty"`
}

func (x *ZwidDataMsg) Reset() {
	*x = ZwidDataMsg{}
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ZwidDataMsg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ZwidDataMsg) ProtoMessage() {}

func (x *ZwidDataMsg) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ZwidDataMsg.ProtoReflect.Descriptor instead.
func (*ZwidDataMsg) Descriptor() ([]byte, []int) {
	return file_general_services_charlist_v1_charlist_proto_rawDescGZIP(), []int{5}
}

func (x *ZwidDataMsg) GetZwid() int32 {
	if x != nil {
		return x.Zwid
	}
	return 0
}

func (x *ZwidDataMsg) GetCharListData() []*CharListDataMsg {
	if x != nil {
		return x.CharListData
	}
	return nil
}

// Type:Http
type GetCharListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result   int32          `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	ZwidData []*ZwidDataMsg `protobuf:"bytes,2,rep,name=zwidData,proto3" json:"zwidData,omitempty"`
}

func (x *GetCharListReply) Reset() {
	*x = GetCharListReply{}
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCharListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCharListReply) ProtoMessage() {}

func (x *GetCharListReply) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_charlist_v1_charlist_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCharListReply.ProtoReflect.Descriptor instead.
func (*GetCharListReply) Descriptor() ([]byte, []int) {
	return file_general_services_charlist_v1_charlist_proto_rawDescGZIP(), []int{6}
}

func (x *GetCharListReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GetCharListReply) GetZwidData() []*ZwidDataMsg {
	if x != nil {
		return x.ZwidData
	}
	return nil
}

var File_general_services_charlist_v1_charlist_proto protoreflect.FileDescriptor

var file_general_services_charlist_v1_charlist_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x63, 0x68, 0x61, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x76, 0x31, 0x2f, 0x63,
	0x68, 0x61, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x63,
	0x68, 0x61, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x89, 0x01, 0x0a, 0x0b, 0x43, 0x68, 0x61,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x73, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x77, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47, 0x75,
	0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14,
	0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x22, 0x4b, 0x0a, 0x13, 0x53, 0x61, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x08, 0x63,
	0x68, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x63, 0x68, 0x61, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x4d, 0x73, 0x67, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x22, 0x2b, 0x0a, 0x11, 0x53, 0x61, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2a,
	0x0a, 0x0e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x79, 0x0a, 0x0f, 0x43, 0x68,
	0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x73, 0x67, 0x12, 0x18, 0x0a,
	0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47,
	0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x47,
	0x75, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x68, 0x61, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x63, 0x0a, 0x0b, 0x5a, 0x77, 0x69, 0x64, 0x44, 0x61, 0x74,
	0x61, 0x4d, 0x73, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x7a, 0x77, 0x69, 0x64, 0x12, 0x40, 0x0a, 0x0c, 0x63, 0x68, 0x61, 0x72,
	0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x63, 0x68, 0x61, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x4d, 0x73, 0x67, 0x52, 0x0c, 0x63, 0x68,
	0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x22, 0x60, 0x0a, 0x10, 0x47, 0x65,
	0x74, 0x43, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x34, 0x0a, 0x08, 0x7a, 0x77, 0x69, 0x64, 0x44, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x6c,
	0x69, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x5a, 0x77, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x4d,
	0x73, 0x67, 0x52, 0x08, 0x7a, 0x77, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x32, 0x64, 0x0a, 0x0e,
	0x43, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x6f, 0x74, 0x4e, 0x65, 0x74, 0x12, 0x52,
	0x0a, 0x0c, 0x53, 0x61, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20,
	0x2e, 0x63, 0x68, 0x61, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x61, 0x76,
	0x65, 0x43, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x61, 0x76, 0x65, 0x43, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x00, 0x32, 0x8a, 0x01, 0x0a, 0x0c, 0x43, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x48,
	0x74, 0x74, 0x70, 0x12, 0x7a, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x6c, 0x69, 0x73,
	0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x1d, 0x2e, 0x63, 0x68, 0x61, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x68, 0x61, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x67, 0x65, 0x74, 0x63, 0x68, 0x61,
	0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x7b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x42,
	0x1d, 0x5a, 0x1b, 0x63, 0x68, 0x61, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x68, 0x61, 0x72, 0x6c, 0x69, 0x73, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_general_services_charlist_v1_charlist_proto_rawDescOnce sync.Once
	file_general_services_charlist_v1_charlist_proto_rawDescData = file_general_services_charlist_v1_charlist_proto_rawDesc
)

func file_general_services_charlist_v1_charlist_proto_rawDescGZIP() []byte {
	file_general_services_charlist_v1_charlist_proto_rawDescOnce.Do(func() {
		file_general_services_charlist_v1_charlist_proto_rawDescData = protoimpl.X.CompressGZIP(file_general_services_charlist_v1_charlist_proto_rawDescData)
	})
	return file_general_services_charlist_v1_charlist_proto_rawDescData
}

var file_general_services_charlist_v1_charlist_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_general_services_charlist_v1_charlist_proto_goTypes = []any{
	(*CharListMsg)(nil),         // 0: charlist.v1.CharListMsg
	(*SaveCharListRequest)(nil), // 1: charlist.v1.SaveCharListRequest
	(*SaveCharListReply)(nil),   // 2: charlist.v1.SaveCharListReply
	(*GetCharListReq)(nil),      // 3: charlist.v1.GetCharListReq
	(*CharListDataMsg)(nil),     // 4: charlist.v1.CharListDataMsg
	(*ZwidDataMsg)(nil),         // 5: charlist.v1.ZwidDataMsg
	(*GetCharListReply)(nil),    // 6: charlist.v1.GetCharListReply
}
var file_general_services_charlist_v1_charlist_proto_depIdxs = []int32{
	0, // 0: charlist.v1.SaveCharListRequest.charList:type_name -> charlist.v1.CharListMsg
	4, // 1: charlist.v1.ZwidDataMsg.charListData:type_name -> charlist.v1.CharListDataMsg
	5, // 2: charlist.v1.GetCharListReply.zwidData:type_name -> charlist.v1.ZwidDataMsg
	1, // 3: charlist.v1.CharListDotNet.SaveCharList:input_type -> charlist.v1.SaveCharListRequest
	3, // 4: charlist.v1.CharListHttp.GetCharListData:input_type -> charlist.v1.GetCharListReq
	2, // 5: charlist.v1.CharListDotNet.SaveCharList:output_type -> charlist.v1.SaveCharListReply
	6, // 6: charlist.v1.CharListHttp.GetCharListData:output_type -> charlist.v1.GetCharListReply
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_general_services_charlist_v1_charlist_proto_init() }
func file_general_services_charlist_v1_charlist_proto_init() {
	if File_general_services_charlist_v1_charlist_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_general_services_charlist_v1_charlist_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_general_services_charlist_v1_charlist_proto_goTypes,
		DependencyIndexes: file_general_services_charlist_v1_charlist_proto_depIdxs,
		MessageInfos:      file_general_services_charlist_v1_charlist_proto_msgTypes,
	}.Build()
	File_general_services_charlist_v1_charlist_proto = out.File
	file_general_services_charlist_v1_charlist_proto_rawDesc = nil
	file_general_services_charlist_v1_charlist_proto_goTypes = nil
	file_general_services_charlist_v1_charlist_proto_depIdxs = nil
}
