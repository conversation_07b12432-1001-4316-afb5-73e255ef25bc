using System;
using System.Collections.Generic;
using System.Linq;
using Game.Core;
using BattleServer.Game;

namespace BattleServer.Game.Core
{
    /// <summary>
    /// 战斗事件基类
    /// </summary>
    public abstract class BattleEvent
    {
        public long BattleId { get; }
        public DateTime Timestamp { get; }

        protected BattleEvent(long battleId)
        {
            BattleId = battleId;
            Timestamp = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// 战斗状态变化事件
    /// </summary>
    public class BattleStateChangedEvent : BattleEvent
    {
        public BattleState OldState { get; }
        public BattleState NewState { get; }
        public int CountdownMs { get; }

        public BattleStateChangedEvent(long battleId, BattleState oldState, BattleState newState, int countdownMs)
            : base(battleId)
        {
            OldState = oldState;
            NewState = newState;
            CountdownMs = countdownMs;
        }
    }

    /// <summary>
    /// 回合开始事件
    /// </summary>
    public class RoundStartedEvent : BattleEvent
    {
        public int RoundCount { get; }

        public RoundStartedEvent(long battleId, int roundCount) : base(battleId)
        {
            RoundCount = roundCount;
        }
    }

    /// <summary>
    /// 战斗加速事件
    /// </summary>
    public class BattleAcceleratingEvent : BattleEvent
    {
        public BattleAcceleratingEvent(long battleId) : base(battleId)
        {
        }
    }

    /// <summary>
    /// 玩家事件基类
    /// </summary>
    public abstract class PlayerEvent : BattleEvent
    {
        public long PlayerId { get; }

        protected PlayerEvent(long battleId, long playerId) : base(battleId)
        {
            PlayerId = playerId;
        }
    }

    /// <summary>
    /// 所有玩家准备就绪事件
    /// </summary>
    public class AllPlayersReadyEvent : BattleEvent
    {
        public AllPlayersReadyEvent(long battleId) : base(battleId) { }
    }

    /// <summary>
    /// 玩家淘汰事件
    /// </summary>
    public class PlayerEliminatedEvent : PlayerEvent
    {
        public PlayerEliminatedEvent(long battleId, long playerId) : base(battleId, playerId) { }
    }

    /// <summary>
    /// 游戏结束事件
    /// </summary>
    public class GameOverEvent : BattleEvent
    {
        public long WinnerId { get; }

        public GameOverEvent(long battleId, long winnerId) : base(battleId)
        {
            WinnerId = winnerId;
        }
    }

    /// <summary>
    /// 实体事件基类
    /// </summary>
    public abstract class EntityEvent : BattleEvent
    {
        public Entity Entity { get; }
        public int Row { get; }
        public int Column { get; }

        protected EntityEvent(long battleId, Entity entity, int row, int column) : base(battleId)
        {
            Entity = entity;
            Row = row;
            Column = column;
        }
    }

    /// <summary>
    /// 实体创建事件
    /// </summary>
    public class EntityCreatedEvent : EntityEvent
    {
        public EntityCreatedEvent(long battleId, Entity entity, int row, int column)
            : base(battleId, entity, row, column) { }
    }

    /// <summary>
    /// 实体移动事件
    /// </summary>
    public class EntityMovedEvent : EntityEvent
    {
        public int FromRow { get; }
        public int FromColumn { get; }

        public EntityMovedEvent(long battleId, Entity entity, int fromRow, int fromColumn, int toRow, int toColumn)
            : base(battleId, entity, toRow, toColumn)
        {
            FromRow = fromRow;
            FromColumn = fromColumn;
        }
    }

    /// <summary>
    /// 实体合并事件
    /// </summary>
    public class EntityMergedEvent : BattleEvent
    {
        public Entity SourceEntity { get; }
        public Entity TargetEntity { get; }
        public int SourceRow { get; }
        public int SourceColumn { get; }
        public int TargetRow { get; }
        public int TargetColumn { get; }

        public EntityMergedEvent(long battleId, Entity sourceEntity, Entity targetEntity,
            int sourceRow, int sourceColumn, int targetRow, int targetColumn) : base(battleId)
        {
            SourceEntity = sourceEntity;
            TargetEntity = targetEntity;
            SourceRow = sourceRow;
            SourceColumn = sourceColumn;
            TargetRow = targetRow;
            TargetColumn = targetColumn;
        }
    }

    /// <summary>
    /// 实体移除事件
    /// </summary>
    public class EntityRemovedEvent : EntityEvent
    {
        public EntityRemovedEvent(long battleId, Entity entity, int row, int column)
            : base(battleId, entity, row, column) { }
    }

    /// <summary>
    /// 事件处理器接口
    /// </summary>
    public interface IBattleEventHandler<in T> where T : BattleEvent
    {
        void Handle(T battleEvent);
    }

    /// <summary>
    /// 简单的事件总线
    /// </summary>
    public class BattleEventBus
    {
        private readonly Dictionary<Type, List<object>> _handlers = new();

        public void Subscribe<T>(IBattleEventHandler<T> handler) where T : BattleEvent
        {
            var eventType = typeof(T);
            if (!_handlers.ContainsKey(eventType))
            {
                _handlers[eventType] = new List<object>();
            }
            _handlers[eventType].Add(handler);
        }

        public void Subscribe<T>(Action<T> handler) where T : BattleEvent
        {
            Subscribe(new ActionHandler<T>(handler));
        }

        public void Unsubscribe<T>(IBattleEventHandler<T> handler) where T : BattleEvent
        {
            var eventType = typeof(T);
            if (_handlers.ContainsKey(eventType))
            {
                _handlers[eventType].Remove(handler);
            }
        }

        public void Publish<T>(T battleEvent) where T : BattleEvent
        {
            var eventType = typeof(T);
            if (_handlers.ContainsKey(eventType))
            {
                foreach (var handler in _handlers[eventType].Cast<IBattleEventHandler<T>>())
                {
                    try
                    {
                        handler.Handle(battleEvent);
                    }
                    catch (Exception ex)
                    {
                        // 记录异常但不中断其他处理器
                        LiteFrame.Framework.Log.Error($"Error handling event {eventType.Name}: {ex.Message}");
                    }
                }
            }
        }

        public void Clear()
        {
            _handlers.Clear();
        }

        private class ActionHandler<T> : IBattleEventHandler<T> where T : BattleEvent
        {
            private readonly Action<T> _action;

            public ActionHandler(Action<T> action)
            {
                _action = action;
            }

            public void Handle(T battleEvent)
            {
                _action(battleEvent);
            }
        }
    }

    /// <summary>
    /// 战斗超时事件
    /// </summary>
    public class BattleTimeoutEvent : BattleEvent
    {
        public BattleState State { get; }

        public BattleTimeoutEvent(long battleId, BattleState state)
            : base(battleId)
        {
            State = state;
        }
    }

    /// <summary>
    /// Buff选择超时事件
    /// </summary>
    public class BuffSelectionTimeoutEvent : BattleEvent
    {
        public BuffSelectionTimeoutEvent(long battleId) : base(battleId)
        {
        }
    }
}
