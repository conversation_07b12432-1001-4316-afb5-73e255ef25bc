# BattleServer 发布说明

## 环境要求
- Windows 11
- .NET 9.0 SDK (已安装)
- Rider 2025.1 (已安装)

## 发布方法

### 方法一：使用Rider IDE（最简单）

1. **在Rider中右键点击 `BattleServer` 项目**
2. **选择 "Publish"**
3. **配置发布设置**：
   - **Target**: Folder
   - **Target Location**: 选择输出目录（如 `D:\BattleServer\Release`）
   - **Configuration**: Release
   - **Target Framework**: net9.0
   - **Deployment Mode**: Self-contained（推荐）
   - **Target Runtime**: win-x64
   - **勾选 "Produce single file"**（可选，生成单个exe文件）

4. **点击 "Publish" 按钮**

### 方法二：使用批处理脚本（推荐）

1. **双击运行 `publish.bat`**
   - 会自动发布多个版本（Windows自包含、框架依赖、Linux版本）
   - 输出到 `.\publish` 目录

### 方法三：使用PowerShell脚本（功能最全）

1. **右键点击 `publish.ps1` → "使用PowerShell运行"**
   - 或在PowerShell中运行：`.\publish.ps1`
   - 支持参数自定义：`.\publish.ps1 -Configuration Release -OutputDir "D:\MyOutput"`

### 方法四：手动命令行

打开命令提示符，导航到BattleServer目录，运行：

```bash
# 自包含单文件版本（推荐）
dotnet publish BattleServer/BattleServer.csproj -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -o "./publish/win-x64"

# 框架依赖版本（需要目标机器安装.NET 9.0）
dotnet publish BattleServer/BattleServer.csproj -c Release -r win-x64 --self-contained false -o "./publish/win-x64-framework"
```

## 发布版本说明

### 1. 自包含版本 (Self-contained)
- **文件**: `BattleServer.exe`
- **大小**: 约 60-100MB
- **优点**: 无需目标机器安装.NET运行时，可直接运行
- **缺点**: 文件较大
- **推荐用于**: 生产环境部署

### 2. 框架依赖版本 (Framework-dependent)
- **文件**: `BattleServer.exe` + 依赖文件
- **大小**: 约 1-5MB
- **优点**: 文件小
- **缺点**: 需要目标机器安装.NET 9.0运行时
- **推荐用于**: 开发/测试环境

### 3. 单文件版本 (Single File)
- **文件**: 单个 `BattleServer.exe`
- **优点**: 部署简单，只有一个文件
- **缺点**: 首次启动稍慢（需要解压）

## 运行发布的程序

### Windows环境
```bash
# 直接运行
BattleServer.exe

# 带参数运行
BattleServer.exe --config config.yaml --port 8080
```

### Linux环境
```bash
# 添加执行权限
chmod +x BattleServer

# 运行
./BattleServer

# 带参数运行
./BattleServer --config config.yaml --port 8080
```

## 部署注意事项

1. **配置文件**: 确保配置文件（如 config.yaml）与可执行文件在同一目录
2. **依赖资源**: 确保所需的资源文件（如表格数据）正确部署
3. **网络端口**: 确保目标机器的防火墙允许程序使用的端口
4. **日志目录**: 确保程序有权限创建日志文件和目录
5. **数据库连接**: 确保数据库连接字符串正确配置

## 故障排除

### 常见问题

1. **"找不到.NET运行时"**
   - 使用自包含版本，或在目标机器安装.NET 9.0运行时

2. **"权限被拒绝"**
   - 确保程序有足够的文件系统权限
   - 在Linux上使用 `chmod +x` 添加执行权限

3. **"配置文件找不到"**
   - 确保配置文件与可执行文件在同一目录
   - 检查配置文件路径是否正确

4. **"端口被占用"**
   - 修改配置文件中的端口号
   - 或使用命令行参数指定不同端口

## 性能优化建议

1. **使用Release配置**: 确保使用Release模式发布
2. **启用ReadyToRun**: 已在项目中配置，提升启动性能
3. **考虑AOT编译**: 对于性能要求极高的场景，可考虑Native AOT
4. **监控内存使用**: 在生产环境中监控内存和CPU使用情况

## 更新部署

1. **停止旧版本服务**
2. **备份配置文件和数据**
3. **替换可执行文件**
4. **恢复配置文件**
5. **启动新版本服务**
6. **验证服务正常运行**
