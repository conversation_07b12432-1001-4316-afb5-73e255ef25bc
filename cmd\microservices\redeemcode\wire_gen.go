// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"liteframe/internal/microservices/redeemcode/biz"
	"liteframe/internal/microservices/redeemcode/conf"
	"liteframe/internal/microservices/redeemcode/data"
	"liteframe/internal/microservices/redeemcode/registry"
	"liteframe/internal/microservices/redeemcode/server"
	"liteframe/internal/microservices/redeemcode/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init redeemCode application.
func wireApp(confServer *conf.Server, confData *conf.Data, bootstrap *conf.Bootstrap, logger log.Logger, arg map[string]string) (*kratos.App, func(), error) {
	cmdable := data.NewRedisCmd(confData, logger)
	dataData, err := data.NewData(cmdable, logger)
	if err != nil {
		return nil, nil, err
	}
	redeemCodeRepo := data.NewRedeemCodeRepo(dataData, logger)
	redeemCodeUsecase := biz.NewRedeemCodeUsecase(redeemCodeRepo, logger, bootstrap)
	redeemCodeService := service.NewRedeemCodeServiceService(redeemCodeUsecase)
	grpcServer := server.NewGRPCServer(confServer, redeemCodeService, logger)
	httpServer := server.NewHTTPServer(confServer, redeemCodeService, logger)
	confRegistry := registry.ProvideRegistry(bootstrap)
	etcdRegistry := registry.NewEtcdRegistry(confRegistry)
	app := newApp(logger, grpcServer, httpServer, etcdRegistry, arg)
	return app, func() {
	}, nil
}
