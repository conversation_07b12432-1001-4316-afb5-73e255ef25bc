# BattleServer 发布脚本 (PowerShell)
param(
    [string]$Configuration = "Release",
    [string]$OutputDir = ".\publish",
    [switch]$CleanFirst = $true,
    [switch]$OpenOutput = $true
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "BattleServer 发布脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

$ProjectPath = "BattleServer\BattleServer.csproj"

# 检查项目文件是否存在
if (-not (Test-Path $ProjectPath)) {
    Write-Host "错误: 找不到项目文件 $ProjectPath" -ForegroundColor Red
    exit 1
}

# 清理旧的发布文件
if ($CleanFirst -and (Test-Path $OutputDir)) {
    Write-Host "清理旧的发布文件..." -ForegroundColor Yellow
    Remove-Item $OutputDir -Recurse -Force
}

# 创建输出目录
New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null

Write-Host ""
Write-Host "开始发布 BattleServer..." -ForegroundColor Cyan
Write-Host "配置: $Configuration" -ForegroundColor Gray
Write-Host "输出目录: $OutputDir" -ForegroundColor Gray
Write-Host ""

# 发布配置数组
$PublishConfigs = @(
    @{
        Name = "Windows x64 (自包含单文件)"
        Runtime = "win-x64"
        SelfContained = $true
        SingleFile = $true
        OutputPath = "$OutputDir\win-x64"
    },
    @{
        Name = "Windows x64 (框架依赖)"
        Runtime = "win-x64"
        SelfContained = $false
        SingleFile = $false
        OutputPath = "$OutputDir\win-x64-framework"
    },
    @{
        Name = "Linux x64 (自包含单文件)"
        Runtime = "linux-x64"
        SelfContained = $true
        SingleFile = $true
        OutputPath = "$OutputDir\linux-x64"
    }
)

$SuccessCount = 0
$TotalCount = $PublishConfigs.Count

foreach ($config in $PublishConfigs) {
    $stepNum = $SuccessCount + 1
    Write-Host "[$stepNum/$TotalCount] 发布 $($config.Name)..." -ForegroundColor Yellow
    
    # 构建发布命令参数
    $publishArgs = @(
        "publish", $ProjectPath,
        "-c", $Configuration,
        "-r", $config.Runtime,
        "--self-contained", $config.SelfContained.ToString().ToLower(),
        "-o", $config.OutputPath
    )
    
    if ($config.SingleFile) {
        $publishArgs += "-p:PublishSingleFile=true"
        $publishArgs += "-p:PublishReadyToRun=true"
        $publishArgs += "-p:IncludeNativeLibrariesForSelfExtract=true"
    }
    
    # 执行发布命令
    $process = Start-Process -FilePath "dotnet" -ArgumentList $publishArgs -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host "  ✓ 发布成功" -ForegroundColor Green
        $SuccessCount++
        
        # 显示生成的文件信息
        $exeName = if ($config.Runtime.StartsWith("win")) { "BattleServer.exe" } else { "BattleServer" }
        $exePath = Join-Path $config.OutputPath $exeName
        if (Test-Path $exePath) {
            $fileInfo = Get-Item $exePath
            $sizeInMB = [math]::Round($fileInfo.Length / 1MB, 2)
            Write-Host "    文件: $exePath" -ForegroundColor Gray
            Write-Host "    大小: $sizeInMB MB" -ForegroundColor Gray
        }
    } else {
        Write-Host "  ✗ 发布失败 (退出代码: $($process.ExitCode))" -ForegroundColor Red
    }
    Write-Host ""
}

Write-Host "========================================" -ForegroundColor Green
if ($SuccessCount -eq $TotalCount) {
    Write-Host "发布完成！所有配置都成功发布。" -ForegroundColor Green
} else {
    Write-Host "发布完成，但有 $($TotalCount - $SuccessCount) 个配置失败。" -ForegroundColor Yellow
}
Write-Host "========================================" -ForegroundColor Green

Write-Host ""
Write-Host "输出目录结构:" -ForegroundColor Cyan
foreach ($config in $PublishConfigs) {
    $exeName = if ($config.Runtime.StartsWith("win")) { "BattleServer.exe" } else { "BattleServer" }
    $exePath = Join-Path $config.OutputPath $exeName
    if (Test-Path $exePath) {
        Write-Host "  ✓ $($config.Name): $exePath" -ForegroundColor Green
    } else {
        Write-Host "  ✗ $($config.Name): 发布失败" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "使用说明:" -ForegroundColor Cyan
Write-Host "- 自包含版本可以直接运行，无需安装.NET运行时" -ForegroundColor Gray
Write-Host "- 框架依赖版本需要目标机器安装.NET 9.0运行时" -ForegroundColor Gray
Write-Host "- 单文件版本启动可能稍慢，但部署简单" -ForegroundColor Gray

# 打开输出目录
if ($OpenOutput -and (Test-Path $OutputDir)) {
    Write-Host ""
    Write-Host "正在打开输出目录..." -ForegroundColor Yellow
    Start-Process explorer.exe -ArgumentList (Resolve-Path $OutputDir).Path
}

Write-Host ""
Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
