package system

import (
	"context"
	def "liteframe/internal/common/constant/def"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/game-logic/gameserver/game_def/actor_def"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
	"time"
)

// SeasonSystem 赛季系统
type SeasonSystem struct {
	dispatch        *actor.Dispatcher
	currentSeasonId int32 // 当前赛季ID
	seasonEndTime   int64 // 当前赛季结束时间戳
}

// NewSeasonSystem 创建一个新的赛季系统
func NewSeasonSystem() *SeasonSystem {
	system := &SeasonSystem{
		dispatch: actor.NewDispatcher(100, func(uid uint64, id uint32, ms int64) {
			if ms > 100 {
				log.Debug("Season system", log.Kv("id", id), log.Kv("cost", ms))
			}
		}),
	}
	system.registerHandler()
	return system
}

// PID 获取赛季系统的PID
func (s *SeasonSystem) PID() actor.PID {
	return actor_def.SeasonSystemPID
}

// Process 处理消息
func (s *SeasonSystem) Process(msg *actor.Message) {
	if err := s.dispatch.Dispatch(context.Background(), msg); err != nil {
		log.Error("season system dispatch failed", log.Err(err), log.Kv("msgId", msg.Id))
	}
}

// OnStop 停止处理
func (s *SeasonSystem) OnStop() {
}

// registerHandler 注册消息处理器
func (s *SeasonSystem) registerHandler() {
	s.dispatch.Register(uint32(def.MsgId_Actor_Second), s.serverTickHandler)
}

// OnStarted 启动阶段处理
func (s *SeasonSystem) OnStarted() {
	// 加载配置：从全局配置中读取开服时间戳和赛季周期
	openServerTime := global.OpenServerTime
	seasonDays := s.getSeasonDays() // 14天

	// 计算状态：根据配置和当前时间，动态计算出当前赛季ID和当前赛季的结束时间戳
	currentTime := time.Now().Unix()
	seasonDuration := int64(seasonDays * 24 * 60 * 60) // 转换为秒

	// 计算当前是第几个赛季（从1开始）
	elapsedTime := currentTime - openServerTime
	if elapsedTime < 0 {
		elapsedTime = 0
	}
	s.currentSeasonId = int32(elapsedTime/seasonDuration) + 1

	// 计算当前赛季的结束时间
	s.seasonEndTime = openServerTime + int64(s.currentSeasonId)*seasonDuration

	log.Info("Season system started",
		log.Kv("open_server_time", openServerTime),
		log.Kv("season_days", seasonDays),
		log.Kv("current_season_id", s.currentSeasonId),
		log.Kv("season_end_time", s.seasonEndTime),
		log.Kv("current_time", currentTime))
}

// serverTickHandler 服务器定时任务处理
func (s *SeasonSystem) serverTickHandler(ctx context.Context, msg *actor.Message) error {
	currentTime := time.Now().Unix()

	// 检查是否到了赛季结束时间
	if currentTime >= s.seasonEndTime {
		s.handleSeasonEnd()
	}

	return nil
}

// handleSeasonEnd 处理赛季结束
func (s *SeasonSystem) handleSeasonEnd() {
	oldSeasonId := s.currentSeasonId
	newSeasonId := oldSeasonId + 1

	log.Info("Season ending",
		log.Kv("old_season_id", oldSeasonId),
		log.Kv("new_season_id", newSeasonId))

	// 协调排行榜发奖
	s.processSeasonRewards(oldSeasonId)

	// 广播重置指令
	s.broadcastSeasonReset(oldSeasonId, newSeasonId)

	// 进入新周期
	s.currentSeasonId = newSeasonId
	seasonDays := s.getSeasonDays()
	seasonDuration := int64(seasonDays * 24 * 60 * 60)
	s.seasonEndTime = global.OpenServerTime + int64(s.currentSeasonId)*seasonDuration

	log.Info("Season reset completed",
		log.Kv("new_season_id", s.currentSeasonId),
		log.Kv("new_season_end_time", s.seasonEndTime))
}

// processSeasonRewards 处理赛季奖励发放
func (s *SeasonSystem) processSeasonRewards(seasonId int32) {
	// TODO: 向排行榜系统请求旧赛季的最终排名快照
	// TODO: 遍历排名列表，根据 MainRankSeason 表的配置，为每个上榜玩家匹配奖励
	// TODO: 调用邮件系统接口，将赛季排名奖励通过邮件发送给上榜玩家

	log.Info("Processing season rewards", log.Kv("season_id", seasonId))
	// 这里暂时只记录日志，具体的排行榜和邮件系统集成需要后续实现
}

// broadcastSeasonReset 广播赛季重置指令给所有玩家（包括离线玩家）
func (s *SeasonSystem) broadcastSeasonReset(oldSeasonId, newSeasonId int32) {
	// 创建内部消息 SeasonResetReq
	resetMsg := &cs.SeasonResetReq{
		OldId: oldSeasonId,
		NewId: newSeasonId,
	}

	// 将此消息发送给 PlayerSystem Actor
	err := actor.Send(s.PID(), actor_def.PlayerSystemPID, &actor.Message{
		Id:   uint32(def.MsgId_Actor_SeasonSystem2PlayerSystem),
		Data: resetMsg,
	})

	if err != nil {
		log.Error("Failed to send season reset to PlayerSystem for all players",
			log.Err(err),
			log.Kv("old_season_id", oldSeasonId),
			log.Kv("new_season_id", newSeasonId))
	} else {
		log.Info("Season reset message sent to PlayerSystem for all players",
			log.Kv("old_season_id", oldSeasonId),
			log.Kv("new_season_id", newSeasonId))
	}
}

// getSeasonDays 获取赛季天数配置
func (s *SeasonSystem) getSeasonDays() int32 {
	// TODO: 从配置表读取 setting_key=MainRankSeasonTime=14
	// return table.GetMainRankSeasonTime()
	// 暂时返回默认值14天
	return 14
}

// GetCurrentSeasonId 获取当前赛季ID
func (s *SeasonSystem) GetCurrentSeasonId() int32 {
	return s.currentSeasonId
}

// GetSeasonEndTime 获取当前赛季结束时间
func (s *SeasonSystem) GetSeasonEndTime() int64 {
	return s.seasonEndTime
}
