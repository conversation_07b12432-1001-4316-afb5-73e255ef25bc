syntax="proto3";

import "PublicMessage.proto";
import "PublicEnum.proto";

package natsrpc;
option go_package = "liteframe/internal/common/natsrpc";


message MatchRequest {
  PBBattlePlayerInfo player = 1; // 玩家信息
  PBBattleTeamInfo team = 2;
}

message MatchResponse {
  int32 code = 1; // 1成功，other 失败
}

message CancelMatchRequest {
  uint64 uid = 1; // 玩家id
  int32 stage_id = 3; //补充参数
}

message CancelMatchResponse {
  int32 code = 1; // 1成功，other 失败
}

// matchserver 上的匹配服务
service MatchService {
    rpc Match (MatchRequest) returns (MatchResponse) {}
    rpc CancelMatch (CancelMatchRequest) returns (CancelMatchResponse) {}
}
