// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v4.23.2
// source: microservices/rank/v1/rank.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationRankListHttpGetRankListData = "/Aurora.PlayerInfoServer.RankListHttp/GetRankListData"

type RankListHttpHTTPServer interface {
	GetRankListData(context.Context, *GetRankListReq) (*GetRankListReply, error)
}

func RegisterRankListHttpHTTPServer(s *http.Server, srv RankListHttpHTTPServer) {
	r := s.Route("/")
	r.POST("/api/rankservice/ranklist", _RankListHttp_GetRankListData0_HTTP_Handler(srv))
}

func _RankListHttp_GetRankListData0_HTTP_Handler(srv RankListHttpHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetRankListReq
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationRankListHttpGetRankListData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetRankListData(ctx, req.(*GetRankListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetRankListReply)
		return ctx.Result(200, reply)
	}
}

type RankListHttpHTTPClient interface {
	GetRankListData(ctx context.Context, req *GetRankListReq, opts ...http.CallOption) (rsp *GetRankListReply, err error)
}

type RankListHttpHTTPClientImpl struct {
	cc *http.Client
}

func NewRankListHttpHTTPClient(client *http.Client) RankListHttpHTTPClient {
	return &RankListHttpHTTPClientImpl{client}
}

func (c *RankListHttpHTTPClientImpl) GetRankListData(ctx context.Context, in *GetRankListReq, opts ...http.CallOption) (*GetRankListReply, error) {
	var out GetRankListReply
	pattern := "/api/rankservice/ranklist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationRankListHttpGetRankListData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
