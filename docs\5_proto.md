# Protobuf开发规范

详细规范文档：[protobuf_guide.md](12_protobuf_guide.md)

## 说明

- 协议内容由服务端定义，自动同步(CI/CD)至客户端
- 客户端发往服务端消息以CS开头
- 服务端发往客户端消息以SC开头

## 规范

- 文件名小写，使用 lower_snake_case.proto 格式
- 指定包名和包路径:

```proto
   package xxxpb;
   option go_package = "module/.../xxxpb";
```

- 缩进使用 2 个空格
- 消息名使用 `PascalCase`（首字母大写），字段名使用 `lower_snake_case`：

```proto
   message SongServerRequest {
     optional string song_name = 1;
   }
```

- repeated 字段使用复数命名：

```proto
repeated string keys = 1;
    ...
    repeated MyMessage accounts = 17;
```

- 枚举名使用 PascalCase（首字母大写），枚举值使用 CAPITALS_WITH_UNDERSCORES：

```proto
enum FooBar {
  FOO_BAR_UNSPECIFIED = 0;
  FOO_BAR_FIRST_VALUE = 1;
  FOO_BAR_SECOND_VALUE = 2;
}
```

## 消息定义

#### DB数据结构：

* proto在：`res/proto/dbstruct.proto`
* 生成文件在：`common/protos/dbstruct/dbstruct.pb.go`

#### 客户端与服务器协议交互数据结构：

* proto在：`res/proto/cs.proto`
* 生成文件在：`common/protos/cs/cs.pb.go`

#### 客户端与服务器通用数据结构：

* proto在：`res/proto/comm.proto`
* 生成文件在：`common/protos/comm/comm.pb.go`

#### 通用错误码：

* proto在：`res/proto/retcode.proto`
* 生成文件在：`common/protos/retcode/retcode.pb.go`

## 生成

运行 `make proto`

## 注意事项

- ***不要重复使用字段编号!!!***
- ***不要更改字段类型!!!***
- ***为已删除的字段保留字段编号!!!***
- ***枚举值不要使用 `0` 作为有效值，避免optional字段***
- ***为已删除的枚举值保留字段编号!!!***

```protobuf
enum Foo {
  reserved 2, 15, 9 to 11, 40 to max;
  reserved "FOO", "BAR";
}
```

- ***使用 `deprecated` 选项设置弃用字段，设置为 `true` ，表示该字段已弃用，新代码不应该再次使用!!!***

> ***注意：如果该弃用字段未被任何代码引用，应该删除该字段并保留该字段编号。***

```protobuf
int32 old_field = 6 [deprecated = true];
```


