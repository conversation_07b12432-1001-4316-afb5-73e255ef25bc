// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"liteframe/internal/general-services/textdetect/conf"
	"liteframe/internal/general-services/textdetect/registry"
	"liteframe/internal/general-services/textdetect/server"
	"liteframe/internal/general-services/textdetect/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init textdetect application.
func wireApp(yiDunConfig *conf.YiDunConfig, badWords *conf.BadWords, confServer *conf.Server, confRegistry *conf.Registry, logger log.Logger, arg map[string]string) (*kratos.App, func(), error) {
	textdetectService := service.NewTextdetectService(confServer, yiDunConfig, badWords, logger)
	grpcServer := server.NewGRPCServer(confServer, textdetectService, logger)
	etcdRegistry := registry.NewEtcdRegistry(confRegistry)
	app := newApp(logger, grpcServer, etcdRegistry, arg)
	return app, func() {
	}, nil
}
