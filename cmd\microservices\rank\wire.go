//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"liteframe/internal/microservices/rank/biz"
	"liteframe/internal/microservices/rank/conf"
	"liteframe/internal/microservices/rank/data"
	"liteframe/internal/microservices/rank/registry"
	"liteframe/internal/microservices/rank/server"
	"liteframe/internal/microservices/rank/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init rankservice application.
func wireApp(*conf.Server, *conf.Data, *conf.Bootstrap, log.Logger, map[string]string) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, registry.ProviderSet, newApp))
}
