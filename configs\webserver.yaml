app:
  server_id: 10102 # 服务器ID 与游戏服server_id保持一致
  web_port: 8090
  debug: true
  unified: false # 是否同服 false为分服

log:
  console: true
  filepath: "./log/webserver.log"
  level: "DEBUG"
  rotate: true
  rotate_size: 100
  max_age: 30
  max_backups: 5
  compress: false
  rotate_daily: true

game:
  host: "************"
  port: 10087

# SDK配置
sdk:
  enable: true  # 是否启用SDK验证
  type: "cysdk" # 使用的SDK类型: cysdk, foreignsdk
  mode: 0       # SDK模式: 0测试环境 1正式环境
  cysdk:
    app_key: "*************"
    app_secret: "da31c8c742c44dabb40d949e94a142a8"
    test_url: "http://tmobilebilling.changyou.com/billing"
    prod_url: "http://mobilebilling.changyou.com/billing"
  foreignsdk:
    app_key: "*************"
    app_secret: "0b139dfc9b3741a09bbd2f6269f22089"
    test_url: "https://tnsdk.gaming.com/account-api"
    prod_url: "https://nsdk.gaming.com/account-api"

# HTTP工作池配置
http_pool:
  workers: 10        # 工作协程数
  queue_size: 1000   # 任务队列大小
  timeout: 5000      # 请求超时时间(ms)

db:
  redis:
    addr: *********:6379
    passwd: ""
    read_timeout: 10
    write_timeout: 10
    db: 0
    idle_timeout: 10
    max_idle: 10
    max_connections: 0 # 0 表示不限制 