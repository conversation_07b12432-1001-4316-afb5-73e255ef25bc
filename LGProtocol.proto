// 协议ID类型为short，-32767 到 32767
//StartMessageID = 4000; // 必须以;分号结束
//MaxMessageID = 4999; // 必须以;分号结束
syntax = "proto3";
option go_package = "liteframe/internal/common/protos/cs";

package GamePackage;


import "PublicMessage.proto";
import "PublicEnum.proto";

//Test
message LG_Test_Req
{
	string platformID = 1;
}
// 通知Lobby执行JS返回
message LG_GlobalToLobbyRunJS_Req
{
	string resultStr= 1;							//要运行的脚本返回结果
	string scriptExecuteID = 2;						//脚本唯一ID，CMS用于提取结果
}
//获取所有活动的状态信息
message LG_GlobalGetAllActivityInfo_Req
{
	int32 serverId = 1;							//服务器的id
}
//请求挖宝活动房间号
message LG_GlobalActivityDigRoom_Req
{
	int64 platformId = 1;	//参与的玩家ID
}
//请求兑换码兑换
message LG_CDKeyUse_Req
{
	int64 platformId 					= 1;	//当前玩家ID
	int32 playerCreateTime 				= 2;	//角色创建时间戮，单位：秒
	int32 playerLevel					= 3;	//当前等级
	string code							= 4;	//兑换码
	int32 groupUseCount					= 5;	//玩家在当前码所在批次中已使用的次数
}