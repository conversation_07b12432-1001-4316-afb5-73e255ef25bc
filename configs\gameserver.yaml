# 服务器ID
server_id: 10102
# 最大连接数
max_conn: 150000

unified: false # 是否同服 false为分服

# profile 端口 对内暴露
web_port: 21002
gmt_port: 10086
csv_path: "../configs/table/"
plugin_name: "../bin/game_plugin"

# 对客户端的网络配置
client:
  ip: 0.0.0.0
  out_ip: ************
  port: 10087
  # 客户端请求的消息包最大长度
  max_rec_msg_size: 1024
  # 响应给客户端的消息包最大长度
  max_send_msg_size: 131072
  # 超时时间: 30秒 没收到消息踢下线
  read_timeout: 30
  # 单客户端连接 每秒请求限制
  qps: 8

db:
  mongo:
    url: "mongodb://127.0.0.1:27017"
    user: ""
    password: ""
    db: "game"
    auth_type: ""
    auth_source: ""
    operate_timeout: 5s
    connect_timeout: 5s
  redis:
    addr: *********:6379
    passwd: ""
    read_timeout: 10
    write_timeout: 10
    db: 0
    idle_timeout: 10
    max_idle: 10  # 最大空闲连接数
    max_connections: 0 # 最大连接数 0 表示不限制

log:
  console: true
  filepath: "./log/gameserver.log"
  level: "DEBUG"
  rotate: true
  rotate_size: 100
  max_age: 30
  max_backups: 5
  compress: false
  rotate_daily: true
  bi_log_dir: "./log/cylog/"
  bi_norm_version: "V1"


# 支付SDK配置
pay_sdk_config:
  # 支付SDK类型
  type: "cysdk"  # 可选: cysdk, foreignsdk
  # 回调服务端口
  callback_port: 21001
  # 0: 测试环境, 1: 正式环境
  mode: 0
  # HTTP工作池工作数量
  worker_num: 10
  # HTTP工作池队列大小
  queue_size: 1000
  # 超时时间(秒)
  timeout: 30
  # 畅游SDK配置
  cysdk:
    # 应用唯一标识
    app_key: "1740541306287"
    # 应用密钥
    app_secret: "da31c8c742c44dabb40d949e94a142a8"
    # CYSDK测试环境URL
    test_url: "http://tmobilebilling.changyou.com/billing"
    # CYSDK生产环境URL
    prod_url: "http://mobilebilling.changyou.com/billing"
  # 国外SDK配置
  foreignsdk:
    # 应用唯一标识
    app_key: "1721907000970"
    # 应用密钥
    app_secret: "0b139dfc9b3741a09bbd2f6269f22089"
    # 国外SDK测试环境URL
    test_url: "https://tnsdk.gaming.com/billing"
    # 国外SDK生产环境URL
    prod_url: "https://nsdk.gaming.com/billing"

#服务器加固(防私服系统)
pxx_config:
  # 是否启用加固，需要开启则需要联系运维添加服务器的内外网IP,否则无法启动服务器
  pxx_enable: false
  # 加固服务器地址(由引擎部搭建完成后提供)
  server_url: "http://pxx.gaming.com.cn:16900/tech"

