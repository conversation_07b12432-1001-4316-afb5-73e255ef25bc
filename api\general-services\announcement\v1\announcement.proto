//
// @Author:zhouchen
// @Description: 消息包文件定义
// @Data: Created in 20:01 2023/6/6
syntax = "proto3";

package message.v1;

import "google/api/annotations.proto";

option go_package = "announcement/api/announcement/v1;v1";

// The relation service definition.
service Announcement {
rpc GetAnnouncementListData (GetAnnouncementListReq) returns (GetAnnouncementListReply) {
option (google.api.http) = {
get : "/announcement/api/announcement/getannouncement",
};
}
rpc AddAnnouncementItemData (NoticeItem) returns (GetAnnouncementListReply) {
option (google.api.http) = {
post : "/announcement/api/announcement/addannouncement",
body : "*",
};
}
rpc DelAnnouncementItemData (GetAnnouncementListReq) returns (GetAnnouncementListReply) {
option (google.api.http) = {
get : "/announcement/api/announcement/delannouncement/{noticeid}",
};
}

}

//Type:Http
message GetAnnouncementListReq
{
string noticeid = 1;
}

//Type:Http
message NoticeItem
{
string NoticeId = 1;
string Title = 2;
string Priority = 3;
string Context1 = 4;
string Context2 = 5;
string Context3 = 6;
int64 CreateTime = 7;
int64 LastTime = 8;
int32  AutoOpen = 9;
string BgUrl = 10;
}

//Type:Http
message GetAnnouncementListReply
{
int32 result = 1;
int32 autoOpen = 2;
repeated NoticeItem noticelistdata = 3;
}

