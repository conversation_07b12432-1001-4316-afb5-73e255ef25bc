// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"liteframe/internal/microservices/rank/biz"
	"liteframe/internal/microservices/rank/conf"
	"liteframe/internal/microservices/rank/data"
	"liteframe/internal/microservices/rank/registry"
	"liteframe/internal/microservices/rank/server"
	"liteframe/internal/microservices/rank/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init rankservice application.
func wireApp(confServer *conf.Server, confData *conf.Data, bootstrap *conf.Bootstrap, logger log.Logger, arg map[string]string) (*kratos.App, func(), error) {
	cmdable := data.NewRedisCmd(confData, logger)
	dataData, err := data.NewData(cmdable, logger)
	if err != nil {
		return nil, nil, err
	}
	rankServiceRepo := data.NewRankServiceRepo(dataData, logger)
	rankServiceUsecase := biz.NewRankServiceUsecase(rankServiceRepo, logger)
	rankService := service.NewRankService(rankServiceUsecase, logger)
	grpcServer := server.NewGRPCServer(confServer, rankService, logger)
	httpServer := server.NewHTTPServer(confServer, rankService, logger)
	confRegistry := registry.ProvideRegistry(bootstrap)
	etcdRegistry := registry.NewEtcdRegistry(confRegistry)
	app := newApp(logger, grpcServer, httpServer, etcdRegistry, arg)
	return app, func() {
	}, nil
}
