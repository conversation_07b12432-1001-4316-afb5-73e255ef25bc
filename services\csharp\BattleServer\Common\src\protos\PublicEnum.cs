// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: PublicEnum.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
namespace Game.Core {

  /// <summary>Holder for reflection information generated from PublicEnum.proto</summary>
  public static partial class PublicEnumReflection {

    #region Descriptor
    /// <summary>File descriptor for PublicEnum.proto</summary>
    public static pbr::FileDescriptor Descriptor {
      get { return descriptor; }
    }
    private static pbr::FileDescriptor descriptor;

    static PublicEnumReflection() {
      byte[] descriptorData = global::System.Convert.FromBase64String(
          string.Concat(
            "ChBQdWJsaWNFbnVtLnByb3RvKmQKC0xvZ2luQnlUeXBlEhUKEUxvZ2luQnlU",
            "eXBlX090aGVyEAASHAoYTG9naW5CeVR5cGVfUVFHYW1lQ2VudGVyEAESIAoc",
            "TG9naW5CeVR5cGVfV2VDaGF0R2FtZUNlbnRlchACKnIKCU1vbmV5VHlwZRIS",
            "Cg5Nb25leVR5cGVfTm9uZRAAEhIKDk1vbmV5VHlwZV9Hb2xkEAESFQoRTW9u",
            "ZXlUeXBlX0RpYW1vbmQQAhITCg9Nb25leVR5cGVfUG93ZXIQAxIRCg1Nb25l",
            "eVR5cGVfRXhwEAQqiQEKDUF3YXJkSXRlbVR5cGUSFwoTQXdhcmRJdGVtVHlw",
            "ZV9Nb25leRAAEhYKEkF3YXJkSXRlbVR5cGVfSXRlbRABEhcKE0F3YXJkSXRl",
            "bVR5cGVfRXF1aXAQAhIXChNBd2FyZEl0ZW1UeXBlX1NraWxsEAMSFQoRQXdh",
            "cmRJdGVtVHlwZV9HZW0QBCpWCgxGdW5jTG9ja1R5cGUSFQoRRnVuY0xvY2tU",
            "eXBlX0xvY2sQABIXChNGdW5jTG9ja1R5cGVfVW5Mb2NrEAESFgoSRnVuY0xv",
            "Y2tUeXBlX0Nsb3NlEAIqQwoNU2NlbmVEcm9wVHlwZRIZChVTY2VuZURyb3BU",
            "eXBlX01vbnN0ZXIQABIXChNTY2VuZURyb3BUeXBlX0hvcnNlEAEqzQEKClJl",
            "ZERvdFR5cGUSEwoPUmVkRG90VHlwZV9Ob25lEAASFAoQUmVkRG90VHlwZV9T",
            "a2lsbBABEhMKD1JlZERvdFR5cGVfTWFpbBACEhUKEVJlZERvdFR5cGVfRnJp",
            "ZW5kEAMSGgoWUmVkRG90VHlwZV9GdW5jUHJldmlldxAEEhoKFlJlZERvdFR5",
            "cGVfSGVhdmVubHlEYW8QBRIcChhSZWREb3RUeXBlX0RhaWx5UGx1c0dpZnQQ",
            "BhISCg5SZWREb3RUeXBlX01BWBAHKi4KC0VHZW5kZXJUeXBlEgsKB0RlZmF1",
            "bHQQABIHCgNNYW4QARIJCgVXb21hbhACKoQNCg1BdHRyaWJ1dGVUeXBlEhYK",
            "EkF0dHJpYnV0ZVR5cGVfTk9ORRAAEhgKFEF0dHJpYnV0ZVR5cGVfQXR0YWNr",
            "EAESFAoQQXR0cmlidXRlVHlwZV9IcBACEhUKEUF0dHJpYnV0ZVR5cGVfRGVm",
            "EAMSGwoXQXR0cmlidXRlVHlwZV9Nb3ZlU3BlZWQQBBIYChRBdHRyaWJ1dGVU",
            "eXBlX0d1bkF0axAFEhwKGEF0dHJpYnV0ZVR5cGVfUGh5c2ljc0F0axAGEhgK",
            "FEF0dHJpYnV0ZVR5cGVfSWNlQXRrEAcSIAocQXR0cmlidXRlVHlwZV9FbGVj",
            "dHJpY2l0eUF0axAIEhkKFUF0dHJpYnV0ZVR5cGVfRmlyZUF0axAJEhsKF0F0",
            "dHJpYnV0ZVR5cGVfRW5lcmd5QXRrEAoSGQoVQXR0cmlidXRlVHlwZV9XaW5k",
            "QXRrEAsSGwoXQXR0cmlidXRlVHlwZV9QZW5ldHJhdGUQDBIYChRBdHRyaWJ1",
            "dGVUeXBlX0hpdFByZRAUEhoKFkF0dHJpYnV0ZVR5cGVfRG9kZ2VQcmUQFRIb",
            "ChdBdHRyaWJ1dGVUeXBlX0F0dGFja1ByZRAWEhcKE0F0dHJpYnV0ZVR5cGVf",
            "SHBQcmUQFxIYChRBdHRyaWJ1dGVUeXBlX0RlZlByZRAYEh4KGkF0dHJpYnV0",
            "ZVR5cGVfTW92ZVNwZWVkUHJlEBkSHQoZQXR0cmlidXRlVHlwZV9Dcml0aWNh",
            "bFByZRAaEiUKIUF0dHJpYnV0ZVR5cGVfQ3JpdGljYWxNdWx0aXBsZVByZRAb",
            "EiMKH0F0dHJpYnV0ZVR5cGVfQ3JpdGljYWxSZXNpc3RQcmUQHBIfChtBdHRy",
            "aWJ1dGVUeXBlX0Jhc2VEYW1hZ2VQcmUQHRIeChpBdHRyaWJ1dGVUeXBlX0d1",
            "bkRhbWFnZVByZRAeEiIKHkF0dHJpYnV0ZVR5cGVfUGh5c2ljc0RhbWFnZVBy",
            "ZRAfEh4KGkF0dHJpYnV0ZVR5cGVfSWNlRGFtYWdlUHJlECASJgoiQXR0cmli",
            "dXRlVHlwZV9FbGVjdHJpY2l0eURhbWFnZVByZRAhEh8KG0F0dHJpYnV0ZVR5",
            "cGVfRmlyZURhbWFnZVByZRAiEiEKHUF0dHJpYnV0ZVR5cGVfRW5lcmd5RGFt",
            "YWdlUHJlECMSHwobQXR0cmlidXRlVHlwZV9XaW5kRGFtYWdlUHJlECQSHgoa",
            "QXR0cmlidXRlVHlwZV9QZW5ldHJhdGVQcmUQJRIbChdBdHRyaWJ1dGVUeXBl",
            "X0d1bkRlZlByZRAmEh8KG0F0dHJpYnV0ZVR5cGVfUGh5c2ljc0RlZlByZRAn",
            "EhsKF0F0dHJpYnV0ZVR5cGVfSWNlRGVmUHJlECgSIwofQXR0cmlidXRlVHlw",
            "ZV9FbGVjdHJpY2l0eURlZlByZRApEhwKGEF0dHJpYnV0ZVR5cGVfRmlyZURl",
            "ZlByZRAqEh4KGkF0dHJpYnV0ZVR5cGVfRW5lcmd5RGVmUHJlECsSHAoYQXR0",
            "cmlidXRlVHlwZV9XaW5kRGVmUHJlECwSJAogQXR0cmlidXRlVHlwZV9Hcm91",
            "bmREZW1hZ2VBZGRQcmUQLRIkCiBBdHRyaWJ1dGVUeXBlX01pZGFpckRhbWFn",
            "ZUFkZFByZRAuEiIKHkF0dHJpYnV0ZVR5cGVfTmVhckRhbWFnZUFkZFByZRAv",
            "EiUKIUF0dHJpYnV0ZVR5cGVfTG9uZ0Rpc0RhbWFnZUFkZFByZRAwEioKJkF0",
            "dHJpYnV0ZVR5cGVfU21hbGxNb25zdGVyRGFtYWdlQWRkUHJlEDESKgomQXR0",
            "cmlidXRlVHlwZV9FbGl0ZU1vbnN0ZXJEYW1hZ2VBZGRQcmUQMhIpCiVBdHRy",
            "aWJ1dGVUeXBlX0Jvc3NNb25zdGVyRGFtYWdlQWRkUHJlEDMSHwobQXR0cmli",
            "dXRlVHlwZV9EZWNyZWFzZUd1bkNEEDQSIQodQXR0cmlidXRlVHlwZV9EZWNy",
            "ZWFzZVNraWxsQ0QQNRIeChpBdHRyaWJ1dGVUeXBlX0dvbGRCb251c1ByZRBQ",
            "Eh0KGUF0dHJpYnV0ZVR5cGVfRXhwQm9udXNQcmUQURIcChhBdHRyaWJ1dGVU",
            "eXBlX1dhckNvaW5QcmUQUhIaChZBdHRyaWJ1dGVUeXBlX01hZ2F6aW5lEFMS",
            "HQoZQXR0cmlidXRlVHlwZV9NYWdhemluZVByZRBUKmoKC01vbnN0ZXJUeXBl",
            "EhQKEE1vbnN0ZXJUeXBlX05vbmUQABIVChFNb25zdGVyVHlwZV9TbWFsbBAB",
            "EhgKFE1vbnN0ZXJUeXBlX0FkdmFuY2VkEAISFAoQTW9uc3RlclR5cGVfQm9z",
            "cxADKkAKCkJhdHRsZVR5cGUSFQoRQmF0dGxlVHlwZV9Db21tb24QABIbChdC",
            "YXR0bGVUeXBlX1N0YW5kaW5nUGlsZRBkKtIBCgtCYXR0bGVTdGF0ZRIOCgpT",
            "VEFURV9OT05FEAASFQoRU1RBVEVfUk9VTkRfU1RBUlQQARIVChFTVEFURV9Q",
            "UkVQQVJBVElPThACEhkKFVNUQVRFX0JBVFRMRV9TVEFSVElORxADEhwKGFNU",
            "QVRFX0JBVFRMRV9JTl9QUk9HUkVTUxAEEhoKFlNUQVRFX1JPVU5EX1NFVFRM",
            "RU1FTlQQBRIbChdTVEFURV9FTElNSU5BVElPTl9DSEVDSxAGEhMKD1NUQVRF",
            "X0dBTUVfT1ZFUhAHKn0KDUluc3RhbmNlU3RhdGUSFAoQSU5TVEFOQ0VfV0FJ",
            "VElORxAAEh8KG0lOU1RBTkNFX0JBVFRMRV9JTl9QUk9HUkVTUxABEhwKGElO",
            "U1RBTkNFX0JBVFRMRV9GSU5JU0hFRBACEhcKE0lOU1RBTkNFX1NFVFRMRU1F",
            "TlQQAyqzAQoLUGxheWVyU3RhdGUSEgoOUExBWUVSX1dBSVRJTkcQABIZChVQ",
            "TEFZRVJfU0VMRUNUSU5HX0JVRkYQARIbChdQTEFZRVJfUkVDRUlWSU5HX0hF",
            "Uk9FUxACEhkKFVBMQVlFUl9GUkVFX09QRVJBVElPThADEhAKDFBMQVlFUl9S",
            "RUFEWRAEEhQKEFBMQVlFUl9JTl9CQVRUTEUQBRIVChFQTEFZRVJfRUxJTUlO",
            "QVRFRBAGKlUKDEFkUmV3YXJkVHlwZRIYChRCQVRUTEVfQURSRVdBUkRfTk9O",
            "RRAAEhYKEkJBVFRMRV9TVVBQTFlfRFJPUBABEhMKD0JBVFRMRV9CTEVTU0lO",
            "RxACKmAKCUdhY2hhVHlwZRITCg9HYWNoYVR5cGVfRXF1aXAQABITCg9HYWNo",
            "YVR5cGVfU2tpbGwQARIVChFHYWNoYVR5cGVfSGFsbG93cxACEhIKDkdhY2hh",
            "VHlwZV9QZXRzEAMqhgEKCUVxdWlwVHlwZRIOCgpFcXVpcF9OT05FEAASEAoM",
            "RXF1aXBfQnJhY2VyEAESEAoMRXF1aXBfSGVsbWV0EAISDwoLRXF1aXBfU2hv",
            "ZXMQAxIPCgtFcXVpcF9QYW50cxAEEhAKDEVxdWlwX0dsb3ZlcxAFEhEKDUVx",
            "dWlwX0Nsb3RoZXMQBio5Cg9Ecm9wRXhwcmVzc1R5cGUSEgoORXhwcmVzc19O",
            "b3JtYWwQABISCg5FeHByZXNzX1N0YWdlcxABKrcBCgtRdWFsaXR5VHlwZRIQ",
            "CgxRdWFsaXR5X0dyZXkQABIRCg1RdWFsaXR5X0dyZWVuEAESEAoMUXVhbGl0",
            "eV9CbHVlEAISEgoOUXVhbGl0eV9QdXJwbGUQAxIQCgxRdWFsaXR5X0dvbGQQ",
            "BBISCg5RdWFsaXR5X09yYW5nZRAFEg8KC1F1YWxpdHlfUmVkEAYSEwoPUXVh",
            "bGl0eV9EZWVwUmVkEAcSEQoNUXVhbGl0eV9BcmdzOBAIKs4CCghNYWlsVHlw",
            "ZRIWChJNYWlsVHlwZV9Ob25SZXdhcmQQABITCg9NYWlsVHlwZV9SZXdhcmQQ",
            "ARIZChVNYWlsVHlwZV9Ob3RpZnlSZXdhcmQQAhIXChNNYWlsVHlwZV9QbGF5",
            "ZXJBdXRoEAMSGwoXTWFpbFR5cGVfUGxheWVyQXV0aEluZm8QBBIdChlNYWls",
            "VHlwZV9QbGF5ZXJNYWlsUmVtb3ZlEAUSFAoQTWFpbFR5cGVfR2lmdFJNQhAG",
            "EiEKHU1haWxUeXBlX0dsb2JhbEFjdGl2aXR5UGxheWVyEAcSDwoLTWFpbFR5",
            "cGVfQUQQCBIcChhNYWlsVHlwZV9HdWlsZE9mZmxpbmVNc2cQCRIXChNNYWls",
            "VHlwZV9RdWVzdEF3YXJkEAoSJAogTWFpbFR5cGVfQWN0aXZpdHlfT3BlblNl",
            "cnZlcl9FbmQQCypdCg1NYWlsU3RhdGVUeXBlEhgKFE1haWxTdGF0ZVR5cGVf",
            "VW5SZWFkEAASFgoSTWFpbFN0YXRlVHlwZV9SZWFkEAESGgoWTWFpbFN0YXRl",
            "VHlwZV9Hb3RBd2FyZBACKmMKDk1haWxSZWFzb25UeXBlEhkKFU1haWxSZWFz",
            "b25UeXBlX1Rlc3RHTRAAEhsKF01haWxSZWFzb25UeXBlX0dNUmVtb3RlEAES",
            "GQoVTWFpbFJlYXNvblR5cGVfU3lzdGVtEAIqewoQR3VpZGVUcmlnZ2VyVHlw",
            "ZRIICgROb25lEAASDQoJTmV3UGxheWVyEAESCgoGT3BlblVpEAISCwoHQ2xv",
            "c2VVaRADEhAKDFVubG9ja0Z1bmNJZBAEEhIKDlBhc3NlZE1haW5MaW5lEAUS",
            "DwoLRmluaXNoR3VpZGUQBirBAgoTTmV3R3VpZGVUcmlnZ2VyVHlwZRIeChpO",
            "ZXdHdWlkZVRyaWdnZXJUeXBlX05vcm1hbBAAEicKI05ld0d1aWRlVHJpZ2dl",
            "clR5cGVfR2V0Q29tcGxldGVUYXNrEAESJQohTmV3R3VpZGVUcmlnZ2VyVHlw",
            "ZV9NaXNzaW9uRmluaXNoEAISIwofTmV3R3VpZGVUcmlnZ2VyVHlwZV9OZXdH",
            "dWlsZEVuZBADEiMKH05ld0d1aWRlVHJpZ2dlclR5cGVfSW5zdGFuY2VFbmQQ",
            "BBIiCh5OZXdHdWlkZVRyaWdnZXJUeXBlX0NyZWF0ZU5hbWUQBRIoCiROZXdH",
            "dWlkZVRyaWdnZXJUeXBlX0Nvb3BlcmF0aW9uU3RhZ2UQBhIiCh5OZXdHdWlk",
            "ZVRyaWdnZXJUeXBlX0xvdHRlcnlFbmQQBypNCg9OZXdHdWlkZUZ1blR5cGUS",
            "GgoWTmV3R3VpZGVGdW5UeXBlX05vcm1hbBAAEh4KGk5ld0d1aWRlRnVuVHlw",
            "ZV9DaGFuZ2VOYW1lEAEqeAoMU2lnbkluVG9UeXBlEhUKEVNpZ25JblRvVHlw",
            "ZV9Ob25lEAASGQoVU2lnbkluVG9UeXBlX1NldmVuRGF5EAESGQoVU2lnbklu",
            "VG9UeXBlX0V2ZXJ5RGF5EAISGwoXU2lnbkluVG9UeXBlX01vbnRoU3RhZ2UQ",
            "AypaCgxSZXdhcmRTdGF0dXMSFgoSUmV3YXJkU3RhdHVzX0RvaW5nEAASFwoT",
            "UmV3YXJkU3RhdHVzX0ZpbmlzaBABEhkKFVJld2FyZFN0YXR1c19SZWNlaXZl",
            "ZBACKlgKClJld2FyZFR5cGUSFwoTUkVXQVJEX1RZUEVfVU5LTk9XThAAEhQK",
            "EFJFV0FSRF9UWVBFX1JBTksQARIbChdSRVdBUkRfVFlQRV9TRUFTT05fUkFO",
            "SxACKlIKCEl0ZW1UeXBlEg8KC1VuYXZhaWxhYmxlEAASCAoERHJvcBABEhAK",
            "DEhhbmdVcFJld2FyZBACEg0KCUNob29zZUJveBADEgoKBkRldnJpcxAEKkAK",
            "FUl0ZW1TdWJUeXBlX0Nob29zZUJveBISCg5DaG9vc2VCb3hfT25jZRAAEhMK",
            "D0Nob29zZUJveF9NdWx0aRABKjMKEEl0ZW1TdWJUeXBlX0Ryb3ASDgoKRHJv",
            "cF9GaXhlZBAAEg8KC0Ryb3BfUmFuZG9tEAEqWgoSSXRlbVN1YlR5cGVfRGV2",
            "cmlzEg8KC0RldnJpc19Ta2luEAASEgoORGV2cmlzX0ZpcmVhcm0QARIPCgtE",
            "ZXZyaXNfV2FsbBACEg4KCkRldnJpc19QZXQQAyodCgtCYWdJdGVtVHlwZRIO",
            "CgpOb3JtYWxJdGVtEAAq0AEKE1N5c3RlbVNob3dFcnJvclR5cGUSHAoYU3lz",
            "dGVtU2hvd0Vycm9yVHlwZV9Ob25lEAASHAoYU3lzdGVtU2hvd0Vycm9yVHlw",
            "ZV9LaWNrEAESHwobU3lzdGVtU2hvd0Vycm9yVHlwZV9SZXBsYWNlEAISHwob",
            "U3lzdGVtU2hvd0Vycm9yVHlwZV9DaGF0QmFuEAMSHAoYU3lzdGVtU2hvd0Vy",
            "cm9yVHlwZV9UaXBzEAQSHQoZU3lzdGVtU2hvd0Vycm9yVHlwZV9Mb2dpbhAF",
            "KrABCg1HaWZ0UGFja3NUeXBlEhYKEkdpZnRQYWNrc1R5cGVfTm9uZRAAEhcK",
            "E0dpZnRQYWNrc1R5cGVfTGltaXQQARIYChRHaWZ0UGFja3NUeXBlX1dlZWts",
            "eRACEhsKF0dpZnRQYWNrc1R5cGVfRGFpbHlQbHVzEAMSGgoWR2lmdFBhY2tz",
            "VHlwZV9XZWVrQ2FyZBAEEhsKF0dpZnRQYWNrc1R5cGVfUmVjb21tZW5kEAUq",
            "fwoQR2lmdFBhY2tzQnV5VHlwZRIYChRHaWZ0UGFja3NCdXlUeXBlX0FkdhAA",
            "EhgKFEdpZnRQYWNrc0J1eVR5cGVfUm1iEAESGQoVR2lmdFBhY2tzQnV5VHlw",
            "ZV9GcmVlEAISHAoYR2lmdFBhY2tzQnV5VHlwZV9EaWFtb25kEAMqjQEKCkNo",
            "YXJnZVR5cGUSEwoPQ2hhcmdlVHlwZV9Ob25lEAASFgoSQ2hhcmdlVHlwZV9E",
            "aWFtb25kEAESHgoaQ2hhcmdlVHlwZV9Nb250aENhcmROb3JtYWwQAhIdChlD",
            "aGFyZ2VUeXBlX01vbnRoQ2FyZFN1cGVyEAMSEwoPQ2hhcmdlVHlwZV9JdGVt",
            "EAQqdwoTQ29tbW9uQm94UmV3YXJkVHlwZRIYChRDb21tb25Cb3hSZXdhcmRf",
            "Tm9uZRAAEiAKHENvbW1vbkJveFJld2FyZF9EYWlseU1pc3Npb24QARIkCiBD",
            "b21tb25Cb3hSZXdhcmRfU2V2ZW5EYXlBY3Rpdml0eRACKl4KDkJhdHRsZVBh",
            "c3NUeXBlEhcKE0JhdHRsZVBhc3NUeXBlX05PTkUQABIXChNCYXR0bGVQYXNz",
            "VHlwZV9CQVNFEAESGgoWQmF0dGxlUGFzc1R5cGVfQURWQU5DRRACKnoKFUJh",
            "dHRsZVBhc3NNaXNzaW9uVHlwZRIeChpCYXR0bGVQYXNzTWlzc2lvblR5cGVf",
            "Tk9ORRAAEh8KG0JhdHRsZVBhc3NNaXNzaW9uVHlwZV9EQUlMWRABEiAKHEJh",
            "dHRsZVBhc3NNaXNzaW9uVHlwZV9XRUVLTFkQAipbCgpBd2FyZFN0YXRlEhoK",
            "FkF3YXJkU3RhdGVfVU5BVkFJTEFCTEUQABIYChRBd2FyZFN0YXRlX0FWQUlM",
            "QUJMRRABEhcKE0F3YXJkU3RhdGVfUkVDRUlWRUQQAipjCgxBY3Rpdml0eVR5",
            "cGUSGwoXQWN0aXZpdHlUeXBlX0JhdHRsZVBhc3MQABIaChZBY3Rpdml0eVR5",
            "cGVfU2V2ZW5UYXNrEAESGgoWQWN0aXZpdHlUeXBlX1NldmVuU2lnbhACKl4K",
            "DkFjdGl2aXR5U3RhdHVzEhoKFkFjdGl2aXR5U3RhdHVzX05PX09QRU4QABIY",
            "ChRBY3Rpdml0eVN0YXR1c19TVEFSVBABEhYKEkFjdGl2aXR5U3RhdHVzX0VO",
            "RBACKtgFChRJdGVtQWNxdWlzaXRpb25TdGF0ZRIdChlJdGVtQWNxdWlzaXRp",
            "b25TdGF0ZV9OT05FEAASIAocSXRlbUFjcXVpc2l0aW9uU3RhdGVfRGlhbW9u",
            "ZBABEh0KGUl0ZW1BY3F1aXNpdGlvblN0YXRlX1N0YXIQAhIgChxJdGVtQWNx",
            "dWlzaXRpb25TdGF0ZV9Fc3NlbmNlEAMSJAogSXRlbUFjcXVpc2l0aW9uU3Rh",
            "dGVfQWlyUHVyaWZpZXIQBBImCiJJdGVtQWNxdWlzaXRpb25TdGF0ZV9VcGdy",
            "YWRlVGlja2V0EAUSKAokSXRlbUFjcXVpc2l0aW9uU3RhdGVfRHVwbGljYXRl",
            "VGlja2V0EAYSHwobSXRlbUFjcXVpc2l0aW9uU3RhdGVfU2hvdmVsEAcSIgoe",
            "SXRlbUFjcXVpc2l0aW9uU3RhdGVfRHJhZ29uRWdnEAgSIwofSXRlbUFjcXVp",
            "c2l0aW9uU3RhdGVfTWFnaWNDbG90aBAJEh8KG0l0ZW1BY3F1aXNpdGlvblN0",
            "YXRlX0NoaXNlbBAKEjIKLkl0ZW1BY3F1aXNpdGlvblN0YXRlX0RyYWdvbkNv",
            "bXBvdW5kU2xvdFVwZ3JhZGUQCxIlCiFJdGVtQWNxdWlzaXRpb25TdGF0ZV9T",
            "bGltZVJlY3VyaXQQDBIyCi5JdGVtQWNxdWlzaXRpb25TdGF0ZV9EcmFnb25G",
            "YWN0b3J5TGV2ZWxJdGVtUm9iEA0SLAooSXRlbUFjcXVpc2l0aW9uU3RhdGVf",
            "RHJhZ29uQnVpbGRSZXNvdXJjZRAOEioKJkl0ZW1BY3F1aXNpdGlvblN0YXRl",
            "X0RyYWdvbkZhY3RvcnlUZWNoEA8SKQolSXRlbUFjcXVpc2l0aW9uU3RhdGVf",
            "R3VpbGRGYWN0b3J5SXRlbRAQEicKI0l0ZW1BY3F1aXNpdGlvblN0YXRlX0Zh",
            "c2hpb25CVFNJdGVtEBEqSAoPSXJvblNvdXJjZUFEeXBlEhgKFElyb25Tb3Vy",
            "Y2VBRHlwZV9OT05FEAASGwoXSXJvblNvdXJjZUFEeXBlX09mZkxpbmUQASp3",
            "Cg1HdWlsZFBvc2l0aW9uEhgKFEd1aWxkUG9zaXRpb25fTm9ybWFsEAASFwoT",
            "R3VpbGRQb3NpdGlvbl9FbGl0ZRABEhYKEkd1aWxkUG9zaXRpb25fVmljZRAC",
            "EhsKF0d1aWxkUG9zaXRpb25fUHJlc2lkZW50EAMq2gQKCEd1aWxkT3B0EhEK",
            "DUd1aWxkT3B0X05vbmUQABIVChFHdWlsZE9wdF9FZGl0SWNvbhABEhUKEUd1",
            "aWxkT3B0X0VkaXROYW1lEAISFwoTR3VpbGRPcHRfRWRpdE5vdGljZRADEhYK",
            "Ekd1aWxkT3B0X0VkaXRBcHBseRAEEhkKFUd1aWxkT3B0X0FwcGx5TWdyTGlz",
            "dBAFEhoKFkd1aWxkT3B0X0FwcGx5TWdyQWdyZWUQBhIbChdHdWlsZE9wdF9B",
            "cHBseU1nclJlZnVzZRAHEhoKFkd1aWxkT3B0X01lbWJlck1nckxpc3QQCBIf",
            "ChtHdWlsZE9wdF9NZW1iZXJNZ3JQcmVzaWRlbnQQCRIaChZHdWlsZE9wdF9N",
            "ZW1iZXJNZ3JWaWNlEAoSGwoXR3VpbGRPcHRfTWVtYmVyTWdyRWxpdGUQCxIc",
            "ChhHdWlsZE9wdF9NZW1iZXJNZ3JOb3JtYWwQDBIaChZHdWlsZE9wdF9NZW1i",
            "ZXJNZ3JLaWNrEA0SEQoNR3VpbGRPcHRfUXVpdBAOEhQKEEd1aWxkT3B0X0Rp",
            "c21pc3MQDxIUChBHdWlsZE9wdF9JbXBlYWNoEBASHAoYR3VpbGRPcHRfU2Vu",
            "ZFdvcmxkSW52aXRlEBESHQoZR3VpbGRPcHRfU2VuZFBsYXllckludml0ZRAS",
            "Eh0KGUd1aWxkT3B0X0JhcmdhaW5pbmdOb3RpY2UQExIdChlHdWlsZE9wdF9F",
            "ZGl0QW5ub3VuY2VtZW50EBQSHgoaR3VpbGRPcHRfQXBwbHlNZ3JSZWplY3RB",
            "bGwQFSqaAgoMR3VpbGRMb2dUeXBlEhYKEkd1aWxkTG9nVHlwZV9MZWF2ZRAA",
            "EhUKEUd1aWxkTG9nVHlwZV9Kb2luEAESGQoVR3VpbGRMb2dUeXBlX0ltcGVh",
            "Y2gxEAISGQoVR3VpbGRMb2dUeXBlX0ltcGVhY2gyEAMSGAoUR3VpbGRMb2dU",
            "eXBlX01nclZpY2UQBBIaChZHdWlsZExvZ1R5cGVfTWdyTm9ybWFsEAUSFwoT",
            "R3VpbGRMb2dUeXBlX0RvbmF0ZRAGEhoKFkd1aWxkTG9nVHlwZV9QcmVzaWRl",
            "bnQQBxIcChhHdWlsZExvZ1R5cGVfQXV0b0NoYW5nZTEQCBIcChhHdWlsZExv",
            "Z1R5cGVfQXV0b0NoYW5nZTIQCSq2AgoPR3VpbGRVcGRhdGVUeXBlEhsKF0d1",
            "aWxkVXBkYXRlVHlwZV9VTktOT1dOEAASIAocR3VpbGRVcGRhdGVUeXBlX0pP",
            "SU5FRF9HVUlMRBABEh4KGkd1aWxkVXBkYXRlVHlwZV9MRUZUX0dVSUxEEAIS",
            "JQohR3VpbGRVcGRhdGVUeXBlX0tJQ0tFRF9GUk9NX0dVSUxEEAMSIwofR3Vp",
            "bGRVcGRhdGVUeXBlX0dVSUxEX0RJU01JU1NFRBAEEiQKIEd1aWxkVXBkYXRl",
            "VHlwZV9QT1NJVElPTl9DSEFOR0VEEAUSIgoeR3VpbGRVcGRhdGVUeXBlX0dV",
            "SUxEX0xFVkVMX1VQEAYSLgoqR3VpbGRVcGRhdGVUeXBlX0FQUExZX1BST0NF",
            "U1NFRF9PUl9FWFBJUkVEEAcqugEKFkd1aWxkQXBwbGljYXRpb25TdGF0dXMS",
            "HgoaQVBQTElDQVRJT05fU1RBVFVTX1VOS05PV04QABIeChpBUFBMSUNBVElP",
            "Tl9TVEFUVVNfUEVORElORxABEh8KG0FQUExJQ0FUSU9OX1NUQVRVU19BUFBS",
            "T1ZFRBACEh8KG0FQUExJQ0FUSU9OX1NUQVRVU19SRUpFQ1RFRBADEh4KGkFQ",
            "UExJQ0FUSU9OX1NUQVRVU19FWFBJUkVEEAQqmgEKHUd1aWxkU3lzdGVtSW50",
            "ZXJuYWxBY3Rpb25UeXBlEhsKF0lOVEVSTkFMX0FDVElPTl9VTktOT1dOEAAS",
            "KQolSU5URVJOQUxfQUNUSU9OX0RJU01JU1NfR1VJTERfTUVNQkVSUxABEjEK",
            "LUlOVEVSTkFMX0FDVElPTl9HVUlMRF9MRVZFTF9VUF9OT1RJRllfTUVNQkVS",
            "UxACKkgKCENoYXRUeXBlEhIKDkNoYXRUeXBlX1dvcmxkEAASEgoOQ2hhdFR5",
            "cGVfR3VpbGQQARIUChBDaGF0VHlwZV9Qcml2YXRlEAIqWAoLQ2hhdE1zZ1R5",
            "cGUSFAoQQ2hhdE1zZ1R5cGVfVGV4dBAAEhsKF0NoYXRNc2dUeXBlX0d1aWxk",
            "SW52aXRlEAESFgoSQ2hhdE1zZ1R5cGVfU3lzdGVtEAIqYAoWSW5CYXR0bGVT",
            "aG93V2luZG93VHlwZRIXChNVSV9TZWxlY3RfTm9ybWFsUG9wEAASFQoRVUlf",
            "U2VsZWN0X1Nob3BQb3AQARIWChJVSV9TZWxlY3RfTHVja3lQb3AQAirSAQoY",
            "R2xvYmFsQnJvYWRjYXN0RXZlbnRUeXBlEiEKHUdsb2JhbEJyb2FkY2FzdEV2",
            "ZW50VHlwZV9Ob25lEAASMwovR2xvYmFsQnJvYWRjYXN0RXZlbnRUeXBlX0Jh",
            "blBsYXllckd1aWxkQm9zc1JhbmsQARIqCiZHbG9iYWxCcm9hZGNhc3RFdmVu",
            "dFR5cGVfQ2hnUGxheWVyTmFtZRACEjIKLkdsb2JhbEJyb2FkY2FzdEV2ZW50",
            "VHlwZV9DaGdHdWlsZE5hbWVBbmROb3RpY2UQAypCCgtTaWduSW5TdGF0ZRIM",
            "CghOb1NpZ25JbhAAEhAKDFNpZ25lZEluTm9SZRABEhMKD1NpZ25lZEluQW5k",
            "UmVlZBACKnEKEVBheW1lbnRNb2R1bGVUeXBlEggKBFNob3AQABIPCgtGaXJz",
            "dENoYXJnZRABEg8KC01vbnRobHlDYXJkEAISDgoKR3JhZGVkRnVuZBADEhIK",
            "Dk1vbnRobHlDYXJkTmV3EAQSDAoIVGltZVNob3AQBSphCgxNaXNzaW9uU3Rh",
            "dGUSGwoXTWlzc2lvblN0YXRlX1VuZmluaXNoZWQQABIZChVNaXNzaW9uU3Rh",
            "dGVfRmluaXNoZWQQARIZChVNaXNzaW9uU3RhdGVfUmVjZWl2ZWQQAiqOAQoL",
            "TWlzc2lvblR5cGUSFAoQTWlzc2lvblR5cGVfTWFpbhAAEhUKEU1pc3Npb25U",
            "eXBlX0RhaWx5EAESGAoUTWlzc2lvblR5cGVfU2V2ZW5EYXkQAhIbChdNaXNz",
            "aW9uVHlwZV9BY2hpZXZlbWVudBADEhsKF01pc3Npb25UeXBlX0hlYXZlbmx5",
            "RGFvEAQqQgoRREJHYWNoYUJvbnVzU3RhdGUSCgoGTm9QdWxsEAASDgoKUHVs",
            "bGVkTm9SZRABEhEKDVB1bGxlZEFuZFJlZWQQAirDAQoQU2VydmVyUmVzdWx0",
            "Q29kZRIVChFSZXN1bHRDb2RlX1NVQ0NFUxAAEhQKEFJlc3VsdENvZGVfRXJy",
            "b3IQARIiCh5SZXN1bHRDb2RlX0N1cnJlbmN5X05vdF9Fbm91Z2gQAhIeChpS",
            "ZXN1bHRDb2RlX0l0ZW1fTm90X0Vub3VnaBADEhoKFlJlc3VsdENvZGVfUEFS",
            "QU1fRVJST1IQBBIiCh5SZXN1bHRDb2RlX0NPTkZJR19OT1RfQ09OVEFJTlMQ",
            "BSo0ChBGdW5jUHJldmlld3N0YXRlEggKBExvY2sQABIJCgVBd2FyZBABEgsK",
            "B0F3YXJkZWQQAkI1WidsaXRlZnJhbWUvaW50ZXJuYWwvY29tbW9uL3Byb3Rv",
            "cy9wdWJsaWOqAglHYW1lLkNvcmViBnByb3RvMw=="));
      descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
          new pbr::FileDescriptor[] { },
          new pbr::GeneratedClrTypeInfo(new[] {typeof(global::Game.Core.LoginByType), typeof(global::Game.Core.MoneyType), typeof(global::Game.Core.AwardItemType), typeof(global::Game.Core.FuncLockType), typeof(global::Game.Core.SceneDropType), typeof(global::Game.Core.RedDotType), typeof(global::Game.Core.EGenderType), typeof(global::Game.Core.AttributeType), typeof(global::Game.Core.MonsterType), typeof(global::Game.Core.BattleType), typeof(global::Game.Core.BattleState), typeof(global::Game.Core.InstanceState), typeof(global::Game.Core.PlayerState), typeof(global::Game.Core.AdRewardType), typeof(global::Game.Core.GachaType), typeof(global::Game.Core.EquipType), typeof(global::Game.Core.DropExpressType), typeof(global::Game.Core.QualityType), typeof(global::Game.Core.MailType), typeof(global::Game.Core.MailStateType), typeof(global::Game.Core.MailReasonType), typeof(global::Game.Core.GuideTriggerType), typeof(global::Game.Core.NewGuideTriggerType), typeof(global::Game.Core.NewGuideFunType), typeof(global::Game.Core.SignInToType), typeof(global::Game.Core.RewardStatus), typeof(global::Game.Core.RewardType), typeof(global::Game.Core.ItemType), typeof(global::Game.Core.ItemSubType_ChooseBox), typeof(global::Game.Core.ItemSubType_Drop), typeof(global::Game.Core.ItemSubType_Devris), typeof(global::Game.Core.BagItemType), typeof(global::Game.Core.SystemShowErrorType), typeof(global::Game.Core.GiftPacksType), typeof(global::Game.Core.GiftPacksBuyType), typeof(global::Game.Core.ChargeType), typeof(global::Game.Core.CommonBoxRewardType), typeof(global::Game.Core.BattlePassType), typeof(global::Game.Core.BattlePassMissionType), typeof(global::Game.Core.AwardState), typeof(global::Game.Core.ActivityType), typeof(global::Game.Core.ActivityStatus), typeof(global::Game.Core.ItemAcquisitionState), typeof(global::Game.Core.IronSourceADype), typeof(global::Game.Core.GuildPosition), typeof(global::Game.Core.GuildOpt), typeof(global::Game.Core.GuildLogType), typeof(global::Game.Core.GuildUpdateType), typeof(global::Game.Core.GuildApplicationStatus), typeof(global::Game.Core.GuildSystemInternalActionType), typeof(global::Game.Core.ChatType), typeof(global::Game.Core.ChatMsgType), typeof(global::Game.Core.InBattleShowWindowType), typeof(global::Game.Core.GlobalBroadcastEventType), typeof(global::Game.Core.SignInState), typeof(global::Game.Core.PaymentModuleType), typeof(global::Game.Core.MissionState), typeof(global::Game.Core.MissionType), typeof(global::Game.Core.DBGachaBonusState), typeof(global::Game.Core.ServerResultCode), typeof(global::Game.Core.FuncPreviewstate), }, null, null));
    }
    #endregion

  }
  #region Enums
  /// <summary>
  ///=====================平台能力开始================================
  /// </summary>
  public enum LoginByType {
    [pbr::OriginalName("LoginByType_Other")] Other = 0,
    [pbr::OriginalName("LoginByType_QQGameCenter")] QqgameCenter = 1,
    [pbr::OriginalName("LoginByType_WeChatGameCenter")] WeChatGameCenter = 2,
  }

  /// <summary>
  ///货币类型
  /// </summary>
  public enum MoneyType {
    /// <summary>
    ///未知的类型
    /// </summary>
    [pbr::OriginalName("MoneyType_None")] None = 0,
    /// <summary>
    ///金币
    /// </summary>
    [pbr::OriginalName("MoneyType_Gold")] Gold = 1,
    /// <summary>
    ///钻石
    /// </summary>
    [pbr::OriginalName("MoneyType_Diamond")] Diamond = 2,
    /// <summary>
    ///体力
    /// </summary>
    [pbr::OriginalName("MoneyType_Power")] Power = 3,
    /// <summary>
    ///经验
    /// </summary>
    [pbr::OriginalName("MoneyType_Exp")] Exp = 4,
  }

  /// <summary>
  ///掉落类型
  /// </summary>
  public enum AwardItemType {
    /// <summary>
    ///货币
    /// </summary>
    [pbr::OriginalName("AwardItemType_Money")] Money = 0,
    /// <summary>
    ///普通物品
    /// </summary>
    [pbr::OriginalName("AwardItemType_Item")] Item = 1,
    /// <summary>
    ///装备
    /// </summary>
    [pbr::OriginalName("AwardItemType_Equip")] Equip = 2,
    /// <summary>
    ///技能
    /// </summary>
    [pbr::OriginalName("AwardItemType_Skill")] Skill = 3,
    /// <summary>
    ///宝石
    /// </summary>
    [pbr::OriginalName("AwardItemType_Gem")] Gem = 4,
  }

  /// <summary>
  ///功能解锁状态
  /// </summary>
  public enum FuncLockType {
    /// <summary>
    ///未解锁
    /// </summary>
    [pbr::OriginalName("FuncLockType_Lock")] Lock = 0,
    /// <summary>
    ///解锁
    /// </summary>
    [pbr::OriginalName("FuncLockType_UnLock")] UnLock = 1,
    /// <summary>
    ///关闭
    /// </summary>
    [pbr::OriginalName("FuncLockType_Close")] Close = 2,
  }

  /// <summary>
  ///场景掉落类型
  /// </summary>
  public enum SceneDropType {
    /// <summary>
    ///小怪
    /// </summary>
    [pbr::OriginalName("SceneDropType_Monster")] Monster = 0,
    /// <summary>
    ///彩虹马
    /// </summary>
    [pbr::OriginalName("SceneDropType_Horse")] Horse = 1,
  }

  /// <summary>
  ///红点枚举
  /// </summary>
  public enum RedDotType {
    /// <summary>
    /// </summary>
    [pbr::OriginalName("RedDotType_None")] None = 0,
    /// <summary>
    ///技能红点		
    /// </summary>
    [pbr::OriginalName("RedDotType_Skill")] Skill = 1,
    /// <summary>
    ///邮件红点		
    /// </summary>
    [pbr::OriginalName("RedDotType_Mail")] Mail = 2,
    /// <summary>
    ///好友红点
    /// </summary>
    [pbr::OriginalName("RedDotType_Friend")] Friend = 3,
    /// <summary>
    ///功能预告
    /// </summary>
    [pbr::OriginalName("RedDotType_FuncPreview")] FuncPreview = 4,
    /// <summary>
    ///天道修为
    /// </summary>
    [pbr::OriginalName("RedDotType_HeavenlyDao")] HeavenlyDao = 5,
    /// <summary>
    ///每日特惠礼包
    /// </summary>
    [pbr::OriginalName("RedDotType_DailyPlusGift")] DailyPlusGift = 6,
    /// <summary>
    ///放在最后
    /// </summary>
    [pbr::OriginalName("RedDotType_MAX")] Max = 7,
  }

  /// <summary>
  ///性别枚举
  /// </summary>
  public enum EGenderType {
    /// <summary>
    ///神秘
    /// </summary>
    [pbr::OriginalName("Default")] Default = 0,
    /// <summary>
    ///男性
    /// </summary>
    [pbr::OriginalName("Man")] Man = 1,
    /// <summary>
    ///女性
    /// </summary>
    [pbr::OriginalName("Woman")] Woman = 2,
  }

  /// <summary>
  ///属性
  /// </summary>
  public enum AttributeType {
    /// <summary>
    ///空
    /// </summary>
    [pbr::OriginalName("AttributeType_NONE")] None = 0,
    /// <summary>
    ///基础攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_Attack")] Attack = 1,
    /// <summary>
    ///血量
    /// </summary>
    [pbr::OriginalName("AttributeType_Hp")] Hp = 2,
    /// <summary>
    ///基础防御
    /// </summary>
    [pbr::OriginalName("AttributeType_Def")] Def = 3,
    /// <summary>
    ///移速
    /// </summary>
    [pbr::OriginalName("AttributeType_MoveSpeed")] MoveSpeed = 4,
    /// <summary>
    ///枪械攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_GunAtk")] GunAtk = 5,
    /// <summary>
    ///物理攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_PhysicsAtk")] PhysicsAtk = 6,
    /// <summary>
    ///冰攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_IceAtk")] IceAtk = 7,
    /// <summary>
    ///电攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_ElectricityAtk")] ElectricityAtk = 8,
    /// <summary>
    ///火攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_FireAtk")] FireAtk = 9,
    /// <summary>
    ///能量攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_EnergyAtk")] EnergyAtk = 10,
    /// <summary>
    ///风攻击力
    /// </summary>
    [pbr::OriginalName("AttributeType_WindAtk")] WindAtk = 11,
    /// <summary>
    ///穿透值
    /// </summary>
    [pbr::OriginalName("AttributeType_Penetrate")] Penetrate = 12,
    /// <summary>
    ///命中
    /// </summary>
    [pbr::OriginalName("AttributeType_HitPre")] HitPre = 20,
    /// <summary>
    ///闪避
    /// </summary>
    [pbr::OriginalName("AttributeType_DodgePre")] DodgePre = 21,
    /// <summary>
    ///基础攻击百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_AttackPre")] AttackPre = 22,
    /// <summary>
    ///血量百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_HpPre")] HpPre = 23,
    /// <summary>
    ///基础抗百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_DefPre")] DefPre = 24,
    /// <summary>
    ///移动速度百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_MoveSpeedPre")] MoveSpeedPre = 25,
    /// <summary>
    ///暴击率
    /// </summary>
    [pbr::OriginalName("AttributeType_CriticalPre")] CriticalPre = 26,
    /// <summary>
    ///暴击倍数
    /// </summary>
    [pbr::OriginalName("AttributeType_CriticalMultiplePre")] CriticalMultiplePre = 27,
    /// <summary>
    ///暴击抵抗
    /// </summary>
    [pbr::OriginalName("AttributeType_CriticalResistPre")] CriticalResistPre = 28,
    /// <summary>
    ///输出伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_BaseDamagePre")] BaseDamagePre = 29,
    /// <summary>
    ///枪械伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_GunDamagePre")] GunDamagePre = 30,
    /// <summary>
    ///物理伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_PhysicsDamagePre")] PhysicsDamagePre = 31,
    /// <summary>
    ///冰伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_IceDamagePre")] IceDamagePre = 32,
    /// <summary>
    ///电伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_ElectricityDamagePre")] ElectricityDamagePre = 33,
    /// <summary>
    ///火伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_FireDamagePre")] FireDamagePre = 34,
    /// <summary>
    ///能量伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_EnergyDamagePre")] EnergyDamagePre = 35,
    /// <summary>
    ///风伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_WindDamagePre")] WindDamagePre = 36,
    /// <summary>
    ///穿透值百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_PenetratePre")] PenetratePre = 37,
    /// <summary>
    ///枪械抵抗
    /// </summary>
    [pbr::OriginalName("AttributeType_GunDefPre")] GunDefPre = 38,
    /// <summary>
    ///物理抵抗
    /// </summary>
    [pbr::OriginalName("AttributeType_PhysicsDefPre")] PhysicsDefPre = 39,
    /// <summary>
    ///冰抵抗
    /// </summary>
    [pbr::OriginalName("AttributeType_IceDefPre")] IceDefPre = 40,
    /// <summary>
    ///电抵抗百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_ElectricityDefPre")] ElectricityDefPre = 41,
    /// <summary>
    ///火抵抗百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_FireDefPre")] FireDefPre = 42,
    /// <summary>
    ///能量抵抗百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_EnergyDefPre")] EnergyDefPre = 43,
    /// <summary>
    ///风抵抗百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_WindDefPre")] WindDefPre = 44,
    /// <summary>
    ///地面单位伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_GroundDemageAddPre")] GroundDemageAddPre = 45,
    /// <summary>
    ///空中单位伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_MidairDamageAddPre")] MidairDamageAddPre = 46,
    /// <summary>
    ///近战单位伤害增加
    /// </summary>
    [pbr::OriginalName("AttributeType_NearDamageAddPre")] NearDamageAddPre = 47,
    /// <summary>
    ///远程单位伤害增幅
    /// </summary>
    [pbr::OriginalName("AttributeType_LongDisDamageAddPre")] LongDisDamageAddPre = 48,
    /// <summary>
    ///小怪的伤害加成
    /// </summary>
    [pbr::OriginalName("AttributeType_SmallMonsterDamageAddPre")] SmallMonsterDamageAddPre = 49,
    /// <summary>
    ///精英伤害加成
    /// </summary>
    [pbr::OriginalName("AttributeType_EliteMonsterDamageAddPre")] EliteMonsterDamageAddPre = 50,
    /// <summary>
    ///Boss伤害加成
    /// </summary>
    [pbr::OriginalName("AttributeType_BossMonsterDamageAddPre")] BossMonsterDamageAddPre = 51,
    /// <summary>
    ///减少枪械CD
    /// </summary>
    [pbr::OriginalName("AttributeType_DecreaseGunCD")] DecreaseGunCd = 52,
    /// <summary>
    ///减少技能CD
    /// </summary>
    [pbr::OriginalName("AttributeType_DecreaseSkillCD")] DecreaseSkillCd = 53,
    /// <summary>
    ///金币加成
    /// </summary>
    [pbr::OriginalName("AttributeType_GoldBonusPre")] GoldBonusPre = 80,
    /// <summary>
    ///经验加成
    /// </summary>
    [pbr::OriginalName("AttributeType_ExpBonusPre")] ExpBonusPre = 81,
    /// <summary>
    ///战币加成
    /// </summary>
    [pbr::OriginalName("AttributeType_WarCoinPre")] WarCoinPre = 82,
    /// <summary>
    ///弹夹数量
    /// </summary>
    [pbr::OriginalName("AttributeType_Magazine")] Magazine = 83,
    /// <summary>
    ///弹夹数量百分比
    /// </summary>
    [pbr::OriginalName("AttributeType_MagazinePre")] MagazinePre = 84,
  }

  /// <summary>
  ///怪物的类型
  /// </summary>
  public enum MonsterType {
    /// <summary>
    ///无
    /// </summary>
    [pbr::OriginalName("MonsterType_None")] None = 0,
    /// <summary>
    ///小怪
    /// </summary>
    [pbr::OriginalName("MonsterType_Small")] Small = 1,
    /// <summary>
    ///精英
    /// </summary>
    [pbr::OriginalName("MonsterType_Advanced")] Advanced = 2,
    /// <summary>
    ///Boss
    /// </summary>
    [pbr::OriginalName("MonsterType_Boss")] Boss = 3,
  }

  /// <summary>
  ///战斗的类型或是副本类型
  /// </summary>
  public enum BattleType {
    /// <summary>
    ///主线的关卡
    /// </summary>
    [pbr::OriginalName("BattleType_Common")] Common = 0,
    /// <summary>
    ///主界面站桩的副本信息
    /// </summary>
    [pbr::OriginalName("BattleType_StandingPile")] StandingPile = 100,
  }

  /// <summary>
  /// 战斗状态枚举
  /// </summary>
  public enum BattleState {
    [pbr::OriginalName("STATE_NONE")] StateNone = 0,
    /// <summary>
    /// 回合开始（匹配对手）
    /// </summary>
    [pbr::OriginalName("STATE_ROUND_START")] StateRoundStart = 1,
    /// <summary>
    /// 准备阶段（包含buff选择、英雄生成、自由操作）
    /// </summary>
    [pbr::OriginalName("STATE_PREPARATION")] StatePreparation = 2,
    /// <summary>
    /// 战斗开始（所有玩家准备完毕或超时）
    /// </summary>
    [pbr::OriginalName("STATE_BATTLE_STARTING")] StateBattleStarting = 3,
    /// <summary>
    /// 战斗进行中（两场战斗都在进行）
    /// </summary>
    [pbr::OriginalName("STATE_BATTLE_IN_PROGRESS")] StateBattleInProgress = 4,
    /// <summary>
    /// 回合结算（两场战斗都结束）
    /// </summary>
    [pbr::OriginalName("STATE_ROUND_SETTLEMENT")] StateRoundSettlement = 5,
    /// <summary>
    /// 淘汰检查
    /// </summary>
    [pbr::OriginalName("STATE_ELIMINATION_CHECK")] StateEliminationCheck = 6,
    /// <summary>
    /// 游戏结束
    /// </summary>
    [pbr::OriginalName("STATE_GAME_OVER")] StateGameOver = 7,
  }

  public enum InstanceState {
    /// <summary>
    /// 等待战斗开始
    /// </summary>
    [pbr::OriginalName("INSTANCE_WAITING")] InstanceWaiting = 0,
    /// <summary>
    /// 战斗进行中
    /// </summary>
    [pbr::OriginalName("INSTANCE_BATTLE_IN_PROGRESS")] InstanceBattleInProgress = 1,
    /// <summary>
    /// 战斗结束，等待另一场
    /// </summary>
    [pbr::OriginalName("INSTANCE_BATTLE_FINISHED")] InstanceBattleFinished = 2,
    /// <summary>
    /// 结算中
    /// </summary>
    [pbr::OriginalName("INSTANCE_SETTLEMENT")] InstanceSettlement = 3,
  }

  public enum PlayerState {
    /// <summary>
    /// 等待中
    /// </summary>
    [pbr::OriginalName("PLAYER_WAITING")] PlayerWaiting = 0,
    /// <summary>
    /// 选择Buff中
    /// </summary>
    [pbr::OriginalName("PLAYER_SELECTING_BUFF")] PlayerSelectingBuff = 1,
    /// <summary>
    /// 接收英雄中
    /// </summary>
    [pbr::OriginalName("PLAYER_RECEIVING_HEROES")] PlayerReceivingHeroes = 2,
    /// <summary>
    /// 自由操作中
    /// </summary>
    [pbr::OriginalName("PLAYER_FREE_OPERATION")] PlayerFreeOperation = 3,
    /// <summary>
    /// 已准备
    /// </summary>
    [pbr::OriginalName("PLAYER_READY")] PlayerReady = 4,
    /// <summary>
    /// 战斗中
    /// </summary>
    [pbr::OriginalName("PLAYER_IN_BATTLE")] PlayerInBattle = 5,
    /// <summary>
    /// 已淘汰
    /// </summary>
    [pbr::OriginalName("PLAYER_ELIMINATED")] PlayerEliminated = 6,
  }

  /// <summary>
  /// 战斗结算额外奖励机制
  /// </summary>
  public enum AdRewardType {
    [pbr::OriginalName("BATTLE_ADREWARD_NONE")] BattleAdrewardNone = 0,
    /// <summary>
    /// 对战补给
    /// </summary>
    [pbr::OriginalName("BATTLE_SUPPLY_DROP")] BattleSupplyDrop = 1,
    /// <summary>
    /// 对战庇佑
    /// </summary>
    [pbr::OriginalName("BATTLE_BLESSING")] BattleBlessing = 2,
  }

  /// <summary>
  ///======================战斗相关结束================================
  ///抽卡类型
  /// </summary>
  public enum GachaType {
    /// <summary>
    ///装备
    /// </summary>
    [pbr::OriginalName("GachaType_Equip")] Equip = 0,
    /// <summary>
    ///技能
    /// </summary>
    [pbr::OriginalName("GachaType_Skill")] Skill = 1,
    /// <summary>
    ///圣物
    /// </summary>
    [pbr::OriginalName("GachaType_Hallows")] Hallows = 2,
    /// <summary>
    ///宠物
    /// </summary>
    [pbr::OriginalName("GachaType_Pets")] Pets = 3,
  }

  /// <summary>
  ///装备类型
  /// </summary>
  public enum EquipType {
    /// <summary>
    ///占位
    /// </summary>
    [pbr::OriginalName("Equip_NONE")] EquipNone = 0,
    /// <summary>
    ///护臂
    /// </summary>
    [pbr::OriginalName("Equip_Bracer")] EquipBracer = 1,
    /// <summary>
    ///头盔
    /// </summary>
    [pbr::OriginalName("Equip_Helmet")] EquipHelmet = 2,
    /// <summary>
    ///鞋子
    /// </summary>
    [pbr::OriginalName("Equip_Shoes")] EquipShoes = 3,
    /// <summary>
    ///裤子
    /// </summary>
    [pbr::OriginalName("Equip_Pants")] EquipPants = 4,
    /// <summary>
    /// 手套
    /// </summary>
    [pbr::OriginalName("Equip_Gloves")] EquipGloves = 5,
    /// <summary>
    /// 衣服
    /// </summary>
    [pbr::OriginalName("Equip_Clothes")] EquipClothes = 6,
  }

  /// <summary>
  ///大数值公式类型
  /// </summary>
  public enum DropExpressType {
    /// <summary>
    ///普通计算公式（只返回base值）
    /// </summary>
    [pbr::OriginalName("Express_Normal")] ExpressNormal = 0,
    /// <summary>
    ///关卡计算公式
    /// </summary>
    [pbr::OriginalName("Express_Stages")] ExpressStages = 1,
  }

  /// <summary>
  ///统一品质类型
  /// </summary>
  public enum QualityType {
    /// <summary>
    ///灰
    /// </summary>
    [pbr::OriginalName("Quality_Grey")] QualityGrey = 0,
    /// <summary>
    ///绿
    /// </summary>
    [pbr::OriginalName("Quality_Green")] QualityGreen = 1,
    /// <summary>
    ///蓝
    /// </summary>
    [pbr::OriginalName("Quality_Blue")] QualityBlue = 2,
    /// <summary>
    ///紫
    /// </summary>
    [pbr::OriginalName("Quality_Purple")] QualityPurple = 3,
    /// <summary>
    ///金
    /// </summary>
    [pbr::OriginalName("Quality_Gold")] QualityGold = 4,
    /// <summary>
    ///橙
    /// </summary>
    [pbr::OriginalName("Quality_Orange")] QualityOrange = 5,
    /// <summary>
    ///红
    /// </summary>
    [pbr::OriginalName("Quality_Red")] QualityRed = 6,
    /// <summary>
    ///深红
    /// </summary>
    [pbr::OriginalName("Quality_DeepRed")] QualityDeepRed = 7,
    /// <summary>
    ///预留
    /// </summary>
    [pbr::OriginalName("Quality_Args8")] QualityArgs8 = 8,
  }

  /// <summary>
  ///邮件的类型
  /// </summary>
  public enum MailType {
    /// <summary>
    ///非奖励类型邮件
    /// </summary>
    [pbr::OriginalName("MailType_NonReward")] NonReward = 0,
    /// <summary>
    /// 有奖励邮件
    /// </summary>
    [pbr::OriginalName("MailType_Reward")] Reward = 1,
    /// <summary>
    ///通知奖励类型邮件
    /// </summary>
    [pbr::OriginalName("MailType_NotifyReward")] NotifyReward = 2,
    /// <summary>
    ///玩家权限控制刷新-全
    /// </summary>
    [pbr::OriginalName("MailType_PlayerAuth")] PlayerAuth = 3,
    /// <summary>
    ///玩家权限控制刷新-单或组
    /// </summary>
    [pbr::OriginalName("MailType_PlayerAuthInfo")] PlayerAuthInfo = 4,
    /// <summary>
    ///删除玩家身上的多个邮件
    /// </summary>
    [pbr::OriginalName("MailType_PlayerMailRemove")] PlayerMailRemove = 5,
    /// <summary>
    ///道具直购（所有付费都走邮件发放）
    /// </summary>
    [pbr::OriginalName("MailType_GiftRMB")] GiftRmb = 6,
    /// <summary>
    ///GlobalServer发送的玩家个人邮件
    /// </summary>
    [pbr::OriginalName("MailType_GlobalActivityPlayer")] GlobalActivityPlayer = 7,
    /// <summary>
    ///后台广告回调邮件
    /// </summary>
    [pbr::OriginalName("MailType_AD")] Ad = 8,
    /// <summary>
    ///公会操作时对离线玩家通知（写入离线数据）
    /// </summary>
    [pbr::OriginalName("MailType_GuildOfflineMsg")] GuildOfflineMsg = 9,
    /// <summary>
    ///问卷奖励
    /// </summary>
    [pbr::OriginalName("MailType_QuestAward")] QuestAward = 10,
    /// <summary>
    ///开服活动结束
    /// </summary>
    [pbr::OriginalName("MailType_Activity_OpenServer_End")] ActivityOpenServerEnd = 11,
  }

  /// <summary>
  ///邮件的打开类型
  /// </summary>
  public enum MailStateType {
    /// <summary>
    ///未读
    /// </summary>
    [pbr::OriginalName("MailStateType_UnRead")] UnRead = 0,
    /// <summary>
    ///已读
    /// </summary>
    [pbr::OriginalName("MailStateType_Read")] Read = 1,
    /// <summary>
    ///已领取
    /// </summary>
    [pbr::OriginalName("MailStateType_GotAward")] GotAward = 2,
  }

  /// <summary>
  ///邮件的打开类型
  /// </summary>
  public enum MailReasonType {
    /// <summary>
    /// 测试
    /// </summary>
    [pbr::OriginalName("MailReasonType_TestGM")] TestGm = 0,
    /// <summary>
    ///GM管理员
    /// </summary>
    [pbr::OriginalName("MailReasonType_GMRemote")] Gmremote = 1,
    /// <summary>
    ///系统邮件
    /// </summary>
    [pbr::OriginalName("MailReasonType_System")] System = 2,
  }

  /// <summary>
  ///旧版新手引导触发类型
  /// </summary>
  public enum GuideTriggerType {
    [pbr::OriginalName("None")] None = 0,
    [pbr::OriginalName("NewPlayer")] NewPlayer = 1,
    [pbr::OriginalName("OpenUi")] OpenUi = 2,
    [pbr::OriginalName("CloseUi")] CloseUi = 3,
    [pbr::OriginalName("UnlockFuncId")] UnlockFuncId = 4,
    [pbr::OriginalName("PassedMainLine")] PassedMainLine = 5,
    [pbr::OriginalName("FinishGuide")] FinishGuide = 6,
  }

  /// <summary>
  ///新手引导的触发类型
  /// </summary>
  public enum NewGuideTriggerType {
    /// <summary>
    ///无效的触发
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_Normal")] Normal = 0,
    /// <summary>
    ///领取主线完成任务
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_GetCompleteTask")] GetCompleteTask = 1,
    /// <summary>
    ///完成任务
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_MissionFinish")] MissionFinish = 2,
    /// <summary>
    ///新手结束触发下一个新手
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_NewGuildEnd")] NewGuildEnd = 3,
    /// <summary>
    ///金币 经验 主线 成功结束
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_InstanceEnd")] InstanceEnd = 4,
    /// <summary>
    ///起名
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_CreateName")] CreateName = 5,
    /// <summary>
    ///玩家首次挑战合作舞台挑战胜利触发引导
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_CooperationStage")] CooperationStage = 6,
    /// <summary>
    ///抽卡结束
    /// </summary>
    [pbr::OriginalName("NewGuideTriggerType_LotteryEnd")] LotteryEnd = 7,
  }

  /// <summary>
  ///新手引导的功能类型
  /// </summary>
  public enum NewGuideFunType {
    /// <summary>
    ///无效的触发
    /// </summary>
    [pbr::OriginalName("NewGuideFunType_Normal")] Normal = 0,
    /// <summary>
    ///起名
    /// </summary>
    [pbr::OriginalName("NewGuideFunType_ChangeName")] ChangeName = 1,
  }

  /// <summary>
  ///签到的类型
  /// </summary>
  public enum SignInToType {
    /// <summary>
    ///未知的类型
    /// </summary>
    [pbr::OriginalName("SignInToType_None")] None = 0,
    /// <summary>
    ///七天签到
    /// </summary>
    [pbr::OriginalName("SignInToType_SevenDay")] SevenDay = 1,
    /// <summary>
    ///每天签到
    /// </summary>
    [pbr::OriginalName("SignInToType_EveryDay")] EveryDay = 2,
    /// <summary>
    ///每月阶段奖励
    /// </summary>
    [pbr::OriginalName("SignInToType_MonthStage")] MonthStage = 3,
  }

  /// <summary>
  ///奖励状态
  /// </summary>
  public enum RewardStatus {
    /// <summary>
    ///未完成
    /// </summary>
    [pbr::OriginalName("RewardStatus_Doing")] Doing = 0,
    /// <summary>
    ///已完成
    /// </summary>
    [pbr::OriginalName("RewardStatus_Finish")] Finish = 1,
    /// <summary>
    ///已领奖
    /// </summary>
    [pbr::OriginalName("RewardStatus_Received")] Received = 2,
  }

  /// <summary>
  ///奖励类型
  /// </summary>
  public enum RewardType {
    [pbr::OriginalName("REWARD_TYPE_UNKNOWN")] Unknown = 0,
    /// <summary>
    /// 普通段位奖励
    /// </summary>
    [pbr::OriginalName("REWARD_TYPE_RANK")] Rank = 1,
    /// <summary>
    /// 赛季段位奖励
    /// </summary>
    [pbr::OriginalName("REWARD_TYPE_SEASON_RANK")] SeasonRank = 2,
  }

  /// <summary>
  ///道具类型
  /// </summary>
  public enum ItemType {
    /// <summary>
    ///不可使用类
    /// </summary>
    [pbr::OriginalName("Unavailable")] Unavailable = 0,
    /// <summary>
    ///掉落类型
    /// </summary>
    [pbr::OriginalName("Drop")] Drop = 1,
    /// <summary>
    ///挂机奖励类型
    /// </summary>
    [pbr::OriginalName("HangUpReward")] HangUpReward = 2,
    /// <summary>
    ///自选宝箱
    /// </summary>
    [pbr::OriginalName("ChooseBox")] ChooseBox = 3,
    /// <summary>
    ///碎片类型
    /// </summary>
    [pbr::OriginalName("Devris")] Devris = 4,
  }

  public enum ItemSubType_ChooseBox {
    /// <summary>
    ///只能选一个
    /// </summary>
    [pbr::OriginalName("ChooseBox_Once")] ChooseBoxOnce = 0,
    /// <summary>
    ///可批量多选
    /// </summary>
    [pbr::OriginalName("ChooseBox_Multi")] ChooseBoxMulti = 1,
  }

  /// <summary>
  ///道具子类型 (掉落类型)
  /// </summary>
  public enum ItemSubType_Drop {
    /// <summary>
    ///固定类型
    /// </summary>
    [pbr::OriginalName("Drop_Fixed")] DropFixed = 0,
    /// <summary>
    ///随机类
    /// </summary>
    [pbr::OriginalName("Drop_Random")] DropRandom = 1,
  }

  /// <summary>
  ///道具子类型 (碎片类型)
  /// </summary>
  public enum ItemSubType_Devris {
    /// <summary>
    ///皮肤
    /// </summary>
    [pbr::OriginalName("Devris_Skin")] DevrisSkin = 0,
    /// <summary>
    ///枪械
    /// </summary>
    [pbr::OriginalName("Devris_Firearm")] DevrisFirearm = 1,
    /// <summary>
    ///城墙
    /// </summary>
    [pbr::OriginalName("Devris_Wall")] DevrisWall = 2,
    /// <summary>
    ///宠物
    /// </summary>
    [pbr::OriginalName("Devris_Pet")] DevrisPet = 3,
  }

  /// <summary>
  ///背包对象结构类型
  /// </summary>
  public enum BagItemType {
    /// <summary>
    ///常规物品类型
    /// </summary>
    [pbr::OriginalName("NormalItem")] NormalItem = 0,
  }

  /// <summary>
  ///系统提示
  /// </summary>
  public enum SystemShowErrorType {
    /// <summary>
    ///没有特殊需求的error
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_None")] None = 0,
    /// <summary>
    ///踢人
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_Kick")] Kick = 1,
    /// <summary>
    ///顶号
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_Replace")] Replace = 2,
    /// <summary>
    ///禁言
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_ChatBan")] ChatBan = 3,
    /// <summary>
    ///仅弹窗提示
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_Tips")] Tips = 4,
    /// <summary>
    ///登录
    /// </summary>
    [pbr::OriginalName("SystemShowErrorType_Login")] Login = 5,
  }

  /// <summary>
  ///礼包类型
  /// </summary>
  public enum GiftPacksType {
    [pbr::OriginalName("GiftPacksType_None")] None = 0,
    /// <summary>
    ///限时礼包
    /// </summary>
    [pbr::OriginalName("GiftPacksType_Limit")] Limit = 1,
    /// <summary>
    /// </summary>
    [pbr::OriginalName("GiftPacksType_Weekly")] Weekly = 2,
    /// <summary>
    ///每日特惠礼包
    /// </summary>
    [pbr::OriginalName("GiftPacksType_DailyPlus")] DailyPlus = 3,
    /// <summary>
    ///周卡礼包
    /// </summary>
    [pbr::OriginalName("GiftPacksType_WeekCard")] WeekCard = 4,
    /// <summary>
    ///推荐礼包
    /// </summary>
    [pbr::OriginalName("GiftPacksType_Recommend")] Recommend = 5,
  }

  /// <summary>
  ///礼包购买类型
  /// </summary>
  public enum GiftPacksBuyType {
    /// <summary>
    ///广告购买
    /// </summary>
    [pbr::OriginalName("GiftPacksBuyType_Adv")] Adv = 0,
    /// <summary>
    ///人民币购买
    /// </summary>
    [pbr::OriginalName("GiftPacksBuyType_Rmb")] Rmb = 1,
    /// <summary>
    ///免费领取
    /// </summary>
    [pbr::OriginalName("GiftPacksBuyType_Free")] Free = 2,
    /// <summary>
    ///钻石购买礼包
    /// </summary>
    [pbr::OriginalName("GiftPacksBuyType_Diamond")] Diamond = 3,
  }

  /// <summary>
  ///月卡类型
  /// </summary>
  public enum ChargeType {
    /// <summary>
    /// </summary>
    [pbr::OriginalName("ChargeType_None")] None = 0,
    /// <summary>
    ///钻石档位
    /// </summary>
    [pbr::OriginalName("ChargeType_Diamond")] Diamond = 1,
    /// <summary>
    ///普通
    /// </summary>
    [pbr::OriginalName("ChargeType_MonthCardNormal")] MonthCardNormal = 2,
    /// <summary>
    ///超级
    /// </summary>
    [pbr::OriginalName("ChargeType_MonthCardSuper")] MonthCardSuper = 3,
    /// <summary>
    ///道具直购
    /// </summary>
    [pbr::OriginalName("ChargeType_Item")] Item = 4,
  }

  /// <summary>
  ///通用阶段宝箱 特殊固定类别
  /// </summary>
  public enum CommonBoxRewardType {
    /// <summary>
    /// </summary>
    [pbr::OriginalName("CommonBoxReward_None")] CommonBoxRewardNone = 0,
    /// <summary>
    ///每日宝箱
    /// </summary>
    [pbr::OriginalName("CommonBoxReward_DailyMission")] CommonBoxRewardDailyMission = 1,
    /// <summary>
    ///七日任务活动宝箱
    /// </summary>
    [pbr::OriginalName("CommonBoxReward_SevenDayActivity")] CommonBoxRewardSevenDayActivity = 2,
  }

  /// <summary>
  ///=====================战令开始===================================
  /// 战令类型
  /// </summary>
  public enum BattlePassType {
    /// <summary>
    /// 未购买
    /// </summary>
    [pbr::OriginalName("BattlePassType_NONE")] None = 0,
    /// <summary>
    /// 基础战令
    /// </summary>
    [pbr::OriginalName("BattlePassType_BASE")] Base = 1,
    /// <summary>
    /// 进阶战令
    /// </summary>
    [pbr::OriginalName("BattlePassType_ADVANCE")] Advance = 2,
  }

  /// <summary>
  /// 战令任务类型
  /// </summary>
  public enum BattlePassMissionType {
    /// <summary>
    /// 非法
    /// </summary>
    [pbr::OriginalName("BattlePassMissionType_NONE")] None = 0,
    /// <summary>
    /// 每日
    /// </summary>
    [pbr::OriginalName("BattlePassMissionType_DAILY")] Daily = 1,
    /// <summary>
    /// 每周
    /// </summary>
    [pbr::OriginalName("BattlePassMissionType_WEEKLY")] Weekly = 2,
  }

  /// <summary>
  /// 战令奖励领取状态
  /// </summary>
  public enum AwardState {
    /// <summary>
    /// 未领取
    /// </summary>
    [pbr::OriginalName("AwardState_UNAVAILABLE")] Unavailable = 0,
    /// <summary>
    /// 可领取
    /// </summary>
    [pbr::OriginalName("AwardState_AVAILABLE")] Available = 1,
    /// <summary>
    /// 已领取
    /// </summary>
    [pbr::OriginalName("AwardState_RECEIVED")] Received = 2,
  }

  /// <summary>
  ///=====================战令结束===================================
  /// 活动类型
  /// </summary>
  public enum ActivityType {
    /// <summary>
    ///0.战令
    /// </summary>
    [pbr::OriginalName("ActivityType_BattlePass")] BattlePass = 0,
    /// <summary>
    ///1.七日任务（仅占位）
    /// </summary>
    [pbr::OriginalName("ActivityType_SevenTask")] SevenTask = 1,
    /// <summary>
    ///2.七日签到（仅占位）
    /// </summary>
    [pbr::OriginalName("ActivityType_SevenSign")] SevenSign = 2,
  }

  /// <summary>
  /// 活动状态
  /// </summary>
  public enum ActivityStatus {
    /// <summary>
    ///0.未开启
    /// </summary>
    [pbr::OriginalName("ActivityStatus_NO_OPEN")] NoOpen = 0,
    /// <summary>
    ///1.已开始（系统预处理阶段，服务器使用）
    /// </summary>
    [pbr::OriginalName("ActivityStatus_START")] Start = 1,
    /// <summary>
    ///3.已结束（客户端判断此状态为活动正式结束）
    /// </summary>
    [pbr::OriginalName("ActivityStatus_END")] End = 2,
  }

  /// <summary>
  ///=====================道具前往获取开始===================================
  /// </summary>
  public enum ItemAcquisitionState {
    /// <summary>
    /// 0.表示不可用
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_NONE")] None = 0,
    /// <summary>
    /// 钻石
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_Diamond")] Diamond = 1,
    /// <summary>
    /// 星星
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_Star")] Star = 2,
    /// <summary>
    /// 神之精华
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_Essence")] Essence = 3,
    /// <summary>
    /// 空气净化器
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_AirPurifier")] AirPurifier = 4,
    /// <summary>
    /// 建筑升级门票
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_UpgradeTicket")] UpgradeTicket = 5,
    /// <summary>
    /// 副本门票
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DuplicateTicket")] DuplicateTicket = 6,
    /// <summary>
    /// 铲子
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_Shovel")] Shovel = 7,
    /// <summary>
    /// 龙蛋
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DragonEgg")] DragonEgg = 8,
    /// <summary>
    /// 魔法布料
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_MagicCloth")] MagicCloth = 9,
    /// <summary>
    /// 凿子
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_Chisel")] Chisel = 10,
    /// <summary>
    /// 龙底座升级材料
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DragonCompoundSlotUpgrade")] DragonCompoundSlotUpgrade = 11,
    /// <summary>
    /// 史莱姆招募道具
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_SlimeRecurit")] SlimeRecurit = 12,
    /// <summary>
    /// 悬空工厂升级道具
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DragonFactoryLevelItemRob")] DragonFactoryLevelItemRob = 13,
    /// <summary>
    /// 其他建筑升级道具
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DragonBuildResource")] DragonBuildResource = 14,
    /// <summary>
    /// 科技点道具获取
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_DragonFactoryTech")] DragonFactoryTech = 15,
    /// <summary>
    /// 公会科技道具（远古晶石
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_GuildFactoryItem")] GuildFactoryItem = 16,
    /// <summary>
    /// 技能突破道具获取
    /// </summary>
    [pbr::OriginalName("ItemAcquisitionState_FashionBTSItem")] FashionBtsitem = 17,
  }

  /// <summary>
  ///=====================视频广告的类型开始==================================
  /// 激励视频
  /// </summary>
  public enum IronSourceADype {
    /// <summary>
    /// 0.表示不可用
    /// </summary>
    [pbr::OriginalName("IronSourceADype_NONE")] None = 0,
    /// <summary>
    /// 挂机奖励X1
    /// </summary>
    [pbr::OriginalName("IronSourceADype_OffLine")] OffLine = 1,
  }

  /// <summary>
  ///=====================公会系统开始==================================
  ///公会成员身份，保持权限递增的顺序
  /// </summary>
  public enum GuildPosition {
    /// <summary>
    ///普通成员
    /// </summary>
    [pbr::OriginalName("GuildPosition_Normal")] Normal = 0,
    /// <summary>
    ///精英成员
    /// </summary>
    [pbr::OriginalName("GuildPosition_Elite")] Elite = 1,
    /// <summary>
    ///长老（副会长）
    /// </summary>
    [pbr::OriginalName("GuildPosition_Vice")] Vice = 2,
    /// <summary>
    ///会长
    /// </summary>
    [pbr::OriginalName("GuildPosition_President")] President = 3,
  }

  /// <summary>
  ///公会操作（仅列出需要验证权限的操作）
  /// </summary>
  public enum GuildOpt {
    /// <summary>
    /// 0.无操作
    /// </summary>
    [pbr::OriginalName("GuildOpt_None")] None = 0,
    /// <summary>
    /// 1.修改旗帜
    /// </summary>
    [pbr::OriginalName("GuildOpt_EditIcon")] EditIcon = 1,
    /// <summary>
    /// 2.修改名字
    /// </summary>
    [pbr::OriginalName("GuildOpt_EditName")] EditName = 2,
    /// <summary>
    /// 3.修改宣言
    /// </summary>
    [pbr::OriginalName("GuildOpt_EditNotice")] EditNotice = 3,
    /// <summary>
    /// 4.修改申请条件
    /// </summary>
    [pbr::OriginalName("GuildOpt_EditApply")] EditApply = 4,
    /// <summary>
    /// 5.申请列表查看
    /// </summary>
    [pbr::OriginalName("GuildOpt_ApplyMgrList")] ApplyMgrList = 5,
    /// <summary>
    /// 6.同意申请
    /// </summary>
    [pbr::OriginalName("GuildOpt_ApplyMgrAgree")] ApplyMgrAgree = 6,
    /// <summary>
    /// 7.拒绝申请
    /// </summary>
    [pbr::OriginalName("GuildOpt_ApplyMgrRefuse")] ApplyMgrRefuse = 7,
    /// <summary>
    /// 8.成员管理列表查看
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrList")] MemberMgrList = 8,
    /// <summary>
    /// 9.任命会长
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrPresident")] MemberMgrPresident = 9,
    /// <summary>
    /// 10.任命副会长
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrVice")] MemberMgrVice = 10,
    /// <summary>
    /// 11.认命精英
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrElite")] MemberMgrElite = 11,
    /// <summary>
    /// 12.任命为普通成员
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrNormal")] MemberMgrNormal = 12,
    /// <summary>
    /// 13.踢出公会
    /// </summary>
    [pbr::OriginalName("GuildOpt_MemberMgrKick")] MemberMgrKick = 13,
    /// <summary>
    /// 14.退出公会
    /// </summary>
    [pbr::OriginalName("GuildOpt_Quit")] Quit = 14,
    /// <summary>
    /// 15.解散公会
    /// </summary>
    [pbr::OriginalName("GuildOpt_Dismiss")] Dismiss = 15,
    /// <summary>
    /// 16.弹劾会长
    /// </summary>
    [pbr::OriginalName("GuildOpt_Impeach")] Impeach = 16,
    /// <summary>
    /// 17.发送世界邀请
    /// </summary>
    [pbr::OriginalName("GuildOpt_SendWorldInvite")] SendWorldInvite = 17,
    /// <summary>
    /// 18.发送私聊邀请
    /// </summary>
    [pbr::OriginalName("GuildOpt_SendPlayerInvite")] SendPlayerInvite = 18,
    /// <summary>
    /// 19.提醒砍价
    /// </summary>
    [pbr::OriginalName("GuildOpt_BargainingNotice")] BargainingNotice = 19,
    /// <summary>
    /// 20.修改公告
    /// </summary>
    [pbr::OriginalName("GuildOpt_EditAnnouncement")] EditAnnouncement = 20,
    /// <summary>
    /// 21.一键拒绝所有申请
    /// </summary>
    [pbr::OriginalName("GuildOpt_ApplyMgrRejectAll")] ApplyMgrRejectAll = 21,
  }

  /// <summary>
  ///公会日志类型
  /// </summary>
  public enum GuildLogType {
    /// <summary>
    ///0.离开公会
    /// </summary>
    [pbr::OriginalName("GuildLogType_Leave")] Leave = 0,
    /// <summary>
    ///1.加入公会
    /// </summary>
    [pbr::OriginalName("GuildLogType_Join")] Join = 1,
    /// <summary>
    ///2.弹劾成功
    /// </summary>
    [pbr::OriginalName("GuildLogType_Impeach1")] Impeach1 = 2,
    /// <summary>
    ///3.被弹劾
    /// </summary>
    [pbr::OriginalName("GuildLogType_Impeach2")] Impeach2 = 3,
    /// <summary>
    ///4.任命副会长
    /// </summary>
    [pbr::OriginalName("GuildLogType_MgrVice")] MgrVice = 4,
    /// <summary>
    ///5.任命为普通成员
    /// </summary>
    [pbr::OriginalName("GuildLogType_MgrNormal")] MgrNormal = 5,
    /// <summary>
    ///6.捐献增加经验
    /// </summary>
    [pbr::OriginalName("GuildLogType_Donate")] Donate = 6,
    /// <summary>
    ///7.成为会长
    /// </summary>
    [pbr::OriginalName("GuildLogType_President")] President = 7,
    /// <summary>
    ///8.转让成功为会长
    /// </summary>
    [pbr::OriginalName("GuildLogType_AutoChange1")] AutoChange1 = 8,
    /// <summary>
    ///9.被转让出会长，降为成员
    /// </summary>
    [pbr::OriginalName("GuildLogType_AutoChange2")] AutoChange2 = 9,
  }

  /// <summary>
  /// 公会通知
  /// 用于 G2PGuildGeneralUpdateNtf，告知 Player Actor 其公会相关状态需要更新的具体类型
  /// </summary>
  public enum GuildUpdateType {
    /// <summary>
    /// 未知或默认类型，不应实际使用
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_UNKNOWN")] Unknown = 0,
    /// <summary>
    /// --- 玩家与公会的从属关系变化 ---
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_JOINED_GUILD")] JoinedGuild = 1,
    /// <summary>
    /// 玩家主动退出了当前公会 场景: 玩家执行退出公会操作成功后。
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_LEFT_GUILD")] LeftGuild = 2,
    /// <summary>
    /// 玩家被从当前公会踢出 场景: 官员执行踢人操作，目标是此玩家。
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_KICKED_FROM_GUILD")] KickedFromGuild = 3,
    /// <summary>
    /// 玩家所在的公会被解散 (无论是会长主动还是系统自动) 场景: 玩家是某公会成员，该公会被解散。
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_GUILD_DISMISSED")] GuildDismissed = 4,
    /// <summary>
    /// --- 玩家在公会内的状态变化 ---
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_POSITION_CHANGED")] PositionChanged = 5,
    /// <summary>
    /// --- 公会本身的状态变化 (通知成员) ---
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_GUILD_LEVEL_UP")] GuildLevelUp = 6,
    /// <summary>
    /// --- 玩家申请列表状态变化 ---
    /// </summary>
    [pbr::OriginalName("GuildUpdateType_APPLY_PROCESSED_OR_EXPIRED")] ApplyProcessedOrExpired = 7,
  }

  /// <summary>
  /// 公会申请记录的状态
  /// </summary>
  public enum GuildApplicationStatus {
    /// <summary>
    /// 未知或无效状态。
    /// </summary>
    [pbr::OriginalName("APPLICATION_STATUS_UNKNOWN")] ApplicationStatusUnknown = 0,
    /// <summary>
    /// 申请已提交，等待联盟官员审批。
    /// </summary>
    [pbr::OriginalName("APPLICATION_STATUS_PENDING")] ApplicationStatusPending = 1,
    /// <summary>
    /// 申请已被联盟官员批准。
    /// </summary>
    [pbr::OriginalName("APPLICATION_STATUS_APPROVED")] ApplicationStatusApproved = 2,
    /// <summary>
    /// 申请已被联盟官员拒绝。
    /// </summary>
    [pbr::OriginalName("APPLICATION_STATUS_REJECTED")] ApplicationStatusRejected = 3,
    /// <summary>
    /// 申请因超时（例如24小时未被处理）而自动失效。玩家未能加入联盟。
    /// </summary>
    [pbr::OriginalName("APPLICATION_STATUS_EXPIRED")] ApplicationStatusExpired = 4,
  }

  /// <summary>
  /// 公会内部通知类型
  /// </summary>
  public enum GuildSystemInternalActionType {
    [pbr::OriginalName("INTERNAL_ACTION_UNKNOWN")] InternalActionUnknown = 0,
    /// <summary>
    /// 公会解散，通知成员
    /// </summary>
    [pbr::OriginalName("INTERNAL_ACTION_DISMISS_GUILD_MEMBERS")] InternalActionDismissGuildMembers = 1,
    /// <summary>
    /// 联盟升级，通知成员
    /// </summary>
    [pbr::OriginalName("INTERNAL_ACTION_GUILD_LEVEL_UP_NOTIFY_MEMBERS")] InternalActionGuildLevelUpNotifyMembers = 2,
  }

  /// <summary>
  ///=====================公会系统结束===================================
  ///=====================聊天系统结束===================================
  ///聊天类型
  /// </summary>
  public enum ChatType {
    [pbr::OriginalName("ChatType_World")] World = 0,
    [pbr::OriginalName("ChatType_Guild")] Guild = 1,
    [pbr::OriginalName("ChatType_Private")] Private = 2,
  }

  /// <summary>
  ///聊天信息类型
  /// </summary>
  public enum ChatMsgType {
    /// <summary>
    ///普通文本
    /// </summary>
    [pbr::OriginalName("ChatMsgType_Text")] Text = 0,
    /// <summary>
    ///公会邀请信息
    /// </summary>
    [pbr::OriginalName("ChatMsgType_GuildInvite")] GuildInvite = 1,
    /// <summary>
    ///系统信息
    /// </summary>
    [pbr::OriginalName("ChatMsgType_System")] System = 2,
  }

  /// <summary>
  ///战斗内触发逻辑打开窗口类型
  /// </summary>
  public enum InBattleShowWindowType {
    /// <summary>
    ///三选一buff
    /// </summary>
    [pbr::OriginalName("UI_Select_NormalPop")] UiSelectNormalPop = 0,
    /// <summary>
    ///商店购买buff
    /// </summary>
    [pbr::OriginalName("UI_Select_ShopPop")] UiSelectShopPop = 1,
    /// <summary>
    ///击杀boss或精英怪物掉落buff
    /// </summary>
    [pbr::OriginalName("UI_Select_LuckyPop")] UiSelectLuckyPop = 2,
  }

  /// <summary>
  ///全局广播事件类型
  /// </summary>
  public enum GlobalBroadcastEventType {
    /// <summary>
    ///默认无处理
    /// </summary>
    [pbr::OriginalName("GlobalBroadcastEventType_None")] None = 0,
    /// <summary>
    ///全局封禁玩家所在公会的BOSS排行信息，伤害置为0
    /// </summary>
    [pbr::OriginalName("GlobalBroadcastEventType_BanPlayerGuildBossRank")] BanPlayerGuildBossRank = 1,
    /// <summary>
    ///强制修改玩家名称
    /// </summary>
    [pbr::OriginalName("GlobalBroadcastEventType_ChgPlayerName")] ChgPlayerName = 2,
    /// <summary>
    ///强制修改公会名称和公告信息
    /// </summary>
    [pbr::OriginalName("GlobalBroadcastEventType_ChgGuildNameAndNotice")] ChgGuildNameAndNotice = 3,
  }

  /// <summary>
  ///=====================七日签到开始===================================
  /// </summary>
  public enum SignInState {
    /// <summary>
    ///未签到
    /// </summary>
    [pbr::OriginalName("NoSignIn")] NoSignIn = 0,
    /// <summary>
    ///已签到未领取
    /// </summary>
    [pbr::OriginalName("SignedInNoRe")] SignedInNoRe = 1,
    /// <summary>
    ///已签到已领取
    /// </summary>
    [pbr::OriginalName("SignedInAndReed")] SignedInAndReed = 2,
  }

  /// <summary>
  ///=====================七日签到结束===================================
  ///======================支付开始=========================================
  /// </summary>
  public enum PaymentModuleType {
    /// <summary>
    /// 商店
    /// </summary>
    [pbr::OriginalName("Shop")] Shop = 0,
    /// <summary>
    /// 首充
    /// </summary>
    [pbr::OriginalName("FirstCharge")] FirstCharge = 1,
    /// <summary>
    /// 月卡
    /// </summary>
    [pbr::OriginalName("MonthlyCard")] MonthlyCard = 2,
    /// <summary>
    /// 基金
    /// </summary>
    [pbr::OriginalName("GradedFund")] GradedFund = 3,
    /// <summary>
    /// 月卡2.0
    /// </summary>
    [pbr::OriginalName("MonthlyCardNew")] MonthlyCardNew = 4,
    /// <summary>
    /// 限时商店(日、周、月礼包)
    /// </summary>
    [pbr::OriginalName("TimeShop")] TimeShop = 5,
  }

  /// <summary>
  ///======================任务开始=========================================
  ///任务状态
  /// </summary>
  public enum MissionState {
    /// <summary>
    ///未完成
    /// </summary>
    [pbr::OriginalName("MissionState_Unfinished")] Unfinished = 0,
    /// <summary>
    ///已完成
    /// </summary>
    [pbr::OriginalName("MissionState_Finished")] Finished = 1,
    /// <summary>
    ///已领取
    /// </summary>
    [pbr::OriginalName("MissionState_Received")] Received = 2,
  }

  /// <summary>
  ///任务类型
  /// </summary>
  public enum MissionType {
    /// <summary>
    ///主线任务
    /// </summary>
    [pbr::OriginalName("MissionType_Main")] Main = 0,
    /// <summary>
    ///日常任务
    /// </summary>
    [pbr::OriginalName("MissionType_Daily")] Daily = 1,
    /// <summary>
    ///七日任务
    /// </summary>
    [pbr::OriginalName("MissionType_SevenDay")] SevenDay = 2,
    /// <summary>
    ///成就任务
    /// </summary>
    [pbr::OriginalName("MissionType_Achievement")] Achievement = 3,
    /// <summary>
    ///天道修为
    /// </summary>
    [pbr::OriginalName("MissionType_HeavenlyDao")] HeavenlyDao = 4,
  }

  /// <summary>
  ///=====================千抽开始===================================
  ///千抽状态
  /// </summary>
  public enum DBGachaBonusState {
    /// <summary>
    ///未抽卡
    /// </summary>
    [pbr::OriginalName("NoPull")] NoPull = 0,
    /// <summary>
    ///已抽未领取
    /// </summary>
    [pbr::OriginalName("PulledNoRe")] PulledNoRe = 1,
    /// <summary>
    ///已抽已领取
    /// </summary>
    [pbr::OriginalName("PulledAndReed")] PulledAndReed = 2,
  }

  /// <summary>
  /// 错误码
  /// </summary>
  public enum ServerResultCode {
    /// <summary>
    /// 成功
    /// </summary>
    [pbr::OriginalName("ResultCode_SUCCES")] ResultCodeSucces = 0,
    /// <summary>
    ///  服务端报错
    /// </summary>
    [pbr::OriginalName("ResultCode_Error")] ResultCodeError = 1,
    /// <summary>
    /// 金币不足
    /// </summary>
    [pbr::OriginalName("ResultCode_Currency_Not_Enough")] ResultCodeCurrencyNotEnough = 2,
    /// <summary>
    /// 道具不足
    /// </summary>
    [pbr::OriginalName("ResultCode_Item_Not_Enough")] ResultCodeItemNotEnough = 3,
    /// <summary>
    /// 客户端参数错误
    /// </summary>
    [pbr::OriginalName("ResultCode_PARAM_ERROR")] ResultCodeParamError = 4,
    /// <summary>
    /// 配置不存在错误
    /// </summary>
    [pbr::OriginalName("ResultCode_CONFIG_NOT_CONTAINS")] ResultCodeConfigNotContains = 5,
  }

  /// <summary>
  ///=======================功能预告奖励================================
  /// </summary>
  public enum FuncPreviewstate {
    /// <summary>
    /// 未解锁
    /// </summary>
    [pbr::OriginalName("Lock")] Lock = 0,
    /// <summary>
    /// 可领奖
    /// </summary>
    [pbr::OriginalName("Award")] Award = 1,
    /// <summary>
    ///已领奖
    /// </summary>
    [pbr::OriginalName("Awarded")] Awarded = 2,
  }

  #endregion

}

#endregion Designer generated code
