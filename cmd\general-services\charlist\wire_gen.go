// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"liteframe/internal/general-services/charlist/biz"
	"liteframe/internal/general-services/charlist/conf"
	"liteframe/internal/general-services/charlist/data"
	"liteframe/internal/general-services/charlist/registry"
	"liteframe/internal/general-services/charlist/server"
	"liteframe/internal/general-services/charlist/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init charlist application.
func wireApp(confServer *conf.Server, bootstrap *conf.Bootstrap, logger log.Logger) (*kratos.App, func(), error) {
	cmdable := data.NewRedisCmd(bootstrap, logger)
	dataData, err := data.NewData(cmdable, logger)
	if err != nil {
		return nil, nil, err
	}
	charlistRepo := data.NewCharListRepo(dataData, logger)
	charlistUsecase := biz.NewCharlistUsecase(charlistRepo, logger)
	charlistService := service.NewCharlistService(charlistUsecase, logger)
	grpcServer := server.NewGRPCServer(confServer, charlistService, logger)
	httpServer := server.NewHTTPServer(confServer, charlistService, logger)
	confRegistry := registry.ProvideRegistry(bootstrap)
	etcdRegistry := registry.NewEtcdRegistry(confRegistry)
	app := newApp(logger, grpcServer, httpServer, etcdRegistry)
	return app, func() {
	}, nil
}
