//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"liteframe/internal/microservices/chat/biz"
	"liteframe/internal/microservices/chat/conf"
	"liteframe/internal/microservices/chat/data"
	"liteframe/internal/microservices/chat/registry"
	"liteframe/internal/microservices/chat/server"
	"liteframe/internal/microservices/chat/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init chatservice application.
func wireApp(*conf.ChatServerInfo, *conf.Server, *conf.Bootstrap, log.Logger, map[string]string) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, registry.ProviderSet, newApp))
}
