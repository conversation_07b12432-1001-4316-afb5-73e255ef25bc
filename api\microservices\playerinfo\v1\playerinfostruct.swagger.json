{"swagger": "2.0", "info": {"title": "microservices/playerinfo/v1/playerinfostruct.proto", "version": "version not set"}, "tags": [{"name": "PlayerInfoService"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/v1/playerinfo": {"post": {"summary": "查询玩家信息", "operationId": "PlayerInfoService_GetPlayerInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerGetPlayerInfoReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerGetPlayerInfoRequest"}}], "tags": ["PlayerInfoService"]}}, "/v1/playerinfolist": {"post": {"summary": "查询玩家信息", "operationId": "PlayerInfoService_GetPlayerInfoList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/PlayerInfoServerGetPlayerInfoListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/PlayerInfoServerGetPlayerInfoListRequest"}}], "tags": ["PlayerInfoService"]}}}, "definitions": {"PlayerInfoServerAccountGuid_PISct": {"type": "object", "properties": {"Guid": {"type": "string", "format": "uint64"}, "ZoneWorldId": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerBagAttr_PISct": {"type": "object", "properties": {"BankLockState": {"type": "array", "items": {"type": "boolean"}}, "EquipItemAttrs": {"type": "object", "additionalProperties": {"$ref": "#/definitions/PlayerInfoServerItemAttr_PISct"}}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerBaseAttr_PISct": {"type": "object", "properties": {"CharModelID": {"type": "integer", "format": "int32"}, "Level": {"type": "integer", "format": "int32"}, "Name": {"type": "string"}, "SceneId": {"type": "integer", "format": "int32"}, "Sex": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerBloodVessel_PISct": {"type": "object", "properties": {"BigNode": {"type": "integer", "format": "int32"}, "FightPoint": {"type": "array", "items": {"type": "number", "format": "double"}}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerCharBaseAttr_PISct": {"type": "object", "properties": {"CharFaceMake": {"$ref": "#/definitions/PlayerInfoServerFaceMakeInfo_PISct"}, "CreateTime": {"type": "string", "format": "uint64"}, "ForbiddenLoginEndTime": {"type": "string", "format": "uint64"}, "Guid": {"type": "string", "format": "uint64"}, "ServerNum": {"type": "integer", "format": "int64"}, "ZoneWorldID": {"type": "integer", "format": "int64"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerCharGuildAttr_PISct": {"type": "object", "properties": {"GuildGuid": {"type": "string", "format": "uint64"}, "GuildLevel": {"type": "integer", "format": "int64"}, "GuildName": {"type": "string"}, "GuildPostId": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerDyeData_PISct": {"type": "object", "properties": {"FaceData": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerFaceMakeInfo_PISct": {"type": "object", "properties": {"FmDic": {"type": "object", "additionalProperties": {"$ref": "#/definitions/PlayerInfoServerDyeData_PISct"}}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerGetPlayerInfoListReply": {"type": "object", "properties": {"PlayerInfoList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerPlayerInfo"}}, "ePlayerInfoList": {"type": "array", "items": {"type": "string", "format": "uint64"}}}, "title": "Type:Http\nType:Inner\nTarget:W2S"}, "PlayerInfoServerGetPlayerInfoListRequest": {"type": "object", "properties": {"playerInfoAskUnitList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerPlayerInfoAskUnit"}}}, "title": "Type:Http\nType:Inner\nTarget:S2W\nResponse W2S_GetPlayerInfoListReply"}, "PlayerInfoServerGetPlayerInfoReply": {"type": "object", "properties": {"PlayerInfo": {"$ref": "#/definitions/PlayerInfoServerPlayerInfo"}, "ePlayerInfo": {"type": "string", "format": "uint64"}}, "title": "Type:Http\nType:Inner\nTarget:W2S"}, "PlayerInfoServerGetPlayerInfoRequest": {"type": "object", "properties": {"AccountGuid": {"$ref": "#/definitions/PlayerInfoServerAccountGuid_PISct"}, "ePlayerInfo": {"type": "string", "format": "uint64", "title": "类型实际是 ENUM_PlayerInfo"}}, "title": "Type:Http\nType:Inner\nTarget:S2W\nResponse W2S_GetPlayerInfoReply"}, "PlayerInfoServerItemAttr_PISct": {"type": "object", "properties": {"GUID": {"type": "string", "format": "uint64"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPetFightInfo_PISct": {"type": "object", "properties": {"PetInfo": {"$ref": "#/definitions/PlayerInfoServerPetInfoAttr_PISct"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPetFlairInfo_PISct": {"type": "object", "properties": {"AllAddValue": {"type": "integer", "format": "int32"}, "BaseValue": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPetGeniusInfo_PISct": {"type": "object", "properties": {"Amend": {"type": "number", "format": "float"}, "ID": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPetInfoAttr_PISct": {"type": "object", "properties": {"BasicAttrList": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}}, "CombatModelID": {"type": "integer", "format": "int32"}, "EvolveLevel": {"type": "integer", "format": "int32"}, "FightPoint": {"type": "integer", "format": "int32"}, "Flair": {"type": "object", "additionalProperties": {"$ref": "#/definitions/PlayerInfoServerPetFlairInfo_PISct"}}, "GeniusCount": {"type": "integer", "format": "int32"}, "GeniusList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerPetGeniusInfo_PISct"}}, "GUID": {"type": "string", "format": "uint64"}, "GWTHRate": {"type": "number", "format": "float"}, "IsFightPet": {"type": "integer", "format": "int32"}, "IsLocked": {"type": "boolean"}, "Level": {"type": "integer", "format": "int32"}, "LevelEXP": {"type": "string", "format": "int64"}, "Name": {"type": "string"}, "PetID": {"type": "integer", "format": "int32"}, "Quality": {"type": "integer", "format": "int32"}, "SkillBookList": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/PlayerInfoServerPetSkillBookInfo_PISct"}}, "StarLevel": {"type": "integer", "format": "int32"}, "XDValueAGI": {"type": "integer", "format": "int32"}, "XDValueCON": {"type": "integer", "format": "int32"}, "XDValueINT": {"type": "integer", "format": "int32"}, "XDValueSTR": {"type": "integer", "format": "int32"}, "XianDan": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPetSkillBookInfo_PISct": {"type": "object", "properties": {"Level": {"type": "integer", "format": "int32"}, "SkillBookID": {"type": "integer", "format": "int32"}, "Slot": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPet_PISct": {"type": "object", "properties": {"CombatPet": {"$ref": "#/definitions/PlayerInfoServerPetFightInfo_PISct"}, "HenshinPet": {"$ref": "#/definitions/PlayerInfoServerPetInfoAttr_PISct"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPlayerBaseInfo": {"type": "object", "properties": {"FightPoint": {"type": "string", "format": "int64"}, "HenshinPetId": {"type": "integer", "format": "int32"}, "Level": {"type": "integer", "format": "int32"}, "Name": {"type": "string"}, "SceneId": {"type": "integer", "format": "int32"}, "Sex": {"type": "integer", "format": "int32"}, "UnitType": {"type": "integer", "format": "int32"}, "ForbiddenLoginEndTime": {"type": "string", "format": "uint64"}, "Guid": {"type": "string", "format": "uint64"}, "ServerNum": {"type": "integer", "format": "int64"}, "ZoneWorldID": {"type": "integer", "format": "int64"}, "HenshinPet": {"$ref": "#/definitions/PlayerInfoServerPetInfoAttr_PISct"}, "PetID": {"type": "integer", "format": "int32"}, "SelfTeamIndexId": {"type": "integer", "format": "int32"}, "TeamId": {"type": "string", "format": "uint64"}, "TeamJobTitle": {"type": "integer", "format": "int32"}, "TeamMemCount": {"type": "integer", "format": "int32"}, "TeamPlayerCount": {"type": "integer", "format": "int32"}, "TeamType": {"type": "integer", "format": "int32"}, "GuildGuid": {"type": "string", "format": "uint64"}, "GuildName": {"type": "string"}, "HeadIcon": {"type": "integer", "format": "int32"}, "HeadFrame": {"type": "integer", "format": "int32"}, "IsOnline": {"type": "boolean"}, "LastLoginTime": {"type": "string", "format": "int64"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPlayerInfo": {"type": "object", "properties": {"PlayerBaseInfoPI": {"$ref": "#/definitions/PlayerInfoServerPlayerBaseInfo"}, "BaseAttrPI": {"$ref": "#/definitions/PlayerInfoServerBaseAttr_PISct"}, "BagAttrPI": {"$ref": "#/definitions/PlayerInfoServerBagAttr_PISct"}, "CharBaseAttrPI": {"$ref": "#/definitions/PlayerInfoServerCharBaseAttr_PISct"}, "PetPI": {"$ref": "#/definitions/PlayerInfoServerPet_PISct"}, "TeamPI": {"$ref": "#/definitions/PlayerInfoServerTeam_PISct"}, "CharGuildAttrPI": {"$ref": "#/definitions/PlayerInfoServerCharGuildAttr_PISct"}, "BloodVesselPI": {"$ref": "#/definitions/PlayerInfoServerBloodVessel_PISct"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerPlayerInfoAskUnit": {"type": "object", "properties": {"AccountGuid": {"$ref": "#/definitions/PlayerInfoServerAccountGuid_PISct"}, "ePlayerInfo": {"type": "string", "format": "uint64", "title": "类型实际是 ENUM_PlayerInfo"}}, "title": "Type:Http\nType:Inner"}, "PlayerInfoServerSyncPlayerInfoReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}, "title": "Type:Inner\nTarget:W2S"}, "PlayerInfoServerTeam_PISct": {"type": "object", "properties": {"SelfTeamIndexId": {"type": "integer", "format": "int32"}, "TeamFollowState": {"type": "integer", "format": "int32"}, "TeamId": {"type": "string", "format": "uint64"}, "TeamJobTitle": {"type": "integer", "format": "int32"}, "TeamMemCount": {"type": "integer", "format": "int32"}, "TeamPlayerCount": {"type": "integer", "format": "int32"}, "TeamTarget": {"type": "integer", "format": "int32"}, "TeamType": {"type": "integer", "format": "int32"}}, "title": "Type:Http\nType:Inner"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}}}