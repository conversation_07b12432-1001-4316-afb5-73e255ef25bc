syntax = "proto3";

import "DBProtocol.proto";

package g2m;
option go_package="liteframe/internal/common/protos/g2m";

message G2M_LoadOrCreateUser{
  string account = 1;
  uint64 server_id = 2;
  uint64 uid = 3;
}

message M2G_LoadOrCreateUser {
  dbstruct.UserDB user = 1;
  bool  isNew = 2;
  uint32 code = 3;
}

message G2M_SaveUser {
  uint64 uid = 1; // 按用户 id 查找
  repeated bytes data = 2;
  dbstruct.UserDB user = 3;
}

// 加载用户数据
message G2M_LoadUser {
  string name = 1; // 按用户名查找，如果名字为空就按 id 查找
  uint64 uid = 2; // 按用户 id 查找
  string account = 3; // 按账户查找
  uint64 server_id = 4;
}

// 加载用户数据返回值
message M2G_LoadUser {
  dbstruct.UserDB user = 1;
}

// 根据账号获得或创建 uid
message G2M_LoadCreateUid {
  string account = 1;
  uint64 server_id = 2;
}

message M2G_LoadCreateUid {
  uint64 uid = 1;
  uint32 code = 2;
}

// 通用数据操作
message G2M_SaveData {
  uint64 server_id = 1;
  bytes data = 2;
}

message G2M_LoadData {
  uint64 server_id = 1;
}

message M2G_LoadData {
  bytes data = 1;
}

message G2M_DeleteData {
  uint64 server_id = 1;
}

message G2M_FindDuplicateNamesData {
  string name = 1;
}

message M2G_FindDuplicateNamesData {
  int64 num = 1;
}

message G2M_ChangeNameData {
  uint64 uid = 1;
  string name = 2;
  string old_name = 3;
}

// 加载所有公会数据
message G2M_LoadGuildData {
}

message M2G_LoadGuildData {
    uint32 code = 1;
    bytes guild_data = 2;  // 序列化的整个GuildDB数据
}

// 存储单个公会数据
message G2M_SaveGuildData {
    int64 guild_id = 1;
    bytes guild_data = 2;  // 序列化的单个GuildData数据
}

message M2G_SaveGuildData {
    uint32 code = 1;
}

// 删除指定公会数据
message G2M_DeleteGuildData {
    int64 guild_id = 1;
}

message M2G_DeleteGuildData {
    uint32 code = 1;
}

// 加载支付数据
message G2M_LoadPaymentData {
}

message M2G_LoadPaymentData {
    uint32 code = 1;
    bytes payment_data = 2;  // 序列化的整个PaymentDB数据
}

// 存储支付数据
message G2M_SavePaymentData {
    string order_key = 1;
    bytes payment_data = 2;  // 序列化的PaymentDB数据
}

message M2G_SavePaymentData {
    uint32 code = 1;
}

// 分批次加载玩家数据
message G2M_LoadUserSnapDataBatch {
  uint64 last_uid = 1;
  int64 limit = 2;
}

// 加载所有玩家数据
message M2G_LoadUserSnapDataBatch {
  uint32 code = 1;
  bytes user_data = 2;  // 序列化的整个UserData数据
  uint64 last_uid = 3;
  bool has_more = 4;
}
message G2M_LoadAllAccountMap {
  uint64 server_id = 1;
}

message M2G_LoadAllAccountMap {
  uint32 code = 1;
  map<string, uint64> account_map = 2;  // 序列化的整个AccountMap数据
}
// 加载举报数据
message G2M_LoadTipOffData {
}

message M2G_LoadTipOffData {
    uint32 code = 1;
    bytes tipOff_data = 2;  // 序列化的整个TipOffDat数据
}
// 存储举报数据
message G2M_SaveTipOffData {
    bytes tipOff_data = 2;  // 序列化的TipOffDat数据
}

message M2G_SaveTipOffData {
    uint32 code = 1;
}

// 加载赛季buff数据
message G2M_LoadSeasonBuffData {
}
message M2G_LoadSeasonBuffData {
  uint32 code = 1;
  bytes seasonBuff_data = 2;  // 序列化的整个赛季buff数据
}
// 存储赛季buff数据
message G2M_SaveSeasonBuffData {
  bytes seasonBuff_data = 2;  // 序列化的整个赛季buff数据
}
message M2G_SaveSeasonBuffData {
  uint32 code = 1;
}
