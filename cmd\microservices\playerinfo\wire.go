//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"liteframe/internal/microservices/playerinfo/biz"
	"liteframe/internal/microservices/playerinfo/conf"
	"liteframe/internal/microservices/playerinfo/data"
	"liteframe/internal/microservices/playerinfo/registry"
	"liteframe/internal/microservices/playerinfo/server"
	"liteframe/internal/microservices/playerinfo/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init playerinfoserver application.
func wireApp(*conf.Server, *conf.Data, *conf.Bootstrap, log.Logger, map[string]string) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, registry.ProviderSet, newApp))
}
