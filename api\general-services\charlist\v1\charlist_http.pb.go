// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.28.1
// source: general-services/charlist/v1/charlist.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationCharListHttpGetCharListData = "/charlist.v1.CharListHttp/GetCharListData"

type CharListHttpHTTPServer interface {
	GetCharListData(context.Context, *GetCharListReq) (*GetCharListReply, error)
}

func RegisterCharListHttpHTTPServer(s *http.Server, srv CharListHttpHTTPServer) {
	r := s.Route("/")
	r.GET("/api/charlist/getcharlist/{account}", _CharListHttp_GetCharListData0_HTTP_Handler(srv))
}

func _CharListHttp_GetCharListData0_HTTP_Handler(srv CharListHttpHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetCharListReq
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		if err := ctx.BindVars(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationCharListHttpGetCharListData)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetCharListData(ctx, req.(*GetCharListReq))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetCharListReply)
		return ctx.Result(200, reply)
	}
}

type CharListHttpHTTPClient interface {
	GetCharListData(ctx context.Context, req *GetCharListReq, opts ...http.CallOption) (rsp *GetCharListReply, err error)
}

type CharListHttpHTTPClientImpl struct {
	cc *http.Client
}

func NewCharListHttpHTTPClient(client *http.Client) CharListHttpHTTPClient {
	return &CharListHttpHTTPClientImpl{client}
}

func (c *CharListHttpHTTPClientImpl) GetCharListData(ctx context.Context, in *GetCharListReq, opts ...http.CallOption) (*GetCharListReply, error) {
	var out GetCharListReply
	pattern := "/api/charlist/getcharlist/{account}"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationCharListHttpGetCharListData))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
