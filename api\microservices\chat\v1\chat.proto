//下面这行很重要，是表明你这个Service用于哪个URL的，见游戏服的GrpcAddressType
//GrpcAddressType:ChatLocalServer
//GrpcAddressType:ChatZoneServer
//GrpcAddressType:ChatGlobalServer
//GrpcServerType:all

syntax = "proto3";

package Aurora.PlayerInfoServer;

import "google/api/annotations.proto";
import "microservices/playerinfo/v1/playerinfostruct.proto";

option go_package = "liteframe/api/microservices/chat/v1;v1";

//ServiceStart
service ChatService {
rpc SendChatMessage(SendChatMessageRequest) returns (SendChatMessageReply) {
}

rpc BrocastChatMessage(G2G_ChatMessage) returns (SendChatMessageReply) {
}

rpc OperNoticeProxy(DispatchOperNoticeInfo) returns (DispatchOperNoticeReply) {
}
}
//ServiceEnd

//Type:ClientWebSocket
enum WebSocketMessageIDWebSc
{
ChatMessage_ID = 0;
RequestOfflineMessage_ID = 1;
OperNoticeProxy = 2;
}

//Type:ClientWebSocket
enum DispatchOperNoticeInfoEnumType
{
none = 0;
tips = 1; // 提示 参数传字典
tips_Param = 2; // 提示 参数传字典   123|param1|param2
tips_guidGetName = 3; // 提示 参数传字典  123|guid
// friend  1001 --- 2000
Friend_start = 1000;
Friend_AddFriendPlayer = 1001;
Friend_DelFriendPlayer = 1002;
Friend_GroupBaseInfo = 1003;
Friend_InviteJoinGroupList = 1004;
Friend_UpdateSelfGroupListInfo = 1005;
Friend_ApplyFriend = 1006;//好友申请
Friend_ApproveFriendSingle = 1007;//同意一个好友
Friend_ApproveFriendAll = 1008;//同意所有好友
Friend_RemoveFriendSingle = 1009;//拒绝单个好友
Friend_RemoveFriendAll = 1010;//拒绝所有好友
Friend_DeleteFriend = 1011;//删除好友
Friend_end = 2000;
// 调查问卷  2001
Questionnaire_AskList = 2001;
}

//Type:ClientWebSocket
//Type:ServerWebSocket
message ChatPlayerInfo
{
string name = 1;
int32 level = 2;
int32 iconID = 3;
string iconUrl = 4;
int32 iconFrameID = 5;
int32 petID = 6;
string guildName = 7;
int32 serverID  = 8;
}

//Type:ClientWebSocket
//Type:ServerWebSocket
enum MessageChannelType
{
MessageType_Invalid = 0;
MessageType_World = 1;			//LocalServer,ZoneServer
MessageType_Team = 2;			//GlobalServer
MessageType_Raid = 3;			//GlobalServer
MessageType_Guild = 4;			//LocalServer
MessageType_Near = 5;			//GlobalServer
MessageType_Speaker = 6;		//LocalServer,ZoneServer
MessageType_PrivateChat = 7;	//GlobalServer
MessageType_GroupChat = 8;		//GlobalServer
MessageType_Recruit = 9;		//LocalServer,ZoneServer
MessageType_System = 10;		//LocalServer,ZoneServer,GlobalServer
MessageType_Scene = 11;			//GlobalServer
MessageType_GongGao = 12;		//LocalServer,ZoneServer,GlobalServer
}

//Type:ClientWebSocket
//Type:ServerWebSocket
enum MessageSubType{
MessageSubType_Invalid = 0;
MessageSubType_Broadcast = 1;
MessageSubType_Private = 2;
MessageSubType_Team = 3;
MessageSubType_Guild = 4;
MessageSubType_Near = 5;
MessageSubType_Scene = 6;
MessageSubType_Broadcast_Save = 7;
}

//Type:ClientWebSocket
//Type:ServerWebSocket
enum TargetChatServer
{
TargetServer_Invalid = 0;
//本服
TargetServer_LocalServer = 1;
//区服
TargerServer_ZoneServer = 2;
//跨服
TargetServer_GlobalServer = 3;
TargetServer_Proxy = 10;
}

//Type:ClientWebSocket
//Type:ServerWebSocket
enum ChatChannelType
{
ChatType_Invalid = 0;
ChatType_Message = 1;
ChatType_BigEmoji = 2;
ChatType_RedEnvelope = 3;
ChatType_Voice = 4;
}

//Type:ClientWebSocket
//Type:ServerWebSocket
enum ChatShareItemType
{
ShareType_Item = 0;
ShareType_Pet = 1;
ShareType_Mount = 2;
ShareType_TagFriend = 3;
ShareType_Coordinate = 4;
ShareType_FormatClientStr = 5;
ShareType_InviteGuild = 6;
ShareType_TeamUp = 7;
}

//Type:ClientWebSocket
//Type:ServerWebSocket
message ChatShareItem
{
ChatShareItemType shareType = 1;
uint64 guid = 2;
bytes info = 3;
}

//Type:ClientWebSocket
//Type:ServerWebSocket
//WebSocketMessageIDWebSc.ChatMessage_ID
message ChatMessage
{
int64 timeStamp = 1;
int32 msgId = 2;
int32 zoneWorldId = 3;
uint64 senderGuid = 4;
PlayerBaseInfo senderPlayerInfo = 5;
MessageChannelType messageType = 6;
MessageSubType messageSubType = 7;
TargetChatServer targetChatServer = 8;
ChatChannelType channelType = 10;
uint64 paramId1 = 11;
int64 paramId2 = 12;
string message = 13;
repeated ChatShareItem itemList = 14;
int32 headFrameId = 15;
string headIconId = 16;
}

message SendChatMessageRequest
{
ChatMessage chatMessage = 1;
repeated uint64 receiverGuids = 2;
bool NeedTextDetect = 3;
int64 SaveTime = 4;
}

// The response message containing the message
message SendChatMessageReply
{
int32 result = 1;
}

message G2G_ChatMessage
{
ChatMessage chatMessage = 1;
repeated uint64 receiverGuids = 2;
bool NeedTextDetect = 3;
int64 SaveTime = 4;
}

//Type:ClientWebSocket
//WebSocketMessageIDWebSc.RequestOfflineMessage_ID
message RequestOfflineMessage
{
MessageChannelType messageType = 1;
MessageSubType messageSubType = 2;
TargetChatServer targetChatServer = 3;
uint64 paramId1 = 4;
}


//Type:ClientWebSocket
//WebSocketMessageIDWebSc.OperNoticeProxy
message DispatchOperNoticeInfo
{
uint64   TargetGuid = 1;
int32 	OperType = 2;
string 	OperParamStr = 10;
}

message DispatchOperNoticeReply
{
int32   Result = 1;
int32 	OperType = 2;
}

