// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.3
// - protoc             v5.28.1
// source: microservices/playerinfo/v1/playerinfostruct.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationPlayerInfoServiceGetPlayerInfo = "/Aurora.PlayerInfoServer.PlayerInfoService/GetPlayerInfo"
const OperationPlayerInfoServiceGetPlayerInfoList = "/Aurora.PlayerInfoServer.PlayerInfoService/GetPlayerInfoList"

type PlayerInfoServiceHTTPServer interface {
	// GetPlayerInfo 查询玩家信息
	GetPlayerInfo(context.Context, *GetPlayerInfoRequest) (*GetPlayerInfoReply, error)
	// GetPlayerInfoList 查询玩家信息
	GetPlayerInfoList(context.Context, *GetPlayerInfoListRequest) (*GetPlayerInfoListReply, error)
}

func RegisterPlayerInfoServiceHTTPServer(s *http.Server, srv PlayerInfoServiceHTTPServer) {
	r := s.Route("/")
	r.POST("/v1/playerinfo", _PlayerInfoService_GetPlayerInfo0_HTTP_Handler(srv))
	r.POST("/v1/playerinfolist", _PlayerInfoService_GetPlayerInfoList0_HTTP_Handler(srv))
}

func _PlayerInfoService_GetPlayerInfo0_HTTP_Handler(srv PlayerInfoServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPlayerInfoRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlayerInfoServiceGetPlayerInfo)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPlayerInfo(ctx, req.(*GetPlayerInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPlayerInfoReply)
		return ctx.Result(200, reply)
	}
}

func _PlayerInfoService_GetPlayerInfoList0_HTTP_Handler(srv PlayerInfoServiceHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in GetPlayerInfoListRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationPlayerInfoServiceGetPlayerInfoList)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.GetPlayerInfoList(ctx, req.(*GetPlayerInfoListRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*GetPlayerInfoListReply)
		return ctx.Result(200, reply)
	}
}

type PlayerInfoServiceHTTPClient interface {
	GetPlayerInfo(ctx context.Context, req *GetPlayerInfoRequest, opts ...http.CallOption) (rsp *GetPlayerInfoReply, err error)
	GetPlayerInfoList(ctx context.Context, req *GetPlayerInfoListRequest, opts ...http.CallOption) (rsp *GetPlayerInfoListReply, err error)
}

type PlayerInfoServiceHTTPClientImpl struct {
	cc *http.Client
}

func NewPlayerInfoServiceHTTPClient(client *http.Client) PlayerInfoServiceHTTPClient {
	return &PlayerInfoServiceHTTPClientImpl{client}
}

func (c *PlayerInfoServiceHTTPClientImpl) GetPlayerInfo(ctx context.Context, in *GetPlayerInfoRequest, opts ...http.CallOption) (*GetPlayerInfoReply, error) {
	var out GetPlayerInfoReply
	pattern := "/v1/playerinfo"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlayerInfoServiceGetPlayerInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *PlayerInfoServiceHTTPClientImpl) GetPlayerInfoList(ctx context.Context, in *GetPlayerInfoListRequest, opts ...http.CallOption) (*GetPlayerInfoListReply, error) {
	var out GetPlayerInfoListReply
	pattern := "/v1/playerinfolist"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationPlayerInfoServiceGetPlayerInfoList))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
