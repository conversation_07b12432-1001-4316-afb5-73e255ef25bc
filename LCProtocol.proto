//协议 ID 类型为 short，-32767 到 32767
//StartMessageID = 2000; // 必须以;分号结束
//MaxMessageID = 2999; // 必须以;分号结束
syntax = "proto3";
option go_package = "liteframe/internal/common/protos/cs";

option csharp_namespace = "Game.Core";

import "PublicMessage.proto";
import "PublicEnum.proto";

//Logic 服返回返回客户端登录结果
message LC_LOGIN_RET
{
	int32 result = 1;	//1: 成功，其他错误
	bool isCreateRole = 2;	//是否是第一次创建角色
}

//服务器到客户端的 heartbeat
message LCHeartBeat
{

}

//Logic 同步玩家数据
message LC_PlayerData_Sync
{
	PBPlayerInfo playerBaseInfo = 1;   //玩家基础数据
	bool iSReconnect = 2;   //是否重连
}

//通知客户端最后一个登录消息包
message LC_PlayerData_Complate
{
	bool iSReconnect = 1;   //是否重连
}


//醒目提示
message LCNotice
{
	string notice = 1;			//notice 的内容
}

//服务器登陆返回的时间
message LCLoginUpdateServerTime
{
	int32	timeZoneSecond		= 1;	//服务器时区秒差
	int64	centuryYearSecond = 2;		//缓存一个固定的时间搓  主要用于计算一些差值方便  防止一些 int 数据溢出
	int64	ServerTime		= 3;	//服务器时间
}

//服务器时间返回普通的通知
message LCUpdateServerTime
{
	int64	ServerTime		= 3;	//服务器时间
}

//服务器提醒客户端跨天
message LCNotifyCrossDay
{
}

//Logic 同步玩家基础资源信息
message LC_PlayerResource_Sync
{
	PBPlayerMoneyInfo moneyData = 1;  //代币信息
}
//======================关卡  Begin=========================================
//掉落道具同步展示
message LCDropItemListRes
{
	repeated PBDropItemDataInfo itemInfo = 1;
	int32 dropType = 2;  // 0 主界面左下角 1 宝箱掉落 2 单条 tips 3 UI_GetReward_Logic 点击不显示 tips 4 UI_GetReward_Logic 点击显示 tips 5 宝箱显示 6 龙合成 7 用于挖矿宝箱三选一 9 爬塔
}
//场景里面里面掉落
message LCSceneDropItemListRes
{
	repeated PBDropItemDataInfo itemInfo = 1;		//掉落的信息
	int32 stageId = 2;								//场景的 id
	int32 x = 3;									//场景里面的坐标
	int32 y = 4;									//场景里面的坐标
	SceneDropType dropType = 5;									//0 小怪  1 彩虹马
}

//======================关卡   End=========================================
//====================红点相关开始=======================

//红点
message LCSyncRedDotData
{
	repeated PBRedDotData reddata = 1;
}


//===================红点相关结束========================
//=========================背包相关开始=============================
//背包物品同步
message LCSyncBagItemListRst
{
	repeated PBBagItemInfo bagItemList = 1; //背包物品集合
}

//=========================背包相关结束=============================

//==========================道具相关开始============================

//同步全部物品信息
message LCAllItemList
{
	repeated PBItemDataInfo itemInfo = 1;
}

//同步单个物品信息
message LCSingleItem
{
	PBItemDataInfo itemInfo = 1;
}
//=========================道具相关结束=============================


//======================属性升级  Begin=========================================
//属性升级 回包
message LCAttributeLevelUp
{
    int32 result          =1;         //0 成功   1 失败
	PBAttributeInfo info  =2;         //升级后的属性
	repeated PBAttributeInfo unlockList =3;//升级后解锁的属性列表
}
//======================属性升级  End=========================================

//======================角色升级  Begin=========================================
//增加经验返回
message LC_AddExp_RES
{
	bool isLevelUp = 1;//true 升级成功，false 未升级，但经验有变动
	int32 addExp = 2;//实际增加的值
	int32 afterExp = 3;//增加后的值
	int32 beforeLevel = 4;//增加前等级
	int32 afterLevel = 5;//增加后等级
	repeated int32 funcList = 6;//解锁的功能列表
	repeated int32 skillList = 7;//解锁的技能列表
}
//======================角色升级  End=========================================

//======================挂机系统  Begin=========================================
//挂机面板回包
message LCOpenHook
{
	//int32 collectDuration = 1;//收集时长，单位：分钟
	int64 hookStartTime = 1; // 开始挂机时间 单位：秒
	//int32 collectGold = 2; //获得的金币总值
	//int32 curGoldSpeed = 3; //收集速度 单位：金币数/分钟
	//int32 collectExp = 4; //获得的经验总值，大数值，字符串表示
	//int32 curExpSpeed = 4; //收集速度 单位：每产出一个图纸的秒数
	repeated PBDropItemDataInfo itemInfo = 5; //普通挂机：展示的奖励（暂时只用到了物品ID和数量）
    //int32 sweepCount= 6; //扫荡的次数
	//int32 extraBtnState = 7;//额外奖励按钮状态 0.正常显示，1.置灰 未到 1 小时，2.今日已达上限
	int32 extraCollectNums = 7;	// 剩余额外奖励次数
	//int32 extraCollectGold = 8; //额外奖励获得的金币总值，大数值，字符串表示
	//int32 extraCollectExp = 9; //额外奖励获得的经验总值，大数值，字符串表示
	repeated PBDropItemDataInfo extraItemInfo = 10; //额外奖励：展示的奖励
	//int32 elapsedExtratime = 11; //额外奖励：已经过去的时间，单位：秒，仅针对按钮状态为 1
	//bool login = 12;//是否是登录下发
}
// 领取挂机奖励 (额外奖励也用这个)
message LCReceiveHook
{
	int32 result          =1;         //0 成功   1 失败
}
//领取挂机额外奖励
//message LCReceiveExtraHook
//{
	//int32 result          =1;         //0 成功   1 失败
//}
//领取扫荡奖励
message LCReceiveHookSweep
{
	int32 result          =1;         //0 成功   1 失败
                int32 sweepCount          =2;         //扫荡次数
}
//======================挂机系统  End=========================================


//======================邮件相关开始==========================

//返回所有的邮件
message LCMailListRes
{
	repeated PBMail mails = 1; //新邮件列表
}
//读取邮件返回
message LCReadMailRes
{
	int64 mailId = 1; //已读取的邮件 ID
}

//领取奖励返回
message LCReceiveMailRes
{
	MailType mailType = 1;	     //邮件类型
	int64 mailId = 2; 			 //已经领取附件的邮件 ID
	MailStateType mailState = 3; //邮件的状态
}
//玩家领取所有邮件
message LCReceiveAllMailRes
{
	repeated PBMail mails = 1; //新邮件列表
}

//删除一封邮件返回
message LCDelMailRes
{
	MailType MailType = 1; //邮件类型
	int64 mailId = 2; //删除的邮件 id
}

//一键删除返回
message LCDelAllReadMailRes
{
	repeated PBMail mails = 1; //新邮件列表
}
//问卷奖励邮件返回
message LCMailQuestionAwardRes
{
	repeated PBMail mails = 1; //新邮件列表
}
//======================邮件相关结束==========================

//=====================账号信息开始=============================
//改名
message LCChangeNameRes
{
	string Name = 1;   		//名字：失败返回空
	int32 ErrorType = 2;	//错误类型：-1、没有错误 1、长度不符 2、名字重复
	int32 Source = 3; //标识来源：0 标识是改名界面，1 标识是起名界面
}

//修改个性签名返回
message LCChangeSignRst
{
	string signContent = 1; //修改后的签名信息
}

//修改玩家性别返回
message LCChangeGenderRst
{
	EGenderType gender = 1; //枚举性别
}

//申请销号返回
message LCCancellationRst
{
	int64 calmEndTime = 1; //冷静期结束时间戳秒（真正执行销号状态的时间）
}

//=====================账号信息结束=============================

//===================新版新手引导开始==========================
//初始化新手的步骤信息
message LCInitNewGuideStepInfoRes
{
	repeated int32 historyNewGuideIds = 1;						//历史的完成新手 (仅用于客户端需发起的 引导)
	PBCommonKeyValue curNewGuideSetpInfo = 2;					//当前的引导信息
}
//同步新手的步骤信息
message LCNewGuideStepInfoRes
{
	repeated int32 historyNewGuideIds = 1;						//历史的完成新手 (仅用于客户端需发起的 引导)
	PBCommonKeyValue curNewGuideSetpInfo = 2;					//当前的引导信息
}
//更新当前的新手信息
message LCUpCurNewGuideStepInfoRes
{
	PBCommonKeyValue curNewGuideSetpInfo = 1;					//当前的引导信息
}

//新手引导信息同步
message LCSyncNewGuideInfoRsp
{
	repeated int32 finishedGroupIds = 1; //已完成的引导组ID集
	PBIntPair curGuide = 2;				 //当前进行中的引导信息
}
//===================新版新手引导结束==========================

//======================签到开始=========================================
//签到信息返回
message LCSignInInfoResponse
{
	SignInToType SignInType 		= 1;	//签到类型（七日，月签，月签阶段奖励）
	repeated int32 SignInIdList		= 2;	//	签到的表格 id
	repeated int32 SignInStateList 	= 3;	//	领取状态 1 可领取 0 已领取 -1 未领取
	bool isFirstLogin 				= 4;	//每日首次登陆弹窗
	bool isFirstUnLock 				= 5;	//七日签到是否首次解锁
	int32 monthSignNum 				= 6; 	//月签到累计签到天数
}
// 签到奖励信息确认
message LCSignInGetRewardResponse
{
	SignInToType SignInType = 1;	// 签到类型（只用于月签和月签阶段奖励）
	int32 dayAddr 			= 2;	// 位置
	int32 state				= 3;	// 1 可领取 0 已领取 -1 未领取
	int32 monthSignNum 		= 4; 	// 月签到累计签到天数
}

// 月签到一键领取签到奖励信息
message LCSignMonthGetRewardResponse
{
	repeated int32 dayAddr 	= 1; //签到的天数
	repeated int32 state	= 2; //1 可领取 0 已领取 -1 未领取
	int32 monthSignNum 		= 3; //月签到累计签到天数
}
// 七日签到领取奖励
message LCSignSevenRewardResponse
{
	repeated int32 dayAddr 	= 1;	    //签到的天数
	repeated int32 state 	= 2;	//1 可领取 0 已领取 -1 未领取
}
//======================签到结束=========================================


//======================系统提示开始=========================================
//通知系统提示的
message LCSystemShowErrorMessageBox
{
	SystemShowErrorType errorType = 1;
	string countStr = 2;		//提示的内容
	int32 specialArgs = 3;		//特殊的参数信息 没有特殊要求不要赋值
	int32 codeNum = 4;			//错误码 LobbyDisConnectCode
}

//======================系统提示结束=========================================

//=====================玩家设置相关开始=============================
//向客户端同步返回设置数据
message LCSyncSettingRes
{
	PBSettingData data = 1;//设置相关结构
}

//=====================玩家设置相关结束=============================
//====================社交分裂开始=======================
//登录同步
message LCSyncAllFriendInvitationRes
{
	int64 invitationCode = 1; //邀请码
	repeated PBFriendRasinRewardData rasinRewardDataList = 2;
	repeated PBFriendGrowthRewardData growthRewardDataList = 3;
	int64 curPlayerUID = 4; //当前玩家 UID
	int64 invitationCodeStatus = 5; // >0 就是已经填写过邀请 值就是那个人的 ID  <=0 目前还可以填写邀请码
}
//打开界面同步
message LCFriendInvitationMainRes
{
	int64 invitationCode = 1; //邀请码
	repeated PBFriendRasinRewardData rasinRewardDataList = 2;
	repeated PBFriendGrowthRewardData growthRewardDataList = 3;
	int64 curPlayerUID = 4; //当前玩家 UID
	int64 invitationCodeStatus = 5; // >0 就是已经填写过邀请 值就是那个人的 ID  <=0 目前还可以填写邀请码
}
//同步单条奖励领取
message LCSyncFriendInvitationRes
{
	PBFriendRasinRewardData rasinRewardData = 1;
}
//社交分裂任务列表
message LCSyncFriendGrowthQuestRewardRes
{
	int64 playerUID = 1; //玩家 UID
	repeated PBFriendGrowthQuestRewardData growthQuestRewardDataList = 2;
}
//同步社交分裂任务
message LCSyncFriendGrowthReceiveRewardRes
{
	int64 playerUID = 1; //玩家 UID
	PBFriendGrowthQuestRewardData growthQuestRewardData = 2;
}
//分享成功 同步
message LCSyncSharePosterRes
{
	repeated PBFriendGrowthRewardData growthRewardDataList = 3;
}
//邀请码状态
message LCSyncEnterInvitationCodeRes
{
	int32 invitationCodeStatus = 1; //0 成功 1 失败填写错误邀请码  2 失败单向邀请关系
}
//===================社交分裂结束========================

//====================限时礼包开始==============================
// 同步所有的限时礼包

//====================限时礼包结束==============================
//====================================充值配置开始====================================
//充值标记同步
message LCSyncChargeRes
{
	int32 chargeFlag = 1; //各挡位是否首充标记 默认 0  bit 位含义见 表 TablePayment FirstFlag 列
}
//米大师钻石充值、道具直购、IOS 订阅发货成功 [c#]
message LC_MIDAS_DILIVERY_SUCCESS
{
}

//米大师钻石充值、道具直购、IOS 订阅发货成功 [c#]
message LC_MIDAS_DILIVERY_RepeatPurchase
{
	int32 midasItemId = 1; //midasItemId
	int32 Diamond = 2;//转化的钻石
}

//米大师钻石充值压测占位
message LC_MIDAS_DIAMOND_NTF
{

}

//米大师道具直购压测占位
message LC_MIDAS_ITEM_NTF
{
	int32 packetgroup = 1; //
	int32 dropItemId = 2;//
}
//===================================充值配置结束======================================

//断线重连成功结束标识
message LCReConnectComplete
{

}


//=====================AF 埋点相关数据=============================
//AF 埋点相关数据
message LCAFData
{
	int32 AFType = 1;// 1 铲子累计挖 20 次后上报  2 完成 50 次装备召唤后上报 3 完成 200 次装备召唤后上报 4 完成 50 次技能召唤后上报 1 5 完成 200 次技能召唤后上报 1   6 完成 50 次宠物召唤后上报 1  7 完成 200 次宠物召唤后上报 1 8 升级龙  9 合成龙 10 解锁时装 11 解锁宠物 12 DPS 副本有效次数
}
//======================AF 埋点相关数据=============================
//=====================通用阶段宝箱相关数据=============================

//阶段宝箱相关数据
message LCCommonExpBoxData
{
	repeated PBCommonExpBoxInfo BoxData = 1;
}

//完成阶段宝箱
message LCFinishCommonExpBoxData
{
	ServerResultCode code = 1;
	int32 box_id = 2;
}
//======================通用阶段宝箱相关数据=============================

//======================活动开始=========================================
message LCPushActivityInfo
{
	repeated PBActivityInfo activityInfoList = 1;
}

//领取活动奖励
message LCGetActivityReward
{
	int32 result = 1;		// 0 成功   1 失败
	int32 activity_id = 2; //活动 ID
	repeated int32 reward_param = 3; //奖励参数
}

//开放中的七日活动数据
message LCGetSevenDayActivityData
{
	int32 activity_id = 1; //活动 ID
	int32 unlock_day = 2; //解锁的天数
	int64 end_time = 3; //活动结束时间(秒级时间戳)
}

//========================活动结束==========================================
//=============排行榜相关开始=============================
// 在线 5 点消息通知
message LCServerTimeDailyFiveRes
{

}
//排行榜操作
message LCRankListRes
{
	int32 rankType = 1;   //排行榜类型
	repeated PBPlayerBaseInfo playerList = 2; //个人排行榜
	PBPlayerBaseInfo myPbPlayerBaseInfo = 3;  //我的信息
	int32 myRankNum = 4;		//我的名次
	int32 myBeforeRankNum = 5;		//上次我的名次
	//附加参数 不同排行榜 不同的用途
	repeated int32 iParam = 6;
}
//排行榜排行奖励操作
message LCRankRewardRes
{
	int32 rankType = 1;   //排行榜类型
	int32 result = 2;   //排行榜类型 1 成功
}
//=============排行榜相关结束=============================


//广告客户端上报数据
message LCClientAdData
{
	int32 result = 1; //0 成功   1 失败
	int32  Adtype = 2; //  类型 0.广告任务  1.广告邮件
	int64  Addata = 3; //  数据 任务 ID 或邮件 ID 等唯一标识
}

//======================问卷  Start=====================================
//同步生效的问卷列表
message LCSyncQuestList
{
	repeated PBQuestDataInfo questInfoList = 1; //问卷列表
}
//同步单个问卷
message LCSyncSingleQuest
{
	PBQuestDataInfo questInfo = 1;
}
//======================问卷  End=====================================


//请求其他玩家信息
message LCPlayerOtherInfo
{
	PBPlayerOtherInfo otherPlayer = 1; //其他玩家信息
}


//=====================公会系统开始==================================
//公会大厅主界面（已在公会中）
message LCGuildMain
{
	string name 	= 1;	//名称
	int32 level 	= 2;	//等级
	int32 iconId 	= 3;	//图标
	int32 showId	= 4;	//显示 ID
	repeated PBGuildMember topList = 5;	//前三名贡献成员
	int32 bossStageId	= 6;	//当前 BOSS 关卡 ID
	int32 bossOpenTime 	= 7;	//Boss 开启时间戮，单位：秒，0 表示不显示倒计时，可以攻击 BOSS。
	int32 techOpenTime 	= 8;	//科技开启时间戮，单位：秒，0 表示不显示倒计时，可以升科技。
}
//公会推荐列表界面（不在公会时）
message LCGuildRecommendList
{
	int32 errorcode = 1;                          // 错误码，0 表示成功
	repeated PBGuildRecommend guildList = 2;
}
//公会搜索
message LCGuildSearch
{
	PBGuildRecommend guildInfo		= 1;
}
//公会搜索
message LCGuildApply
{
    int32 errorcode = 1; // 错误码，0 表示成功
    int64 guild_id = 2;  // 申请的联盟 ID
}
//公会大厅
message LCGuildHall
{
    int32 errorcode = 1;                               // 错误码，0 表示成功
    PBGuildDetailInfo guild_info = 2;                  // 联盟的核心详细信息 (新增，使用公共结构)
    repeated PBGuildMember member_list = 3;            // 成员列表 (原 LCGuildHall 的 member_list)
    GuildPosition cur_pos = 4;                         // 请求者自己当前身份 (原 LCGuildHall 的 cur_pos)
}
//公会科技
message LCGuildTech
{
	repeated PBGuildTech techList	= 1; //已拥有的科技列表
}
//公会科技强化
message LCGuildTechLevelup
{
	int32 result 		= 1; 	//0 成功   1 失败
	PBGuildTechLevelup tech 	= 2;	//升级的科技最新信息
}
//公会科技重置
message LCGuildTechReset
{
	repeated PBGuildTech techList	= 1; //已拥有的科技列表
}

//修改公会信息
message LCGuildEdit
{
	int32 errorcode = 1; 	 // 错误码，0 表示成功
	GuildOpt opt	= 2;	 //具体操作 0.修改旗帜 1.修改名字 2.修改公告 3.修改申请条件 下面的字段根据此操作提交对应值。
	string name 	= 3;	 //名称
	string notice 	= 4;	 //宣言
	int32 iconId 	= 5;	 //图标
	bool freeJoin 	= 6;	 //true 自由加入 false 需要审批，
	int32 reqStage	= 7;	 //审批时需具备的关卡条件：0.无限制 1.困难 2.疯狂 3.地狱
	string announcement = 8; // 修改后的公告 (新增)
}
//公会申请列表
message LCGuildApplyMgrList
{
    int32 errorcode = 1;                      // 错误码，0 表示成功
    repeated PBGuildApply apply_list = 2;     // 申请列表
	int32 today_joined_count = 3;     		  // 今日已通过申请/快速加入的人数
    int32 daily_max_join_limit = 4;   		  // 服务器写死的每日最大入盟人数上限 (方便客户端显示 X/Y)
}
//公会成员管理
message LCGuildMemberMgrList
{
    int32 errorcode = 1;                        // 错误码，0 表示成功
    repeated PBGuildMember member_list = 2;     // 成员列表 (用于管理界面)
}
//公会申请列表操作
message LCGuildApplyMgr
{
    int32 errorcode = 1;         // 错误码，0 表示成功
    GuildOpt opt = 2;            // 具体操作
    int64 applicant_id = 3;      // 被处理的申请者 id
	PBGuildMember member = 4;	 // 被操作的成员的公会信息
}
//公会成员管理
message LCGuildMemberMgr
{
    int32 errorcode = 1;           // 错误码，0 表示成功
    GuildOpt opt = 2;              // 具体操作
    int64 target_player_id = 3;    // 被操作的成员 id
	PBGuildMember member = 4;	   // 被操作的成员的公会信息
}
//退出公会
message LCGuildQuit
{
	int32 errorcode = 1; // 错误码，0 表示成功
}
//解散公会
message LCGuildDismiss
{
	int32 errorcode = 1; // 错误码，0 表示成功
}
//弹劾会长
message LCGuildImpeach
{
	int32 result 		= 1; 	//0 成功   1 失败
}
//发送世界邀请
message LCGuildSendWorldInvite
{
	int32 result 		= 1; 	//0 成功   1 失败
}
//发送私聊邀请
message LCGuildSendPlayerInvite
{
	int32 result 		= 1; 	//0 成功   1 失败
	int64 toPlayerId 	= 2;   //私聊的对象 ID
}
//公会邀请加入
message LCGuildInviteJoin
{
	int32 result 	= 1; 	//0 成功   1 失败
	int64 id		= 2;	//公会 ID，提交用
}
//公会捐献
message LCGuildDonate
{
	int32 result 		= 1; 	//0 成功   1 失败
	int32 opt			= 2;	//捐献操作：0.免费 1.金币 2.钻石
	repeated int32 remainCount	= 3;	//各个档剩余捐献次数
	int32 level 		= 4;	//等级
	int32 exp			= 5;	//当前经验
	int64 contribution	= 6;	//自己贡献度
	int64 guildContribution	= 7;	//公会贡献度
}
//公会排行榜
message LCGuildRank
{
	repeated PBGuildRankInfo guildList 	= 1;
	int32 myGuildRank					= 2; //-1 表示未上榜
}
//公会日志
message LCGuildLog
{
	repeated PBGuildLog logList = 1; //已按时间降序的日志列表
}
//公会砍价礼包点击
message LCGuildBargainingInfo
{
	PBBargainingGift bargainingGift = 1; 	//砍价礼包详情
}
//公会砍价礼包提醒砍价
message LCGuildBargainingNotice
{
	int32 result 			= 1; 	//0 成功   1 失败
	int64 id				= 2;	//礼包唯一 ID
	int32 nextNoticeTime	= 3;	//下一次通知未砍价的时间戮，单位：秒，默认为 0，表示可以立即通知。
}
//公会砍价礼包砍价
message LCGuildBargaining
{
	int32 result 					= 1; 	//0 成功   1 失败
	PBBargainingGift bargainingGift = 2; 	//砍价礼包详情
}
//公会砍价礼包购买
message LCGuildBargainingBuy
{
	int32 result 					= 1; 	//0 成功   1 失败
	PBBargainingGift bargainingGift = 2; 	//砍价礼包详情
}
//同步玩家公会 ID 数据
message LC_Guild_Sync
{
	int64 guildId 						= 1;	//当前所在的公会 ID，0 表示没有加入任何公会。
	string name 						= 2;	//名称
	int32 level 						= 3;	//等级
	int32 iconId 						= 4;	//图标
	repeated PBGuildTech techList		= 5;	//已拥有的科技列表
	repeated int64 pending_apply_guild_ids = 6; // 玩家当前已申请但未处理的联盟ID列表 (新增)
	GuildOpt opt = 7;							// 触发此时同步数据的公会操作
	GuildPosition position = 8; 				// 职位
}
//公会 BOSS 进入
message LCGuildBossEnter
{
	int32 stageId 		= 1;	//当前关卡 ID
	int64 hp			= 2;	//当前血量
	int32 buyCount		= 3;	//今日已购买次数
	bool atked			= 4;	//是否挑战过 True:挑战过
}
//公会 BOSS 挑战开始
message LCGuildBossAtkBegin
{
	int32 stageId 		= 1;	//当前关卡 ID
	int64 hp			= 2;	//当前血量
	repeated PBGuildBossRankPlayer playerList = 3;	//该 BOSS 的伤害榜前 3 名
}
//公会 BOSS 扫荡
message LCGuildBossSweep
{
	int32 stageId = 1;
	repeated PBDropItemDataInfo itemInfo = 2; //展示的奖励
	int32 stageResult = 3;  //战斗结果 1 胜利 2 失败 3.破纪录的失败
	int32 firstreward = 4;  //是否是首通
	int64 stagecount = 5;  // 用于携带杀敌次数 或者输出伤害等
	int64 hp = 6;	//当前血量
}
//公会 BOSS 次数购买
message LCGuildBossBuyCount
{
	int32 result 		= 1; 	//0 成功   1 失败
	int32 buyCount		= 2;	//今日已购买次数
}
//公会 BOSS 总排行
message LCGuildBossRank
{
	repeated PBGuildBossRankPlayer playerList = 1;	//伤害榜
}
// 创建公会
message LCGuildCreate
{
	int32 errorCode = 1;
	int64 guildId = 2;
}
//公会商店
message LCGuildShop
{
    int32 errorCode = 1;
    repeated PBGuildShopOrder orderList = 2;
}
//公会商店购买
message LCGuildShopBuy
{
    int32 errorCode = 1;
    int32 id        = 2; //商品 ID
    int32 type      = 3; //购买类型 1.free 2.adfree 3.normal
    int32 count     = 4; //购买数量
}
//=====================公会系统结束===================================

//=====================聊天开始===================================
// 同步消息
message LCSyncChatInfo
{
  ChatType  chattype =1;
  repeated PBChatInfo  chatList =2;
}
// 广播协会消息
message LCBroadcastGuideChatInfo
{
  ChatType  chattype =1;
  PBChatInfo  guideChat =2;
}
// 私聊通知消息
message LCBroadcastPrivateChatInfo
{
  PBPrivateChatInfo  privatChat =2;
}
// 登录私聊
message LCLoginGetAllPrivateChatInfo
{
  repeated PBPrivateChatInfo  privatChat =2;
}
// 登录世界 和协会
message LCLoginGetAllChatInfo
{
  ChatType  chattype =1;
  repeated PBChatInfo  chatList =2;
}

//=====================聊天结束===================================

//=====================好友开始===================================
//同步推荐好友
message LCSyncRecommendFriendRes
{
	repeated PBPlayerBaseInfo friendRecommendListInfo = 1;	//推荐的好友信息
	bool changeBatch = 2;									//是否是换一批请求 true:是 false:否
}

//同步所有好友信息
message LCSyncAllFriendsRes
{
	repeated PBPlayerBaseInfo allFriends = 1;				//好友的列表信息
	repeated PBPlayerBaseInfo friendApplyList = 2;			//好友申请的列表
	repeated PBCommonLongKeyValue createFriendTimeList = 3;	//key:玩家 id，value:创建时间
}
// 黑名单列表
message LCBlackPlayerRes
{
	repeated PBPlayerBaseInfo blackPlayerList = 1;
}
//申请列表 增加好友
message LCSyncRequestAddOneFriendRes
{
	PBPlayerBaseInfo friendInfo = 1;
}
// 同意 增加好友返回
message LCSyncAddFriendRes
{
	repeated PBPlayerBaseInfo friendInfo = 1;
}
// 拒绝好友返回
message LCSyncRefuseFriendRes
{
	int64 playerUid = 1;//0 表示一键拒绝
}
// 删除好友返回
message LCSyncDelOneFriendRes
{
	int64 playerUid = 1;
}

// 操作黑名单
message LCblacklistOperationRes
{
	PBPlayerBaseInfo playerInfo = 1;
	int32 type = 2;//1:添加，2：移除
}
//同步好友创建时间
message LCSyncCreateFriendTime
{
	repeated PBCommonLongKeyValue createFriendTimeList = 1;	//key:玩家 id，value:创建时间
}
//同步礼物信息
message LCSyncGiftInfoRes
{
	repeated PBGiftItemData giftItems = 1;				// 收到礼物的信息 (还未领取)
	int32 sendTime = 2;									// 当日赠送次数
	int32 recTime = 3;									// 当日获得次数
	repeated int64 uidSend = 4;							// 当日已经赠送的玩家 id 列表
	repeated int64 uidReceive = 5;						// 当日已经收礼的玩家 id 列表
	repeated PBCommonLongKeyValue giftStatusList = 6;	//礼物状态
}

//同步收礼信息
message LCSyncRecGiftInfoRes
{
	repeated PBGiftItemData giftItems = 1;				// 收到礼物的信息 (还未领取)
	repeated int64 uidReceive = 2;						// 当日已经收礼的玩家 id 列表
	int32 recTime = 3;									// 当日获得次数
	repeated PBCommonLongKeyValue giftStatusList = 4;	//礼物状态
}

//同步送礼信息
message LCSyncSendGiftInfoRes
{
	int32 sendTime = 1;									// 当日赠送次数
	repeated int64 uidSend = 2;							// 当日已经赠送的玩家 id 列表
	repeated PBCommonLongKeyValue giftStatusList = 3;	//礼物上一次的状态 key:玩家 id  value:0 已赠送 1:已领取
}
//领取礼物展示恭喜获得
message LCShowRecGift
{
	int32 itemId  = 6;		//领取礼物道具 id
	int32 itemNum = 7;		//领取礼物的数量
}
//同步已发出申请的好友
message LCSyncSendApplyFriend
{
	repeated int64 uidSendApply = 1;// 已申请的好友 uid
}
// 点击好友申请返回
message LCApplyFriendRes
{
	int32 result = 1;//0 成功   1 失败
	int32 type = 2;	 // 0:单个申请 1:一键申请
}
//=====================好友结束===================================

//====================================兑换码开始====================================
//兑换码
message LCCDKeyUse
{
	int32 result 	= 1; 	//0 成功   1 失败
	string code 	= 2; 	//兑换码
}
//====================================兑换码结束====================================


//======================重连成功开始=========================================
//重连成功后通知客户端重连成功 补充消息包用的
message LCReNetReconnect
{
}
//======================重连成功结束=========================================
//公会 TOP 请求
message LCGuildTopList
{
	repeated PBGuildMember topList = 5;	//前三名贡献成员，如无公会可能没有赋值
}


//======================头像开始=========================================
//同步头像列表
message LCSyncHeadIconList
{
	int32 headIconIdInUse = 1;				//当前正在使用的头像
	repeated PBHeadIconDataInfo list = 2;	//头像列表
}
//同步单个头像
message LCSyncSingleHeadIcon
{
	PBHeadIconDataInfo headIconInfo = 1;
}
//替换头像数据
message LCReplaceHeadIcon
{
	int32 result = 1;			//0 成功 1 失败
	int32 headIconIdInUse = 2;	//当前正在使用的头像
}
//手动解锁头像，前提是已拥有。
message LCUnlockHeadIcon
{
	int32 result =1; 					 //0 成功   1 失败
	PBHeadIconDataInfo headIconData = 2; //操作的头像 ID
}
//======================头像结束=========================================
//======================头像框开始=========================================
//同步头像框列表
message LCSyncHeadFrameList
{
	int32 type = 1;						//0:同步所有 1:新增  2:删除（过期）
	int32 frameIdInUse = 2; 			//当前正在使用的头像框
	repeated PBHeadFrameInfo list = 3;	//头像框信息
	bool firstUnlock = 4;				//是否是首次解锁
}
//同步单个头像框
message LCSyncSingleHeadFrame
{
	PBHeadFrameInfo headFrameInfo = 1;
}
//替换头像框
message LCReplaceHeadFrame
{
	int32 result = 1; 		//0 成功 1 失败
	int32 frameIdInUse = 2; //当前正在使用的头像框
}
//解锁头像框
message LCUnlockHeadFrame
{
	int32 result = 1;	//0 成功 1 失败
	PBHeadFrameInfo headFrameInfo = 2;
}

//======================商城购买礼包=========================================
message LCGiftBuyRes
{
	int32 giftId = 1;//礼包ID
	bool success = 2;//购买是否成功
}
//登陆推送商城礼包数据
message LCShopGiftList
{
	repeated PBShopGiftData gifts = 1;
	int32 updateType = 2;//同步类型 1全部数据同步，2部分数据更新
}
//======================商城购买礼包结束=========================================

//======================限时商城购买礼包=========================================
message LCTimeGiftBuyRes
{
	int32 timeGiftId = 1;//TimeGiftPacks礼包ID
	bool success = 2;//购买是否成功
}
//登陆推送商城礼包数据
message LCTimeShopGiftList
{
	repeated PBTimeGiftData gifts = 1;
	int32 updateType = 2;//同步类型 1全部数据同步，2部分数据更新
}
//======================限时商城购买礼包结束=========================================

//======================七日签到开始=========================================
// 返回七日签到信息
message LCSevenSignInGetDataRe
{
	repeated PBSevenSignInfo signData = 1;		// 签到数据
}

// 返回领取七日签到奖励
message LCSevenSignInGetAwardRe
{
	int32 result = 1; 		// 1-成功 >1-失败
}
//======================七日签到结束=========================================

//======================每日签到开始=========================================
// 请求每日签到信息
message LCDailySignInGetDataRe
{
	int32 result = 1;
	int32 days = 2;								// 已经签到天数
	int32 group = 3;								// 奖励组
	repeated int32 receivedDays = 4;			// 已领奖天
	repeated int32 receivedAccrued = 5;			// 已领奖累签
}

// 请求领取每日签到奖励
message LCDailySignInGetAwardRe
{
	int32 result = 1;
}

// 请求领取每日签到累签奖励
message LCDailySignInGetAccruedAwardRe
{
	int32 result = 1;
}
//======================每日签到结束=========================================

//======================首冲礼包=========================================
//领取首冲礼包奖励成功失败返回
message LCGetFristChargeRewardRe
{
	int32 giftId = 1;
	int32 result = 2;	// 0-成功 >0-失败
}
//首冲礼包数据同步消息
message LCFirstChargeGetDataRe
{
	repeated PBFirstChargeData firstChargeReInfo = 2;
}
//购买首冲礼包
message LCBuyFirstChargeGiftRet
{
	int32 giftId = 1;
	int32 result = 2;
}
//======================首冲礼包结束=========================================

//======================充值返利开始==========================================
// 响应请求充值返利数据
message LCTopupRebateGetDataRe 
{
    repeated PBTopupRebateInfo topupRebateData = 1; // 充值返利数据
}

// 响应领取充值返利奖励
message LCTopupRebateGetAwardRe 
{
    int32 result = 1; 	// 1-成功 >1-失败
	int32 topupTaskId = 2; // 充值返利任务Id
}
//======================充值返利结束==========================================

//======================月卡开始==========================================
// 响应请求月卡数据
message LCMonthlyCardGetDataRe
{
	PBMonthlyCardInfo monthlyCardData = 1;
}
// 响应请求购买月卡
message LCMonthlyCardBuyCardRe
{
	int32 result = 1; 	// 1-成功 >1-失败
	int32 cardId = 2;	// 月卡Id
}
//======================月卡结束==========================================

//======================月卡2.0开始==========================================
// 响应请求月卡2.0数据
message LCMonthlyCardNewGetDataRe
{
	repeated PBMonthlyCardNewInfo monthlyCardNewData = 1;
	bool canExtraReward = 2;
}
// 响应请求月卡2.0额外奖励
message LCMonthlyCardNewGetExtraReward
{
	int32 result = 1;	// 1-成功 >1-失败
}
//======================月卡2.0结束==========================================

//======================等级基金开始==========================================
//响应请求等级基金数据
message LCGradedFundGetDataRe
{
	PBGradedFundInfo gradedFundData = 1; // 等级基金数据
}

//响应购买等级基金
message LCGradedFundBuyFundRe
{
	int32 result = 1; 	// 1-成功 >1-失败
	int32 gradedFundIdx = 2; // 基金阶段索引[表格Id]
}

//响应领取普通等级基金 
message LCGradedFundGetComWealRe
{
	int32 result = 1; 	// 1-成功 >1-失败
	int32 levelStageIdx = 2; // 等级阶段索引[表格Id]
}

//响应领取超级等级基金
message LCGradedFundGetSuperWealRe
{
	int32 result = 1; 	// 1-成功 >1-失败
	int32 levelStageIdx = 2; // 等级阶段索引[表格Id]
}
//======================等级基金结束==========================================

//======================任务开始==========================================
//响应请求任务数据
message LCMissionGetData
{
	repeated PBMissionInfo missionData = 1; // 任务数据
}

//响应提交任务 （领取任务奖励）
message LCMissionSubmit
{
	ServerResultCode result = 1; 	// 1-成功 >1-失败
}

//======================任务结束==========================================

//======================奖励通用展示开始==========================================
message LCCommonAwardDisplay
{
    repeated PBCommonAwardDisplay awardInfos = 1; // 奖励信息
	int32 operateReason = 2; // 操作原因
}
//======================奖励通用展示结束==========================================

//=====================支付开始===================================
// 响应付款预请求
message LCPaymentPreRequestRe
{
	int32 result = 1;		// 0 成功   1 失败
	string pushInfo = 2;	// 透传字段
}
// 非SDK正式充值情况下，充值逻辑功能返回消息
message LCPaymentRequestTestRe
{
	int32 resule = 1; //成功失败
	int32 payType = 2;//对应的充值功能模块ID
	int32 param1 = 3;//所需动态参数1
	int32 param2 = 4;//所需动态参数2
}
//=====================支付结束===================================

//==========================功能开启开始============================
//	登录开放列表下发
message LCFunctionOpenPushAll
{
	repeated PBFunctionData functionList = 1; // 功能开放列表 ID & state
}

//	功能开放同步前端
message LCFunctionOpenSync
{
	repeated PBFunctionData functionList = 1; // 功能开放 ID & state
}
//==========================功能开启结束============================

//=====================删除账号开始===================================
// 删除账号
message LCDeleteAccount
{
	int32 result = 1;		// 0 成功   1 失败
	int32 errorCode = 2;	// 橙柚账号token验证返回的错误码
}
//=====================删除账号结束===================================


//=====================礼包码开始===================================
// 返回使用礼包码结果
message LCRedeemCodeRewardRst
{
	int32 RedeemCodeResult = 1;  // 使用结果 包括福利码已被使用、未到开放时间等错误状态
}
//=====================礼包码结束===================================

//======================千抽开始=========================================
// 返回千抽信息
message LCGachaBonusGetDataRe
{
	int32 stage = 1;							// 关卡
	int32 group = 2;							// 奖励组
	repeated int32 receivedStages = 3;			// 已领奖关卡
}

// 返回领取千抽奖励
message LCGachaBonusGetAwardRe
{
	int32 result = 1; 		// 1-成功 >1-失败
}
//======================千抽结束=========================================

//==========================功能预告开始============================
//	登录领取列表下发
message LCFuncPrevPushAll
{
	repeated PBFunctionPreview FuncRewardList = 1; // 功能预告列表 ID & 奖励state
}
// 领取解锁奖励结果
message LCFuncPrevReward
{
	int32 Result = 1;  	// 领取奖励结果
	int32 FuncId = 2;	// 功能预告Id
}
//==========================功能预告结束============================

//=====================问卷开始=================================
// 领取问卷奖励
message LCGetQuestionReward
{
	int32 result = 1;		// 0 成功   1 失败
}

// 登录推送已领取过奖励的问卷
message LCSyncRewardQuestion
{
	repeated int32 ids = 2; // 已领取过奖励的问卷
}
//=====================问卷结束==================================

// 轮盘抽奖
message LCGachaWheelReward
{
	int32 rewardId = 1;		// 奖励 ID
}
//=============无限塔开始=============================
//爬塔首页
message LC_TowerMain
{
	int32 curStageId = 1; //当前已完成关卡ID
}
//爬塔同步整包
message LC_SyncTower
{
	int32 curStageId = 1; //当前已完成关卡ID
}
//=============无限塔结束=============================
//============日进斗金开始=============================
//首页
message LCTotalRechargeGetReward
{
   int32 tableId = 1;//天ID
}
//数据
message LCTotalRechargeData
{
   	int64 endTime = 1;							// 结束时间
    map<int32, int32> rewardData  =2;     // 奖励信息
	int32 totalDay = 3;//l累计天数
}
//=============日进斗金结束=============================
//============好友邀请开始=============================
//邀请任务列表
message LCInviteTaskList
{
	repeated PBInviteTask tasks = 1; // 任务列表
}
//邀请分享
message LCInviteTaskShare
{
	int32 id = 1; // 平台
	repeated PBInviteTask tasks = 2; // 更新的任务列表
}
//领取邀请奖励
message LCInviteTaskGetReward
{
	int32 id = 1; // 任务id
	PBInviteTask task = 2; // 更新的任务
}
//=============好友邀请结束=============================
//====================竞技场开始=======================

//竞技场获取数据
message LCArenaGetData
{
	int32 score = 1; // 竞技场积分
	repeated PBArenaRivalInfo rivalList = 2; // 对手列表
}

//竞技场请求挑战对手
message LCArenaChallengeResult
{
	int32 rivalIdx = 1; // 对手索引 1~3
	int32 result = 2; // 挑战结果 0 成功   1 失败
}

//===================竞技场结束========================

//======================= 体力开始 ====================
// 响应打开体力界面 返回可购买次数
message LCOpenPowerBuyingRes
{
    int32 normalRemains = 1;                // 普通钻石购买剩余次数
    int32 missRemains = 2;                        // 补购次数 前一天补购次数+普通次数
    int32 videoRemains = 3;                        // 看视频免费获得次数
}
// 响应体力购买  买完后剩余次数
message LCPowerBuyingRes
{
    int32 result = 1;                        // 购买结果
    int32 normalRemains = 2;        // 普通钻石购买剩余次数 
    int32 missRemains = 3;                // 补购次数 前一天补购次数+普通次数
    int32 videoRemains = 4;                // 看视频免费获得次数
}
// 服务端响应可领取的体力包列表
message LCSyncPowerRewardList 
{
    repeated PBPowerRewardDataInfo powerRewardInfoList = 1;  // 玩家当前未领取的体力包列表
    int64 lastPowerRewardTime = 2; // 玩家最后一次生成体力包的时间
}
// 服务器响应领取体力包结果
message LCAllPowerRewardRes
{
    int32 Result = 1;                // 领取结果
    int32 PowerRewardNum = 2;        // 体力包个数
}
// 服务器响应领取指定体力包
message LCOnePowerRewardRes
{
    int32 Result = 1;                // 领取结果
    repeated PBPowerRewardDataInfo powerRewardInfoList = 2;  // 玩家当前未领取的体力包列表
    int64 lastPowerRewardTime = 3; // 玩家最后一次生成体力包的时间
}
//======================= 体力结束 =====================

//=================== 天道修为开始 ========================
// 请求当前信息
message LCHeavenlyDaoInfoRes
{
    int32 level = 1; //玩家天道修为当前等级
		bool  isPromote = 2; //玩家天道修为当前等级是否可晋升
}
// 晋升结果
message LCHeavenlyDaoPromoteRes
{
    bool result = 1;       //晋升结果  true: 成功, false:失败
}
//=================== 天道修为结束 ========================

//====================每日特惠开始=====================================
//周卡数据
message LCWeekCardDataRes
{
    int64  overTime=1;                 //结束时间戳
	repeated PBCommonKeyValue  dailyRewardStatus=2;         //每日状态
	int32  weekCardStatus=3;           //月卡状态
}
//====================每日特惠结束=====================================
//=================== 举报开始 ========================
// 举报信息反馈
message LCTipOffRes
{
  bool result = 1;       //举报结果  true: 成功, false:失败
}
//=================== 举报结束 ========================

//=================== 英雄开始 ========================
//英雄列表
message LCHeroListResp
{
	int32 errorCode = 1;
	repeated PBHeroInfo heroList = 2;	// 英雄列表
}
//英雄升级
message LCHeroLevelUpRes
{
	int32 errorCode = 1;
	int32 level = 2;				  // 英雄等级
	int32 exp = 3;					  // 英雄碎片/经验
}
//英雄觉醒等级升级
message LCHeroAwakeLevelUpRes
{
	int32 errorCode = 1;
	int32 awakeLevel = 2;			  // 英雄觉醒等级
}
//=================== 英雄结束 ========================

//=================== 阵容开始 ========================
//获取所有阵容信息
message LCLineupListResp
{
	int32 error_code = 1;
	repeated PBLineupInfo lineup_list = 2; // 玩家当前拥有的所有阵容的列表
	repeated int32 unlocked_ids = 3;       // 玩家已解锁的阵容槽位
	int32 current_id = 4;         			   // 玩家当前激活/正在使用的阵容槽位
}
//阵容槽位解锁
message LCLineupUnlockSlotRes
{
	int32 errorCode = 1;
	repeated int32 unlocked_ids = 2;       // 玩家已解锁的阵容槽位id列表
}
//切换阵容
message LCLineupSwitchResp
{
	int32 error_code = 1;
	int32 id = 2;	// 当前激活/正在使用的阵容槽位
}
//英雄上阵
message LCLineupSetReq
{
	int32 errorCode = 1;
	PBLineupInfo lineup = 2; // 上阵后阵容列表
}
//阵容重命名
message LCLineupRenameResp
{
	int32 error_code = 1;
	string newName = 2;
}
//=================== 阵容结束 ========================

//=================== 赛季buff开始 ========================
//赛季buff信息
message LCSeasonBuffRes
{
	int32 errorCode = 1;
	PBSeasonBuff buff = 2;	// 赛季buff
}
//=================== 赛季buff结束 ========================

//=================== 战斗开始 ========================
//匹配返回
message LCMatchRsp
{
	int32 code = 1;
	int32 sceneId=2;           //战场ID
}

//匹配成功通知
message LCMatchSuccessNotify
{
	int32 code = 1;
	repeated PBBattlePlayerInfo players = 2; //所有玩家信息
}

//请求开始回合战斗
message LCRoundBattleStartResp
{
	int32 code = 1;
}

//新回合开始
message LCRoundStartNotify
{
	int32 code = 1;
	repeated int32 buffers = 2;   //3 个buffer选择
	repeated PBPlayerBoard playerBoards = 3; // 双方玩家的棋盘信息
}

//buffer 选择
message LCSelectBufferResp
{
	int32 code = 1;
	int32 bufferId = 2;
	repeated PBCheckerBoard newHeroes = 3; // 选择Buff后，本回合新生成的英雄信息
}

//英雄合成
message LCMergeRsp
{
	int32 code = 1;
	int32 from = 2;	//合成源格子ID
	int32 to =3 ; //合成目标格子ID
	PBCheckerBoard newHero = 4; // 合成的新英雄数据
}

//准备返回
message LCReadyRsp
{
	int32 code = 1;
	repeated PBBattlePlayerState playerState = 21; //所有玩家信息
}

//战斗结束
message LCRoundBattleEndResp
{
	int32 code = 1;
}

//战斗开始
message LCRoundBattleStartNotify
{
	int64 uid = 1; //战斗唯一ID
	int32 seed = 2; //随机种子
	repeated PBBattleCampInfo team = 3; //对战双方的数据 第一个是先手方
}

//战斗结束
message LCRoundBattleEndNotify
{
	uint64 winUid = 1;
	uint64 loseUid = 2;
	bool isEnd = 3; // 整场战斗是否要结束
}

//整场战斗结束
message LCBattleEndNotify
{
	int32 rank = 1;     					// 排名 1-4                            
    repeated PBBattleHeroInfo heros = 2;    // 阵容
    int32 win_streak = 3;                   // 连胜场次
    int32 trophy_before = 4;                // 结算前的奖杯数      
    int32 trophy_after = 5;                 // 结算后的奖杯数
    int32 supply_times = 6;       			// "对战补给"当日剩余次数  
    int32 blessing_times = 7;   		    // "对战庇佑"当日剩余次数
}

// 结算相关
// 结算额外广告奖励
message LCClaimAdRewardRsp {
    int32 code = 1;
    AdRewardType type = 2;
    int32 current_trophy = 3; // 当前奖杯
	repeated PBDropItemDataInfo itemInfo = 4; // 掉落奖励信息
}
//=================== 战斗结束 ========================

//=================== 赛季开始 ========================
// 请求领取奖励
message LCClaimSeasonRewardRsp {
    int32 code = 1;
    RewardType type = 2;
    int32 id = 3;
}
// 请求赛季相关信息 
message LCSeasonInfoRsp {
    int32 code = 1;
    
    int32 trophy = 2;				// 奖杯
    int32 current_win_streak = 3;   // 连胜场次
    
    int32 current_season_id = 4; 	// 赛季id
    
    repeated PBRankRewardState rank_reward_states = 5;         // 所有普通段位奖励的领取状态
    repeated PBRankRewardState season_rank_reward_states = 6;  // 当前赛季所有段位奖励的领取状态
    
    repeated SeasonTrophyInfo season_history = 7; // 赛季历史
}

// 服务器主动推送：赛季重置通知
message LCSeasonResetNotify {
    int32 old_season_id = 1;
    int32 new_season_id = 2;
    int32 final_trophy_last_season = 3;  // 上赛季最终奖杯
    int32 initial_trophy_new_season = 4; // 新赛季初始奖杯
}
//=================== 赛季结束 ========================
