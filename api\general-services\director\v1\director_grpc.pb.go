//
// @Author:zhouchen
// @Description: 消息包文件定义
// @Data: Created in 20:01 2023/6/6

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.23.2
// source: general-services/director/v1/director.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Director_GetDirectorData_FullMethodName = "/director.v1.Director/GetDirectorData"
)

// DirectorClient is the client API for Director service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// The relation service definition.
type DirectorClient interface {
	GetDirectorData(ctx context.Context, in *GetDirectorReq, opts ...grpc.CallOption) (*GetDirectorReply, error)
}

type directorClient struct {
	cc grpc.ClientConnInterface
}

func NewDirectorClient(cc grpc.ClientConnInterface) DirectorClient {
	return &directorClient{cc}
}

func (c *directorClient) GetDirectorData(ctx context.Context, in *GetDirectorReq, opts ...grpc.CallOption) (*GetDirectorReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDirectorReply)
	err := c.cc.Invoke(ctx, Director_GetDirectorData_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DirectorServer is the server API for Director service.
// All implementations must embed UnimplementedDirectorServer
// for forward compatibility.
//
// The relation service definition.
type DirectorServer interface {
	GetDirectorData(context.Context, *GetDirectorReq) (*GetDirectorReply, error)
	mustEmbedUnimplementedDirectorServer()
}

// UnimplementedDirectorServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDirectorServer struct{}

func (UnimplementedDirectorServer) GetDirectorData(context.Context, *GetDirectorReq) (*GetDirectorReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDirectorData not implemented")
}
func (UnimplementedDirectorServer) mustEmbedUnimplementedDirectorServer() {}
func (UnimplementedDirectorServer) testEmbeddedByValue()                  {}

// UnsafeDirectorServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DirectorServer will
// result in compilation errors.
type UnsafeDirectorServer interface {
	mustEmbedUnimplementedDirectorServer()
}

func RegisterDirectorServer(s grpc.ServiceRegistrar, srv DirectorServer) {
	// If the following call pancis, it indicates UnimplementedDirectorServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Director_ServiceDesc, srv)
}

func _Director_GetDirectorData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDirectorReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DirectorServer).GetDirectorData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Director_GetDirectorData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DirectorServer).GetDirectorData(ctx, req.(*GetDirectorReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Director_ServiceDesc is the grpc.ServiceDesc for Director service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Director_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "director.v1.Director",
	HandlerType: (*DirectorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetDirectorData",
			Handler:    _Director_GetDirectorData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "general-services/director/v1/director.proto",
}
