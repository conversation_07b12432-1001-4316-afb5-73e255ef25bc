[{"ID": 100101, "NextId": -1, "ButtonPathType": 1, "ButtonPath": "", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": -1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": -1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002801, "DialogPosType": 0, "DialogOffsetY": 350, "AreaId": -1, "IsBattleStop": 0}, {"ID": 100201, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Main/Top/task/bg", "UIOpen": [], "CheckFunId": -1, "GoBackId": 100201, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002802, "DialogPosType": 1, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 110001, "NextId": 110002, "ButtonPathType": 3, "ButtonPath": "UI_Main/Top/task/bg", "UIOpen": [], "CheckFunId": -1, "GoBackId": 110001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 1, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 110002, "NextId": 110003, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/charButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 110002, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002803, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 110003, "NextId": 110004, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/chatacterScrollView/Viewport/Content/EditorRuntime_Content_0/PlayerAttributeItem/upgradeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 110002, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 110004, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/chatacterScrollView/Viewport/Content/EditorRuntime_Content_0/PlayerAttributeItem/upgradeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 110020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 110020, "NextId": 110004, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/charButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 110020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002803, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 120001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Main/Top/task/bg", "UIOpen": [], "CheckFunId": -1, "GoBackId": 120020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 1, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 120020, "NextId": 120021, "ButtonPathType": 3, "ButtonPath": "UI_Main/Top/task/bg", "UIOpen": [], "CheckFunId": -1, "GoBackId": 120020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 1, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 120021, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/charButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 120021, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002803, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 130001, "NextId": 130002, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/chatacterScrollView/Viewport/Content/EditorRuntime_Content_1/PlayerAttributeItem/upgradeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 130020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 130002, "NextId": 130003, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/chatacterScrollView/Viewport/Content/EditorRuntime_Content_1/PlayerAttributeItem/upgradeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 130021, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 130003, "NextId": -1, "ButtonPathType": 1, "ButtonPath": "UI_CharacterUpgrade/listRootBg/chatacterScrollView/Viewport/Content/EditorRuntime_Content_1/PlayerAttributeItem/upgradeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": -1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002818, "DialogPosType": 2, "DialogOffsetY": 400, "AreaId": -1, "IsBattleStop": 0}, {"ID": 130020, "NextId": 130001, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/charButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 130020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002803, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 130021, "NextId": 130002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/charButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 130021, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002803, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 140001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Main/Top/task/bg", "UIOpen": [], "CheckFunId": -1, "GoBackId": 140001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 1, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 150001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Main/Top/task/bg", "UIOpen": [], "CheckFunId": -1, "GoBackId": 150001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 1, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 160001, "NextId": 160002, "ButtonPathType": 3, "ButtonPath": "UI_Main/Bottom/PlayerPromotion/Button", "UIOpen": [], "CheckFunId": -1, "GoBackId": 160001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002837, "DialogPosType": 0, "DialogOffsetY": -343, "AreaId": -1, "IsBattleStop": 1}, {"ID": 160002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_PlayerPromotion/Frame/PromoteButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 160001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002838, "DialogPosType": 0, "DialogOffsetY": -265, "AreaId": -1, "IsBattleStop": 1}, {"ID": 170001, "NextId": 170002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/charButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 170001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002853, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 170002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/chatacterScrollView/Viewport/Content/EditorRuntime_Content_0/PlayerAttributeItem/upgradeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 170001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002854, "DialogPosType": 0, "DialogOffsetY": -200, "AreaId": -1, "IsBattleStop": 0}, {"ID": 180001, "NextId": 180002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/lotteryButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 180001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002804, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 180002, "NextId": 180003, "ButtonPathType": 3, "ButtonPath": "UI_Lottery/Main/equipAndskill/LotteryEquipPart/lottery35Button", "UIOpen": [], "CheckFunId": -1, "GoBackId": 180001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 180003, "NextId": 180004, "ButtonPathType": 3, "ButtonPath": "UI_LotteryResult_Equip/blockbg", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": 2, "ScrollView": -1, "FindDelay": 410, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 180004, "NextId": -1, "ButtonPathType": 1, "ButtonPath": "UI_Lottery/Main/equipAndskill/LotteryEquipPart/Slider/num", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": -1, "ScrollView": -1, "FindDelay": 150, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002820, "DialogPosType": 2, "DialogOffsetY": 483, "AreaId": -1, "IsBattleStop": 1}, {"ID": 190001, "NextId": 190002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 190001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002805, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 190002, "NextId": 190003, "ButtonPathType": 3, "ButtonPath": "UI_Equip/listRoot/Tab_EquipPart/ScrollView/Viewport/Content/EditorRuntime_Content_3/equipItem", "UIOpen": [], "CheckFunId": -1, "GoBackId": 190001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 190003, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Equip_DetailPop/Frame/Btn/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 190001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 200001, "NextId": 200002, "ButtonPathType": 3, "ButtonPath": "UI_Equip/listRoot/Tab_EquipPart/ToggleGroup_sub/Toggle_02", "UIOpen": [], "CheckFunId": -1, "GoBackId": 200020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 200002, "NextId": 200003, "ButtonPathType": 3, "ButtonPath": "UI_Equip/listRoot/Tab_EquipPart/ScrollView/Viewport/Content/EditorRuntime_Content_3/equipItem", "UIOpen": [], "CheckFunId": -1, "GoBackId": 200020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 200003, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Equip_DetailPop/Frame/Btn/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 200020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 200020, "NextId": 200001, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 200020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 210001, "NextId": 210002, "ButtonPathType": 3, "ButtonPath": "UI_Equip/listRoot/Tab_EquipPart/ToggleGroup_sub/Toggle_03", "UIOpen": [], "CheckFunId": -1, "GoBackId": 210020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 210002, "NextId": 210003, "ButtonPathType": 3, "ButtonPath": "UI_Equip/listRoot/Tab_EquipPart/ScrollView/Viewport/Content/EditorRuntime_Content_3/equipItem", "UIOpen": [], "CheckFunId": -1, "GoBackId": 210020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 210003, "NextId": 210004, "ButtonPathType": 3, "ButtonPath": "UI_Equip_DetailPop/Frame/Btn/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 210020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 210004, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 210020, "NextId": 210001, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 210020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 220001, "NextId": 220002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 220001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002872, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 220002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/process/Mission/lvpupButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 220001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 223001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/mapScrollView/Viewport/Content/sizeRoot/map_ground/area_yulongsuo/dragonBuildingItem_4/DragonBuildSonItem_1/Ruins/buildingImage_1", "UIOpen": [], "CheckFunId": -1, "GoBackId": 223020, "Masking": 2, "ScrollView": 1, "FindDelay": 100, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002840, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 223020, "NextId": 223001, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 223020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 226001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/mapScrollView/Viewport/Content/sizeRoot/map_ground/area_yulongsuo/dragonBuildingItem_4/DragonBuildSonItem_2/Ruins/buildingImage_1", "UIOpen": [], "CheckFunId": -1, "GoBackId": 226020, "Masking": 2, "ScrollView": 1, "FindDelay": 150, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 226020, "NextId": 226001, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 226020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 228001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Activity_TreasureDigPop/Frame/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 228020, "Masking": 2, "ScrollView": 1, "FindDelay": 150, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 228020, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 228020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 230001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/mapScrollView/Viewport/Content/sizeRoot/map_ground/area_yulongsuo/dragonBuildingItem_4/BuildOpen/buildingImage/enterButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 230020, "Masking": 2, "ScrollView": 1, "FindDelay": 670, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002841, "DialogPosType": 0, "DialogOffsetY": -500, "AreaId": 4, "IsBattleStop": 1}, {"ID": 230020, "NextId": 230021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 230020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 230021, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/growButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 230020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 231001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonCompound/bottombg/ScrollView/Viewport/Content/eggButton_1", "UIOpen": [], "CheckFunId": -1, "GoBackId": 231020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 231020, "NextId": 231021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 231020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 231021, "NextId": 231001, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/growButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 231020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 240001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonCompound/bg/dragonCell_2/RawImageRoot", "UIOpen": [], "CheckFunId": -1, "GoBackId": 240020, "Masking": -1, "ScrollView": -1, "FindDelay": 50, "IsHighlight": 5, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002842, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 240020, "NextId": 240021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 240020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 240021, "NextId": 240001, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/growButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 240020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 241001, "NextId": 241002, "ButtonPathType": 3, "ButtonPath": "UI_DragonCompound/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 241003, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002843, "DialogPosType": 0, "DialogOffsetY": -500, "AreaId": -1, "IsBattleStop": 1}, {"ID": 241002, "NextId": 241003, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 241003, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 241003, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Main/Bottom/skillBar/dragonSkill/quality", "UIOpen": [], "CheckFunId": -1, "GoBackId": 241003, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002844, "DialogPosType": 0, "DialogOffsetY": -500, "AreaId": -1, "IsBattleStop": 0}, {"ID": 250001, "NextId": 250002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/lotteryButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 250001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002806, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 250002, "NextId": 250003, "ButtonPathType": 3, "ButtonPath": "UI_Lottery/Main/equipAndskill/LotterySkillPart/lottery35Button", "UIOpen": [], "CheckFunId": -1, "GoBackId": 250001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 250003, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_LotteryResult_Equip/blockbg", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": 2, "ScrollView": -1, "FindDelay": 410, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 260001, "NextId": 260002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/charButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 260001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002807, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 260002, "NextId": 260003, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/ToggleGroup/Toggle_02", "UIOpen": [], "CheckFunId": -1, "GoBackId": 260001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 260003, "NextId": 260004, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/skillScrollView/Viewport/Content/EditorRuntime_Content_0/quality", "UIOpen": [], "CheckFunId": -1, "GoBackId": 260001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 260004, "NextId": 260005, "ButtonPathType": 3, "ButtonPath": "UI_Skill_DetailPop/Frame/Btn/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 260001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 260005, "NextId": 260006, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/skillScrollView/Viewport/Content/EditorRuntime_Content_1/quality/onButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 260020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 260006, "NextId": 260007, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/charButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 260007, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 1}, {"ID": 260007, "NextId": 260008, "ButtonPathType": 3, "ButtonPath": "UI_Main/Bottom/skillBar/skillLayout/skill_01/quality", "UIOpen": [], "CheckFunId": -1, "GoBackId": 260007, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002808, "DialogPosType": 0, "DialogOffsetY": -500, "AreaId": -1, "IsBattleStop": 0}, {"ID": 260008, "NextId": 260009, "ButtonPathType": 3, "ButtonPath": "UI_Main/Bottom/skillBar/auto_2on_Button", "UIOpen": [], "CheckFunId": -1, "GoBackId": 260008, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002809, "DialogPosType": 0, "DialogOffsetY": -500, "AreaId": -1, "IsBattleStop": 0}, {"ID": 260009, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Main/Bottom/skillBar/dragonSkill/autoButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 260009, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002871, "DialogPosType": 0, "DialogOffsetY": -500, "AreaId": -1, "IsBattleStop": 0}, {"ID": 260020, "NextId": 260021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/charButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 260020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002807, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 260021, "NextId": 260005, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/ToggleGroup/Toggle_02", "UIOpen": [], "CheckFunId": -1, "GoBackId": 260020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 270001, "NextId": 270002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/challengeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 270001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002811, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 270002, "NextId": 270003, "ButtonPathType": 3, "ButtonPath": "UI_Instance/resourceInstance/ScrollView/Viewport/Content/Instance_Gold/challengeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 270001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 270003, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Instance_StageChoosePop/Frame/sureButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 270001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 280002, "NextId": 280003, "ButtonPathType": 3, "ButtonPath": "UI_Main_StageMapPop/Frame/getButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 280001, "Masking": 2, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 280003, "NextId": 280004, "ButtonPathType": 3, "ButtonPath": "UI_GetReward/confirmButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": 2, "ScrollView": -1, "FindDelay": 50, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 280004, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Main_StageMapPop/Frame/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 290001, "NextId": 290002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 290001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002830, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 290002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/process/Mission/lvpupButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 290001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 295001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Activity_TreasureDigPop/Frame/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 295020, "Masking": 2, "ScrollView": 1, "FindDelay": 150, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 295020, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 295020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 300001, "NextId": 300002, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/mapScrollView/Viewport/Content/sizeRoot/map_ground/area_baozang/dragonBuildingItem_3/BuildOpen/buildingImage/enterButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 300020, "Masking": 2, "ScrollView": 1, "FindDelay": 670, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002832, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": 3, "IsBattleStop": 0}, {"ID": 300002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonMining/mask/moveRoot/MiningGridLayout/mineGridItem_14/bgClick", "UIOpen": [], "CheckFunId": -1, "GoBackId": 300020, "Masking": 2, "ScrollView": 1, "FindDelay": 50, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 300020, "NextId": 300021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 300020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 300021, "NextId": 300002, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/digButton_1", "UIOpen": [], "CheckFunId": -1, "GoBackId": 300020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 276, "AreaId": 3, "IsBattleStop": 0}, {"ID": 310001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonMining/mask/moveRoot/MiningGridLayout/mineGridItem_20/bgClick", "UIOpen": [], "CheckFunId": -1, "GoBackId": 310020, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002865, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 310020, "NextId": 310021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 310020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 310021, "NextId": 310001, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/digButton_1", "UIOpen": [], "CheckFunId": -1, "GoBackId": 310020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 276, "AreaId": 3, "IsBattleStop": 0}, {"ID": 311001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonMining/mask/moveRoot/MiningGridLayout/mineGridItem_21/bgClick", "UIOpen": [], "CheckFunId": -1, "GoBackId": 311020, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 311020, "NextId": 311021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 311020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 311021, "NextId": 311001, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/digButton_1", "UIOpen": [], "CheckFunId": -1, "GoBackId": 311020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 276, "AreaId": 3, "IsBattleStop": 0}, {"ID": 320001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonMining/topLayer/Bottombg/goGrowButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 320020, "Masking": 2, "ScrollView": 1, "FindDelay": 100, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002814, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 320020, "NextId": 320021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 320020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 320021, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/growButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 320020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 276, "AreaId": -1, "IsBattleStop": 0}, {"ID": 330001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonCompound/bottombg/ScrollView/Viewport/Content/eggButton_1", "UIOpen": [], "CheckFunId": -1, "GoBackId": 330020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 330020, "NextId": 330021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 330020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 330021, "NextId": 330001, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/growButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 330020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 276, "AreaId": -1, "IsBattleStop": 0}, {"ID": 340001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonCompound/bottombg/ScrollView/Viewport/Content/eggButton_1", "UIOpen": [], "CheckFunId": -1, "GoBackId": 340020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 340020, "NextId": 340021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 340020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 340021, "NextId": 340001, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/growButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 340020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 276, "AreaId": -1, "IsBattleStop": 0}, {"ID": 350001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonCompound/bg/dragonCell_1/RawImageRoot", "UIOpen": [], "CheckFunId": -1, "GoBackId": 350020, "Masking": -1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 3, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002833, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 350020, "NextId": 350021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 350020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 350021, "NextId": 350001, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/growButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 350020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 276, "AreaId": -1, "IsBattleStop": 0}, {"ID": 360001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonCompound/bg/dragonCell_2/RawImageRoot", "UIOpen": [], "CheckFunId": -1, "GoBackId": 360020, "Masking": -1, "ScrollView": -1, "FindDelay": 50, "IsHighlight": 4, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002815, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 360020, "NextId": 360021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 360020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 360021, "NextId": 360001, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/growButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 360020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 276, "AreaId": -1, "IsBattleStop": 0}, {"ID": 370001, "NextId": 370002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/challengeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 370001, "Masking": 1, "ScrollView": -1, "FindDelay": 50, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002812, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 370002, "NextId": 370003, "ButtonPathType": 3, "ButtonPath": "UI_Instance/resourceInstance/ScrollView/Viewport/Content/Instance_Exp/challengeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 370001, "Masking": 1, "ScrollView": -1, "FindDelay": 50, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 370003, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Instance_StageChoosePop/Frame/sureButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 370001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 380001, "NextId": 380002, "ButtonPathType": 3, "ButtonPath": "UI_Instance_StageChoosePop/Frame/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 380002, "Masking": 2, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 1, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 380002, "NextId": 380003, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/charButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 380002, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002810, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 380003, "NextId": 380004, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/ToggleGroup/Toggle_03", "UIOpen": [], "CheckFunId": -1, "GoBackId": 380002, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 380004, "NextId": 380005, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/talentScrollView/ToggleGroup_sub/Toggle_02", "UIOpen": [], "CheckFunId": -1, "GoBackId": 380002, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 380005, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/talentScrollView/Viewport/Content/EditorRuntime_Content_0/PlayerAttributeItem/upgradeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 380002, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 390001, "NextId": 390002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/challengeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 390001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002816, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 390002, "NextId": 390003, "ButtonPathType": 3, "ButtonPath": "UI_Instance/top/ScrollView/Viewport/ToggleGroup/Toggle_03", "UIOpen": [], "CheckFunId": -1, "GoBackId": 390001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 390003, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Instance/bosschallengeInstance/Low/framebg/challengeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 390001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 400001, "NextId": 400002, "ButtonPathType": 3, "ButtonPath": "UI_Main/Bottom/PlayerPromotion/Button", "UIOpen": [], "CheckFunId": -1, "GoBackId": 400001, "Masking": 2, "ScrollView": -1, "FindDelay": 150, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002845, "DialogPosType": 0, "DialogOffsetY": -343, "AreaId": -1, "IsBattleStop": 0}, {"ID": 400002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_PlayerPromotion/Frame/PromoteButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 400001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002846, "DialogPosType": 0, "DialogOffsetY": -265, "AreaId": -1, "IsBattleStop": 0}, {"ID": 410002, "NextId": 410003, "ButtonPathType": 3, "ButtonPath": "UI_Main_StageMapPop/Frame/getButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 410001, "Masking": 2, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 410003, "NextId": 410004, "ButtonPathType": 3, "ButtonPath": "UI_GetReward/confirmButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": 1, "ScrollView": -1, "FindDelay": 50, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 410004, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Main_StageMapPop/Frame/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 420001, "NextId": 420002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 420001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002822, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 420002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/process/Mission/lvpupButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 420001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 425001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Activity_TreasureDigPop/Frame/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 425020, "Masking": 2, "ScrollView": 1, "FindDelay": 150, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 425020, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 425020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 430001, "NextId": 430002, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/mapScrollView/Viewport/Content/sizeRoot/map_ground/area_jinbijiacheng/dragonBuildingItem_9/BuildOpen/buildingImage/lvupButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 430020, "Masking": 2, "ScrollView": 1, "FindDelay": 720, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002826, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": 9, "IsBattleStop": 0}, {"ID": 430002, "NextId": -1, "ButtonPathType": 1, "ButtonPath": "", "UIOpen": [], "CheckFunId": -1, "GoBackId": 430020, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": -1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002874, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": 9, "IsBattleStop": 0}, {"ID": 430020, "NextId": 430001, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 430020, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 450001, "NextId": 450002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 450001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002857, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 450002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/process/Mission/lvpupButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 450001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 1, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 452001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Activity_TreasureDigPop/Frame/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 452020, "Masking": 2, "ScrollView": 1, "FindDelay": 150, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 452020, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 452020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 454001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/mapScrollView/Viewport/Content/sizeRoot/map_ground/area_shikuang/dragonBuildingItem_13/BuildOpen/buildingImage/receiveButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 454020, "Masking": 2, "ScrollView": 1, "FindDelay": 750, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002870, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": 13, "IsBattleStop": 0}, {"ID": 454020, "NextId": 454001, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 454020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 456001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_GetReward/confirmButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 456020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 456020, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 456020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 458001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/mapScrollView/Viewport/Content/sizeRoot/map_ground/area_jinbijiacheng/dragonBuildingItem_9/BuildOpen/buildingImage/lvupButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 458020, "Masking": 2, "ScrollView": 1, "FindDelay": 50, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002869, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": 9, "IsBattleStop": 0}, {"ID": 458020, "NextId": 458001, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 458020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 460001, "NextId": 460002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 460001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002825, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 460002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/process/Mission/lvpupButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 460001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 463001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Activity_TreasureDigPop/Frame/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 463020, "Masking": 2, "ScrollView": 1, "FindDelay": 150, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 463020, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 463020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 465001, "NextId": 465002, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/mapScrollView/Viewport/Content/sizeRoot/map_ground/area_dabenying/dragonBuildingItem_2/BuildOpen/buildingImage/lvupButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 465020, "Masking": 2, "ScrollView": 1, "FindDelay": 670, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002823, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": 2, "IsBattleStop": 0}, {"ID": 465002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland_LvUpPop/Frame/notMax/lvpupButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 465020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002836, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 465020, "NextId": 465001, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 465020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 470001, "NextId": 470002, "ButtonPathType": 3, "ButtonPath": "UI_Main/Top/buttonList_RT/albumButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 470001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 2, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002847, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 470002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Album/Frame/ScrollView/Viewport/Content/EditorRuntime_Content_0/btn/sizeData/button/strengthenButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 470001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002848, "DialogPosType": 0, "DialogOffsetY": -153, "AreaId": -1, "IsBattleStop": 0}, {"ID": 480001, "NextId": 480002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/guildButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 480001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002875, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 480002, "NextId": -1, "ButtonPathType": 1, "ButtonPath": "", "UIOpen": [], "CheckFunId": -1, "GoBackId": 480001, "Masking": 2, "ScrollView": -1, "FindDelay": -1, "IsHighlight": -1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002876, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 490001, "NextId": 490002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 490001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002868, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 490002, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/process/Mission/lvpupButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 490001, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 495001, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Activity_TreasureDigPop/Frame/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 495020, "Masking": 2, "ScrollView": 1, "FindDelay": 150, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 495020, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 495020, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 500001, "NextId": 500002, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/mapScrollView/Viewport/Content/sizeRoot/map_ground/area_jiayuan/dragonBuildingItem_12/BuildOpen/buildingImage/enterButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 500020, "Masking": 2, "ScrollView": 1, "FindDelay": 670, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002859, "DialogPosType": 0, "DialogOffsetY": -600, "AreaId": 12, "IsBattleStop": 0}, {"ID": 500002, "NextId": 500004, "ButtonPathType": 3, "ButtonPath": "UI_DragonFactory/areaWidget/trackItem_3/Slider/Storage/icon", "UIOpen": [], "CheckFunId": -1, "GoBackId": 500020, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 500004, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonFactory_ResDetailPop/Frame/goButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 500020, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 500020, "NextId": 500021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 500020, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 500021, "NextId": 500002, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/sendButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 500020, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501001, "NextId": 501002, "ButtonPathType": 3, "ButtonPath": "UI_GetReward/confirmButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501020, "Masking": 2, "ScrollView": 1, "FindDelay": 320, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501002, "NextId": 501003, "ButtonPathType": 3, "ButtonPath": "UI_DragonFactory/slimeButton/lvbgbtn", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501030, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002860, "DialogPosType": 2, "DialogOffsetY": 700, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501003, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_DragonFactory_LvUpPop/strengthenButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501030, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501020, "NextId": 501021, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501020, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501021, "NextId": 501001, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/sendButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501020, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501030, "NextId": 501031, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501030, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501031, "NextId": 501002, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/sendButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501030, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501501, "NextId": 501502, "ButtonPathType": 3, "ButtonPath": "UI_DragonFactory_LvUpPop/Bottombg/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501520, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501502, "NextId": 501503, "ButtonPathType": 1, "ButtonPath": "UI_DragonFactory/robButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501520, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 2, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002866, "DialogPosType": 0, "DialogOffsetY": -500, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501503, "NextId": -1, "ButtonPathType": 1, "ButtonPath": "UI_DragonFactory/autoButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501520, "Masking": 1, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 2, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002867, "DialogPosType": 0, "DialogOffsetY": -500, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501520, "NextId": 501521, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/dragonButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501520, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 501521, "NextId": 501502, "ButtonPathType": 3, "ButtonPath": "UI_DragonIsland/topLayer/sendButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 501520, "Masking": 2, "ScrollView": 1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 510001, "NextId": 510002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/challengeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 510001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002813, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 510002, "NextId": 510003, "ButtonPathType": 3, "ButtonPath": "UI_Instance/top/ScrollView/Viewport/ToggleGroup/Toggle_02", "UIOpen": [], "CheckFunId": -1, "GoBackId": 510001, "Masking": 1, "ScrollView": -1, "FindDelay": 40, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 510003, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Instance/dpsInstance/Button", "UIOpen": [], "CheckFunId": -1, "GoBackId": 510001, "Masking": 1, "ScrollView": -1, "FindDelay": 40, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 520001, "NextId": 520002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/lotteryButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 520001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002827, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 520002, "NextId": 520003, "ButtonPathType": 3, "ButtonPath": "UI_Lottery/Main/ToggleGroup/Toggle_03", "UIOpen": [], "CheckFunId": -1, "GoBackId": 520001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 520003, "NextId": 520004, "ButtonPathType": 3, "ButtonPath": "UI_Lottery/Main/PetView/LotteryPetPart/lottery35Button", "UIOpen": [], "CheckFunId": -1, "GoBackId": 520001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 520004, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_LotteryResult_Pet/normal/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": 2, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 530001, "NextId": 530002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 530001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002828, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 530002, "NextId": 530003, "ButtonPathType": 3, "ButtonPath": "UI_Equip/listRoot/ToggleGroup/Toggle_04", "UIOpen": [], "CheckFunId": -1, "GoBackId": 530001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 530003, "NextId": 530004, "ButtonPathType": 3, "ButtonPath": "UI_Equip/listRoot/Tab_PetPart/listRoot/ScrollView/Viewport/Content/EditorRuntime_Content_0/ItemButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 530001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 530004, "NextId": 530005, "ButtonPathType": 3, "ButtonPath": "UI_Pet_DetailPop/Frame/Btn/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 530001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 530005, "NextId": -1, "ButtonPathType": 1, "ButtonPath": "", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": -1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": -1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002829, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 540001, "NextId": 540002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/challengeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 540001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002849, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 540002, "NextId": 540003, "ButtonPathType": 3, "ButtonPath": "UI_Instance/top/ScrollView/Viewport/ToggleGroup/Toggle_05", "UIOpen": [], "CheckFunId": -1, "GoBackId": 540001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 540003, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Instance/InsTowerPart/challengeBtn", "UIOpen": [], "CheckFunId": -1, "GoBackId": 540001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002850, "DialogPosType": 1, "DialogOffsetY": -936, "AreaId": -1, "IsBattleStop": 0}, {"ID": 550001, "NextId": 550002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/charButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 550001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002817, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 550002, "NextId": 550003, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/ToggleGroup/Toggle_04", "UIOpen": [], "CheckFunId": -1, "GoBackId": 550001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 550003, "NextId": 550004, "ButtonPathType": 3, "ButtonPath": "UI_CharacterUpgrade/listRootBg/refine/freshButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 550001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 550004, "NextId": -1, "ButtonPathType": 1, "ButtonPath": "UI_CharacterUpgrade/listRootBg/refine/left/refineItem_1/unlockState/GameImage", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": -1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002819, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 560001, "NextId": 560002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/lotteryButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 560001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002851, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 560002, "NextId": 560003, "ButtonPathType": 3, "ButtonPath": "UI_Lottery/Main/ToggleGroup/Toggle_02", "UIOpen": [], "CheckFunId": -1, "GoBackId": 560001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 560003, "NextId": 560004, "ButtonPathType": 3, "ButtonPath": "UI_Lottery/Main/LotteryHallows/choose/lotteryButton_2", "UIOpen": [], "CheckFunId": -1, "GoBackId": 560001, "Masking": 1, "ScrollView": -1, "FindDelay": 100, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 560004, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_LotteryResult_Hallows/normal/closeButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": -1, "Masking": 2, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 570001, "NextId": 570002, "ButtonPathType": 3, "ButtonPath": "UI_MainBottom/buttonLayout/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 570001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": 1002852, "DialogPosType": 2, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 570002, "NextId": 570003, "ButtonPathType": 3, "ButtonPath": "UI_Equip/listRoot/ToggleGroup/Toggle_03", "UIOpen": [], "CheckFunId": -1, "GoBackId": 570001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 570003, "NextId": 570004, "ButtonPathType": 3, "ButtonPath": "UI_Equip/listRoot/Tab_Hallows/listRoot/hallowsEquipItem_4", "UIOpen": [], "CheckFunId": -1, "GoBackId": 570001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}, {"ID": 570004, "NextId": -1, "ButtonPathType": 3, "ButtonPath": "UI_Hallows_DetailPop/Frame/Btn/equipButton", "UIOpen": [], "CheckFunId": -1, "GoBackId": 570001, "Masking": 1, "ScrollView": -1, "FindDelay": -1, "IsHighlight": 1, "HandDir": 1, "HighlightScaleX": -1, "HighlightScaleY": -1, "OffsetX": 0, "OffsetY": 0, "DialogID": -1, "DialogPosType": 0, "DialogOffsetY": 0, "AreaId": -1, "IsBattleStop": 0}]