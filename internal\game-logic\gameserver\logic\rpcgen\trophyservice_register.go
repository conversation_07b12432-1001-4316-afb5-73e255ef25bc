// Code generated by rpcparse. DO NOT EDIT.

package rpcgen

import (
	"context"
	"google.golang.org/protobuf/proto"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/rpc_def"
	"liteframe/internal/game-logic/gameserver/logic/player"
	"liteframe/pkg/actor"
	"liteframe/pkg/log"
)

// RegisterTrophyServicecomment
func RegisterTrophyService(dispatch *actor.Dispatcher, service TrophyServiceInterface) {

	// claimseasonreward
	ClaimSeasonRewardHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLClaimSeasonRewardReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("TrophyService:ClaimSeasonReward unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCClaimSeasonRewardRsp{}
			p.SendToClient(rpc_def.LCClaimSeasonRewardRsp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCClaimSeasonRewardRsp{}
			p.SendToClient(rpc_def.LCClaimSeasonRewardRsp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.ClaimSeasonReward(ctx, p, in)
		p.SendToClient(rpc_def.LCClaimSeasonRewardRsp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLClaimSeasonRewardReq), ClaimSeasonRewardHandler)

	// seasoninfo
	SeasonInfoHandler := func(ctx context.Context, data *actor.Message) (err error) {
		rcvMsg := data.Data.(player.PlayerMsg)
		p := rcvMsg.P

		in := &cs.CLSeasonInfoReq{}
		err = proto.Unmarshal(rcvMsg.Data, in)
		if err != nil {
			log.Error("TrophyService:SeasonInfo unmarshal failed", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id), log.Err(err))
			return
		}

		rcvMsg.Debug(in)

		// rcv repeated request return cache
		cache := p.LastPacket(uint16(data.Id))
		if cache != nil {
			p.ResponseCache()
			return
		}

		// rpc in black list
		if dispatch.IsBlackId(uint16(data.Id), data.Uid) {
			out := &cs.LCSeasonInfoRsp{}
			p.SendToClient(rpc_def.LCSeasonInfoRsp, out, false)
			log.Warn("rpc in blacklist", log.Kv("Uid", p.Uid()), log.Kv("dataId:", data.Id))
			return
		}

		// rpc before hook
		if code := service.Before(p, uint16(data.Id), in); code != error_code.ERROR_OK {
			out := &cs.LCSeasonInfoRsp{}
			p.SendToClient(rpc_def.LCSeasonInfoRsp, out, false)
			return
		}

		p.OnRcvPacket(uint16(data.Id))
		ret := service.SeasonInfo(ctx, p, in)
		p.SendToClient(rpc_def.LCSeasonInfoRsp, ret, false)
		return
	}
	dispatch.Register(uint32(rpc_def.CLSeasonInfoReq), SeasonInfoHandler)
}
