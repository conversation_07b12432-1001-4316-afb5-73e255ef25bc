{"swagger": "2.0", "info": {"title": "general-services/charlist/v1/charlist.proto", "version": "version not set"}, "tags": [{"name": "CharListDotNet"}, {"name": "CharListHttp"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/charlist/getcharlist/{account}": {"get": {"operationId": "CharListHttp_GetCharListData", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/v1GetCharListReply"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/rpcStatus"}}}, "parameters": [{"name": "account", "in": "path", "required": true, "type": "string"}], "tags": ["CharListHttp"]}}}, "definitions": {"protobufAny": {"type": "object", "properties": {"@type": {"type": "string"}}, "additionalProperties": {}}, "rpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/protobufAny"}}}}, "v1CharListDataMsg": {"type": "object", "properties": {"account": {"type": "string"}, "charGuid": {"type": "string", "format": "uint64"}, "charName": {"type": "string"}, "level": {"type": "integer", "format": "int32"}}, "title": "Type:Http"}, "v1CharListMsg": {"type": "object", "properties": {"zwid": {"type": "integer", "format": "int32"}, "account": {"type": "string"}, "charGuid": {"type": "string", "format": "uint64"}, "charName": {"type": "string"}, "level": {"type": "integer", "format": "int32"}}}, "v1GetCharListReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}, "zwidData": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1ZwidDataMsg"}}}, "title": "Type:Http"}, "v1SaveCharListReply": {"type": "object", "properties": {"result": {"type": "integer", "format": "int32"}}, "title": "The response message containing the message"}, "v1ZwidDataMsg": {"type": "object", "properties": {"zwid": {"type": "integer", "format": "int32"}, "charListData": {"type": "array", "items": {"type": "object", "$ref": "#/definitions/v1CharListDataMsg"}}}, "title": "Type:Http"}}}