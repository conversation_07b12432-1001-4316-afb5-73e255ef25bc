syntax = "proto3";

import "descriptor.proto";
import "PublicMessage.proto";
import "PublicEnum.proto";

package natsrpc;
option go_package = "liteframe/internal/common/natsrpc";
option csharp_namespace = "BattleServer.Service";

message AuthReq {
  string uuid = 1;
  string token = 2;
}

message AuthResp {
  int32 code = 1;
  string message = 2;
}

message MatchResultRequest {
  bool success = 1; // true if the match was successful
  uint64 uid = 2; // 请求匹配的玩家id
  int64 battle_id = 3; // 房间id
  int64 server_id = 5; // 房间所在服务器id
  repeated PBBattlePlayerInfo target = 6; // 匹配到的玩家信息
  int64 create_time = 7; // 房间创建时间
}

message MatchResultResponse {
  int32 code = 1; // 0表示成功，其他表示失败
}

message BattleStateChangeReq {
  int64 battle_id = 1;          // 战斗ID
  BattleState state = 2;        // 战斗状态
  int32 remain_time_ms = 3;     // 当前阶段剩余时间(毫秒)
  int32 round_count = 4;        // 当前回合数
}

message BattleStateChangeResp {
  int32 code = 1; // 0表示成功，其他表示失败
}

//新回合开始
message RoundStartReq
{
  uint64 uid = 1; 
  repeated int32 buffers = 2;   // 3 个buffer选择
  repeated PBPlayerBoard playerBoards = 3; // 双方玩家的棋盘信息
}

message RoundStartResp
{
	int32 code = 1;
}

//战斗开始
message RoundBattleStartReq
{
  uint64 uid = 1; 
  int32 seed = 2; //随机种子
  int64 battle_id = 3;
  repeated PBBattleCampInfo team = 4; //对战双方的数据 第一个是先手方
}

message RoundBattleStartResp
{
	int32 code = 1;
}

message RoundBattleEndReq
{
  uint64 uid = 1; 
  uint64 winUid = 2;
  uint64 loseUid = 3;
  bool isEnd = 4;
}

message RoundBattleEndResp
{
	int32 code = 1;
}

//整场战斗结束
message BattleEndReq
{
  uint64 uid = 1;                       // 玩家ID
  int64 battle_id = 2;                  // 战斗ID
  int32 rank = 3;                       // 最终排名
  int32 win_streak = 4;                 // 战斗结束时的连胜场次
  repeated PBBattleHeroInfo heros = 5;  // 最终阵容
}

//整场战斗结束
message BattleEndResp
{
	int32 code = 1;
}

service GameService {
  rpc Auth(AuthReq) returns (AuthResp) {
    option (use_server_id) = true;
  };

  rpc MatchResult(MatchResultRequest) returns (MatchResultResponse){
    option (use_server_id) = true;
    option (publish) = true;
  }

  rpc RoundStart(RoundStartReq) returns (RoundStartResp){
    option (use_server_id) = true;
    option (publish) = true;
  }

  rpc RoundBattleStart(RoundBattleStartReq) returns (RoundBattleStartResp){
    option (use_server_id) = true;
    option (publish) = true;
  }

  rpc RoundBattleEnd(RoundBattleEndReq) returns (RoundBattleEndResp){
    option (use_server_id) = true;
    option (publish) = true;
  }

  rpc BattleEnd(BattleEndReq) returns (BattleEndResp){
    option (use_server_id) = true;
    option (publish) = true;
  }

  rpc OnBattleStateChanged(BattleStateChangeReq) returns (BattleStateChangeResp) {
    option (use_server_id) = true;
    option (publish) = true;
  }
}