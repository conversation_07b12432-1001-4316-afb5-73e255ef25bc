package main

import (
	"fmt"
	"liteframe/internal/common/constant"
	"liteframe/internal/common/version"
	"liteframe/internal/game-logic/payserver/server"
	"os"

	"github.com/urfave/cli/v2"
)

func main() {
	app := cli.App{
		Name:    string(constant.ServiceNamePayServer),
		Flags:   server.Flags(),
		Version: version.String(),
		Action:  mainLoop,
	}
	err := app.Run(os.Args)
	if err != nil {
		fmt.Printf("server run failed! err:%v", err.Error())
	} else {
		fmt.Printf("server close!")
	}
}

func mainLoop(c *cli.Context) error {
	s := server.NewServer()

	if err := s.Init(); err != nil {
		return err
	}

	if err := s.Run(); err != nil {
		return err
	}

	return s.Exit()
}
