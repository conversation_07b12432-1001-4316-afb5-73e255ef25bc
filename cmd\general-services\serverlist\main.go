package main

import (
	"context"
	"flag"
	"fmt"
	"liteframe/internal/common/constant"
	"liteframe/internal/general-services/serverlist/boot"
	"os"

	"github.com/go-kratos/kratos/contrib/registry/etcd/v2"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	"github.com/go-kratos/kratos/v2/transport/http"
	_ "go.uber.org/automaxprocs"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string
	// Version is the version of the compiled software.
	Version       string
	ServerVersion string
	ClientVersion string

	id, _   = os.Hostname()
	PidFile = constant.ServiceNameServerList + ".pid"
)

func newApp(logger log.Logger, gs *grpc.Server, hs *http.Server, r *etcd.Registry, meta map[string]string) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(meta),
		kratos.Logger(logger),
		kratos.Server(
			gs,
			hs,
		),
		kratos.Registrar(r),
		kratos.AfterStart(afterStart),
	)
}

func afterStart(context.Context) error {
	pid := fmt.Sprintf("%d", os.Getpid())
	f, err := os.OpenFile(PidFile, os.O_RDWR|os.O_CREATE|os.O_APPEND, 0666)
	if err != nil {
		panic(err)
	}
	err = os.WriteFile(PidFile, []byte(pid), 0)
	if err != nil {
		panic(err)
	}
	err = f.Close()
	if err != nil {
		panic(err)
	}
	return nil
}

func main() {
	flag.Parse()

	// 初始配置
	bc := boot.NewBootConf()
	bs := bc.Default()
	client := boot.NewEtcdClient(bs)
	bc.LoadConfigFromEtcd(client, bs)

	// 初始化log
	logger := boot.NewBootLog(bs).Run()
	logHelper := log.NewHelper(logger)

	logHelper.Infof("Starting service: name=%s, version=%s", Name, Version)

	metaData := map[string]string{}
	metaData["ServerVersion"] = ServerVersion
	metaData["ClientVersion"] = ClientVersion

	app, cleanup, err := wireApp(bs.Server, bs.Data, bs, logger, client, metaData)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
