package player

import (
	"context"
	"liteframe/internal/common/error_code"
	"liteframe/internal/common/natsrpc"
	"liteframe/internal/common/protos/cs"
	"liteframe/internal/common/protos/dbstruct"
	"liteframe/internal/common/protos/public"
	"liteframe/internal/game-logic/gameserver/global"
	"liteframe/pkg/log"
	"strconv"
)

type Battle struct {
	player         *Player
	battleServerId string
	battleId       int64
}

func NewBattle(player *Player) *Battle {
	return &Battle{player: player}
}

// GetBattleId 获取当前战斗ID
func (b *Battle) GetBattleId() int64 {
	return b.battleId
}

// GetBattleServerId 获取当前战斗服务器ID
func (b *Battle) GetBattleServerId() string {
	return b.battleServerId
}

// IsInBattle 检查玩家是否在战斗中
func (b *Battle) IsInBattle() bool {
	return b.battleServerId != "" && b.battleId != 0
}

// ForceClearBattleState 强制清理战斗状态 - 用于修复状态不一致的情况
func (b *Battle) ForceClearBattleState() {
	if b.battleServerId != "" || b.battleId != 0 {
		log.Error("force clearing inconsistent battle state",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId),
			log.Kv("battleId", b.battleId))

		b.battleServerId = ""
		b.battleId = 0

		log.Info("battle state force cleared", log.Kv("uid", b.player.Uid()))
	}
}

// InitDB 初始化模块数据
func (b *Battle) InitDB(db *dbstruct.UserDB) {

}

// OnCrossDay 实现Module接口的跨天方法
func (b *Battle) OnCrossDay(natural bool, nowUnix int64) {

}

func (b *Battle) Match(ctx context.Context, req *cs.CLMatchReq) *cs.LCMatchRsp {
	//return &cs.LCMatchRsp{}

	ret := &cs.LCMatchRsp{
		Code: int32(error_code.ERROR_OK),
	}

	matchReq := &natsrpc.MatchRequest{
		Player: &public.PBBattlePlayerInfo{
			Uid:      b.player.Uid(),
			Name:     b.player.Name(),
			ServerId: strconv.Itoa(int(global.ServerId)),
			Throphy:  1,
			Kills:    0,
			WinCount: 0,
		},
		Team: &public.PBBattleTeamInfo{
			Heros: []*public.PBBattleHeroInfo{
				{Id: 101, Level: 10, AwakeLevel: 0, StarLevel: 1},
				{Id: 102, Level: 10, AwakeLevel: 0, StarLevel: 1},
				{Id: 103, Level: 10, AwakeLevel: 0, StarLevel: 1},
			},
		},
	}

	//teamInfo := b.player.lineup.getCurrentLineup()
	//for _, value := range teamInfo.HeroIds {
	//	heroInfo := b.player.hero.GetHeroById(value)
	//
	//	pbHero := &public.PBBattleHeroInfo{Id: value, Level: heroInfo.HeroLevel}
	//	matchReq.Team.Heros = append(matchReq.Team.Heros, pbHero)
	//}

	resp, err := matchServiceClient.Match(ctx, matchReq)

	if err != nil {
		ret.Code = resp.Code
		log.Error("match request failed", log.Err(err), log.Kv("uid", b.player.Uid()))
		return ret
	}

	return ret
}

func (b *Battle) RoundBattleStart(ctx context.Context, req *cs.CLRoundBattleStartReq) *cs.LCRoundBattleStartResp {
	ret := &cs.LCRoundBattleStartResp{
		Code: int32(error_code.ERROR_OK),
	}

	// 检查玩家是否在战斗中
	if !b.IsInBattle() {
		log.Error("player not in battle, cannot call EnterBattle",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId),
			log.Kv("battleId", b.battleId),
			log.Kv("battleServerId_empty", b.battleServerId == ""),
			log.Kv("battleId_zero", b.battleId == 0))

		// 如果状态不一致（有battleServerId但没有battleId），强制清理
		if b.battleServerId != "" && b.battleId == 0 {
			log.Error("detected inconsistent battle state, force clearing",
				log.Kv("uid", b.player.Uid()),
				log.Kv("battleServerId", b.battleServerId))
			b.ForceClearBattleState()
		}

		ret.Code = int32(error_code.ERROR_PARAMS)
		return ret
	}

	log.Info("calling EnterBattle RPC",
		log.Kv("uid", b.player.Uid()),
		log.Kv("battleServerId", b.battleServerId))

	matchReq := &natsrpc.EnterBattleReq{
		Uid: b.player.Uid(),
	}
	resp, err := battleServiceClient.EnterBattle(ctx, matchReq, b.battleServerId)
	if err != nil {
		ret.Code = resp.Code
		log.Error("EnterBattle RPC failed",
			log.Err(err),
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId))
		return ret
	}

	log.Info("EnterBattle RPC success",
		log.Kv("uid", b.player.Uid()),
		log.Kv("battleServerId", b.battleServerId),
		log.Kv("responseCode", resp.Code))

	return ret
}

// cleanupBattleOnLogout 玩家登出时清理战斗资源
func (b *Battle) cleanupBattleOnLogout() {
	// 检查玩家是否在战斗中
	if b.battleServerId == "" && b.battleId == 0 {
		log.Debug("player not in battle, no cleanup needed", log.Kv("uid", b.player.Uid()))
		return
	}

	log.Info("player logout, cleaning up battle resources",
		log.Kv("uid", b.player.Uid()),
		log.Kv("battleServerId", b.battleServerId),
		log.Kv("battleId", b.battleId))

	// 调用BattleServer的LeaveBattle RPC接口
	ctx := context.Background()
	leaveBattleReq := &natsrpc.LeaveBattleReq{
		Uid: b.player.Uid(),
	}

	err := battleServiceClient.LeaveBattle(ctx, leaveBattleReq, b.battleServerId)
	if err != nil {
		log.Error("LeaveBattle RPC failed",
			log.Err(err),
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId))
		// 即使RPC失败，也继续清理本地状态
	} else {
		log.Info("LeaveBattle RPC sent successfully",
			log.Kv("uid", b.player.Uid()),
			log.Kv("battleServerId", b.battleServerId))
	}

	// 清理本地状态
	b.battleServerId = ""
	b.battleId = 0

	log.Info("battle cleanup completed for logout player", log.Kv("uid", b.player.Uid()))
}

func (b *Battle) SelectBuffer(ctx context.Context, req *cs.CLSelectBufferReq) *cs.LCSelectBufferResp {
	ret := &cs.LCSelectBufferResp{
		Code: int32(error_code.ERROR_OK),
	}

	matchReq := &natsrpc.SelectBufferReq{
		Uid:      b.player.Uid(),
		BufferID: req.BufferId,
	}
	resp, err := battleServiceClient.SelectBuffer(ctx, matchReq, b.battleServerId)
	if err != nil {
		ret.Code = resp.Code
		log.Error("select buffer request failed", log.Err(err), log.Kv("uid", b.player.Uid()))
		return ret
	}

	// 传递新生成的英雄信息
	if resp.NewHeroes != nil {
		ret.NewHeroes = resp.NewHeroes
	}

	log.Debug("player select buffer", log.Kv("uid", b.player.Uid()), log.Kv("code", resp.Code),
		log.Kv("bufferId", req.BufferId), log.Kv("newHeroes", len(resp.NewHeroes)))

	return ret
}

func (b *Battle) MergeHero(ctx context.Context, req *cs.CLMergeReq) *cs.LCMergeRsp {
	ret := &cs.LCMergeRsp{
		Code: int32(error_code.ERROR_OK),
	}

	matchReq := &natsrpc.MergeHeroReq{
		Uid:  b.player.Uid(),
		From: req.From,
		To:   req.To,
	}

	// 传递移动操作列表
	if req.Moves != nil {
		matchReq.Moves = req.Moves
	}

	resp, err := battleServiceClient.MergeHero(ctx, matchReq, b.battleServerId)
	if err != nil {
		ret.Code = resp.Code
		log.Error("merge hero request failed", log.Err(err), log.Kv("uid", b.player.Uid()))
		return ret
	}

	ret.From = resp.From
	ret.To = resp.To
	if resp.NewHero != nil {
		ret.NewHero = &public.PBCheckerBoard{
			GridID: resp.To, // 使用目标格子ID
			Hero:   resp.NewHero,
		}
	}
	log.Debug("player merge hero", log.Kv("uid", b.player.Uid()), log.Kv("code", resp.Code),
		log.Kv("from", resp.From), log.Kv("to", resp.To), log.Kv("newHero", ret.NewHero))

	return ret
}

func (b *Battle) BattleReady(ctx context.Context, req *cs.CLReadyReq) *cs.LCReadyRsp {
	ret := &cs.LCReadyRsp{
		Code: int32(error_code.ERROR_OK),
	}

	matchReq := &natsrpc.ReadyBattleReq{
		Uid: b.player.Uid(),
	}

	// 传递移动操作列表到BattleServer
	if req.Moves != nil {
		matchReq.Moves = req.Moves
		log.Info("BattleReady with moves", log.Kv("uid", b.player.Uid()), log.Kv("moveCount", len(req.Moves)))
	}

	resp, err := battleServiceClient.BattleReady(ctx, matchReq, b.battleServerId)
	if err != nil {
		ret.Code = resp.Code
		log.Error("battle ready request failed", log.Err(err), log.Kv("uid", b.player.Uid()))
		return ret
	}

	log.Debug("player battle ready", log.Kv("uid", b.player.Uid()), log.Kv("code", resp.Code))

	return ret
}

func (b *Battle) RoundBattleEnd(ctx context.Context, req *cs.CLRoundBattleEndReq) *cs.LCRoundBattleEndResp {
	ret := &cs.LCRoundBattleEndResp{
		Code: int32(error_code.ERROR_OK),
	}

	matchReq := &natsrpc.EndBattleReq{
		Uid: b.player.Uid(),
		Win: req.Win,
	}
	resp, err := battleServiceClient.EndBattle(ctx, matchReq, b.battleServerId)
	if err != nil {
		ret.Code = resp.Code
		log.Error("round end request failed", log.Err(err), log.Kv("uid", b.player.Uid()))
		return ret
	}

	log.Debug("player round battle end",
		log.Kv("uid", b.player.Uid()),
		log.Kv("code", resp.Code))
	return ret
}

// ProcessBattleEnd 处理战斗结束（由BattleServer通过RPC调用）
func (b *Battle) ProcessBattleEnd(req *natsrpc.BattleEndReq) {
	// 调用Trophy模块处理战斗结算
	notify := b.player.Trophy().ProcessBattleEnd(req.Rank, req.WinStreak, req.Heros)

	// 发送结算通知给客户端
	b.player.NotifyBattleEnd(notify)

	// 清理战斗状态
	b.battleServerId = ""
	b.battleId = 0

	log.Info("Battle end processed and notified",
		log.Kv("player_id", b.player.Uid()),
		log.Kv("rank", req.Rank),
		log.Kv("win_streak", req.WinStreak))
}

// ClaimAdReward 领取广告奖励
func (b *Battle) ClaimAdReward(ctx context.Context, req *cs.CLClaimAdRewardReq) *cs.LCClaimAdRewardRsp {
	// 调用Trophy模块处理广告奖励领取
	// TODO 接入广告SDK后,不通过客户端发送消息，直接回调Trophy模块
	return b.player.Trophy().ClaimAdReward(ctx, req)
}
