//
// @Author:zhouchen
// @Description: 消息包文件定义
// @Data: Created in 20:01 2023/6/6

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.2
// 	protoc        v5.28.1
// source: general-services/announcement/v1/announcement.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Type:Http
type GetAnnouncementListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Noticeid string `protobuf:"bytes,1,opt,name=noticeid,proto3" json:"noticeid,omitempty"`
}

func (x *GetAnnouncementListReq) Reset() {
	*x = GetAnnouncementListReq{}
	mi := &file_general_services_announcement_v1_announcement_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAnnouncementListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnnouncementListReq) ProtoMessage() {}

func (x *GetAnnouncementListReq) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_announcement_v1_announcement_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnnouncementListReq.ProtoReflect.Descriptor instead.
func (*GetAnnouncementListReq) Descriptor() ([]byte, []int) {
	return file_general_services_announcement_v1_announcement_proto_rawDescGZIP(), []int{0}
}

func (x *GetAnnouncementListReq) GetNoticeid() string {
	if x != nil {
		return x.Noticeid
	}
	return ""
}

// Type:Http
type NoticeItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoticeId   string `protobuf:"bytes,1,opt,name=NoticeId,proto3" json:"NoticeId,omitempty"`
	Title      string `protobuf:"bytes,2,opt,name=Title,proto3" json:"Title,omitempty"`
	Priority   string `protobuf:"bytes,3,opt,name=Priority,proto3" json:"Priority,omitempty"`
	Context1   string `protobuf:"bytes,4,opt,name=Context1,proto3" json:"Context1,omitempty"`
	Context2   string `protobuf:"bytes,5,opt,name=Context2,proto3" json:"Context2,omitempty"`
	Context3   string `protobuf:"bytes,6,opt,name=Context3,proto3" json:"Context3,omitempty"`
	CreateTime int64  `protobuf:"varint,7,opt,name=CreateTime,proto3" json:"CreateTime,omitempty"`
	LastTime   int64  `protobuf:"varint,8,opt,name=LastTime,proto3" json:"LastTime,omitempty"`
	AutoOpen   int32  `protobuf:"varint,9,opt,name=AutoOpen,proto3" json:"AutoOpen,omitempty"`
	BgUrl      string `protobuf:"bytes,10,opt,name=BgUrl,proto3" json:"BgUrl,omitempty"`
}

func (x *NoticeItem) Reset() {
	*x = NoticeItem{}
	mi := &file_general_services_announcement_v1_announcement_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NoticeItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NoticeItem) ProtoMessage() {}

func (x *NoticeItem) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_announcement_v1_announcement_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NoticeItem.ProtoReflect.Descriptor instead.
func (*NoticeItem) Descriptor() ([]byte, []int) {
	return file_general_services_announcement_v1_announcement_proto_rawDescGZIP(), []int{1}
}

func (x *NoticeItem) GetNoticeId() string {
	if x != nil {
		return x.NoticeId
	}
	return ""
}

func (x *NoticeItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *NoticeItem) GetPriority() string {
	if x != nil {
		return x.Priority
	}
	return ""
}

func (x *NoticeItem) GetContext1() string {
	if x != nil {
		return x.Context1
	}
	return ""
}

func (x *NoticeItem) GetContext2() string {
	if x != nil {
		return x.Context2
	}
	return ""
}

func (x *NoticeItem) GetContext3() string {
	if x != nil {
		return x.Context3
	}
	return ""
}

func (x *NoticeItem) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *NoticeItem) GetLastTime() int64 {
	if x != nil {
		return x.LastTime
	}
	return 0
}

func (x *NoticeItem) GetAutoOpen() int32 {
	if x != nil {
		return x.AutoOpen
	}
	return 0
}

func (x *NoticeItem) GetBgUrl() string {
	if x != nil {
		return x.BgUrl
	}
	return ""
}

// Type:Http
type GetAnnouncementListReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Result         int32         `protobuf:"varint,1,opt,name=result,proto3" json:"result,omitempty"`
	AutoOpen       int32         `protobuf:"varint,2,opt,name=autoOpen,proto3" json:"autoOpen,omitempty"`
	Noticelistdata []*NoticeItem `protobuf:"bytes,3,rep,name=noticelistdata,proto3" json:"noticelistdata,omitempty"`
}

func (x *GetAnnouncementListReply) Reset() {
	*x = GetAnnouncementListReply{}
	mi := &file_general_services_announcement_v1_announcement_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAnnouncementListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnnouncementListReply) ProtoMessage() {}

func (x *GetAnnouncementListReply) ProtoReflect() protoreflect.Message {
	mi := &file_general_services_announcement_v1_announcement_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnnouncementListReply.ProtoReflect.Descriptor instead.
func (*GetAnnouncementListReply) Descriptor() ([]byte, []int) {
	return file_general_services_announcement_v1_announcement_proto_rawDescGZIP(), []int{2}
}

func (x *GetAnnouncementListReply) GetResult() int32 {
	if x != nil {
		return x.Result
	}
	return 0
}

func (x *GetAnnouncementListReply) GetAutoOpen() int32 {
	if x != nil {
		return x.AutoOpen
	}
	return 0
}

func (x *GetAnnouncementListReply) GetNoticelistdata() []*NoticeItem {
	if x != nil {
		return x.Noticelistdata
	}
	return nil
}

var File_general_services_announcement_v1_announcement_proto protoreflect.FileDescriptor

var file_general_services_announcement_v1_announcement_proto_rawDesc = []byte{
	0x0a, 0x33, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x76, 0x31, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76,
	0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x34, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x74,
	0x69, 0x63, 0x65, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x6f, 0x74,
	0x69, 0x63, 0x65, 0x69, 0x64, 0x22, 0x9c, 0x02, 0x0a, 0x0a, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x72, 0x69, 0x6f, 0x72, 0x69,
	0x74, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x31, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x31, 0x12, 0x1a,
	0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x32, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x32, 0x12, 0x1a, 0x0a, 0x08, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x33, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x33, 0x12, 0x1e, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x4c, 0x61, 0x73, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x4c, 0x61, 0x73, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x41, 0x75, 0x74, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x41, 0x75, 0x74, 0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x14,
	0x0a, 0x05, 0x42, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x42,
	0x67, 0x55, 0x72, 0x6c, 0x22, 0x8e, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f,
	0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x75, 0x74,
	0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x61, 0x75, 0x74,
	0x6f, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x3e, 0x0a, 0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x6c,
	0x69, 0x73, 0x74, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f, 0x74, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x6c, 0x69, 0x73,
	0x74, 0x64, 0x61, 0x74, 0x61, 0x32, 0xea, 0x03, 0x0a, 0x0c, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e,
	0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x9b, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41, 0x6e,
	0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61,
	0x74, 0x61, 0x12, 0x22, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x36, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x30, 0x12, 0x2e, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x67, 0x65, 0x74, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x12, 0x92, 0x01, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x41, 0x6e, 0x6e, 0x6f,
	0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x16, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x6f,
	0x74, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x1a, 0x24, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x39,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x33, 0x3a, 0x01, 0x2a, 0x22, 0x2e, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x64, 0x64, 0x61, 0x6e, 0x6e,
	0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0xa6, 0x01, 0x0a, 0x17, 0x44, 0x65,
	0x6c, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x24, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x6e, 0x6f, 0x75, 0x6e,
	0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x41, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x3b, 0x12, 0x39, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e,
	0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x75,
	0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x64, 0x65, 0x6c, 0x61, 0x6e, 0x6e, 0x6f, 0x75,
	0x6e, 0x63, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x7b, 0x6e, 0x6f, 0x74, 0x69, 0x63, 0x65, 0x69,
	0x64, 0x7d, 0x42, 0x25, 0x5a, 0x23, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x75, 0x6e, 0x63, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_general_services_announcement_v1_announcement_proto_rawDescOnce sync.Once
	file_general_services_announcement_v1_announcement_proto_rawDescData = file_general_services_announcement_v1_announcement_proto_rawDesc
)

func file_general_services_announcement_v1_announcement_proto_rawDescGZIP() []byte {
	file_general_services_announcement_v1_announcement_proto_rawDescOnce.Do(func() {
		file_general_services_announcement_v1_announcement_proto_rawDescData = protoimpl.X.CompressGZIP(file_general_services_announcement_v1_announcement_proto_rawDescData)
	})
	return file_general_services_announcement_v1_announcement_proto_rawDescData
}

var file_general_services_announcement_v1_announcement_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_general_services_announcement_v1_announcement_proto_goTypes = []any{
	(*GetAnnouncementListReq)(nil),   // 0: message.v1.GetAnnouncementListReq
	(*NoticeItem)(nil),               // 1: message.v1.NoticeItem
	(*GetAnnouncementListReply)(nil), // 2: message.v1.GetAnnouncementListReply
}
var file_general_services_announcement_v1_announcement_proto_depIdxs = []int32{
	1, // 0: message.v1.GetAnnouncementListReply.noticelistdata:type_name -> message.v1.NoticeItem
	0, // 1: message.v1.Announcement.GetAnnouncementListData:input_type -> message.v1.GetAnnouncementListReq
	1, // 2: message.v1.Announcement.AddAnnouncementItemData:input_type -> message.v1.NoticeItem
	0, // 3: message.v1.Announcement.DelAnnouncementItemData:input_type -> message.v1.GetAnnouncementListReq
	2, // 4: message.v1.Announcement.GetAnnouncementListData:output_type -> message.v1.GetAnnouncementListReply
	2, // 5: message.v1.Announcement.AddAnnouncementItemData:output_type -> message.v1.GetAnnouncementListReply
	2, // 6: message.v1.Announcement.DelAnnouncementItemData:output_type -> message.v1.GetAnnouncementListReply
	4, // [4:7] is the sub-list for method output_type
	1, // [1:4] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_general_services_announcement_v1_announcement_proto_init() }
func file_general_services_announcement_v1_announcement_proto_init() {
	if File_general_services_announcement_v1_announcement_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_general_services_announcement_v1_announcement_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_general_services_announcement_v1_announcement_proto_goTypes,
		DependencyIndexes: file_general_services_announcement_v1_announcement_proto_depIdxs,
		MessageInfos:      file_general_services_announcement_v1_announcement_proto_msgTypes,
	}.Build()
	File_general_services_announcement_v1_announcement_proto = out.File
	file_general_services_announcement_v1_announcement_proto_rawDesc = nil
	file_general_services_announcement_v1_announcement_proto_goTypes = nil
	file_general_services_announcement_v1_announcement_proto_depIdxs = nil
}
