// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"liteframe/internal/microservices/chat/biz"
	"liteframe/internal/microservices/chat/conf"
	"liteframe/internal/microservices/chat/data"
	"liteframe/internal/microservices/chat/registry"
	"liteframe/internal/microservices/chat/server"
	"liteframe/internal/microservices/chat/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init chatservice application.
func wireApp(chatServerInfo *conf.ChatServerInfo, confServer *conf.Server, bootstrap *conf.Bootstrap, logger log.Logger, arg map[string]string) (*kratos.App, func(), error) {
	cmdable := data.NewRedisCmd(bootstrap, logger)
	confRegistry := registry.ProvideRegistry(bootstrap)
	etcdRegistry := registry.NewEtcdRegistry(confRegistry)
	playerInfoServiceClient := data.NewPlayerInfoServiceClient(etcdRegistry)
	textDetectClient := data.NewTextDetectClient(etcdRegistry)
	teamCenterServiceClient := data.NewTeamCenterServiceClient(etcdRegistry)
	rankServiceClient := data.NewRankServiceClient(etcdRegistry)
	friendserviceClient := data.NewFriendserviceClient(etcdRegistry)
	dataData, err := data.NewData(cmdable, logger, playerInfoServiceClient, textDetectClient, teamCenterServiceClient, rankServiceClient, friendserviceClient)
	if err != nil {
		return nil, nil, err
	}
	chatRepo := data.NewChatRepo(dataData, logger, chatServerInfo, etcdRegistry)
	chatUseCase := biz.NewChatUsecase(chatRepo, logger)
	chatService := service.NewChatService(chatServerInfo, bootstrap, logger, chatUseCase)
	websocketServer := server.NewWebsocketServer(confServer, logger, chatService)
	grpcServer := server.NewGRPCServer(confServer, chatService, logger)
	app := newApp(chatServerInfo, logger, websocketServer, grpcServer, etcdRegistry, arg)
	return app, func() {
	}, nil
}
