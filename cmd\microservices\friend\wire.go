//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"liteframe/internal/microservices/friend/biz"
	"liteframe/internal/microservices/friend/conf"
	"liteframe/internal/microservices/friend/data"
	"liteframe/internal/microservices/friend/registry"
	"liteframe/internal/microservices/friend/server"
	"liteframe/internal/microservices/friend/service"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init serverlist application.
func wireApp(*conf.Server, *conf.Data, *conf.Bootstrap, log.Logger, map[string]string) (*kratos.App, func(), error) {
	panic(wire.Build(server.ProviderSet, data.ProviderSet, biz.ProviderSet, service.ProviderSet, registry.ProviderSet, newApp))
}
